package com.sgs.testdatabiz.facade.model.req.rd;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("根据ID更新Report过期状态请求")
public class UpdateReportExpireByIdReq extends BaseModel {
    
    @ApiModelProperty("报告ID")
    @NotNull
    private Long reportId;
    
    @ApiModelProperty("状态值")
    @NotNull
    private Integer activeIndicator;

    @Override
    public String getExtId() {
        return reportId.toString();
    }
}