#!/bin/bash

source "/etc/profile"
GCLOGPATH="/usr/local/applogs/testdatabiz.iapi.sgs.net/gc.log"
APP_LOG="/usr/local/applogs/testdatabiz.iapi.sgs.net"
MAIN_CLASS="com.sgs.testdatabiz.web.TestDataBizApplication"
APP_NAME="testdatabiz.iapi.sgs.net"
CLASS_PATH="conf:lib/*"
nodeip=$(ifconfig eth0|awk '/inet /{print $2}')
JAVA_OPTS=" -server \
            -Xms1024m -Xmx1024m \
            -XX:MaxMetaspaceSize=512m \
            -Djava.rmi.server.hostname=${nodeip} \
            -Dcom.sun.management.jmxremote \
            -Dcom.sun.management.jmxremote.port=8986\
            -Dcom.sun.management.jmxremote.authenticate=false \
            -Dcom.sun.management.jmxremote.ssl=false \
            -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled \
            -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=75 \
            -XX:+ScavengeBeforeFullGC -XX:+CMSScavengeBeforeRemark \
            -XX:+PrintGCDateStamps -verbose:gc -XX:+PrintGCDetails -Xloggc:/usr/local/applogs/${APP_NAME}/gc.log \
            -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M \
            -Dsun.net.inetaddr.ttl=60 \
            -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/local/applogs/${APP_NAME}/heapdump.hprof \
            -Dspring.profiles.active=local"

#############intial work##########################
#cd /usr/local/${APP_NAME}/default
#if [ -e "logs" ]; then
#    rm logs
#fi
#ln -s /usr/local/log/${APP_NAME}/ logs

if [ ! -d $APP_LOG ]; then
    mkdir -p $APP_LOG
fi

##############launch the service##################
java ${JAVA_OPTS} -cp ${CLASS_PATH} ${MAIN_CLASS} >> ${GCLOGPATH} 2>&1

##############check the service####################
#sleep 5
#ps aux | grep ${MAIN_CLASS} | grep -v grep > /dev/null 2>&1
#if [ $? -eq 0 ]; then
#    exit 0
#else
#    exit 1
#fi
