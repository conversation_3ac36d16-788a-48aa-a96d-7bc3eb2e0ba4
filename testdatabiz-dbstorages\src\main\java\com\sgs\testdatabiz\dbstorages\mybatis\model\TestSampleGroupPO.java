package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class TestSampleGroupPO {
    /**
     * id BIGINT(19) 必填<br>
     * ID,Primary key
     */
    private Long id;

    /**
     * sample_group_id VARCHAR(64)<br>
     * 
     */
    private String sampleGroupId;

    /**
     * sample_id VARCHAR(64)<br>
     * FK tb_TestSample
     */
    private String sampleId;

    /**
     * main_sample_flag INTEGER(10)<br>
     * 主测试样标识
     */
    private Integer mainSampleFlag;

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_date TIMESTAMP(19)<br>
     * CreatedDate
     */
    private Date createdDate;

    /**
     * created_by VARCHAR(50)<br>
     * CreatedBy
     */
    private String createdBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * ModifiedDate
     */
    private Date modifiedDate;

    /**
     * modified_by VARCHAR(50)<br>
     * ModifiedBy
     */
    private String modifiedBy;

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 ID,Primary key
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 ID,Primary key
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * sample_group_id VARCHAR(64)<br>
     * 获得 
     */
    public String getSampleGroupId() {
        return sampleGroupId;
    }

    /**
     * sample_group_id VARCHAR(64)<br>
     * 设置 
     */
    public void setSampleGroupId(String sampleGroupId) {
        this.sampleGroupId = sampleGroupId == null ? null : sampleGroupId.trim();
    }

    /**
     * sample_id VARCHAR(64)<br>
     * 获得 FK tb_TestSample
     */
    public String getSampleId() {
        return sampleId;
    }

    /**
     * sample_id VARCHAR(64)<br>
     * 设置 FK tb_TestSample
     */
    public void setSampleId(String sampleId) {
        this.sampleId = sampleId == null ? null : sampleId.trim();
    }

    /**
     * main_sample_flag INTEGER(10)<br>
     * 获得 主测试样标识
     */
    public Integer getMainSampleFlag() {
        return mainSampleFlag;
    }

    /**
     * main_sample_flag INTEGER(10)<br>
     * 设置 主测试样标识
     */
    public void setMainSampleFlag(Integer mainSampleFlag) {
        this.mainSampleFlag = mainSampleFlag;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 CreatedDate
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 CreatedDate
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 CreatedBy
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 CreatedBy
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 ModifiedDate
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 ModifiedDate
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 ModifiedBy
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 ModifiedBy
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 获得 
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 设置 
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sampleGroupId=").append(sampleGroupId);
        sb.append(", sampleId=").append(sampleId);
        sb.append(", mainSampleFlag=").append(mainSampleFlag);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}