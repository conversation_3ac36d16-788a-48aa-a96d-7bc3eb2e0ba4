package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/5/7 16:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPurchaseOrderDTO {

    private RdPurchaseOrderIdDTO id;

    @Data
    public static class RdPurchaseOrderIdDTO{
        @ApiModelProperty(value = "purchaseOrderId",dataType = "string", required = true)
        private String purchaseOrderId;
        @ApiModelProperty(value = "external",dataType = "RdPurchaseOrderIdExternalDTO")
        private RdPurchaseOrderIdExternalDTO external;

        @Data
        public static class RdPurchaseOrderIdExternalDTO{
            @ApiModelProperty(value = "prchaseOrderAssocId",dataType = "string", required = true)
            private String prchaseOrderAssocId;
        }
    }
}
