/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestLineDTO extends RdOrderHeaderDTO implements Serializable {


    @ApiModelProperty(value = "systemId",dataType = "integer", required = true)
    private Integer systemId;
    @ApiModelProperty(value = "testLineInstanceId",dataType = "string", required = true)
    private String testLineInstanceId;
    private String testItemNo;
    @ApiModelProperty(value = "testLineType",dataType = "Integer", required = true)
    private Integer testLineType;
    private Long testLineBaseId;
    @ApiModelProperty(value = "testLineId",dataType = "Integer", required = true)
    private Integer testLineId;
    private Integer testLineVersionId;
    @ApiModelProperty(value = "evaluationAlias",dataType = "String", required = true)
    private String evaluationAlias;
    private String evaluationName;
    private Integer testLineStatus;
    private Integer testLineSeq;
    private Long labSectionBaseId;
    private String labTeam;
    private String productLineAbbr;
    private String testLineRemark;
    //SCI-1378 增加labSectionName
    private String labSectionName;
    private RdTestLineExternalDTO external;
    @ApiModelProperty(value = "citation",dataType = "object", required = true)
    private RdCitationDTO citation;
    private RdWiDTO wi;
    private List<RdAnalyteDTO> analyteList;
    @ApiModelProperty(value = "ppTestLineRelList",dataType = "object", required = true)
    private List<RdPpTestLineRelDTO> ppTestLineRelList;
    private RdConclusionDTO conclusion;
    @ApiModelProperty(value = "languageList",dataType = "object", required = true)
    private List<RdTestLineLanguageDTO> languageList;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "Date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "activeIndicator",dataType = "Integer", required = true)
    private Integer activeIndicator;

    @ApiModelProperty(value = "reportNo",dataType = "String", required = true)
    private String reportNo;



}
