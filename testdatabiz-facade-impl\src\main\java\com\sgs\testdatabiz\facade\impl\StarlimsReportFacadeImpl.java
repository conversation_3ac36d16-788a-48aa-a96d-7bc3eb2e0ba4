package com.sgs.testdatabiz.facade.impl;

import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.SourceDataStrategyFactory;
import com.sgs.testdatabiz.domain.service.StarlimsReportService;
import com.sgs.testdatabiz.facade.IStarlimsReportFacade;
import com.sgs.testdatabiz.facade.model.req.starlims.FolderReportInfoReq;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveStarLimsReportReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("starlimsReportFacade")
public class StarlimsReportFacadeImpl implements IStarlimsReportFacade {

    @Autowired
    StarlimsReportService starlimsReportService;
    @Autowired
    private SourceDataStrategyFactory sourceDataStrategyFactory;

    @Override
    public BaseResponse saveStarlimsReportData(ReceiveStarLimsReportReq reqObject) {
        return BaseResponse.newInstance(sourceDataStrategyFactory.doInvoke(reqObject, SourceTypeEnum.STARLIMS));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse cleanStarlimsData(FolderReportInfoReq reqObject) {
        return BaseResponse.newInstance(starlimsReportService.cleanStarlimsData(reqObject));
    }

    @Override
    public BaseResponse washStarlimsData(JSONObject reqObject) {
        return BaseResponse.newInstance(starlimsReportService.washStarlimsData(reqObject));
    }
}
