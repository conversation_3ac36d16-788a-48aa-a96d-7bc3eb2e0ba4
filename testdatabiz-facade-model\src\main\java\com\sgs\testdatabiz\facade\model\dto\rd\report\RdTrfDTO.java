/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTrfDTO implements Serializable {


    @ApiModelProperty(value = "refSystemId",dataType = "Integer", example = "1", required = true)
    private Integer refSystemId;
    @NotNull
    @ApiModelProperty(value = "trfNo",dataType = "string", required = true)
    private String trfNo;
    private Integer trfStatus;
    @ApiModelProperty(value = "serviceLevel",dataType = "integer",required = true)
    private Integer serviceLevel;
    private String trfTemplateOwner;
    private String trfTemplateId;
    private String trfTemplateName;
    private Date trfSubmissionDate;
    private Date serviceStartDate;
    private Date serviceEndDate;
    private String source;
    /**
     *
     * @Min 1
     * @maxLength 10
     */
    private Integer interfaceExclude;

    private List<RdCustomerProductDTO> customerProductList;
    private List<RdPurchaseOrderDTO> purchaseOrderList;

    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "0:无效 1:有效",dataType = "integer", required = true)
    private Integer activeIndicator;

    @ApiModelProperty(value = "orderList",dataType = "list", required = true)
    private List<RdOrderRelDTO> orderList;

    private RdTrfOtherDTO other;
}
