package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TestLineAnalyteMappingInfoMapper {
    int countByExample(TestLineAnalyteMappingInfoExample example);

    int deleteByExample(TestLineAnalyteMappingInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TestLineAnalyteMappingInfoPO record);

    int insertSelective(TestLineAnalyteMappingInfoPO record);

    List<TestLineAnalyteMappingInfoPO> selectByExample(TestLineAnalyteMappingInfoExample example);

    TestLineAnalyteMappingInfoPO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TestLineAnalyteMappingInfoPO record, @Param("example") TestLineAnalyteMappingInfoExample example);

    int updateByExample(@Param("record") TestLineAnalyteMappingInfoPO record, @Param("example") TestLineAnalyteMappingInfoExample example);

    int updateByPrimaryKeySelective(TestLineAnalyteMappingInfoPO record);

    int updateByPrimaryKey(TestLineAnalyteMappingInfoPO record);
}