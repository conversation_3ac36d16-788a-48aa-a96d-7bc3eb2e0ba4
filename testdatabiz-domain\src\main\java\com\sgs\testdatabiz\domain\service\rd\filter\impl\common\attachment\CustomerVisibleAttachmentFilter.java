package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.attachment;

import com.sgs.framework.model.enums.ActiveType;
import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import com.sgs.testdatabiz.core.enums.FilterType;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdAttachmentDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@DefaultFilter(order = 1)
@Component
public class CustomerVisibleAttachmentFilter extends AbstractAttachmentFilter {
    
    private static final String FILTER_NAME = "CustomerVisibleAttachmentFilter";
    private static final String ATTACHMENT_FIELD = "order.attachmentList";
    private static final String FILTER_MESSAGE = "Filtered customer invisible attachments";

    @Override
    protected String getFilterType() {
        return FilterType.ATTACHMENT_VISIBILITY.name();
    }

    @Override
    protected List<RdAttachmentDO> getAttachmentList(ReportDataDO reportData) {
        return Optional.ofNullable(reportData)
            .map(ReportDataDO::getOrder)
            .map(order -> order.getAttachmentList())
            .orElse(null);
    }

    @Override
    protected String getAttachmentField() {
        return ATTACHMENT_FIELD;
    }

    @Override
    protected String getFilterName() {
        return FILTER_NAME;
    }

    @Override
    protected String getFilterMessage() {
        return FILTER_MESSAGE;
    }

    @Override
    protected boolean filterAttachment(RdAttachmentDO attachment) {
        if(attachment == null|| attachment.getToCustomerFlag() == null){
            return true;
        }
        return Objects.equals(attachment.getToCustomerFlag(), ActiveType.Enable.getStatus());
    }
} 