package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.testline;

import com.sgs.testdatabiz.core.enums.FilterType;
import com.sgs.testdatabiz.core.enums.TestLineType;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestLineDO;
import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import org.springframework.stereotype.Component;

@DefaultFilter(order = 3)
@Component
public class PretreatmentTestLineFilter extends AbstractTestLineFilter {
    
    @Override
    protected String getFilterType() {
        return FilterType.PRETREATMENT_TEST_LINE.name();
    }

    @Override
    protected String getFilterName() {
        return "PretreatmentTestLineFilter";
    }

    @Override
    protected String getFilterMessage() {
        return "Filtered pretreatment test lines";
    }

    @Override
    protected boolean filterTestLine(RdTestLineDO testLine) {
        if (testLine == null) {
            return false;
        }
        if(testLine.getTestLineType() == null){
            return true;
        }
        // 过滤掉预处理测试线
        return !TestLineType.check(testLine.getTestLineType(), TestLineType.Pretreatment);
    }
} 