package com.sgs.testdatabiz.facade.model.rsp.subcontract;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class SubTestDataInfo extends PrintFriendliness {

    private String evaluationAlias;

    private String citationName;

    @ApiModelProperty(value = "Analyte列表")
    private List<AnalyteInfo> analyteList;

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getCitationName() {
        return citationName;
    }

    public void setCitationName(String citationName) {
        this.citationName = citationName;
    }

    public List<AnalyteInfo> getAnalyteList() {
        return analyteList;
    }

    public void setAnalyteList(List<AnalyteInfo> analyteList) {
        this.analyteList = analyteList;
    }
}
