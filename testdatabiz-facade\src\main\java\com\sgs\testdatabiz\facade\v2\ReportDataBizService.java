package com.sgs.testdatabiz.facade.v2;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataBatchDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.facade.model.req.rd.*;
import com.sgs.testdatabiz.facade.model.rsp.rd.BatchExistReportDataResp;
import com.sgs.testdatabiz.facade.model.rsp.rd.ExistReportDataResp;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 10:48
 */
public interface ReportDataBizService {

    /**
     * 导入ReportData
     *
     * @param reportDataDTO import ReportData数据
     * @return BaseResponse结果即为业务操作结果
     */
    BaseResponse<Void> importReportData(@NotNull ReportDataBatchDTO reportDataDTO, Long labId, String labCode, String version, Boolean flag) throws InterruptedException;


    /**
     * 批量导入ReportData
     * @param list ReportData数据
     * @return BaseResponse结果即为业务操作结���
     */
//    BaseResponse<Void> batchImportReportData(@NotEmpty List<ReportDataDTO> list);

    /**
     * 导出ReportData
     *
     * @param labId    labId
     * @param reportNo reportNo
     * @return ReportData明细, 可能为空
     */
    BaseResponse<ReportDataDTO> exportReportData(@NotNull Long labId, @NotEmpty String reportNo, @NotEmpty String labCode);

    /**
     * 导出ReportData列表
     * @param labId labId
     * @param reportNos reportNos
     * @param labCode labCode
     * @return ReportData列表
     */
    BaseResponse<ReportDataDTO> exportReportDataWithFilter(@NotNull Long labId, @NotEmpty String reportNos, @NotEmpty String labCode);

    /**
     * 导出ReportData列表
     * @param labId labId
     * @param reportNos reportNos
     * @param labCode labCode
     * @return ReportData列表
     */
    BaseResponse<List<ReportDataDTO>> exportReportDataListWithFilter(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode,
            @NotEmpty String testSampleLevel);

    BaseResponse<?> exportReportDataList(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode);

    BaseResponse<?> exportReportHeaderList(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode);


    BaseResponse<?> exportReportDataByTrfNo(@NotNull Long labId, @NotBlank String trfNo, @NotEmpty String labCode);

    /**
     * 取消Report,会更改Report的状态为取消
     *
     * @param labId    labId
     * @param reportNo reportNo
     * @return BaseResponse结果即为业务操作结果
     */
    BaseResponse<Void> cancel(@NotNull Long labId, @NotEmpty String reportNo, Integer reportStatus);

    /**
     * 根据ID修改Report状态
     *
     * @param reportId     reportId
     * @param reportStatus 目标状态
     * @return BaseResponse结果即为业务操作结果
     */
    BaseResponse<Void> modifyReportStatusById(@NotNull Long reportId, @NotNull Integer reportStatus);

    /**
     * 根据ID更新Report过期状态
     *
     * @param reportId reportId
     * @param activeIndicator 状态值
     * @return BaseResponse结果即为业务操作结果
     */
    BaseResponse<?> updateReportExpireById(@NotNull Long reportId, @NotNull Integer activeIndicator);
    /**
     * 判断ReportData是否已经存在
     *
     * @param labId    labId
     * @param reportNo reportNo
     * @return ReportData是否存在的结果
     */
    BaseResponse<ExistReportDataResp> existReportData(@NotNull Long labId, @NotEmpty String reportNo, List<String> trfNos);

    BaseResponse<BatchExistReportDataResp> batchExistReportData(@NotNull Long labId, @NotEmpty List<String> reportNos);

    BaseResponse<?> importInvoice(ImportReportInvoiceReq request);

    BaseResponse<?> exportInvoice(ExportReportInvoiceReq request);

    BaseResponse<?> existInvoice(ExistReportInvoiceReq request);

    BaseResponse<?> exportInvoice(ExportReportInvoiceByNosReq request);

    BaseResponse<?> importQuotation(ImportQuotationReq request);

    BaseResponse<?> exportQuotation(ExportQuotationReq request);

    BaseResponse<?> existQuotation(ExistQuotationReq request);


    BaseResponse<?> getSubTestDataInfo(@NotNull ReportDataDTO reportDataDO);

    BaseResponse<?> exportReportDataListAllStatus(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode);

    BaseResponse<?> updateReportExpire(Long labId, List<String> reportNos, String labCode);

    BaseResponse<?> exportByVersion(Long labId, String reportNo, String labCode);

    BaseResponse<?> querySubReportData(QuerySubReportDataReq subReportDataReq);
}
