/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdMaterialAttrDTO implements Serializable {

    private String materialDescription;
    private String materialOtherSampleInfo;
    private String materialEndUse;
    private Integer applicableFlag;
    private String materialRemark;
    private String materialName;
    private String materialColor;
    private String materialTexture;
    private Object extFields;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}