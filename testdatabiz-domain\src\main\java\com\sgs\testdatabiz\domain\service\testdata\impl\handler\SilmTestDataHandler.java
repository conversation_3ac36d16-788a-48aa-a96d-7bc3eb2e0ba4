package com.sgs.testdatabiz.domain.service.testdata.impl.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.otsnotes.facade.model.enums.PriorityLevel;
import com.sgs.otsnotes.facade.model.rsp.matrix.SubContractTestMatrixRsp;
import com.sgs.testdatabiz.core.annotation.TestDataSource;
import com.sgs.testdatabiz.core.constants.Constants;
import com.sgs.testdatabiz.core.util.DateFormatUtil;
import com.sgs.testdatabiz.core.util.LOStringUtil;
import com.sgs.testdatabiz.core.util.ListUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestLineMappingExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMatrixExtPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineMappingInfoPO;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.base.AbstractTestDataHandler;
import com.sgs.testdatabiz.domain.service.testdata.model.ErrorMsg;
import com.sgs.testdatabiz.facade.model.enums.AnalyteTypeEnum;
import com.sgs.testdatabiz.facade.model.enums.BioFieldEnums;
import com.sgs.testdatabiz.facade.model.enums.ExternalCodeTransConditionIdEnum;
import com.sgs.testdatabiz.facade.model.testdata.*;
import com.sgs.testdatabiz.facade.model.testdata.slim.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static com.sgs.testdatabiz.domain.service.testdata.enums.ErrMsgFormatter.*;

/**
 *
 */
@Service
@TestDataSource(channel = SourceTypeEnum.SLIM)
public class SilmTestDataHandler extends AbstractTestDataHandler<SlimJobInfo> {
    private final Logger logger = LoggerFactory.getLogger(SilmTestDataHandler.class);
    @Autowired
    private TestLineMappingExtMapper testLineMappingExtMapper;

    private static final String ND_STRING = "ND";
    private static final String Y_STRING = "Y";
    private static final String LESS_THAN_STRING = "<";
    private static final String ZERO_STRING = "0";

    /**
     * 接口参数验证
     * @param job
     * @return
     */
    @Override
    public boolean inputValidate(SlimJobInfo job) {
        if (StringUtils.isBlank(job.getApproved())) {
            putErrorMsg(new ErrorMsg("approved为空."));
            return false;
        }
        Date approvedDate = DateFormatUtil.parseDate(job.getApproved());
        if (approvedDate == null) {
            putErrorMsg(new ErrorMsg("approved日期格式无效."));
            return false;
        }
        // TODO Yaofeng
        /*if (!StringUtils.equalsIgnoreCase(job.getLabCode(), conf.getSlimLabCode())) {
            return this.putError(String.format("解析到LabCode(%s)与FTP的LabCode(%s)不一致.", job.getLabCode(), conf.getSlimLabCode()));
        }*/
        List<SlimJobBioInfo> jobBios = job.getJobBioFieldList();
        if (jobBios == null || jobBios.isEmpty()) {
            putErrorMsg(new ErrorMsg("JobBioField对象为空."));
            return false;
        }
        SlimJobBioInfo bioField = jobBios.stream().filter(jobBio -> BioFieldEnums.check(jobBio.getBioField(), BioFieldEnums.SubcontractNo)).findFirst().orElse(null);
        if (bioField == null) {
            putErrorMsg(new ErrorMsg(String.format("未找到BioField（%s）.", Constants.JOBBIOFIELD)));
            return false;
        }

        if (StringUtils.isNoneBlank(bioField.getBioValue())) {
            // TODO Yaofeng
            /*if (!StringUtils.equalsIgnoreCase(bioField.getBioValue(), subContractNo)) {
                return this.putError(String.format("BioValue（%s）无效.", bioField.getBioValue()));
            }*/
            List<SlimSampleInfo> testSamples = job.getTestSamples();
            if (testSamples == null || testSamples.isEmpty()) {
                putErrorMsg(new ErrorMsg("对应的Sample 为空."));
                return false;
            }
        }
        if(StringUtils.isBlank(job.getProJob())){
            putErrorMsg(new ErrorMsg("PRO_JOB为空."));
            return false;
        }
        return true;
    }

    /**
     * 构建标准数据对象
     * @param reqObject
     * @return
     */
    @Override
    public ReportTestDataInfo inputBuild(SlimJobInfo reqObject) {
        List<SlimJobBioInfo> jobBios = reqObject.getJobBioFieldList();
        if (jobBios == null || jobBios.isEmpty()) {
            putErrorMsg(new ErrorMsg("JobBioField对象为空."));
            return null;
        }
        SlimJobBioInfo bioField = jobBios.stream().filter(jobBio -> BioFieldEnums.check(jobBio.getBioField(), BioFieldEnums.SubcontractNo)).findFirst().orElse(null);
        if (bioField == null) {
            putErrorMsg(new ErrorMsg(String.format("未找到BioField（%s）.", Constants.JOBBIOFIELD)));
            return null;
        }
        List<SlimSampleInfo> testSamples = reqObject.getTestSamples();
        if (testSamples == null || testSamples.isEmpty()){
            putErrorMsg(new ErrorMsg("对应的Sample 为空."));
            return null;
        }
        String slimJobNo = reqObject.getProJob();
        // 报告可能是XX-XX-01_XX_XX.RTF ，但是我们村的是XX-XX作为slimJobNo,所以这里需要额外的判断下
        String[] slimJobSubNo = reqObject.getProJob().split("-");
        if (slimJobSubNo.length > 2) {
            slimJobNo = reqObject.getProJob().substring(0, reqObject.getProJob().lastIndexOf("-"));
        }

        ReportTestDataInfo report = new ReportTestDataInfo();
        report.setOrderNo(reqObject.getOrderNo());
        report.setSubContractNo(bioField.getBioValue());
        report.setLabCode(reqObject.getLabCode());
        report.setObjectNo(bioField.getBioValue());
        report.setExternalNo(slimJobNo);
        // TODO 处理中的数据要怎么规避
        report.setExternalObjectNo(reqObject.getProJob());

        // TODO Yaofeng
        /*report.setParentOrderNo(reqObject.getParentOrderNo());
        report.setReportNo(reqObject.getReportNo());*/
        report.setProductLineCode(reqObject.getProductLineCode());

        report.setCompletedDate(DateFormatUtil.parseDate(reqObject.getApproved()));
        SourceTypeEnum sourceType = SourceTypeEnum.SLIM;
        report.setSourceType(sourceType.getCode());
        report.setRegionAccount(sourceType.getDesc());
        report.setLanguageId(LanguageType.English.getLanguageId());

        // 构建Test Matrix标准数据对象
        report.setTestMatrixs(this.getTestMatrixInfoList(report, reqObject));

        return report;
    }

    @Override
    protected boolean checkChannelData(ReportTestDataInfo testMatrixs, List<SubContractTestMatrixRsp> subContractTestMatrixs, List<TestDataMatrixExtPO> testDataMatrixInfoList) {
        return true;
    }
    // region 构建标准数据对象

    /**
     * 构建Test Matrix标准数据对象
     * @param reportTestData
     * @param reqObject
     * @return
     */
    private List<TestDataTestMatrixInfo> getTestMatrixInfoList(ReportTestDataInfo reportTestData, SlimJobInfo reqObject){
        List<SlimSampleInfo> testSamples = reqObject.getTestSamples();
        if (testSamples == null || testSamples.isEmpty()){
            putErrorMsg(new ErrorMsg("对应的Sample 为空."));
            return null;
        }
        // 获取SubContract上的TestMatrix
        List<SubContractTestMatrixRsp> subContractTestMatrixs = this.getSubContractTestMatrixList(reportTestData.getSubContractNo(), reqObject.getProductLineCode(), reqObject.getLabCode());
        //
        List<TestLineMappingInfoPO> testLineMappings = this.getTestLineMappingInfoList(subContractTestMatrixs, reqObject.getLabCode());

        List<TestDataTestMatrixInfo> testDataTestMatrixs = Lists.newArrayList();
        for (SlimSampleInfo testSample: testSamples) {
            List<SlimSchemeInfo> slimSchemes = testSample.getSlimCodes();
            if (slimSchemes == null || slimSchemes.isEmpty()){
                continue;
            }
            if (!NumberUtil.equals(testSample.getReportAction(), -1)){
                continue;
            }
            for (SlimSchemeInfo slimScheme: slimSchemes) {
                if (StringUtils.isBlank(slimScheme.getSlimCode())){
                    continue;
                }
                List<SlimAnalyteInfo> testAnalytes = slimScheme.getTestAnalytes();
                if (testAnalytes == null || testAnalytes.isEmpty()){
                    continue;
                }
                List<TestLineMappingInfoPO> slimTestLineMappings = ListUtils.filter(testLineMappings, tlm->{
                    return StringUtils.equalsIgnoreCase(tlm.getSlimCode(), slimScheme.getSlimCode());
                });

                Map<String, SubContractTestMatrixRsp> subContractTestMatrixMaps = Maps.newConcurrentMap();
                for (TestLineMappingInfoPO slimTestLineMapping: slimTestLineMappings) {
                    int ppNo = NumberUtil.toInt(slimTestLineMapping.getPpNo());

                    // 查找分包中是否存在的Matrix
                    List<SubContractTestMatrixRsp> subContractTestMatrixList = this.getSubContractTestMatrixList(subContractTestMatrixs, slimTestLineMapping, ppNo, testSample.getSampleNo());
                    if (subContractTestMatrixList.isEmpty()){
                        continue;
                    }
                    subContractTestMatrixList.forEach(tm->{
                        String testMatrixKey = String.format("%s_%s", tm.getPpNo(), tm.getTestMatrixId());
                        SubContractTestMatrixRsp testMatrix = subContractTestMatrixMaps.get(testMatrixKey);
                        if (testMatrix != null){
                            if (NumberUtil.equals(testMatrix.getMappingPpNo(), ppNo)){
                                return;
                            }
                            testMatrix.setTestLineMappingId(slimTestLineMapping.getId());
                            return;
                        }
                        SubContractTestMatrixRsp newTestMatrix = new SubContractTestMatrixRsp();
                        BeanUtils.copyProperties(tm, newTestMatrix);
                        newTestMatrix.setTestLineMappingId(slimTestLineMapping.getId());

                        subContractTestMatrixMaps.put(testMatrixKey, newTestMatrix);
                    });
                }
                // 当订单上找不到时，则默认创建一个对象
                if (subContractTestMatrixMaps.isEmpty()){
                    SubContractTestMatrixRsp testMatrix = new SubContractTestMatrixRsp();
                    testMatrix.setSlimCode(slimScheme.getSlimCode());

                    // TODO 这里一个Scheme Code 会对应多个不同TL
                    TestLineMappingInfoPO testLineMapping = slimTestLineMappings.stream().findFirst().orElse(null);
                    if (testLineMapping != null){
                        testMatrix.setTestLineId(testLineMapping.getTestLineId());
                    }
                    // 根据TestSampleNo拿对应的TestSampleId
                    SubContractTestMatrixRsp subContractTestMatrix = subContractTestMatrixs.stream().filter(tm -> StringUtils.equalsIgnoreCase(tm.getSampleNo(), testSample.getSampleNo())).findFirst().orElse(null);
                    if (subContractTestMatrix != null){
                        testMatrix.setTestSampleId(subContractTestMatrix.getTestSampleId());
                    }
                    testMatrix.setSampleNo(testSample.getSampleNo());

                    // TODO 如果一个TL来自不同的PP，或TL的CitationId不一样怎么取？？？？
                    SubContractTestMatrixRsp orderTestMatrix = this.getSubContractTestMatrixInfo(subContractTestMatrixs, slimTestLineMappings, testMatrix);

                    if (orderTestMatrix != null){
                        //testMatrix.setTestLineMappingId();
                        testMatrix.setPpVersionId(orderTestMatrix.getPpVersionId());
                        testMatrix.setCitationId(orderTestMatrix.getCitationId());
                        testMatrix.setCitationVersionId(orderTestMatrix.getCitationVersionId());
                        testMatrix.setCitationType(orderTestMatrix.getCitationType());
                    }
                    subContractTestMatrixMaps.put(StringUtils.EMPTY, testMatrix);
                }

                for (SubContractTestMatrixRsp orderTestMatrix: subContractTestMatrixMaps.values()) {
                    TestDataTestMatrixInfo testDataMatrix = getTestMatrixInfo(orderTestMatrix, testSample, slimScheme);
                    if (testDataMatrix == null){
                        continue;
                    }
                    testDataTestMatrixs.add(testDataMatrix);
                }
            }
        }
        return testDataTestMatrixs;
    }

    /**
     *
     * @param subContractTestMatrixs
     * @param slimTestLineMappings
     * @param subContractTestMatrix
     * @return
     */
    private SubContractTestMatrixRsp getSubContractTestMatrixInfo(List<SubContractTestMatrixRsp> subContractTestMatrixs, List<TestLineMappingInfoPO> slimTestLineMappings, SubContractTestMatrixRsp subContractTestMatrix){
        // 找TL对应的TestMatrix数据
        List<SubContractTestMatrixRsp> testMatrixs = ListUtils.filter(subContractTestMatrixs, tm->{
            return NumberUtil.equals(tm.getTestLineId(), subContractTestMatrix.getTestLineId());
        });
        if (testMatrixs.isEmpty()){
            return null;
        }

        // 根据TL筛选的结果，SampleNo二次过滤
        SubContractTestMatrixRsp testMatrix = ListUtils.findFirst(testMatrixs, tm -> {
            return StringUtils.equalsIgnoreCase(tm.getSampleNo(), subContractTestMatrix.getSampleNo()) &&
                slimTestLineMappings.stream().filter(tlm-> NumberUtil.equals(tlm.getStandardId(), tm.getCitationId())).count() > 0;
        });
        if (testMatrix != null){
            return testMatrix;
        }

        final String mixSymbol = "+";
        boolean isMixSample = StringUtils.containsIgnoreCase(subContractTestMatrix.getSampleNo(), mixSymbol);
        if (!isMixSample){
            return null;
        }
        Set<String> mixSampleNos = Sets.newHashSet();
        for (String mixSampleNo: StringUtils.split(subContractTestMatrix.getSampleNo(), mixSymbol)) {
            mixSampleNos.add(mixSampleNo.toUpperCase());
        }
        testMatrix = ListUtils.findFirst(testMatrixs, tm -> {
            return StringUtils.isNotBlank(tm.getSampleNo()) &&
                mixSampleNos.contains(tm.getSampleNo().toUpperCase()) &&
                slimTestLineMappings.stream().filter(tlm-> NumberUtil.equals(tlm.getStandardId(), tm.getCitationId())).count() > 0;
        });
        if (testMatrix != null){
            return testMatrix;
        }

        return ListUtils.findFirst(testMatrixs, tm -> {
            return slimTestLineMappings.stream().filter(tlm-> NumberUtil.equals(tlm.getStandardId(), tm.getCitationId())).count() > 0;
        });
    }

    /**
     *
     * @param subContractTestMatrixs
     * @param labCode
     * @return
     */
    private List<TestLineMappingInfoPO> getTestLineMappingInfoList(List<SubContractTestMatrixRsp> subContractTestMatrixs, String labCode){
        if (subContractTestMatrixs == null || subContractTestMatrixs.isEmpty() || StringUtils.isBlank(labCode)){
            return Lists.newArrayList();
        }
        Map<Integer, Set<Integer>> testLineIds = Maps.newHashMap();
        for (SubContractTestMatrixRsp tm: subContractTestMatrixs) {
            int testLineId = NumberUtil.toInt(tm.getTestLineId());
            int citationId = NumberUtil.toInt(tm.getCitationId());
            if (testLineId <= 0 || citationId <= 0) {
                continue;
            }
            if (!testLineIds.containsKey(testLineId)) {
                testLineIds.put(testLineId, Sets.newLinkedHashSet());
            }
            testLineIds.get(testLineId).add(citationId);
        }
        List<TestLineMappingInfoPO> testLineMappings = testLineMappingExtMapper.getTestLineMappingByTestLineId(labCode, testLineIds.keySet());
        if (testLineMappings == null){
            return Lists.newArrayList();
        }
        return testLineMappings;
    }

    /**
     *
     * @param subContractTestMatrixs
     * @param slimTestLineMapping
     * @param ppNo
     * @param testSampleNo
     * @return
     */
    private List<SubContractTestMatrixRsp> getSubContractTestMatrixList(List<SubContractTestMatrixRsp> subContractTestMatrixs, TestLineMappingInfoPO slimTestLineMapping, int ppNo, String testSampleNo){
        List<SubContractTestMatrixRsp> testMatrixs = ListUtils.filter(subContractTestMatrixs, tm->{
            if (!NumberUtil.equals(tm.getTestLineId(), slimTestLineMapping.getTestLineId())){
                return false;
            }
            if (!NumberUtil.equals(tm.getCitationId(), slimTestLineMapping.getStandardId())){
                return false;
            }
            if (!StringUtils.equalsIgnoreCase(tm.getSampleNo(), testSampleNo)){
                return false;
            }
            /*if (NumberUtil.equals(tm.getPpNo(), ppNo)){
                return true;
            }*/
            return true;
        });
        if (testMatrixs.isEmpty()){
            return testMatrixs;
        }
        // 查询是否满足传入的ppNo数据结果，如果有则直接返回
        List<SubContractTestMatrixRsp> ppTestMatrixs = ListUtils.filter(testMatrixs, tm -> {
            return NumberUtil.equals(tm.getPpNo(), ppNo);
        });
        // Pp No大于零说明配置PP
        if (!ppTestMatrixs.isEmpty() || ppNo > 0){
            return ppTestMatrixs;
        }
        testMatrixs.forEach(tm->{
            tm.setMappingPpNo(ppNo);
        });
        // TODO 订单有PP但配置里没有的场景(ppNo <= 0) -- CAN22-00687101
        return testMatrixs;

        /*if (ppNo <= 0 || !testMatrixs.isEmpty()){
            return testMatrixs;
        }

        *//**
         * TODO 考虑PP找不到要找非PP的数据？？？
         * ？？？？？如果SLIM回来了
         *//*
        return this.getSubContractTestMatrixList(subContractTestMatrixs, slimTestLineMapping, 0, testSampleNo);*/
    }

    /**
     *
     * @param slimScheme
     * @param testMatrix
     * @return
     */
    protected List<TestDataResultInfo> getTestResultInfoList(SlimSchemeInfo slimScheme, TestDataTestMatrixInfo testMatrix){
        List<SlimAnalyteInfo> slimAnalytes = slimScheme.getTestAnalytes();
        if (slimAnalytes == null || slimAnalytes.isEmpty()){
            return null;
        }
        List<TestDataResultInfo> testDataResults = Lists.newArrayList();
        Set<String> conclusions = Sets.newHashSet();
        for (SlimAnalyteInfo slimAnalyte: slimAnalytes) {
            // DIG-7684、DIG-7802
            if (!NumberUtil.equals(slimAnalyte.getReportActive(), -1) || StringUtils.equalsIgnoreCase(slimAnalyte.getAnalyteStatus(), "NA")){
                continue;
            }
            TestDataResultInfo testData = new TestDataResultInfo();
            // TODO Yaofeng
            testData.setTestAnalyteId(null);
            testData.setAnalyteCode(slimAnalyte.getAnalyteCode());
            // TODO 定义常量
            testData.setAnalyteType(StringUtils.startsWithIgnoreCase(slimAnalyte.getAnalyteCode(), "Conclusion") ? 1 : 0);

            testData.setReportLimit(slimAnalyte.getReportLimit());
            testData.setTestAnalyteName(slimAnalyte.getAnalyteName());
            String analyteNameCN = slimAnalyte.getAnalyteNameCN();
            // TODO Yaofeng
            /*if (slimDataType.check(ParseSlimDataType.ParseFTPXml) && slimCheck && StringUtils.isBlank(analyteNameCN)){
                this.getSlimErrorMsg(EmailTypeEnum.AnalyteName, slimErrors, slimScheme.getSlimCode(), testAnalyte.getAnalyteCode());
                continue;
            }*/

            testData.setReportUnit(slimAnalyte.getReportUnit());
            testData.setCasNo(slimAnalyte.getCasNo());
            testData.setLimitUnit(slimAnalyte.getLimitUnit());

            // TODO Yaofeng
            String testValue = LOStringUtil.decode(slimAnalyte.getTestValue());
            if (StringUtils.isBlank(testValue)){
                /*putErrorMsg(TEST_VALUE, slimScheme.getSlimCode(), testMatrix.getTestLineId(), slimAnalyte.getAnalyteCode());
                continue;*/
            }
            // DIG-7744
            /*if (StringUtils.equalsIgnoreCase(testValue, "0") || (StringUtils.startsWithIgnoreCase(testValue, "<") && !StringUtils.equalsIgnoreCase(testValue, slimAnalyte.getFinalValue()) && StringUtils.equalsIgnoreCase(slimAnalyte.getShowNd(), "Y"))) {
                testValue = "ND";
            }*/
            // DIG-8782
            testValue = this.convertNumberND(testValue, slimAnalyte);
            // DIG-8566、DIG-8533
            if (AnalyteTypeEnum.check(testData.getAnalyteType(), AnalyteTypeEnum.Conclusion)){
                if (conclusions.contains(testValue)){
                    putErrorMsg(CONCLUSION, slimScheme.getSlimCode(), null, testMatrix.getTestSampleNo());
                    continue;
                }
                conclusions.add(testValue);

                PriorityLevel priorityLevel = this.getPriorityLevel(slimAnalyte.getTestValue());
                if (priorityLevel != null){
                    testMatrix.setConclusionId(String.valueOf(priorityLevel.getLevel()));
                    testMatrix.setConclusionDisplay(priorityLevel.getMessage());
                }
                continue;
            }
            testData.setTestValue(testValue);
            testData.setAnalyteSeq(slimAnalyte.getAnalyteSeq());

            if (StringUtils.isNotBlank(analyteNameCN) || StringUtils.isNotBlank(slimAnalyte.getReportUnitCN())){
                List<TestDataResultLangInfo> languages = Lists.newArrayList();
                TestDataResultLangInfo language = new TestDataResultLangInfo();
                language.setLanguageId(LanguageType.Chinese.getLanguageId());
                language.setTestAnalyteName(StringUtils.defaultString(analyteNameCN, null));
                language.setReportUnit(StringUtils.defaultString(slimAnalyte.getReportUnitCN(), null));
                languages.add(language);
                testData.setLanguages(languages);
            }

            // TODO AZO测试的Analyte按指定顺序排

            // TODO 如果出现单测合测不匹配的情况下，可以组合测试结果回传


            testDataResults.add(testData);
        }
        return testDataResults;
    }

    /**
     *
     * @param conclusionCode
     * @return
     */
    private PriorityLevel getPriorityLevel(String conclusionCode){
        if (org.apache.commons.lang.StringUtils.isBlank(conclusionCode)){
            return null;
        }
        for (PriorityLevel level: PriorityLevel.values()) {
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(level.getMessage(), conclusionCode)){
                return level;
            }
        }
        return null;
    }

    /**
     * 按条件 将 testValue 转换为 ND
     * @param testValue
     * @param testAnalyte
     * @return
     */
    private String convertNumberND(String testValue, SlimAnalyteInfo testAnalyte) {
        if (StringUtils.startsWithIgnoreCase(testValue, LESS_THAN_STRING) &&
                !StringUtils.equalsIgnoreCase(testValue, testAnalyte.getFinalValue()) &&
                StringUtils.equalsIgnoreCase(testAnalyte.getShowNd(), Y_STRING)) {
            return ND_STRING;
        }
        if (StringUtils.equalsIgnoreCase(testValue, ZERO_STRING)) {
            return ND_STRING;
        }
        if (!org.apache.commons.lang3.math.NumberUtils.isNumber(testValue)) {
            return testValue;
        }

        BigDecimal compareNum = new BigDecimal(ZERO_STRING);
        if (compareNum.compareTo(new BigDecimal(testValue)) == 0) {
            return ND_STRING;
        }
        return testValue;
    }

    // endregion

    /**
     *
     * @param orderTestMatrix
     * @param testSample
     * @param slimScheme
     * @return
     */
    private TestDataTestMatrixInfo getTestMatrixInfo(SubContractTestMatrixRsp orderTestMatrix, SlimSampleInfo testSample, SlimSchemeInfo slimScheme){
        TestDataTestMatrixInfo testMatrix = new TestDataTestMatrixInfo();
        testMatrix.setTestMatrixId(orderTestMatrix.getTestMatrixId());
        testMatrix.setPpNo(orderTestMatrix.getPpNo());
        testMatrix.setPpVersionId(orderTestMatrix.getPpVersionId());
        testMatrix.setTestSampleId(orderTestMatrix.getTestSampleId());
        testMatrix.setTestSampleNo(testSample.getSampleNo());
        testMatrix.setExternalCode(slimScheme.getSlimCode());
        testMatrix.setExternalSampleNo(testSample.getExternalSampleNo());

        testMatrix.setTestLineId(orderTestMatrix.getTestLineId());
        testMatrix.setCitationId(orderTestMatrix.getCitationId());
        testMatrix.setCitationVersionId(orderTestMatrix.getCitationVersionId());
        testMatrix.setCitationType(orderTestMatrix.getCitationType());
        testMatrix.setTestLineMappingId(orderTestMatrix.getTestLineMappingId());

        testMatrix.setMaterialName(testSample.getMaterialName());
        testMatrix.setMaterialTexture(testSample.getMaterialTexture());
        testMatrix.setUsedPosition(testSample.getUsedPosition());
        testMatrix.setMaterialColor(testSample.getMaterialColor());
        testMatrix.setExternalident(testSample.getExternalident());
        testMatrix.setCitationName(orderTestMatrix.getCitationName());
        testMatrix.setEvaluationAlias(orderTestMatrix.getEvaluationAlias());

        Integer testConditionId = ExternalCodeTransConditionIdEnum.getConditionIdByExternalCode(testMatrix.getExternalCode());
        if(testConditionId != null){
            List<TestDataConditionInfo> testConditions = Lists.newArrayList();
            TestDataConditionInfo testCondition = new TestDataConditionInfo();
            testCondition.setTestConditionId(testConditionId);
            testConditions.add(testCondition);
            testMatrix.setTestConditions(testConditions);
        }

        String userField10 = slimScheme.getUserField10();
        if(StringUtils.isNotBlank(userField10)){
            String testLineSeq = userField10.substring(userField10.indexOf("C")+1,userField10.indexOf("_"));
            testMatrix.setTestLineSeq(Long.valueOf(NumberUtil.toInt(testLineSeq)));
        }
        // 构建Test Data标准数据对象
        testMatrix.setTestResults(getTestResultInfoList(slimScheme, testMatrix));

        return testMatrix;
    }

    @Override
    public SourceTypeEnum getChannel() {
        return SourceTypeEnum.SLIM;
    }
}
