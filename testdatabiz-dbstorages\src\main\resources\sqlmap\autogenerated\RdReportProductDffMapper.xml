<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportProductDffMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="rd_report_id" property="rdReportId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="object_type" property="objectType" jdbcType="INTEGER" />
    <result column="product_instance_id" property="productInstanceId" jdbcType="VARCHAR" />
    <result column="form_id" property="formId" jdbcType="VARCHAR" />
    <result column="language_id" property="languageId" jdbcType="INTEGER" />
    <result column="label_code" property="labelCode" jdbcType="VARCHAR" />
    <result column="label_name" property="labelName" jdbcType="VARCHAR" />
    <result column="field_code" property="fieldCode" jdbcType="VARCHAR" />
    <result column="customer_label" property="customerLabel" jdbcType="VARCHAR" />
    <result column="data_type" property="dataType" jdbcType="VARCHAR" />
    <result column="value" property="value" jdbcType="VARCHAR" />
    <result column="seq" property="seq" jdbcType="INTEGER" />
    <result column="display_in_report" property="displayInReport" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, lab_id, rd_report_id, order_no, report_no, object_type, product_instance_id, 
    form_id, language_id, label_code, label_name, field_code, customer_label, data_type, 
    `value`, seq, display_in_report
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_product_dff
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_product_dff
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_product_dff
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffExample" >
    delete from tb_report_product_dff
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffPO" >
    insert into tb_report_product_dff (id, lab_id, rd_report_id, 
      order_no, report_no, object_type, 
      product_instance_id, form_id, language_id, 
      label_code, label_name, field_code, 
      customer_label, data_type, `value`, 
      seq, display_in_report)
    values (#{id,jdbcType=BIGINT}, #{labId,jdbcType=BIGINT}, #{rdReportId,jdbcType=BIGINT}, 
      #{orderNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{objectType,jdbcType=INTEGER}, 
      #{productInstanceId,jdbcType=VARCHAR}, #{formId,jdbcType=VARCHAR}, #{languageId,jdbcType=INTEGER}, 
      #{labelCode,jdbcType=VARCHAR}, #{labelName,jdbcType=VARCHAR}, #{fieldCode,jdbcType=VARCHAR}, 
      #{customerLabel,jdbcType=VARCHAR}, #{dataType,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}, 
      #{seq,jdbcType=INTEGER}, #{displayInReport,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffPO" >
    insert into tb_report_product_dff
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="rdReportId != null" >
        rd_report_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="objectType != null" >
        object_type,
      </if>
      <if test="productInstanceId != null" >
        product_instance_id,
      </if>
      <if test="formId != null" >
        form_id,
      </if>
      <if test="languageId != null" >
        language_id,
      </if>
      <if test="labelCode != null" >
        label_code,
      </if>
      <if test="labelName != null" >
        label_name,
      </if>
      <if test="fieldCode != null" >
        field_code,
      </if>
      <if test="customerLabel != null" >
        customer_label,
      </if>
      <if test="dataType != null" >
        data_type,
      </if>
      <if test="value != null" >
        `value`,
      </if>
      <if test="seq != null" >
        seq,
      </if>
      <if test="displayInReport != null" >
        display_in_report,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="rdReportId != null" >
        #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        #{objectType,jdbcType=INTEGER},
      </if>
      <if test="productInstanceId != null" >
        #{productInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="formId != null" >
        #{formId,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="labelCode != null" >
        #{labelCode,jdbcType=VARCHAR},
      </if>
      <if test="labelName != null" >
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="customerLabel != null" >
        #{customerLabel,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null" >
        #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="seq != null" >
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="displayInReport != null" >
        #{displayInReport,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_product_dff
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_product_dff
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.rdReportId != null" >
        rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.objectType != null" >
        object_type = #{record.objectType,jdbcType=INTEGER},
      </if>
      <if test="record.productInstanceId != null" >
        product_instance_id = #{record.productInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null" >
        form_id = #{record.formId,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        language_id = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.labelCode != null" >
        label_code = #{record.labelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.labelName != null" >
        label_name = #{record.labelName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldCode != null" >
        field_code = #{record.fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerLabel != null" >
        customer_label = #{record.customerLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null" >
        data_type = #{record.dataType,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null" >
        `value` = #{record.value,jdbcType=VARCHAR},
      </if>
      <if test="record.seq != null" >
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.displayInReport != null" >
        display_in_report = #{record.displayInReport,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_product_dff
    set id = #{record.id,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=INTEGER},
      product_instance_id = #{record.productInstanceId,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=VARCHAR},
      language_id = #{record.languageId,jdbcType=INTEGER},
      label_code = #{record.labelCode,jdbcType=VARCHAR},
      label_name = #{record.labelName,jdbcType=VARCHAR},
      field_code = #{record.fieldCode,jdbcType=VARCHAR},
      customer_label = #{record.customerLabel,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=VARCHAR},
      `value` = #{record.value,jdbcType=VARCHAR},
      seq = #{record.seq,jdbcType=INTEGER},
      display_in_report = #{record.displayInReport,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffPO" >
    update tb_report_product_dff
    <set >
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="rdReportId != null" >
        rd_report_id = #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=INTEGER},
      </if>
      <if test="productInstanceId != null" >
        product_instance_id = #{productInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="formId != null" >
        form_id = #{formId,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        language_id = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="labelCode != null" >
        label_code = #{labelCode,jdbcType=VARCHAR},
      </if>
      <if test="labelName != null" >
        label_name = #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        field_code = #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="customerLabel != null" >
        customer_label = #{customerLabel,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null" >
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        `value` = #{value,jdbcType=VARCHAR},
      </if>
      <if test="seq != null" >
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="displayInReport != null" >
        display_in_report = #{displayInReport,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffPO" >
    update tb_report_product_dff
    set lab_id = #{labId,jdbcType=BIGINT},
      rd_report_id = #{rdReportId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=INTEGER},
      product_instance_id = #{productInstanceId,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=VARCHAR},
      language_id = #{languageId,jdbcType=INTEGER},
      label_code = #{labelCode,jdbcType=VARCHAR},
      label_name = #{labelName,jdbcType=VARCHAR},
      field_code = #{fieldCode,jdbcType=VARCHAR},
      customer_label = #{customerLabel,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=VARCHAR},
      `value` = #{value,jdbcType=VARCHAR},
      seq = #{seq,jdbcType=INTEGER},
      display_in_report = #{displayInReport,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report_product_dff
      (`id`,`lab_id`,`rd_report_id`,
      `order_no`,`report_no`,`object_type`,
      `product_instance_id`,`form_id`,`language_id`,
      `label_code`,`label_name`,`field_code`,
      `customer_label`,`data_type`,`value`,
      `seq`,`display_in_report`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.rdReportId, jdbcType=BIGINT},
      #{ item.orderNo, jdbcType=VARCHAR},#{ item.reportNo, jdbcType=VARCHAR},#{ item.objectType, jdbcType=INTEGER},
      #{ item.productInstanceId, jdbcType=VARCHAR},#{ item.formId, jdbcType=VARCHAR},#{ item.languageId, jdbcType=INTEGER},
      #{ item.labelCode, jdbcType=VARCHAR},#{ item.labelName, jdbcType=VARCHAR},#{ item.fieldCode, jdbcType=VARCHAR},
      #{ item.customerLabel, jdbcType=VARCHAR},#{ item.dataType, jdbcType=VARCHAR},#{ item.value, jdbcType=VARCHAR},
      #{ item.seq, jdbcType=INTEGER},#{ item.displayInReport, jdbcType=VARCHAR}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_report_product_dff 
      <set>
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.rdReportId != null"> 
          `rd_report_id` = #{item.rdReportId, jdbcType = BIGINT},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.objectType != null"> 
          `object_type` = #{item.objectType, jdbcType = INTEGER},
        </if> 
        <if test="item.productInstanceId != null"> 
          `product_instance_id` = #{item.productInstanceId, jdbcType = VARCHAR},
        </if> 
        <if test="item.formId != null"> 
          `form_id` = #{item.formId, jdbcType = VARCHAR},
        </if> 
        <if test="item.languageId != null"> 
          `language_id` = #{item.languageId, jdbcType = INTEGER},
        </if> 
        <if test="item.labelCode != null"> 
          `label_code` = #{item.labelCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.labelName != null"> 
          `label_name` = #{item.labelName, jdbcType = VARCHAR},
        </if> 
        <if test="item.fieldCode != null"> 
          `field_code` = #{item.fieldCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerLabel != null"> 
          `customer_label` = #{item.customerLabel, jdbcType = VARCHAR},
        </if> 
        <if test="item.dataType != null"> 
          `data_type` = #{item.dataType, jdbcType = VARCHAR},
        </if> 
        <if test="item.value != null"> 
          `value` = #{item.value, jdbcType = VARCHAR},
        </if> 
        <if test="item.seq != null"> 
          `seq` = #{item.seq, jdbcType = INTEGER},
        </if> 
        <if test="item.displayInReport != null"> 
          `display_in_report` = #{item.displayInReport, jdbcType = VARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>