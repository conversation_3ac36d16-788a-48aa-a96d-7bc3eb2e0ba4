/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdOrderRelInput implements Serializable {

    // add 20230529
    private Integer systemId;
    private String orderNo;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;


}
