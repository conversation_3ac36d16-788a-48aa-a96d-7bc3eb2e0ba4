package com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoPO;
import com.sgs.testdatabiz.facade.model.rsp.TestLineAnalyteMappingRsp;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface TestLineAnalyteExtMapper {
    /**
     *
     * @param rels
     * @return
     */
    Integer batchInsert(@Param("rels") Collection<TestLineAnalyteMappingInfoPO> rels);

    /**
     *
     * @param testLineMappingIds
     * @return
     */
    List<TestLineAnalyteMappingRsp> getTestLineAnalyteMappingList(@Param("testLineMappingIds") Set<Integer> testLineMappingIds, @Param("status")Integer status);

}
