package com.sgs.testdatabiz.web.controllers;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.domain.service.ConfigService;
import com.sgs.testdatabiz.domain.service.TestDataConfigService;
import com.sgs.testdatabiz.facade.TestDataFacade;
import com.sgs.testdatabiz.facade.model.req.TestDataConfigReq;
import com.sgs.testdatabiz.facade.model.req.config.ConfigReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/config")
@Api(value = "/config", tags = "config")
public class ConfigController {

    @Autowired
    private ConfigService configService;
    @Autowired
    private TestDataFacade testDataFacade;


    @PostMapping("/createTable")
    @ApiOperation(value = "createTable")
    public BaseResponse addTable(@RequestBody ConfigReq reqObject) {
        return BaseResponse.newInstance(configService.createTestDataTable(reqObject));
    }

    @GetMapping("/alterTableColumn")
    @ApiOperation(value = "alterTableColumn")
    public BaseResponse alterTableColumn(){
        return BaseResponse.newInstance(configService.alterTableColumn());
    }


    @GetMapping("/alterConfigIndex")
    @ApiOperation(value = "alterConfigIndex")
    public BaseResponse alterConfigIndex() {
        return BaseResponse.newInstance(configService.alterConfigIndex());
    }


    @PostMapping("/queryTestDataConfig")
    @ApiOperation(value = "queryTestDataConfig")
    public BaseResponse queryTestDataConfig(@RequestBody TestDataConfigReq req){
        return testDataFacade.queryTestDataConfig(req);
    }



}
