package com.sgs.testdatabiz.domain.service.testdata.info;

import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.AbstractTestDataService;

public class AbstractTestDataDto extends PrintFriendliness {
    /**
     *
     */
    private SourceTypeEnum sourceTypeEnum;

    /**
     *
     */
    private AbstractTestDataService testDataService;

    public SourceTypeEnum getSourceTypeEnum() {
        return sourceTypeEnum;
    }

    public void setSourceTypeEnum(SourceTypeEnum sourceTypeEnum) {
        this.sourceTypeEnum = sourceTypeEnum;
    }

    public AbstractTestDataService getTestDataService() {
        return testDataService;
    }

    public void setTestDataService(AbstractTestDataService testDataService) {
        this.testDataService = testDataService;
    }
}
