package com.sgs.testdatabiz.facade.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.SlimService;
import com.sgs.testdatabiz.domain.service.SourceDataStrategyFactory;
import com.sgs.testdatabiz.domain.service.testdata.impl.email.SimpleEMailAlarmService;
import com.sgs.testdatabiz.facade.ISlimTestDataFacade;
import com.sgs.testdatabiz.facade.model.req.CleanConclusionReq;
import com.sgs.testdatabiz.facade.model.req.slim.SlimSaveTestDataReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("slimTestDataFacade")
public class SlimFacadeImpl implements ISlimTestDataFacade {
    @Autowired
    private SourceDataStrategyFactory sourceDataStrategyFactory;
    @Autowired
    private SlimService slimService;

    @Autowired
    private SimpleEMailAlarmService eMailAlarmService;

    @Override
    public BaseResponse saveSlimTestData(SlimSaveTestDataReq reqObject) {
        return BaseResponse.newInstance(sourceDataStrategyFactory.doInvoke(reqObject, SourceTypeEnum.SLIM));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse cleanConclusion(CleanConclusionReq reqObject) {
        return BaseResponse.newInstance(slimService.cleanConclusion(reqObject));
    }

    @Override
    public BaseResponse getMailTo(String productLineCode, String locationCode) {
        return BaseResponse.newInstance(eMailAlarmService.getSlimConfigMail(productLineCode, locationCode));
    }

}
