package com.sgs.testdatabiz.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.testdatabiz.core.util.*;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.domain.service.testdata.impl.email.SimpleEMailAlarmService;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldInfo;
import com.sgs.testdatabiz.facade.model.enums.AnalyteTypeEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldLangInfo;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataLangInfo;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataMatrixLangInfo;
import com.sgs.testdatabiz.facade.model.testdata.*;
import com.sgs.testdatabiz.integration.CustomerBizClient;
import com.sgs.testdatabiz.integration.config.req.ConfigGetReq;
import com.sgs.trimslocal.facade.model.enums.ProductLineTypeEnum;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class ReportTestDataService {
    private final Logger logger = LoggerFactory.getLogger(ReportTestDataService.class);
    @Autowired
    private TestDataInfoExtMapper testDataReporExtMapper;
    @Autowired
    private TestDataMatrixInfoExtMapper testDataReportMatrixExtMapper;
    @Autowired
    private TestDataObjectRelExtMapper testDataReportObjectRelExtMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private SnowflakeIdWorker idWorker;
    @Autowired
    private SimpleEMailAlarmService mailAlarmService;
    @Autowired
    private CustomerBizClient customerBizClient;

    private static final String configKey = "canRepeatUpdate";

    private static final String TWO = "2";

    private static final String StarLims = "30";
    private static final String SLim = "31";

    /**
     * @param reqObject
     * @return
     */
    public CustomResult<Void> saveTestData(ReportTestDataInfo reqObject) {
        CustomResult<Void> rspResult = new CustomResult<>();
        // TODO DIG-8590
        /*if (SourceTypeEnum.check(reqObject.getSourceType(), SourceTypeEnum.SLIM)){
            return this.verifyTestData(reqObject);
        }*/

        List<TestDataObjectRelPO> objectRels = Lists.newArrayList();

        // 原报告编号 目前只限于StarLims：取originalReportNo，会把originalReportNo置为无效
        TestDataObjectRelPO originalObjectRel = this.getOriginalObjectRelInfo(reqObject);
        if (originalObjectRel != null) {
            objectRels.add(originalObjectRel);
        }

        // 1.构建TestDataObjectRelPO,如果已经存在设置id更新
        TestDataObjectRelPO objectRel = this.getTestDataObjectRelInfo(reqObject);
        TestDataObjectRelPO existedObjectRel = testDataReportObjectRelExtMapper.getReportObjectRelInfo(objectRel);
        if (existedObjectRel != null) {
            int sourceType = reqObject.getSourceType();
            String identityId = null;
            if (Objects.equals(sourceType, SourceTypeEnum.STARLIMS.getCode())) {
                identityId = StarLims;
            } else if (Objects.equals(sourceType, SourceTypeEnum.SLIM.getCode())) {
                identityId = SLim;
            }
            // TODO 此处增加判断只允许回传一次判断
            ConfigGetReq configGetReq = new ConfigGetReq();
            configGetReq.setIdentityId(identityId);
            configGetReq.setProductLine(reqObject.getProductLineCode());
            configGetReq.setConfigKey(configKey);
            CustomResult<String> stringCustomResult = customerBizClient.queryConfig(configGetReq);
            String data = stringCustomResult.getData();
            if (Func.isNotBlank(data) && Objects.equals(data,TWO)) {
                return rspResult.fail("SubReport Cannot Repeat Feedback!");
            }
            objectRel.setId(existedObjectRel.getId());
        }
        objectRels.add(objectRel);

        // 2.处理LabCode（GZ SL）转SL_GZ TODO
        String suffix = StringUtil.getTestDataSuffix(reqObject.getLabCode());

        // 3.构建Test Matrix实体对象 + TestData实体对象
        List<TestDataInfoPO> testDatas = Lists.newArrayList();
        List<TestDataMatrixInfoPO> testMatrixs = this.getTestMatrixInfoList(reqObject, objectRel, testDatas);

        // 4.查找数据库已经存在的matrix，如果数据库已经存在做更新，不存在设置为无效数据
        Map<String, TestDataMatrixInfoPO> testMatrixMaps = Maps.newLinkedHashMap();
        testMatrixs.stream().forEach(tm -> {
            if (testMatrixMaps.containsKey(tm.getBizVersionId())) {
                // TODO 非SHEIN的订单会出现重复的Matrix，这里又需要存入DB，只能给一个临时的Matrix（DIG-8621）
                tm.setTestMatrixId(String.valueOf(idWorker.nextId()));
                tm.setBizVersionId(this.getTestDataReportMatrixMd5(tm));
                return;
            }
            testMatrixMaps.put(tm.getBizVersionId(), tm);
        });
        testDataReportMatrixExtMapper.queryMatrix(objectRel.getId(), suffix).forEach(dbMatrix -> {
            TestDataMatrixInfoPO testDataMatrix = testMatrixMaps.get(this.getTestDataReportMatrixMd5(dbMatrix));
            if (testDataMatrix != null) {
                testDatas.stream().filter(td -> NumberUtil.equals(td.getTestDataMatrixId(), testDataMatrix.getId())).forEach(td -> {
                    td.setTestDataMatrixId(dbMatrix.getId());
                    td.setBizVersionId(this.getTestDataReportMd5(td));
                });
                testDataMatrix.setId(dbMatrix.getId());
                return;
            }
            dbMatrix.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
            testMatrixs.add(dbMatrix);
        });

        // 5.查找数据库已经存在的TestData，如果数据库已经存在做更新，不存在设置为无效数据
        testDataReporExtMapper.queryTestDataInfo(objectRel.getId(), suffix).forEach(dbTestData -> {
            String dbBizVersionId = this.getTestDataReportMd5(dbTestData);
            TestDataInfoPO testData = testDatas.stream().filter(td ->
                    StringUtils.equalsIgnoreCase(td.getBizVersionId(), dbBizVersionId) ||
                            // QA 存在直接改DB的数据，会导致DB的BizVersionId跟实际算出来的MD5不一致
                            StringUtils.equalsIgnoreCase(td.getBizVersionId(), dbTestData.getBizVersionId())).findFirst().orElse(null);
            if (testData != null) {
                testData.setId(dbTestData.getId());
                return;
            }
            dbTestData.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
            testDatas.add(dbTestData);
        });

        return transactionTemplate.execute((tranStatus) -> {
            try {
                if (testDataReportObjectRelExtMapper.batchInsert(objectRels) <= 0) {
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return rspResult.fail();
                }
                if (!testMatrixs.isEmpty()) {
                    testDataReportMatrixExtMapper.batchInsert(testMatrixs, suffix);
                }
                if (!testDatas.isEmpty()) {
                    testDataReporExtMapper.batchInsert(testDatas, suffix);
                }
                rspResult.setSuccess(true);
                return rspResult;
            } catch (Exception ex) {
                rspResult.setSuccess(false);
                rspResult.setMsg(ex.getMessage());
                logger.error("ReportTestDataService.doDeal, ReportNo_{}, ObjectNo_{}, ExternalNo_{}, Error：{}.", objectRel.getReportNo(), objectRel.getObjectNo(), objectRel.getExternalNo(), ex);
                tranStatus.setRollbackOnly(); // 回滚事务
                return rspResult;
            }
        });
    }

    // region 【实时验证SLIM数据】

    /**
     * 实时验证SLIM数据
     *
     * @param reqObject
     * @return
     */
    public CustomResult<Void> verifyTestData(ReportTestDataInfo reqObject) {
        CustomResult<Void> rspResult = new CustomResult<>();
        /*if (!reqObject.isCustomerCheck()){
            rspResult.setSuccess(true);
            return rspResult;
        }*/

        // 1.构建TestDataObjectRelPO,如果已经存在设置id更新
        TestDataObjectRelPO objectRel = this.getTestDataObjectRelInfo(reqObject);
        TestDataObjectRelPO existedObjectRel = testDataReportObjectRelExtMapper.getReportObjectRelInfo(objectRel);
        if (existedObjectRel != null) {
            objectRel.setId(existedObjectRel.getId());
        }

        // 2.处理LabCode（GZ SL）转SL_GZ TODO
        String suffix = StringUtil.getTestDataSuffix(reqObject.getLabCode());
        List<TestDataMatrixInfoPO> oldTestMatrixs = testDataReportMatrixExtMapper.queryMatrix(objectRel.getId(), suffix);
        List<TestDataInfoPO> oldTestDatas = testDataReporExtMapper.queryTestDataInfo(objectRel.getId(), suffix);

        // 3.构建Test Matrix实体对象 + TestData实体对象
        List<TestDataInfoPO> testDatas = Lists.newArrayList();
        List<TestDataMatrixInfoPO> testMatrixs = this.getTestMatrixInfoList(reqObject, objectRel, testDatas);

        // 4.查找数据库已经存在的matrix，如果数据库已经存在做更新，不存在设置为无效数据
        Map<String, TestDataMatrixInfoPO> testMatrixMaps = Maps.newLinkedHashMap();
        testMatrixs.stream().forEach(tm -> {
            String bizVersionId = this.getCompareTestMatrixMd5(tm);
            if (testMatrixMaps.containsKey(bizVersionId)) {
                // TODO 如果重复抛异常
                return;
            }
            tm.setBizVersionId(bizVersionId);
            testMatrixMaps.put(bizVersionId, tm);
        });
        Set<Long> delTestMatrixIds = Sets.newHashSet();
        Set<Long> oldTestMatrixIds = Sets.newHashSet();

        oldTestMatrixs.forEach(dbMatrix -> {
            dbMatrix.setBizVersionId(this.getCompareTestMatrixMd5(dbMatrix));
            TestDataMatrixInfoPO testDataMatrix = testMatrixMaps.get(dbMatrix.getBizVersionId());
            if (testDataMatrix != null) {
                testDatas.stream().filter(td -> NumberUtil.equals(td.getTestDataMatrixId(), testDataMatrix.getId())).forEach(td -> {
                    td.setTestDataMatrixId(dbMatrix.getId());
                    td.setBizVersionId(this.getCompareTestDataMd5(td));
                });
                oldTestMatrixIds.add(dbMatrix.getId());
                testDataMatrix.setId(dbMatrix.getId());
                return;
            }
            if (!NumberUtil.equals(dbMatrix.getActiveIndicator(), 1)) {
                return;
            }
            delTestMatrixIds.add(dbMatrix.getId());
            /*dbMatrix.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
            testMatrixs.add(dbMatrix);*/
        });

        // 5.查找数据库已经存在的TestData，如果数据库已经存在做更新，不存在设置为无效数据
        Set<Long> delTestDataIds = Sets.newHashSet();
        Set<Long> oldTestDataIds = Sets.newHashSet();
        oldTestDatas.forEach(dbTestData -> {
            dbTestData.setBizVersionId(this.getCompareTestDataMd5(dbTestData));
            TestDataInfoPO testData = testDatas.stream().filter(td ->
                            // QA 存在直接改DB的数据，会导致DB的BizVersionId跟实际算出来的MD5不一致
                            StringUtils.equalsIgnoreCase(td.getBizVersionId(), dbTestData.getBizVersionId())).findFirst().orElse(null);
            if (testData != null) {
                oldTestDataIds.add(dbTestData.getId());
                testData.setId(dbTestData.getId());
                return;
            }
            if (!NumberUtil.equals(dbTestData.getActiveIndicator(), 1) || AnalyteTypeEnum.check(dbTestData.getAnalyteType(), AnalyteTypeEnum.Conclusion)) {
                return;
            }
            delTestDataIds.add(dbTestData.getId());
            /*dbTestData.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
            testDatas.add(dbTestData);*/
        });

        // 数据比对
        long oldTestMatrixSize = oldTestMatrixs.stream().filter(oldTestMatrix -> NumberUtil.equals(oldTestMatrix.getActiveIndicator(), 1)).count();
        if (oldTestMatrixSize <= 0) {
            logger.error("OrderNo_{}_Error，SubContractNo_{}, ExternalNo_{}, ", reqObject.getOrderNo(), reqObject.getSubContractNo(), reqObject.getExternalNo());
            return CustomResult.newSuccessInstance();
        }
        oldTestMatrixs.removeIf(oldTestMatrix -> {
            if (!NumberUtil.equals(oldTestMatrix.getActiveIndicator(), 1)) {
                return true;
            }
            return testMatrixs.stream().filter(testMatrix -> StringUtils.equalsIgnoreCase(testMatrix.getBizVersionId(), oldTestMatrix.getBizVersionId())).count() > 0;
        });

        long oldTestDataSize = oldTestDatas.stream().filter(oldTestData -> NumberUtil.equals(oldTestData.getActiveIndicator(), 1) && AnalyteTypeEnum.check(oldTestData.getAnalyteType(), AnalyteTypeEnum.General)).count();
        oldTestDatas.removeIf(oldTestData -> {
            if (!NumberUtil.equals(oldTestData.getActiveIndicator(), 1) || AnalyteTypeEnum.check(oldTestData.getAnalyteType(), AnalyteTypeEnum.Conclusion)) {
                return true;
            }
            return testDatas.stream().filter(testData -> StringUtils.equalsIgnoreCase(testData.getBizVersionId(), oldTestData.getBizVersionId())).count() > 0;
        });

        if (oldTestMatrixs.size() <= 0 && oldTestDatas.size() <= 0 && oldTestMatrixSize == testMatrixs.stream().filter(testMatrix -> NumberUtil.equals(testMatrix.getActiveIndicator(), 1)).count() && oldTestDataSize == testDatas.stream().filter(testData -> NumberUtil.equals(testData.getActiveIndicator(), 1)).count()) {
            logger.info("OrderNo_{}，SubContractNo_{}, ExternalNo_{}, ", reqObject.getOrderNo(), reqObject.getSubContractNo(), reqObject.getExternalNo());
            return CustomResult.newSuccessInstance();
        }
        Set<Long> newTestMatrixIds = testMatrixs.stream().filter(tm -> !oldTestMatrixIds.contains(tm.getId())).map(tm -> tm.getId()).collect(Collectors.toSet());
        Set<Long> newTestDataIds = testDatas.stream().filter(td -> !oldTestDataIds.contains(td.getId())).map(td -> td.getId()).collect(Collectors.toSet());

        LinkedList<String> mailErrors = Lists.newLinkedList();
        if (!newTestMatrixIds.isEmpty()) {
            mailErrors.add(String.format("Test Matrix 多了 %s 条数据：", newTestMatrixIds.size()));
            mailErrors.add(StringUtils.join(newTestMatrixIds, "、"));
        }
        if (!delTestMatrixIds.isEmpty()) {
            mailErrors.add(String.format("Test Matrix 少了 %s 条数据：", delTestMatrixIds.size()));
            mailErrors.add(StringUtils.join(delTestMatrixIds, "、"));
        }
        if (!newTestDataIds.isEmpty()) {
            mailErrors.add(String.format("Test Data 多了 %s 条数据：", newTestDataIds.size()));
            mailErrors.add(StringUtils.join(newTestDataIds, "、"));
        }
        if (!delTestDataIds.isEmpty()) {
            mailErrors.add(String.format("Test Data 少了 %s 条数据：", delTestDataIds.size()));
            mailErrors.add(StringUtils.join(delTestDataIds, "、"));
        }
        // TODO 发送邮件
        mailAlarmService.doSlimEmail(reqObject, mailErrors);

        testMatrixs.forEach(tm -> {
            tm.setBizVersionId(this.getTestDataReportMatrixMd5(tm));
        });
        testDatas.forEach(td -> {
            td.setBizVersionId(this.getTestDataReportMd5(td));
        });

        // Test Matrix Del
        testDataReportMatrixExtMapper.delTestMatrixInvalid(objectRel.getId(), DateUtils.getNow());
        // Test Data Del
        testDataReporExtMapper.delTestDataInvalid(objectRel.getId(), DateUtils.getNow());

        return transactionTemplate.execute((tranStatus) -> {
            try {
                if (!testMatrixs.isEmpty()) {
                    testDataReportMatrixExtMapper.batchInsertSlim(testMatrixs);
                }
                if (!testDatas.isEmpty()) {
                    testDataReporExtMapper.batchInsertSlim(testDatas);
                }
                rspResult.setSuccess(true);
                return rspResult;
            } catch (Exception ex) {
                logger.error("ReportTestDataService.doDeal, ReportNo_{}, ObjectNo_{}, ExternalNo_{}, Error：{}.", objectRel.getReportNo(), objectRel.getObjectNo(), objectRel.getExternalNo(), ex);
                tranStatus.setRollbackOnly(); // 回滚事务
                return rspResult.fail(ex.getMessage());
            }
        });
    }

    // endregion

    /**
     * @param reqObject
     * @return
     */
    private TestDataObjectRelPO getOriginalObjectRelInfo(ReportTestDataInfo reqObject) {
        if (StringUtils.isBlank(reqObject.getOriginalReportNo())) {
            return null;
        }
        TestDataObjectRelPO rel = new TestDataObjectRelPO();
        rel.setReportNo(reqObject.getReportNo());
        rel.setObjectNo(reqObject.getSubContractNo());
        rel.setExternalNo(reqObject.getExternalNo());
        rel.setSourceType(reqObject.getSourceType());
        // 原报告编号 目前只限于StarLims：取originalReportNo
        rel.setExternalObjectNo(reqObject.getOriginalReportNo());

        TestDataObjectRelPO originalObjectRel = testDataReportObjectRelExtMapper.getReportObjectRelInfo(rel);
        if (originalObjectRel == null) {
            return null;
        }
        // 置为无效，更新DB
        originalObjectRel.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
        return originalObjectRel;
    }

    /**
     * 构建数据库实体对象
     *
     * @param reqObject
     * @return
     */
    private TestDataObjectRelPO getTestDataObjectRelInfo(ReportTestDataInfo reqObject) {
        TestDataObjectRelPO objectRel = new TestDataObjectRelPO();
        objectRel.setId(UUID.randomUUID().toString());
        objectRel.setProductLineCode(reqObject.getProductLineCode());
        objectRel.setOrderNo(reqObject.getOrderNo());
        objectRel.setParentOrderNo(reqObject.getParentOrderNo());
        objectRel.setReportNo(reqObject.getReportNo());
        objectRel.setObjectNo(reqObject.getSubContractNo());
        objectRel.setExternalId(reqObject.getExternalId());
        objectRel.setExternalNo(reqObject.getExternalNo());
        objectRel.setExternalObjectNo(reqObject.getExternalObjectNo());
        objectRel.setLanguageId(reqObject.getLanguageId());
        objectRel.setCompleteDate(reqObject.getCompletedDate());
        objectRel.setSourceType(reqObject.getSourceType());//1、slim 2、job 3、starlims 4、fast 5、subcontract 6、new
        objectRel.setLabCode(reqObject.getLabCode());

        objectRel.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());//0: inactive, 1: active
        objectRel.setBizVersionId(this.getTestDataReportObjectRelMd5(objectRel));
        objectRel.setCreatedBy(reqObject.getRegionAccount());
        objectRel.setCreatedDate(DateUtils.getNow());
        objectRel.setModifiedBy(reqObject.getRegionAccount());
        objectRel.setModifiedDate(DateUtils.getNow());
        objectRel.setExcludeCustomerInterface(Func.isEmpty(reqObject.getExcludeCustomerInterface()) ? "0" : reqObject.getExcludeCustomerInterface());

        return objectRel;

    }

    /**
     * 构建Test Matrix实体对象
     *
     * @param reqObject
     * @param objectRelPO
     * @param testDatas
     * @return
     */
    private List<TestDataMatrixInfoPO> getTestMatrixInfoList(ReportTestDataInfo reqObject, TestDataObjectRelPO objectRelPO, List<TestDataInfoPO> testDatas) {
        List<TestDataMatrixInfoPO> testDataMatrixs = Lists.newArrayList();
        List<TestDataTestMatrixInfo> testMatrixs = reqObject.getTestMatrixs();
        if (testMatrixs == null || testMatrixs.isEmpty()) {
            return testDataMatrixs;
        }

        for (TestDataTestMatrixInfo tm : testMatrixs) {
            TestDataMatrixInfoPO testMatrix = this.getTestDataMatrixInfo(objectRelPO, tm);
            // 2023-06-02 jingjing说除了SL的其他都走原逻辑
            if (!Objects.equals(reqObject.getProductLineCode(), ProductLineTypeEnum.SL.getProductLineAbbr())) {
                testMatrix.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
            }

            // 构建Test Data实体对象
            Map<String, TestDataInfoPO> tdMaps = this.getTestDataInfoList(tm, testMatrix);
            if (tdMaps != null && !tdMaps.isEmpty()) {
                testDatas.addAll(tdMaps.values());
            }
            testDataMatrixs.add(testMatrix);
        }
        return testDataMatrixs;
    }

    /**
     * @param objectRelPO
     * @param tm
     * @return
     */
    public TestDataMatrixInfoPO getTestDataMatrixInfo(TestDataObjectRelPO objectRelPO, TestDataTestMatrixInfo tm) {
        if (objectRelPO == null) {
            objectRelPO = new TestDataObjectRelPO();
        }
        TestDataMatrixInfoPO testMatrix = new TestDataMatrixInfoPO();
        testMatrix.setId(idWorker.nextId());
        testMatrix.setObjectRelId(objectRelPO.getId());

        testMatrix.setTestMatrixId(tm.getTestMatrixId());
        testMatrix.setTestLineMappingId(tm.getTestLineMappingId());

        testMatrix.setExternalId(tm.getExternalId());
        testMatrix.setExternalCode(tm.getExternalCode());

        testMatrix.setPpVersionId(tm.getPpVersionId());
        testMatrix.setAid(tm.getAid());
        testMatrix.setTestLineId(tm.getTestLineId());
        testMatrix.setCitationId(tm.getCitationId());
        testMatrix.setCitationVersionId(tm.getCitationVersionId());
        testMatrix.setCitationType(tm.getCitationType());
        testMatrix.setCitationName(tm.getCitationName());

        testMatrix.setSampleId(tm.getTestSampleId());//externalId
        testMatrix.setTestLineInstanceId(tm.getTestLineInstanceId());
        testMatrix.setMatrixSource(tm.getMatrixSource());
        testMatrix.setSampleNo(tm.getTestSampleNo());
        testMatrix.setExternalSampleNo(tm.getExternalSampleNo());
        testMatrix.setTestLineSeq(tm.getTestLineSeq());

        testMatrix.setSampleSeq(tm.getSampleSeq());
        // TODO Helen 说要让Frank传ConditionName
        List<TestDataConditionInfo> testConditions = tm.getTestConditions();
        if (testConditions != null && !testConditions.isEmpty()) {
            testMatrix.setCondition(JSONObject.toJSONString(testConditions));
        }

        testMatrix.setEvaluationAlias(tm.getEvaluationAlias());
        testMatrix.setMethodDesc(tm.getMethodDesc());
        testMatrix.setConclusionId(tm.getConclusionId());
        testMatrix.setConclusionDisplay(tm.getConclusionDisplay());
        //
        testMatrix.setExtFields(this.getMatrixExtFieldInfo(tm));

        // TODO 多语言
        testMatrix.setLanguages(this.getMatrixLangInfo(tm));
        testMatrix.setBizVersionId(this.getTestDataReportMatrixMd5(testMatrix));
        testMatrix.setActiveIndicator((tm.isDisableMatrix() ? ActiveIndicatorEnum.DISABLE : ActiveIndicatorEnum.ACTIVE).getValue());
        testMatrix.setCreatedBy(objectRelPO.getCreatedBy());
        testMatrix.setCreatedDate(DateUtils.getNow());
        testMatrix.setModifiedBy(objectRelPO.getModifiedBy());
        testMatrix.setModifiedDate(DateUtils.getNow());
        return testMatrix;
    }

    /**
     * @param tm
     * @return
     */
    private String getMatrixExtFieldInfo(TestDataTestMatrixInfo tm) {
        TestDataMatrixExtFieldInfo extField = new TestDataMatrixExtFieldInfo();
        int appFactorId = NumberUtil.toInt(tm.getAppFactorId());
        if (appFactorId > 0) {
            extField.setAppFactorId(tm.getAppFactorId());
        }
        extField.setAppFactorName(StringUtils.defaultIfBlank(tm.getAppFactorName(), null));
        extField.setMaterialName(StringUtils.defaultIfBlank(tm.getMaterialName(), null));
        extField.setMaterialColor(StringUtils.defaultIfBlank(tm.getMaterialColor(), null));
        extField.setMaterialTexture(StringUtils.defaultIfBlank(tm.getMaterialTexture(), null));
        extField.setUsedPosition(StringUtils.defaultIfBlank(tm.getUsedPosition(), null));
        //SCI-1378
        extField.setReferFromSampleNo(StringUtils.defaultIfBlank(tm.getReferFromSampleNo(), null));
        extField.setReferFromReportNo(tm.getReferFromReportNo());
        if (tm.getLanguages() == null) {
            tm.setLanguages(Lists.newLinkedList());
        }
        List<TestDataMatrixExtFieldLangInfo> languages = Lists.newArrayList();
        tm.getLanguages().forEach(lang -> {
            if (StringUtils.isBlank(lang.getMaterialName()) && StringUtils.isBlank(lang.getMaterialColor()) && StringUtils.isBlank(lang.getMaterialTexture())) {
                return;
            }
            TestDataMatrixExtFieldLangInfo language = new TestDataMatrixExtFieldLangInfo();
            BeanUtils.copyProperties(lang, language);
            language.setMaterialName(StringUtils.defaultIfBlank(lang.getMaterialName(), null));
            language.setMaterialColor(StringUtils.defaultIfBlank(lang.getMaterialColor(), null));
            language.setMaterialTexture(StringUtils.defaultIfBlank(lang.getMaterialTexture(), null));
            language.setUsedPosition(StringUtils.defaultIfBlank(lang.getUsedPosition(), null));
            languages.add(language);
        });
        if (!languages.isEmpty()) {
            extField.setLanguages(languages);
        }
        return JSONObject.toJSONString(extField);
    }

    /**
     * @param tm
     * @return
     */
    private String getMatrixLangInfo(TestDataTestMatrixInfo tm) {
        List<TestDataTestMatrixLangInfo> languages = tm.getLanguages();
        if (languages == null || languages.isEmpty()) {
            return null;
        }
        List<TestDataMatrixLangInfo> langs = Lists.newArrayList();
        for (TestDataTestMatrixLangInfo language : languages) {
            LanguageType languageType = LanguageType.findCode(language.getLanguageId());
            if (languageType == null || languageType.getLanguageId() <= 0) {
                continue;
            }
            TestDataMatrixLangInfo lang = new TestDataMatrixLangInfo();
            lang.setLanguageId(languageType.getLanguageId());
            lang.setEvaluationAlias(Transcoding.rtfStrEsc(language.getEvaluationAlias()));

            String conclusionDisplay = Transcoding.unicodeToChar(language.getConclusionDisplay());
            if (StringUtils.isBlank(conclusionDisplay)) {
                conclusionDisplay = language.getConclusionDisplay();
            }
            lang.setConclusionDisplay(conclusionDisplay);
            lang.setMethodDesc(Transcoding.rtfStrEsc(language.getMethodDesc()));

            langs.add(lang);
        }
        if (langs.isEmpty()) {
            return null;
        }
        return JSONObject.toJSONString(langs);
    }

    /**
     * 构建Test Data实体对象
     *
     * @param testMatrix
     * @param testDataMatrixPO
     * @return
     */
    private Map<String, TestDataInfoPO> getTestDataInfoList(TestDataTestMatrixInfo testMatrix, TestDataMatrixInfoPO testDataMatrixPO) {
        Map<String, TestDataInfoPO> testDataMaps = Maps.newConcurrentMap();
        if (testMatrix == null) {
            return testDataMaps;
        }
        List<TestDataResultInfo> tds = testMatrix.getTestResults();
        if (tds == null || tds.isEmpty()) {
            return testDataMaps;
        }
        for (TestDataResultInfo td : tds) {
            TestDataInfoPO testData = new TestDataInfoPO();

            testData.setId(idWorker.nextId());
            testData.setObjectRelId(testDataMatrixPO.getObjectRelId());
            testData.setTestDataMatrixId(testDataMatrixPO.getId());
            testData.setAnalyteCode(td.getAnalyteCode());
            testData.setAnalyteId(NumberUtil.toInt(td.getTestAnalyteId()) > 0 ? td.getTestAnalyteId().toString() : null);
            testData.setAnalyteName(td.getTestAnalyteName());
            AnalyteTypeEnum analyteType = AnalyteTypeEnum.findType(td.getAnalyteType(), AnalyteTypeEnum.General);
            testData.setAnalyteType(analyteType.getType());// 定义枚举 0
            testData.setAnalyteSeq(td.getAnalyteSeq());
            testData.setReportUnit(td.getReportUnit());
            testData.setLimitUnit(td.getLimitUnit());
            testData.setTestValue(td.getTestValue());
            testData.setCasNo(td.getCasNo());
            testData.setReportLimit(td.getReportLimit());
            testData.setConclusionId(td.getConclusionId());

            // TODO 多语言
            testData.setLanguages(this.getResultLangInfo(td));

            testData.setBizVersionId(this.getTestDataReportMd5(testData));
            testData.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
            testData.setCreatedBy(testDataMatrixPO.getCreatedBy());
            testData.setCreatedDate(DateUtils.getNow());
            testData.setModifiedBy(testDataMatrixPO.getCreatedBy());
            testData.setModifiedDate(DateUtils.getNow());
            //SCI-1378 methodLimit对应的是LimitValueFullName
            testData.setLimitValueFullName(td.getMethodLimit());
            testDataMaps.put(testData.getBizVersionId(), testData);
        }
        return testDataMaps;
    }

    /**
     * @param td
     * @return
     */
    private String getResultLangInfo(TestDataResultInfo td) {
        List<TestDataResultLangInfo> languages = td.getLanguages();
        if (languages == null || languages.isEmpty()) {
            return null;
        }
        List<TestDataLangInfo> langs = Lists.newArrayList();
        for (TestDataResultLangInfo language : languages) {
            LanguageType languageType = LanguageType.findCode(language.getLanguageId());
            if (languageType == null || languageType.getLanguageId() <= 0) {
                continue;
            }
            TestDataLangInfo lang = new TestDataLangInfo();
            lang.setLanguageId(languageType.getLanguageId());
            String analyteName = Transcoding.unicodeToChar(language.getTestAnalyteName());
            if (StringUtils.isBlank(analyteName)) {
                analyteName = language.getTestAnalyteName();
            }
            lang.setTestAnalyteName(analyteName);

            String reportUnit = Transcoding.unicodeToChar(language.getReportUnit());
            if (StringUtils.isBlank(reportUnit)) {
                reportUnit = language.getReportUnit();
            }
            lang.setReportUnit(reportUnit);
            langs.add(lang);
        }
        if (langs.isEmpty()) {
            return null;
        }
        return JSONObject.toJSONString(langs);
    }

    /**
     * @param objectRel
     * @return
     */
    protected String getTestDataReportObjectRelMd5(TestDataObjectRelPO objectRel) {
        objectRel.setProductLineCode(StringUtil.isNullOrEmpty(objectRel.getProductLineCode()));
        objectRel.setLabCode(StringUtil.isNullOrEmpty(objectRel.getLabCode()));
        objectRel.setOrderNo(StringUtil.isNullOrEmpty(objectRel.getOrderNo()));
        objectRel.setParentOrderNo(StringUtil.isNullOrEmpty(objectRel.getParentOrderNo()));
        objectRel.setReportNo(StringUtil.isNullOrEmpty(objectRel.getReportNo()));
        objectRel.setObjectNo(StringUtil.isNullOrEmpty(objectRel.getObjectNo()));
        objectRel.setExternalId(StringUtil.isNullOrEmpty(objectRel.getExternalId()));
        objectRel.setExternalNo(StringUtil.isNullOrEmpty(objectRel.getExternalNo()));
        objectRel.setSourceType(NumberUtil.toInt(objectRel.getSourceType()));
        objectRel.setLanguageId(NumberUtil.toInt(objectRel.getLanguageId()));
        objectRel.setExternalObjectNo(StringUtil.isNullOrEmpty(objectRel.getExternalObjectNo()));
        objectRel.setCompleteDate(DateUtils.isNullOrEmpty(objectRel.getCompleteDate()));

        StringBuilder append = new StringBuilder();
        append.append(objectRel.getProductLineCode());
        append.append(objectRel.getLabCode());
        append.append(objectRel.getOrderNo());
        append.append(objectRel.getParentOrderNo());
        append.append(objectRel.getReportNo());
        append.append(objectRel.getObjectNo());
        append.append(objectRel.getExternalId());
        append.append(objectRel.getExternalNo());
        append.append(objectRel.getSourceType());
        append.append(objectRel.getLanguageId());
        append.append(objectRel.getExternalObjectNo());
        //append.append(objectRel.getCompleteDate());
        return DigestUtils.md5Hex(append.toString().toLowerCase());
    }

    /**
     * @param relPO
     * @return
     */
    public String getTestDataReportMatrixMd5(TestDataMatrixInfoPO relPO) {
        relPO.setObjectRelId(StringUtil.isNullOrEmpty(relPO.getObjectRelId()));
        relPO.setTestMatrixId(StringUtil.isNullOrEmpty(relPO.getTestMatrixId()));
        relPO.setExternalCode(StringUtil.isNullOrEmpty(relPO.getExternalCode()));
        relPO.setPpVersionId(NumberUtil.toInt(relPO.getPpVersionId()));
        relPO.setAid(NumberUtil.toLong(relPO.getAid()));
        relPO.setTestLineId(NumberUtil.toInt(relPO.getTestLineId()));
        relPO.setCitationId(NumberUtil.toInt(relPO.getCitationId()));
        relPO.setCitationVersionId(NumberUtil.toInt(relPO.getCitationVersionId()));
        relPO.setSampleId(StringUtil.isNullOrEmpty(relPO.getSampleId()));
        relPO.setSampleNo(StringUtil.isNullOrEmpty(relPO.getSampleNo()));
        relPO.setExternalSampleNo(StringUtil.isNullOrEmpty(relPO.getExternalSampleNo()));
        relPO.setSampleSeq(relPO.getSampleSeq());
        relPO.setExtFields(StringUtil.isNullOrEmpty(relPO.getExtFields()));
        relPO.setCondition(StringUtil.isNullOrEmpty(relPO.getCondition()));
        relPO.setConclusionId(StringUtil.isNullOrEmpty(relPO.getConclusionId()));
        relPO.setConclusionDisplay(StringUtil.isNullOrEmpty(relPO.getConclusionDisplay()));
        relPO.setEvaluationAlias(StringUtil.isNullOrEmpty(relPO.getEvaluationAlias()));
        relPO.setLanguages(StringUtil.isNullOrEmpty(relPO.getLanguages()));
        relPO.setExternalId(StringUtil.isNullOrEmpty(relPO.getExternalId()));
        relPO.setMethodDesc(StringUtil.isNullOrEmpty(relPO.getMethodDesc()));
        relPO.setCitationType(NumberUtil.toInt(relPO.getCitationType()));

        StringBuilder append = new StringBuilder();
        append.append(relPO.getObjectRelId())
                .append(relPO.getTestMatrixId())
                .append(relPO.getExternalCode())
                .append(relPO.getPpVersionId())
                .append(relPO.getAid())
                .append(relPO.getTestLineId())
                .append(relPO.getCitationId())
                .append(relPO.getCitationVersionId())
                .append(relPO.getSampleId())
                .append(relPO.getSampleNo())
                .append(relPO.getExternalSampleNo())
                //.append(relPO.getSampleSeq())
                .append(relPO.getExtFields())
                .append(relPO.getCondition())
                //.append(relPO.getConclusionId())
                //.append(relPO.getConclusionDisplay())
                //.append(relPO.getEvaluationAlias())
                //.append(relPO.getLanguages())
                //.append(relPO.getExternalId())
                //.append(relPO.getMethodDesc())
                .append(relPO.getCitationType());
        return DigestUtils.md5Hex(append.toString().toLowerCase());
    }

    /**
     * @param td
     * @return
     */
    protected String getTestDataReportMd5(TestDataInfoPO td) {
        td.setObjectRelId(StringUtil.isNullOrEmpty(td.getObjectRelId()));
        td.setTestMatrixId(StringUtil.isNullOrEmpty(td.getTestMatrixId()));
        td.setAnalyteId(NumberUtils.toInt(td.getAnalyteId()) > 0 ? td.getAnalyteId() : null);
        td.setAnalyteName(StringUtil.isNullOrEmpty(td.getAnalyteName()));
        td.setPosition(StringUtil.isNullOrEmpty(td.getPosition()));
        td.setAnalyteType(NumberUtil.toInt(td.getAnalyteType()));
        td.setAnalyteCode(StringUtil.isNullOrEmpty(td.getAnalyteCode()));
        td.setAnalyteSeq(NumberUtil.toInt(td.getAnalyteSeq()));
        td.setReportUnit(StringUtil.isNullOrEmpty(td.getReportUnit()));
        td.setTestValue(StringUtil.isNullOrEmpty(td.getTestValue()));
        td.setCasNo(StringUtil.isNullOrEmpty(td.getCasNo()));
        td.setReportLimit(StringUtil.isNullOrEmpty(td.getReportLimit()));
        td.setLimitUnit(StringUtil.isNullOrEmpty(td.getLimitUnit()));
        td.setConclusionId(StringUtil.isNullOrEmpty(td.getConclusionId()));
        td.setTestDataMatrixId(td.getTestDataMatrixId());
        td.setLanguages(StringUtil.isNullOrEmpty(td.getLanguages()));

        StringBuilder append = new StringBuilder();
        append.append(td.getObjectRelId())
                .append(td.getTestMatrixId())
                .append(td.getAnalyteId())
                .append(td.getAnalyteName())
                .append(td.getAnalyteType())
                .append(td.getAnalyteCode())
                .append(td.getReportUnit())
                .append(td.getTestValue())
                .append(td.getCasNo())
                .append(td.getReportLimit())
                .append(td.getLimitUnit())
                .append(td.getConclusionId())
                .append(td.getTestDataMatrixId())
                .append(td.getLanguages());
        return DigestUtils.md5Hex(append.toString().toLowerCase());
    }

    /**
     * @param testMatrix
     * @return
     */
    protected String getCompareTestMatrixMd5(TestDataMatrixInfoPO testMatrix) {
        String oldTestMatrixId = testMatrix.getTestMatrixId();
        Integer oldPpVersionId = testMatrix.getPpVersionId();
        String oldSampleId = testMatrix.getSampleId();

        /*testMatrix.setTestMatrixId(null);
        testMatrix.setPpVersionId(null);
        testMatrix.setSampleId(null);*/

        String bizVersionId = this.getTestDataReportMatrixMd5(testMatrix);

        /*testMatrix.setTestMatrixId(oldTestMatrixId);
        testMatrix.setPpVersionId(oldPpVersionId);
        testMatrix.setSampleId(oldSampleId);*/

        return bizVersionId;
    }

    /**
     * @param td
     * @return
     */
    protected String getCompareTestDataMd5(TestDataInfoPO td) {
        String oldLanguages = td.getLanguages();
        td.setLanguages(null);

        String bizVersionId = this.getTestDataReportMd5(td);

        td.setLanguages(oldLanguages);

        return bizVersionId;
    }
}
