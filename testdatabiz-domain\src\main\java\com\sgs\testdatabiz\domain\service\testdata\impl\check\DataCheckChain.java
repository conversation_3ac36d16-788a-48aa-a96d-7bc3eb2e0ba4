package com.sgs.testdatabiz.domain.service.testdata.impl.check;

import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.condition.Condition;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.strategy.BasicParamsCheckStrategy;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.strategy.DataCheckStrategy;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.strategy.MatrixCheckStrategy;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: shawn.yang
 * @create: 2023-03-27 18:33
 */
public final class DataCheckChain {
    private static final List<StrategyComposite> STRATEGY_COMPOSITE_LIST = new ArrayList<>();


    public static boolean doCheck(ReportTestDataInfo reportTestData,RTDContext context) {
        for (int i = 0; i < STRATEGY_COMPOSITE_LIST.size(); i++) {
            StrategyComposite strategyComposite = STRATEGY_COMPOSITE_LIST.get(i);
            boolean matched = strategyComposite.getCondition().matched(reportTestData);
            if (matched){
                DataCheckStrategy strategy = strategyComposite.getStrategy();
                while (strategy.getNext() != null) {
                    boolean pass = strategy.check(reportTestData, context);
                    if (strategy.isMute()){

                    }

                }


            }


        }


        return false;
    }


    private static class StrategyComposite {
        private final Condition condition;
        private final DataCheckStrategy strategy;

        public StrategyComposite(Condition condition, DataCheckStrategy strategy) {
            this.condition = condition;
            this.strategy = strategy;
        }

        public Condition getCondition() {
            return condition;
        }

        public DataCheckStrategy getStrategy() {
            return strategy;
        }
    }

    {


        // slim SL
        Condition slimSLCondition1 = Condition.builder()
                .and(rtd -> Objects.equals(rtd.getSourceType(), SourceTypeEnum.SLIM.getCode()))
                .and(rtd -> Objects.equals(rtd.getProductLineCode(), ProductLineType.SL.getProductLineAbbr()))
                .build();

        BasicParamsCheckStrategy slimSLStrategy1 = new BasicParamsCheckStrategy();
        slimSLStrategy1.setNext(new MatrixCheckStrategy());
        STRATEGY_COMPOSITE_LIST.add(new StrategyComposite(slimSLCondition1, slimSLStrategy1));

        // slim HL
        Condition slimHLCondition1 = Condition.builder()
                .and(rtd -> Objects.equals(rtd.getSourceType(), SourceTypeEnum.SLIM.getCode()))
                .and(rtd -> Objects.equals(rtd.getProductLineCode(), ProductLineType.HL.getProductLineAbbr()))
                .build();

        BasicParamsCheckStrategy slimHLStrategy1 = new BasicParamsCheckStrategy();
        slimHLStrategy1.setNext(new MatrixCheckStrategy());


    }

}
