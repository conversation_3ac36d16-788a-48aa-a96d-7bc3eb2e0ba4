<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="ObjectRelId" property="objectRelId" jdbcType="VARCHAR" />
        <result column="TestMatrixId" property="testMatrixId" jdbcType="VARCHAR" />
        <result column="TestLineMappingId" property="testLineMappingId" jdbcType="INTEGER" />
        <result column="ExternalId" property="externalId" jdbcType="VARCHAR" />
        <result column="ExternalCode" property="externalCode" jdbcType="VARCHAR" />
        <result column="PpVersionId" property="ppVersionId" jdbcType="INTEGER" />
        <result column="Aid" property="aid" jdbcType="BIGINT" />
        <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
        <result column="CitationId" property="citationId" jdbcType="INTEGER" />
        <result column="CitationVersionId" property="citationVersionId" jdbcType="INTEGER" />
        <result column="CitationType" property="citationType" jdbcType="INTEGER" />
        <result column="CitationName" property="citationName" jdbcType="VARCHAR" />
        <result column="SampleId" property="sampleId" jdbcType="VARCHAR" />
        <result column="SampleNo" property="sampleNo" jdbcType="VARCHAR" />
        <result column="ExternalSampleNo" property="externalSampleNo" jdbcType="VARCHAR" />
        <result column="TestLineSeq" property="testLineSeq" jdbcType="BIGINT" />
        <result column="SampleSeq" property="sampleSeq" jdbcType="VARCHAR" />
        <result column="EvaluationAlias" property="evaluationAlias" jdbcType="VARCHAR" />
        <result column="MethodDesc" property="methodDesc" jdbcType="VARCHAR" />
        <result column="ConclusionId" property="conclusionId" jdbcType="VARCHAR" />
        <result column="ConclusionDisplay" property="conclusionDisplay" jdbcType="VARCHAR" />
        <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="INTEGER" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="lab_id" property="labId" jdbcType="BIGINT" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
        <result column="test_matrix_group_id" property="testMatrixGroupId" jdbcType="INTEGER" />
        <result column="test_line_instance_id" property="testLineInstanceId" jdbcType="VARCHAR" />
        <result column="evaluation_name" property="evaluationName" jdbcType="VARCHAR" />
        <result column="test_line_status" property="testLineStatus" jdbcType="VARCHAR" />
        <result column="test_line_remark" property="testLineRemark" jdbcType="VARCHAR" />
        <result column="citation_full_name" property="citationFullName" jdbcType="VARCHAR" />
        <result column="sample_instance_id" property="sampleInstanceId" jdbcType="VARCHAR" />
        <result column="sample_parent_id" property="sampleParentId" jdbcType="VARCHAR" />
        <result column="sample_type" property="sampleType" jdbcType="VARCHAR" />
        <result column="category" property="category" jdbcType="VARCHAR" />
        <result column="material_color" property="materialColor" jdbcType="VARCHAR" />
        <result column="composition" property="composition" jdbcType="VARCHAR" />
        <result column="material_description" property="materialDescription" jdbcType="VARCHAR" />
        <result column="material_end_use" property="materialEndUse" jdbcType="VARCHAR" />
        <result column="applicable_flag" property="applicableFlag" jdbcType="VARCHAR" />
        <result column="material_other_sample_info" property="materialOtherSampleInfo" jdbcType="VARCHAR" />
        <result column="material_remark" property="materialRemark" jdbcType="VARCHAR" />
        <result column="conclusion_code" property="conclusionCode" jdbcType="VARCHAR" />
        <result column="customer_conclusion" property="customerConclusion" jdbcType="VARCHAR" />
        <result column="conclusion_remark" property="conclusionRemark" jdbcType="VARCHAR" />
        <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
        <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" extends="BaseResultMap" >
        <result column="ExtFields" property="extFields" jdbcType="LONGVARCHAR" />
        <result column="Condition" property="condition" jdbcType="LONGVARCHAR" />
        <result column="Languages" property="languages" jdbcType="LONGVARCHAR" />
        <result column="sample_group" property="sampleGroup" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, ObjectRelId, TestMatrixId, TestLineMappingId, ExternalId, ExternalCode, PpVersionId,
    Aid, TestLineId, CitationId, CitationVersionId, CitationType, CitationName, SampleId,
    SampleNo, ExternalSampleNo, TestLineSeq, SampleSeq,ExtFields,`Condition`, EvaluationAlias, MethodDesc,
    ConclusionId, ConclusionDisplay,Languages,BizVersionId, ActiveIndicator, CreatedBy, CreatedDate,
    ModifiedBy, ModifiedDate, lab_id, order_no, report_no, test_matrix_group_id,
    test_line_instance_id ,
    evaluation_name, test_line_status, test_line_remark, citation_full_name,
    sample_instance_id,
    sample_parent_id, sample_type, category, material_color, composition, material_description,
    material_end_use, applicable_flag, material_other_sample_info, material_remark, conclusion_code,
    customer_conclusion, conclusion_remark,sample_group,matrix_source,rd_report_id
    </sql>
    <sql id="Base_Column_List_Select" >
        id, ObjectRelId, TestMatrixId, TestLineMappingId, ExternalId, ExternalCode, PpVersionId,
    Aid, TestLineId, CitationId, CitationVersionId, CitationType, CitationName, SampleId,
    SampleNo, ExternalSampleNo, TestLineSeq, SampleSeq,ExtFields,`Condition`, EvaluationAlias, MethodDesc,
    ConclusionId, ConclusionDisplay, BizVersionId, ActiveIndicator, CreatedBy, CreatedDate,
    ModifiedBy, ModifiedDate, lab_id as labId, order_no as orderNo, report_no as reportNo, test_matrix_group_id as testMatrixGroupId,
    test_line_instance_id as testLineInstanceId,
    evaluation_name as evaluationName, test_line_status as testLineStatus, test_line_remark as testLineRemark, citation_full_name as citationFullName,
    sample_instance_id as sampleInstanceId,
    sample_parent_id as sampleParentId, sample_type as sampleType, category, material_color as materialColor, composition, material_description as materialDescription,
    material_end_use as materialEndUse, applicable_flag as applicableFlag, material_other_sample_info as materialOtherSampleInfo, material_remark as materialRemark, conclusion_code as conclusionCode,
    customer_conclusion as customerConclusion, conclusion_remark as conclusionRemark,sample_group as sampleGroup, matrix_source as matrixSource,
    rd_report_id as rdReportId
    </sql>

  <insert id="batchInsert">
      INSERT INTO `tb_test_data_matrix_info_${suffix}` (
          <include refid="Base_Column_List" />
      )
      VALUES
      <foreach collection="testMatrixs" item="testMatrix" separator=",">
      (
          #{testMatrix.id},
          #{testMatrix.objectRelId},
          #{testMatrix.testMatrixId},
          #{testMatrix.testLineMappingId},
          #{testMatrix.externalId},
          #{testMatrix.externalCode},
          #{testMatrix.ppVersionId},
          #{testMatrix.aid},
          #{testMatrix.testLineId},
          #{testMatrix.citationId},
          #{testMatrix.citationVersionId},
          #{testMatrix.citationType},
          #{testMatrix.citationName},
          #{testMatrix.sampleId},
          #{testMatrix.sampleNo},
          #{testMatrix.externalSampleNo},
          #{testMatrix.testLineSeq},
          #{testMatrix.sampleSeq},
          #{testMatrix.extFields},
          #{testMatrix.condition},
          #{testMatrix.evaluationAlias},
          #{testMatrix.methodDesc},
          #{testMatrix.conclusionId},
          #{testMatrix.conclusionDisplay},
          #{testMatrix.languages},
          #{testMatrix.bizVersionId},
          #{testMatrix.activeIndicator},
          #{testMatrix.createdBy},
          #{testMatrix.createdDate},
          #{testMatrix.modifiedBy},
          #{testMatrix.modifiedDate},
          #{testMatrix.labId},
          #{testMatrix.orderNo},
          #{testMatrix.reportNo},
          #{testMatrix.testMatrixGroupId},
          #{testMatrix.testLineInstanceId},
          #{testMatrix.evaluationName},
          #{testMatrix.testLineStatus},
          #{testMatrix.testLineRemark},
          #{testMatrix.citationFullName},
          #{testMatrix.sampleInstanceId},
          #{testMatrix.sampleParentId},
          #{testMatrix.sampleType},
          #{testMatrix.category},
          #{testMatrix.materialColor},
          #{testMatrix.composition},
          #{testMatrix.materialDescription},
          #{testMatrix.materialEndUse},
          #{testMatrix.applicableFlag},
          #{testMatrix.materialOtherSampleInfo},
          #{testMatrix.materialRemark},
          #{testMatrix.conclusionCode},
          #{testMatrix.customerConclusion},
          #{testMatrix.conclusionRemark},
          #{testMatrix.sampleGroup},
          #{testMatrix.matrixSource},
          #{testMatrix.rdReportId}
      )
      </foreach>
      ON DUPLICATE KEY UPDATE
          TestMatrixId = VALUES(testMatrixId),
          TestLineMappingId = VALUES(testLineMappingId),
          ExternalId = VALUES(externalId),
          ExternalCode = VALUES(externalCode),
          PpVersionId = VALUES(ppVersionId),
          Aid = VALUES(aid),
          TestLineId = VALUES(testLineId),
          CitationId = VALUES(citationId),
          CitationVersionId = VALUES(citationVersionId),
          CitationType = VALUES(citationType),
          CitationName = VALUES(citationName),
          SampleId = VALUES(sampleId),
          SampleNo = VALUES(sampleNo),
          ExternalSampleNo = VALUES(externalSampleNo),
          TestLineSeq = VALUES(testLineSeq),
          SampleSeq = VALUES(sampleSeq),
          ExtFields = VALUES(extFields),
          `Condition` = VALUES(`Condition`),
          EvaluationAlias = VALUES(evaluationAlias),
          MethodDesc = VALUES(methodDesc),
          ConclusionId = VALUES(conclusionId),
          ConclusionDisplay = VALUES(conclusionDisplay),
          Languages = VALUES(languages),
          ActiveIndicator = VALUES(activeIndicator),
          ModifiedBy = VALUES(modifiedBy),
          ModifiedDate = VALUES(modifiedDate),
          lab_id = VALUES(lab_id),
          order_no = VALUES(order_no),
          report_no = VALUES(report_no),
          test_matrix_group_id = VALUES(test_matrix_group_id),
          test_line_instance_id = VALUES(test_line_instance_id),
          evaluation_name = VALUES(evaluation_name),
          test_line_status = VALUES(test_line_status),
          test_line_remark = VALUES(test_line_remark),
          citation_full_name = VALUES(citation_full_name),
          sample_instance_id = VALUES(sample_instance_id),
          sample_parent_id = VALUES(sample_parent_id),
          sample_type = VALUES(sample_type),
          category = VALUES(category),
          material_color = VALUES(material_color),
          composition = VALUES(composition),
          material_description = VALUES(material_description),
          material_end_use = VALUES(material_end_use),
          applicable_flag = VALUES(applicable_flag),
          material_other_sample_info = VALUES(material_other_sample_info),
          material_remark = VALUES(material_remark),
          conclusion_code = VALUES(conclusion_code),
          customer_conclusion = VALUES(customer_conclusion),
          conclusion_remark = VALUES(conclusion_remark),
          sample_group = VALUES(sample_group),
          matrix_source = VALUES(matrix_source),
          rd_report_id = VALUES(rd_report_id)
  </insert>

    <insert id="batchInsertSlim">
        INSERT INTO `tb_test_data_matrix_info` (
            <include refid="Base_Column_List" />
        )
        VALUES
        <foreach collection="testMatrixs" item="testMatrix" separator=",">
            (
            #{testMatrix.id},
            #{testMatrix.objectRelId},
            #{testMatrix.testMatrixId},
            #{testMatrix.testLineMappingId},
            #{testMatrix.externalId},
            #{testMatrix.externalCode},
            #{testMatrix.ppVersionId},
            #{testMatrix.aid},
            #{testMatrix.testLineId},
            #{testMatrix.citationId},
            #{testMatrix.citationVersionId},
            #{testMatrix.citationType},
            #{testMatrix.citationName},
            #{testMatrix.sampleId},
            #{testMatrix.sampleNo},
            #{testMatrix.externalSampleNo},
            #{testMatrix.testLineSeq},
            #{testMatrix.sampleSeq},
            #{testMatrix.extFields},
            #{testMatrix.condition},
            #{testMatrix.evaluationAlias},
            #{testMatrix.methodDesc},
            #{testMatrix.conclusionId},
            #{testMatrix.conclusionDisplay},
            #{testMatrix.languages},
            #{testMatrix.bizVersionId},
            #{testMatrix.activeIndicator},
            #{testMatrix.createdBy},
            #{testMatrix.createdDate},
            #{testMatrix.modifiedBy},
            #{testMatrix.modifiedDate}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            TestMatrixId = VALUES(testMatrixId),
            TestLineMappingId = VALUES(testLineMappingId),
            ExternalId = VALUES(externalId),
            ExternalCode = VALUES(externalCode),
            PpVersionId = VALUES(ppVersionId),
            Aid = VALUES(aid),
            TestLineId = VALUES(testLineId),
            CitationId = VALUES(citationId),
            CitationVersionId = VALUES(citationVersionId),
            CitationType = VALUES(citationType),
            CitationName = VALUES(citationName),
            SampleId = VALUES(sampleId),
            SampleNo = VALUES(sampleNo),
            ExternalSampleNo = VALUES(externalSampleNo),
            TestLineSeq = VALUES(testLineSeq),
            SampleSeq = VALUES(sampleSeq),
            ExtFields = VALUES(extFields),
            `Condition` = VALUES(`Condition`),
            EvaluationAlias = VALUES(evaluationAlias),
            MethodDesc = VALUES(methodDesc),
            ConclusionId = VALUES(conclusionId),
            ConclusionDisplay = VALUES(conclusionDisplay),
            Languages = VALUES(languages),
            ActiveIndicator = VALUES(activeIndicator),
            ModifiedBy = VALUES(modifiedBy),
            ModifiedDate = VALUES(modifiedDate)
    </insert>

  <update id="updateInvalidOrValid">
    update `tb_test_data_matrix_info_${suffix}`
    set  ActiveIndicator = 0
    and modifiedBy = #{modifiedBy}
    and modifiedDate = NOW()
    where
     objectRelId IN
      <foreach collection="objectRelIds" item="objectRelId" separator="," open="(" close=")">
        #{objectRelId}
      </foreach>
  </update>

  <select id="queryMatrix" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO">
    select
    <include refid="Base_Column_List_Select" />
    from `tb_test_data_matrix_info_${suffix}`
    where objectRelId = #{objectRelId}
  </select>

  <select id="queryTestMatrixInfoByReportId" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO">
    select
    <include refid="Base_Column_List_Select" />
    from `tb_test_data_matrix_info_${suffix}`
    where rd_report_id = #{reportId} and ActiveIndicator = 1
  </select>

    <select id="queryMatrixList" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO">
        select
        <include refid="Base_Column_List_Select" />
        from `tb_test_data_matrix_info_${suffix}`
        where ActiveIndicator = 1 and objectRelId IN
        <foreach collection="objectRelIds" item="objectRelId" separator="," open="(" close=")">
            #{objectRelId}
        </foreach>
    </select>

    <select id="getConclusionInfoList" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataConclusionInfoPO">
        SELECT DISTINCT
            tdrel.ObjectNo AS subContractNo
            ,tdrel.languageId as languageType
            ,tdm.id as TestDataMatrixId
            ,tdm.TestMatrixId
            ,tdm.PpVersionId
            ,tdm.Aid
            ,tdm.TestLineId
            ,tdm.CitationId
            ,tdm.CitationVersionId
            ,tdm.CitationType
            ,tdm.SampleId
            ,tdm.SampleNo
            ,tdm.Languages
            ,tdm.ConclusionId
            ,tdm.ConclusionDisplay
        FROM tb_test_data_object_rel tdrel
        INNER JOIN `tb_test_data_matrix_info_${suffix}` tdm ON tdm.ObjectRelId = tdrel.Id
          AND tdm.ActiveIndicator = 1
        WHERE tdrel.OrderNo = #{orderNo}
          <if test="sourceTypes != null and sourceTypes.size > 0">
            AND tdrel.SourceType IN
            <foreach collection="sourceTypes" item="sourceType" separator="," open="(" close=")">
                #{sourceType}
            </foreach>
          </if>
          AND tdrel.ActiveIndicator = 1
    </select>

    <select id="getAllConclusionInfoList" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataConclusionInfoPO">
        SELECT
            tdrel.ObjectNo AS subContractNo
            ,tdrel.languageId as languageType
            ,tdrel.ReportNo as reportNo
            ,tdrel.ExternalObjectNo as subReportNo
            ,tdm.id as TestDataMatrixId
            ,tdm.ActiveIndicator activeIndicator
            ,tdm.TestMatrixId
            ,tdm.PpVersionId
            ,tdm.Aid
            ,tdm.TestLineId
            ,tdm.CitationId
            ,tdm.CitationVersionId
            ,tdm.CitationType
            ,tdm.CitationName
            ,IFNULL(tdm.SampleId, tdm.SampleNo) as SampleId
            ,tdm.SampleNo
            ,tdm.Languages
            ,tdm.ConclusionId
            ,tdm.ConclusionDisplay
            ,tdm.test_line_instance_id as testLineInstanceId
            ,tdm.EvaluationAlias as evaluationAlias
            ,tdm.ExtFields as extFields
        FROM tb_test_data_object_rel tdrel
        INNER JOIN `tb_test_data_matrix_info_${suffix}` tdm ON tdm.ObjectRelId = tdrel.Id
          AND tdm.ActiveIndicator != 0
        WHERE tdrel.OrderNo = #{orderNo}
          <if test="sourceTypes != null and sourceTypes.size > 0">
            AND tdrel.SourceType IN
            <foreach collection="sourceTypes" item="sourceType" separator="," open="(" close=")">
                #{sourceType}
            </foreach>
          </if>
          AND tdrel.ActiveIndicator = 1
    </select>

    <select id="getTestDataAnalyteConclusionInfoList" resultType="com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataAnalyteConclusionInfoPO">
        select
        td.TestDataMatrixId
        ,td.ConclusionId as analyteConclusionId
        ,td.AnalyteId
        ,td.AnalyteName
        ,td.Languages as Languages
        from tb_test_data_info_${suffix} td
        where td.TestDataMatrixId = #{testDataMatrixId}
        AND td.ActiveIndicator = 1
    </select>

    <select id="getTestDataMatrixInfoList" resultType="com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMatrixExtPO">
        SELECT DISTINCT
            tdo.ExternalNo
            ,tdo.ExternalObjectNo
            ,tdm.TestMatrixId
            ,tdm.ExternalCode
            ,tdm.PpVersionId
            ,tdm.Aid
            ,tdm.TestLineId
            ,tdm.CitationId
            ,tdm.CitationVersionId
            ,tdm.SampleId
            ,tdm.SampleNo
            ,tdm.ExternalSampleNo
            ,tdm.ExtFields
            ,tdm.`Condition`
            ,tdm.CitationType
        FROM tb_test_data_object_rel tdo
        INNER JOIN `tb_test_data_matrix_info_${suffix}` tdm ON tdm.ObjectRelId = tdo.Id AND tdm.ActiveIndicator = 1
        WHERE tdo.OrderNo = #{orderNo}
	      AND tdo.ActiveIndicator = 1
    </select>

    <update id="batchUpdate">
        <foreach collection="testMatrixs" item="testMatrix" separator=";">
            UPDATE `tb_test_data_matrix_info_${suffix}`
              SET ConclusionId = #{testMatrix.conclusionId}
                ,ConclusionDisplay = #{testMatrix.conclusionDisplay}
                ,ModifiedDate = #{testMatrix.modifiedDate}
            WHERE Id = #{testMatrix.id}
              AND ActiveIndicator = 1
        </foreach>
    </update>

    <update id="delTestMatrixInvalid">
        DELETE FROM tb_test_data_matrix_info
        WHERE ObjectRelId = #{objectRelId}
          AND ActiveIndicator = 1
    </update>
    <update id="updateConclusionIdById">
        UPDATE `tb_test_data_matrix_info_${suffix}`
        SET ConclusionId = #{conclusionId}
        WHERE Id = #{testMatrixId}
    </update>
    <delete id="emptyTestMatrixByReport">
        delete from `tb_test_data_matrix_info_${suffix}` where rd_report_id = #{rdReportId}
    </delete>

</mapper>
