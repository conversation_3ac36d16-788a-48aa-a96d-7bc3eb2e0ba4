package com.sgs.testdatabiz.web.config;

import com.sgs.testdatabiz.core.config.TomcatConfig;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.AbstractServletWebServerFactory;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.nio.charset.Charset;

/**
 * Created by <PERSON> on 2019/05/09.
 * Description:
 */
@Configuration
public class WebConfigInitializer extends SpringBootServletInitializer {
    @Resource
    private TomcatConfig tomcatConfig;

    /**
     *
     * @return
     */
    @Bean
    public AbstractServletWebServerFactory createEmbeddedServletContainerFactory() {
        TomcatServletWebServerFactory tomcatFactory = new TomcatServletWebServerFactory();
        // 不需要指定，tomcat的端口号和rest服务的端口号必须保持一致，不然调用不通，tomcat可不指定端口号，用的应该是rest的端口号
        tomcatFactory.setPort(tomcatConfig.getPort());
        tomcatFactory.setProtocol(tomcatConfig.getProtocol());
        tomcatFactory.setUriEncoding(Charset.forName(tomcatConfig.getUriEncoding()));

        tomcatFactory.addConnectorCustomizers(new MyTomcatConnectorCustomizer(tomcatConfig));
        return tomcatFactory;
    }
}
