/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestLineLanguageInput implements Serializable {
    private Integer languageId;
    private String evaluationAlias;
    private String evaluationName;


    private Date lastModifiedTimestamp;
    //SCI-1378 增加labSectionName
    private String labSectionName;
    private Integer activeIndicator;
}
