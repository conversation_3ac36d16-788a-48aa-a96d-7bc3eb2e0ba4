package com.sgs.testdatabiz.facade.model.req.rd;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.facade.model.req.rd.validgroup.ExportReportDataGroup;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ExportDataReq extends BaseModel {

    private String orderNo;
    private String labCode;
    private ReportDataDTO reportData;
}
