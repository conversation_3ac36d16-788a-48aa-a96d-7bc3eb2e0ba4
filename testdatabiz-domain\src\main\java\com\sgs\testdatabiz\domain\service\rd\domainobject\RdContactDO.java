/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdContactDO {

    private String customerContactId;
    private Long bossContactId;
    private Long bossSiteUseId;
    private String contactName;
    private String telephone;
    private String email;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}