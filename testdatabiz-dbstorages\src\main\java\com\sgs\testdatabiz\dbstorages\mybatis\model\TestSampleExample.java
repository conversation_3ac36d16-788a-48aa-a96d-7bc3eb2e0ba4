package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestSampleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TestSampleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIsNull() {
            addCriterion("sample_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIsNotNull() {
            addCriterion("sample_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdEqualTo(String value) {
            addCriterion("sample_instance_id =", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotEqualTo(String value) {
            addCriterion("sample_instance_id <>", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdGreaterThan(String value) {
            addCriterion("sample_instance_id >", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("sample_instance_id >=", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLessThan(String value) {
            addCriterion("sample_instance_id <", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("sample_instance_id <=", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLike(String value) {
            addCriterion("sample_instance_id like", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotLike(String value) {
            addCriterion("sample_instance_id not like", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIn(List<String> values) {
            addCriterion("sample_instance_id in", values, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotIn(List<String> values) {
            addCriterion("sample_instance_id not in", values, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdBetween(String value1, String value2) {
            addCriterion("sample_instance_id between", value1, value2, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotBetween(String value1, String value2) {
            addCriterion("sample_instance_id not between", value1, value2, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdIsNull() {
            addCriterion("sample_parent_id is null");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdIsNotNull() {
            addCriterion("sample_parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdEqualTo(String value) {
            addCriterion("sample_parent_id =", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdNotEqualTo(String value) {
            addCriterion("sample_parent_id <>", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdGreaterThan(String value) {
            addCriterion("sample_parent_id >", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdGreaterThanOrEqualTo(String value) {
            addCriterion("sample_parent_id >=", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdLessThan(String value) {
            addCriterion("sample_parent_id <", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdLessThanOrEqualTo(String value) {
            addCriterion("sample_parent_id <=", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdLike(String value) {
            addCriterion("sample_parent_id like", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdNotLike(String value) {
            addCriterion("sample_parent_id not like", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdIn(List<String> values) {
            addCriterion("sample_parent_id in", values, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdNotIn(List<String> values) {
            addCriterion("sample_parent_id not in", values, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdBetween(String value1, String value2) {
            addCriterion("sample_parent_id between", value1, value2, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdNotBetween(String value1, String value2) {
            addCriterion("sample_parent_id not between", value1, value2, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdIsNull() {
            addCriterion("rd_report_matrix_id is null");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdIsNotNull() {
            addCriterion("rd_report_matrix_id is not null");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdEqualTo(Long value) {
            addCriterion("rd_report_matrix_id =", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdNotEqualTo(Long value) {
            addCriterion("rd_report_matrix_id <>", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdGreaterThan(Long value) {
            addCriterion("rd_report_matrix_id >", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rd_report_matrix_id >=", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdLessThan(Long value) {
            addCriterion("rd_report_matrix_id <", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdLessThanOrEqualTo(Long value) {
            addCriterion("rd_report_matrix_id <=", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdIn(List<Long> values) {
            addCriterion("rd_report_matrix_id in", values, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdNotIn(List<Long> values) {
            addCriterion("rd_report_matrix_id not in", values, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdBetween(Long value1, Long value2) {
            addCriterion("rd_report_matrix_id between", value1, value2, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdNotBetween(Long value1, Long value2) {
            addCriterion("rd_report_matrix_id not between", value1, value2, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andSampleNoIsNull() {
            addCriterion("sample_no is null");
            return (Criteria) this;
        }

        public Criteria andSampleNoIsNotNull() {
            addCriterion("sample_no is not null");
            return (Criteria) this;
        }

        public Criteria andSampleNoEqualTo(String value) {
            addCriterion("sample_no =", value, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoNotEqualTo(String value) {
            addCriterion("sample_no <>", value, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoGreaterThan(String value) {
            addCriterion("sample_no >", value, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoGreaterThanOrEqualTo(String value) {
            addCriterion("sample_no >=", value, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoLessThan(String value) {
            addCriterion("sample_no <", value, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoLessThanOrEqualTo(String value) {
            addCriterion("sample_no <=", value, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoLike(String value) {
            addCriterion("sample_no like", value, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoNotLike(String value) {
            addCriterion("sample_no not like", value, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoIn(List<String> values) {
            addCriterion("sample_no in", values, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoNotIn(List<String> values) {
            addCriterion("sample_no not in", values, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoBetween(String value1, String value2) {
            addCriterion("sample_no between", value1, value2, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleNoNotBetween(String value1, String value2) {
            addCriterion("sample_no not between", value1, value2, "sampleNo");
            return (Criteria) this;
        }

        public Criteria andSampleTypeIsNull() {
            addCriterion("sample_type is null");
            return (Criteria) this;
        }

        public Criteria andSampleTypeIsNotNull() {
            addCriterion("sample_type is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTypeEqualTo(Integer value) {
            addCriterion("sample_type =", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeNotEqualTo(Integer value) {
            addCriterion("sample_type <>", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeGreaterThan(Integer value) {
            addCriterion("sample_type >", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sample_type >=", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeLessThan(Integer value) {
            addCriterion("sample_type <", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sample_type <=", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeIn(List<Integer> values) {
            addCriterion("sample_type in", values, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeNotIn(List<Integer> values) {
            addCriterion("sample_type not in", values, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeBetween(Integer value1, Integer value2) {
            addCriterion("sample_type between", value1, value2, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sample_type not between", value1, value2, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleSeqIsNull() {
            addCriterion("sample_seq is null");
            return (Criteria) this;
        }

        public Criteria andSampleSeqIsNotNull() {
            addCriterion("sample_seq is not null");
            return (Criteria) this;
        }

        public Criteria andSampleSeqEqualTo(Integer value) {
            addCriterion("sample_seq =", value, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqNotEqualTo(Integer value) {
            addCriterion("sample_seq <>", value, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqGreaterThan(Integer value) {
            addCriterion("sample_seq >", value, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("sample_seq >=", value, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqLessThan(Integer value) {
            addCriterion("sample_seq <", value, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqLessThanOrEqualTo(Integer value) {
            addCriterion("sample_seq <=", value, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqIn(List<Integer> values) {
            addCriterion("sample_seq in", values, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqNotIn(List<Integer> values) {
            addCriterion("sample_seq not in", values, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqBetween(Integer value1, Integer value2) {
            addCriterion("sample_seq between", value1, value2, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andSampleSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("sample_seq not between", value1, value2, "sampleSeq");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andCompositionIsNull() {
            addCriterion("composition is null");
            return (Criteria) this;
        }

        public Criteria andCompositionIsNotNull() {
            addCriterion("composition is not null");
            return (Criteria) this;
        }

        public Criteria andCompositionEqualTo(String value) {
            addCriterion("composition =", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotEqualTo(String value) {
            addCriterion("composition <>", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThan(String value) {
            addCriterion("composition >", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThanOrEqualTo(String value) {
            addCriterion("composition >=", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLessThan(String value) {
            addCriterion("composition <", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLessThanOrEqualTo(String value) {
            addCriterion("composition <=", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLike(String value) {
            addCriterion("composition like", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotLike(String value) {
            addCriterion("composition not like", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionIn(List<String> values) {
            addCriterion("composition in", values, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotIn(List<String> values) {
            addCriterion("composition not in", values, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionBetween(String value1, String value2) {
            addCriterion("composition between", value1, value2, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotBetween(String value1, String value2) {
            addCriterion("composition not between", value1, value2, "composition");
            return (Criteria) this;
        }

        public Criteria andColorIsNull() {
            addCriterion("color is null");
            return (Criteria) this;
        }

        public Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (Criteria) this;
        }

        public Criteria andColorEqualTo(String value) {
            addCriterion("color =", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotEqualTo(String value) {
            addCriterion("color <>", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThan(String value) {
            addCriterion("color >", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualTo(String value) {
            addCriterion("color >=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThan(String value) {
            addCriterion("color <", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualTo(String value) {
            addCriterion("color <=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLike(String value) {
            addCriterion("color like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotLike(String value) {
            addCriterion("color not like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorIn(List<String> values) {
            addCriterion("color in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotIn(List<String> values) {
            addCriterion("color not in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorBetween(String value1, String value2) {
            addCriterion("color between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotBetween(String value1, String value2) {
            addCriterion("color not between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkIsNull() {
            addCriterion("sample_remark is null");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkIsNotNull() {
            addCriterion("sample_remark is not null");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkEqualTo(String value) {
            addCriterion("sample_remark =", value, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkNotEqualTo(String value) {
            addCriterion("sample_remark <>", value, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkGreaterThan(String value) {
            addCriterion("sample_remark >", value, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("sample_remark >=", value, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkLessThan(String value) {
            addCriterion("sample_remark <", value, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkLessThanOrEqualTo(String value) {
            addCriterion("sample_remark <=", value, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkLike(String value) {
            addCriterion("sample_remark like", value, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkNotLike(String value) {
            addCriterion("sample_remark not like", value, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkIn(List<String> values) {
            addCriterion("sample_remark in", values, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkNotIn(List<String> values) {
            addCriterion("sample_remark not in", values, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkBetween(String value1, String value2) {
            addCriterion("sample_remark between", value1, value2, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andSampleRemarkNotBetween(String value1, String value2) {
            addCriterion("sample_remark not between", value1, value2, "sampleRemark");
            return (Criteria) this;
        }

        public Criteria andEndUseIsNull() {
            addCriterion("end_use is null");
            return (Criteria) this;
        }

        public Criteria andEndUseIsNotNull() {
            addCriterion("end_use is not null");
            return (Criteria) this;
        }

        public Criteria andEndUseEqualTo(String value) {
            addCriterion("end_use =", value, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseNotEqualTo(String value) {
            addCriterion("end_use <>", value, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseGreaterThan(String value) {
            addCriterion("end_use >", value, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseGreaterThanOrEqualTo(String value) {
            addCriterion("end_use >=", value, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseLessThan(String value) {
            addCriterion("end_use <", value, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseLessThanOrEqualTo(String value) {
            addCriterion("end_use <=", value, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseLike(String value) {
            addCriterion("end_use like", value, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseNotLike(String value) {
            addCriterion("end_use not like", value, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseIn(List<String> values) {
            addCriterion("end_use in", values, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseNotIn(List<String> values) {
            addCriterion("end_use not in", values, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseBetween(String value1, String value2) {
            addCriterion("end_use between", value1, value2, "endUse");
            return (Criteria) this;
        }

        public Criteria andEndUseNotBetween(String value1, String value2) {
            addCriterion("end_use not between", value1, value2, "endUse");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNull() {
            addCriterion("material is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNotNull() {
            addCriterion("material is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialEqualTo(String value) {
            addCriterion("material =", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotEqualTo(String value) {
            addCriterion("material <>", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThan(String value) {
            addCriterion("material >", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThanOrEqualTo(String value) {
            addCriterion("material >=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThan(String value) {
            addCriterion("material <", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThanOrEqualTo(String value) {
            addCriterion("material <=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLike(String value) {
            addCriterion("material like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotLike(String value) {
            addCriterion("material not like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialIn(List<String> values) {
            addCriterion("material in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotIn(List<String> values) {
            addCriterion("material not in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialBetween(String value1, String value2) {
            addCriterion("material between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotBetween(String value1, String value2) {
            addCriterion("material not between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagIsNull() {
            addCriterion("applicable_flag is null");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagIsNotNull() {
            addCriterion("applicable_flag is not null");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagEqualTo(Integer value) {
            addCriterion("applicable_flag =", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagNotEqualTo(Integer value) {
            addCriterion("applicable_flag <>", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagGreaterThan(Integer value) {
            addCriterion("applicable_flag >", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("applicable_flag >=", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagLessThan(Integer value) {
            addCriterion("applicable_flag <", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagLessThanOrEqualTo(Integer value) {
            addCriterion("applicable_flag <=", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagIn(List<Integer> values) {
            addCriterion("applicable_flag in", values, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagNotIn(List<Integer> values) {
            addCriterion("applicable_flag not in", values, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagBetween(Integer value1, Integer value2) {
            addCriterion("applicable_flag between", value1, value2, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("applicable_flag not between", value1, value2, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("last_modified_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("last_modified_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("last_modified_timestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("last_modified_timestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("last_modified_timestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("last_modified_timestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("last_modified_timestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("last_modified_timestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}