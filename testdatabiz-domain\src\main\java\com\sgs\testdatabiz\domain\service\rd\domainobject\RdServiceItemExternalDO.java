/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceItemExternalDO {
    private String testItemId;
    private String testItemName;
    private Integer testCitationId;
    private String testCitationName;
    private List<RdLanguageDO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
