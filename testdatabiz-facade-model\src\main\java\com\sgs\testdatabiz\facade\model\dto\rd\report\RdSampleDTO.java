/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdSampleDTO implements Serializable {
    @ApiModelProperty(value = "testSampleInstanceId",dataType = "string", required = true)
    private String testSampleInstanceId;
    private String templateId;
    // SCI-1490
    private String reportNo;
    @ApiModelProperty(value = "sampleNo",dataType = "string", required = true)
    private String sampleNo;
    // add 20230720
    private String externalSampleNo;
    private String productItemNo;
    private List<RdProductSampleAttrDTO> sampleAttrList;

    private RdConclusionDTO conclusion;
    private Date lastModifiedTimestamp;
    private Integer activeIndicator;
}
