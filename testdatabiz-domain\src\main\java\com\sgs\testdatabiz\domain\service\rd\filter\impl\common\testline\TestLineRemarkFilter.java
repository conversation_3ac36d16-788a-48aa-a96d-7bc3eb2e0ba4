package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.testline;

import com.sgs.testdatabiz.core.enums.FilterType;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestLineDO;
import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import org.springframework.stereotype.Component;

@DefaultFilter(order = 4)
@Component
public class TestLineRemarkFilter extends AbstractTestLineFilter {
    
    @Override
    protected String getFilterType() {
        return FilterType.TEST_LINE_REMARK.name();
    }

    @Override
    protected String getFilterName() {
        return "TestLineRemarkFilter";
    }

    @Override
    protected String getFilterMessage() {
        return "Cleared test line remarks";
    }

    @Override
    protected boolean filterTestLine(RdTestLineDO testLine) {
        if (testLine != null) {
            testLine.setTestLineRemark(null);
        }
        return true; // 保留所有测试线,只清除备注
    }
} 