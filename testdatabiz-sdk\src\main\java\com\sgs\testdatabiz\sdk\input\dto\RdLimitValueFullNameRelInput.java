/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdLimitValueFullNameRelInput implements Serializable {

    private Long talBaseId;
    private Integer manualRequirement;
    private String limitValue1;
    private String limitValue2;
    private String operatorName;
    private String reportDescription;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
