<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.ApiRequestMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="system_id" property="systemId" jdbcType="INTEGER" />
    <result column="lab_code" property="labCode" jdbcType="VARCHAR" />
    <result column="request_id" property="requestId" jdbcType="VARCHAR" />
    <result column="method_name" property="methodName" jdbcType="VARCHAR" />
    <result column="ext_id" property="extId" jdbcType="VARCHAR" />
    <result column="request_header" property="requestHeader" jdbcType="OTHER" />
    <result column="request_body" property="requestBody" jdbcType="OTHER" />
    <result column="response_body" property="responseBody" jdbcType="OTHER" />
    <result column="response_status" property="responseStatus" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, system_id, lab_code, request_id, method_name, ext_id, request_header, request_body,
    response_body, response_status, created_by, created_date, modified_by, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_api_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from tb_api_request
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_api_request
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestExample" >
    delete from tb_api_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO" >
    insert into tb_api_request (id, system_id, lab_code,
                                request_id, method_name, ext_id,
                                request_header, request_body, response_body,
                                response_status, created_by, created_date,
                                modified_by, modified_date)
    values (#{id,jdbcType=BIGINT}, #{systemId,jdbcType=INTEGER}, #{labCode,jdbcType=VARCHAR},
            #{requestId,jdbcType=VARCHAR}, #{methodName,jdbcType=VARCHAR}, #{extId,jdbcType=VARCHAR},
            #{requestHeader,jdbcType=OTHER}, #{requestBody,jdbcType=OTHER}, #{responseBody,jdbcType=OTHER},
            #{responseStatus,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
            #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO" >
    insert into tb_api_request
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="systemId != null" >
        system_id,
      </if>
      <if test="labCode != null" >
        lab_code,
      </if>
      <if test="requestId != null" >
        request_id,
      </if>
      <if test="methodName != null" >
        method_name,
      </if>
      <if test="extId != null" >
        ext_id,
      </if>
      <if test="requestHeader != null" >
        request_header,
      </if>
      <if test="requestBody != null" >
        request_body,
      </if>
      <if test="responseBody != null" >
        response_body,
      </if>
      <if test="responseStatus != null" >
        response_status,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=INTEGER},
      </if>
      <if test="labCode != null" >
        #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null" >
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null" >
        #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="extId != null" >
        #{extId,jdbcType=VARCHAR},
      </if>
      <if test="requestHeader != null" >
        #{requestHeader,jdbcType=OTHER},
      </if>
      <if test="requestBody != null" >
        #{requestBody,jdbcType=OTHER},
      </if>
      <if test="responseBody != null" >
        #{responseBody,jdbcType=OTHER},
      </if>
      <if test="responseStatus != null" >
        #{responseStatus,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestExample" resultType="java.lang.Integer" >
    select count(*) from tb_api_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_api_request
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.systemId != null" >
        system_id = #{record.systemId,jdbcType=INTEGER},
      </if>
      <if test="record.labCode != null" >
        lab_code = #{record.labCode,jdbcType=VARCHAR},
      </if>
      <if test="record.requestId != null" >
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.methodName != null" >
        method_name = #{record.methodName,jdbcType=VARCHAR},
      </if>
      <if test="record.extId != null" >
        ext_id = #{record.extId,jdbcType=VARCHAR},
      </if>
      <if test="record.requestHeader != null" >
        request_header = #{record.requestHeader,jdbcType=OTHER},
      </if>
      <if test="record.requestBody != null" >
        request_body = #{record.requestBody,jdbcType=OTHER},
      </if>
      <if test="record.responseBody != null" >
        response_body = #{record.responseBody,jdbcType=OTHER},
      </if>
      <if test="record.responseStatus != null" >
        response_status = #{record.responseStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_api_request
    set id = #{record.id,jdbcType=BIGINT},
    system_id = #{record.systemId,jdbcType=INTEGER},
    lab_code = #{record.labCode,jdbcType=VARCHAR},
    request_id = #{record.requestId,jdbcType=VARCHAR},
    method_name = #{record.methodName,jdbcType=VARCHAR},
    ext_id = #{record.extId,jdbcType=VARCHAR},
    request_header = #{record.requestHeader,jdbcType=OTHER},
    request_body = #{record.requestBody,jdbcType=OTHER},
    response_body = #{record.responseBody,jdbcType=OTHER},
    response_status = #{record.responseStatus,jdbcType=VARCHAR},
    created_by = #{record.createdBy,jdbcType=VARCHAR},
    created_date = #{record.createdDate,jdbcType=TIMESTAMP},
    modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
    modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO" >
    update tb_api_request
    <set >
      <if test="systemId != null" >
        system_id = #{systemId,jdbcType=INTEGER},
      </if>
      <if test="labCode != null" >
        lab_code = #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null" >
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null" >
        method_name = #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="extId != null" >
        ext_id = #{extId,jdbcType=VARCHAR},
      </if>
      <if test="requestHeader != null" >
        request_header = #{requestHeader,jdbcType=OTHER},
      </if>
      <if test="requestBody != null" >
        request_body = #{requestBody,jdbcType=OTHER},
      </if>
      <if test="responseBody != null" >
        response_body = #{responseBody,jdbcType=OTHER},
      </if>
      <if test="responseStatus != null" >
        response_status = #{responseStatus,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO" >
    update tb_api_request
    set system_id = #{systemId,jdbcType=INTEGER},
        lab_code = #{labCode,jdbcType=VARCHAR},
        request_id = #{requestId,jdbcType=VARCHAR},
        method_name = #{methodName,jdbcType=VARCHAR},
        ext_id = #{extId,jdbcType=VARCHAR},
        request_header = #{requestHeader,jdbcType=OTHER},
        request_body = #{requestBody,jdbcType=OTHER},
        response_body = #{responseBody,jdbcType=OTHER},
        response_status = #{responseStatus,jdbcType=VARCHAR},
        created_by = #{createdBy,jdbcType=VARCHAR},
        created_date = #{createdDate,jdbcType=TIMESTAMP},
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_api_request
    (`id`,`system_id`,`lab_code`,
    `request_id`,`method_name`,`ext_id`,
    `request_header`,`request_body`,`response_body`,
    `response_status`,`created_by`,`created_date`,
    `modified_by`,`modified_date`)
    values
    <foreach collection="list" item="item" index="index" separator="," >
      ( #{ item.id, jdbcType=BIGINT},#{ item.systemId, jdbcType=INTEGER},#{ item.labCode, jdbcType=VARCHAR},
      #{ item.requestId, jdbcType=VARCHAR},#{ item.methodName, jdbcType=VARCHAR},#{ item.extId, jdbcType=VARCHAR},
      #{ item.requestHeader, jdbcType=OTHER},#{ item.requestBody, jdbcType=OTHER},#{ item.responseBody, jdbcType=OTHER},
      #{ item.responseStatus, jdbcType=VARCHAR},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" >
      update tb_api_request
      <set>
        <if test="item.systemId != null">
          `system_id` = #{item.systemId, jdbcType = INTEGER},
        </if>
        <if test="item.labCode != null">
          `lab_code` = #{item.labCode, jdbcType = VARCHAR},
        </if>
        <if test="item.requestId != null">
          `request_id` = #{item.requestId, jdbcType = VARCHAR},
        </if>
        <if test="item.methodName != null">
          `method_name` = #{item.methodName, jdbcType = VARCHAR},
        </if>
        <if test="item.extId != null">
          `ext_id` = #{item.extId, jdbcType = VARCHAR},
        </if>
        <if test="item.requestHeader != null">
          `request_header` = #{item.requestHeader, jdbcType = OTHER},
        </if>
        <if test="item.requestBody != null">
          `request_body` = #{item.requestBody, jdbcType = OTHER},
        </if>
        <if test="item.responseBody != null">
          `response_body` = #{item.responseBody, jdbcType = OTHER},
        </if>
        <if test="item.responseStatus != null">
          `response_status` = #{item.responseStatus, jdbcType = VARCHAR},
        </if>
        <if test="item.createdBy != null">
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if>
        <if test="item.createdDate != null">
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if>
        <if test="item.modifiedBy != null">
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if>
        <if test="item.modifiedDate != null">
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if>
      </set>
      <where>
        <if test="item.id != null">
          and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>
