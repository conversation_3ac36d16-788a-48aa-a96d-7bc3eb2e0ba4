package com.sgs.testdatabiz.web.config;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {
    @Value("${spring.redis.nodes}")
    private String nodes;
    @Value("${spring.redis.password}")
    private String password;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();

        String[] address = nodes.split(",");
        if (address.length == 1){
            SingleServerConfig singleServerConfig = config.useSingleServer();
            // 连接间隔 心跳
            singleServerConfig.setPingConnectionInterval(1000);
            singleServerConfig.setAddress(String.format("redis://%s", address[0]));
            singleServerConfig.setPassword(StringUtils.isBlank(password) ? null : password);
        }
        if (address.length > 1){
            ClusterServersConfig clusterServersConfig = config.useClusterServers();
            clusterServersConfig.setScanInterval(2000);
            // 连接间隔 心跳
            clusterServersConfig.setPingConnectionInterval(1000);
            Lists.newArrayList(address).forEach(node->{
                clusterServersConfig.addNodeAddress(String.format("redis://%s", node));
            });
            clusterServersConfig.setPassword(StringUtils.isBlank(password) ? null : password);
        }
        // 添加主从配置
        // config.useMasterSlaveServers().setMasterAddress("").setPassword("").addSlaveAddress(new String[]{"",""});
        // 集群模式配置 setScanInterval()扫描间隔时间，单位是毫秒, //可以用"rediss://"来启用SSL连接
        return Redisson.create(config);
    }
}
