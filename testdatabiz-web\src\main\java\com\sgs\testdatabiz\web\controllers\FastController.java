package com.sgs.testdatabiz.web.controllers;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.FastFacade;
import com.sgs.testdatabiz.facade.model.req.fast.TestDataInfoReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/fast")
@Api(value = "/fast", tags = "fast")
public class FastController {

    private static final Logger logger = LoggerFactory.getLogger(FastController.class);

    @Autowired
    private FastFacade fastFacade;


    @PostMapping("/fastSaveTestData")
    @ApiOperation(value = "从FAST过来的测试数据应与其他数据一样进入新的test data 表")
    public BaseResponse fastSaveTestData(@RequestBody TestDataInfoReq reqObject) {
        return fastFacade.fastSaveTestData(reqObject);
    }


}
