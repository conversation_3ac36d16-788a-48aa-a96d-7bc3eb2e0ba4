package com.sgs.testdatabiz.core.util;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 * 1 秒 = 1000 毫秒
 * 1 毫秒 = 1000 微秒
 * 1 微秒 = 1000 纳秒
 * Created by <PERSON> on 2019/5/25.
 */
public final class DateUtils {
    private static String YYYYMM = "yyyyMM";
    private static String YYYYMMDD = "yyyyMMdd";
    public static String HHMM = "HH:mm";
    private static String STANDARD_FORMAT = "yyyy-MM-dd HH:mm:ss";

    // 法一
    private final long totalMilliseconds = System.currentTimeMillis();

    private final long totalSeconds = totalMilliseconds / 1000;// 毫秒转为秒
    private final long currentSeconds = totalSeconds % 60;

    private final long totalMinutes = totalSeconds / 60;// 秒转为分钟
    private final long currentMinutes = totalMinutes % 60;

    private final long totalHoures = totalMinutes / 60;// 分钟转为小时
    private final long currentHoure = totalHoures % 24;

    /**
     * 获取日期的C#的 1 Ticks
     * C# 1 Ticks是0.1微妙即100纳秒
     *
     * @param date 日期
     * @return
     */
    public static long toCSharpTicks(Date date) {
        return date.getTime() * 1000 * 1000;
    }

    /**
     * 添加毫秒数
     *
     * @param date         日期
     * @param milliseconds 毫秒数
     * @return
     */
    public static Date addMilliseconds(Date date, int milliseconds) {
        DateTime dateTime = new DateTime(date);
        dateTime.plusMillis(milliseconds);
        return date;
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static long getTime() {
        return now().getTime();
    }

    /**
     *
     * @param field
     * @param amount
     * @return
     */
    public static long getTimeStamp(int field, int amount) {
        return getTime(field, amount).getTime();
    }

    /**
     *
     * @return
     */
    public static Date getTodayTime() {
        return getTodayTime(null);
    }

    /**
     *
     * @return
     */
    public static Date getTodayTime(Long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        if (timeStamp != null && timeStamp.longValue() > 0){
            calendar.setTime(new Date(timeStamp.longValue()));
        }
        // HOUR_OF_DAY 24小时制
        calendar.set(Calendar.HOUR_OF_DAY, 23); // Calendar.HOUR 12小时制
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     *
     * @return
     */
    public static String getToday() {
        return dateFormat(getTodayTime());
    }

    /**
     *
     * @return
     */
    public static String getTime(Long timeStamp) {
        return dateFormat(getTodayTime(timeStamp));
    }

    /**
     *
     * @param dateTime
     * @return
     */
    public static String dateFormat(Date dateTime){
        String format = "yyyyMMddHHmmss";
        if(getMinute(dateTime) > 0){
            format = "yyyyMMddHHmm00";
        }
        if(getHour(dateTime) > 0){
            format = "yyyyMMddHH0000";
        }
        if(getDay(dateTime) > 0){
            format = "yyyyMMdd000000";
        }
        return format(dateTime, format);
    }

    /**
     *
     * @param dateTime
     * @return
     */
    public static long getTime(Date dateTime) {
        if (dateTime == null){
            return getTime();
        }
        return dateTime.getTime();
    }

    public static Date getCurrentDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static Date addMinute(Date currentTime, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentTime);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }

    public static Date addMinute(int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }

    /**
     *
     * @return
     */
    public static String getTime(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, day);
        return format(calendar.getTime(), "yyyy-MM-dd");
    }

    /**
     * @param field
     * @param amount
     * @return
     */
    public static Date getTime(int field, int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(field, amount);
        return calendar.getTime();
    }

    /**
     *
     * @param dateTime
     * @param pattern
     * @return
     */
    public static long getTime1(String dateTime, String pattern) {
        Date date = getDate(dateTime, pattern);
        if (date == null){
            return 0;
        }
        return date.getTime();
    }

    /**
     *
     * @param year
     * @param month
     * @param pattern
     * @return
     */
    public static long getTime(int year, int month, String pattern) {
        if (year <= 0 || month <= 0 || StringUtils.isBlank(pattern)){
            return 0;
        }
        Date date = getDate(String.format("%s%s", year, month), pattern);
        if (date == null){
            return 0;
        }
        return date.getTime();
    }

    /**
     *
     * @return
     */
    public static String getDate(int year) {
        return format(getDate(year, 1), "yyyy-MM-dd");
    }

    /**
     *
     * @param timeStamp
     * @return
     */
    public static boolean isTodayTime(Long timeStamp) {
        return StringUtils.equalsIgnoreCase(getCurrentDate(YYYYMMDD), format(new Date(timeStamp), YYYYMMDD));
    }

    /**
     *
     * @param dateTime
     * @param pattern
     * @return
     */
    public static Date getDate(String dateTime, String pattern) {
        SimpleDateFormat smt = new SimpleDateFormat(pattern);
        try{
            return smt.parse(dateTime);
        }catch (Exception ex){
            return null;
        }
    }

    /**
     *
     * @param currentTime
     * @return
     */
    public static long getLastTime(Long currentTime){
        if (currentTime == null || currentTime.longValue() <= 0){
            return 0L;
        }
        return getLastDate(new Date(currentTime)).getTime();
    }

    public static Date getNow() {
        return DateTime.now().toDate();
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static Date now() {
        return DateTime.now().toDate();
    }

    /**
     * 格式化日期 yyyy-MM-dd HH:mm:ss
     *
     * @param datetime 日期
     */
    public static String format(Date datetime) {
        return format(datetime, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     *
     * @param pattern
     * @return
     */
    public static String getCurrentDate(String pattern) {
        Calendar calendar = Calendar.getInstance();
        return format(calendar.getTime(),pattern);
    }

    /**
     *
     * @param pattern
     * @return
     */
    public static String getDate(String pattern) {
        return format(getNow(), pattern);
    }

    /**
     * 格式化日期
     *
     * @param datetime 日期
     * @param pattern  yyyy年MM月dd日 HH时mm分ss秒
     * @return
     */
    public static String format(Date datetime, String pattern) {
        if (datetime == null) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        return format.format(datetime);
    }

    public static String format(Long timeStamp) {
        return format(timeStamp, "yyyyMMddHHmmss");
    }

    public static String toUTCTime(Long timeStamp) {
        if (timeStamp == null || timeStamp.longValue() <= 0) {
            return StringUtils.EMPTY;
        }
        return format(new Date(timeStamp), STANDARD_FORMAT);
    }

    public static String format(Long timeStamp, String pattern) {
        if (timeStamp == null || timeStamp.longValue() <= 0 ||  StringUtils.isBlank(pattern)) {
            return StringUtils.EMPTY;
        }
        return format(new Date(timeStamp), pattern);
    }

    /**
     *
     * @param source
     * @param pattern
     * @return
     */
    public static int getTime(String source, String pattern) {
        if (StringUtils.isBlank(source) || StringUtils.isBlank(pattern)) {
            return 0;
        }
        try{
            DateFormat dateFormat = new SimpleDateFormat(pattern);
            return NumberUtil.toInt(format(dateFormat.parse(source), pattern.replaceAll(":","")));
        }catch (Exception ex){
            return 0;
        }
    }

    /**
     *
     * @param pattern
     * @return
     */
    public static int getTime(String pattern) {
        return NumberUtil.toInt(format(getNow(), pattern.replaceAll(":", "")));
    }

    /**
     *
     * @return
     */
    public static String getTimeStamp() {
        return format(new Date(),"yyyyMMddHHmmss");
    }

    /**
     *
     * @return
     */
    public static String formatDate(Date date) {
        return format(date, STANDARD_FORMAT);
    }

    /**
     *
     * @return
     */
    public static int getMonth() {
        return NumberUtil.toInt(format(Calendar.getInstance().getTime(), YYYYMM));
    }

    /**
     *
     * @return
     */
    public static int getMonth(Long timeStamp){
        if (timeStamp == null || timeStamp.longValue() <= 0){
            return 0;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     *
     * @return
     */
    public static int getDay() {
        return NumberUtil.toInt(format(Calendar.getInstance().getTime(), YYYYMMDD));
    }

    /**
     * 是否为同一天
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return
     */
    public static boolean isSameDate(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }

        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);

        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);

        boolean isSameYear = calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR);
        boolean isSameMonth = isSameYear && calendar1.get(Calendar.MONTH) == calendar2.get(Calendar.MONTH);
        boolean isSameDate = isSameMonth && calendar1.get(Calendar.DAY_OF_MONTH) == calendar2.get(Calendar.DAY_OF_MONTH);
        return isSameDate;
    }

    /**
     * 取天的值，按月算
     *
     * @param date 日期
     * @return
     */
    public static int getDay(Date date) {
        return getDateFieldValue(date, Calendar.DAY_OF_MONTH);
    }

    /**
     * 取日期对应字段的值
     *
     * @param date  日期
     * @param field 字段，取值为Calendar的静态变量例如：Calendar.DAY_OF_MONTH
     * @return
     */
    public static int getDateFieldValue(Date date, int field) {
        if (date == null) {
            return 0;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(field);
    }

    /**
     * 获取某个日期和当前时间的差的秒数(倒计时用)
     *
     * @param maxDate 日期
     * @return
     */
    public static long getSecondTime(Date maxDate) {
        return getTimeStamp(maxDate, now()) / 1000;
    }

    /**
     * 获取某个日期和当前时间的差的小时数（倒计时用）
     *
     * @param maxDate 日期
     * @return
     */
    public static long getHourTime(Date maxDate) {
        return getTimeStamp(maxDate, now()) / 1000 / 60 / 60;
    }

    /**
     * 获取某个日期和当前时间的差的分钟数,当前时间-指定时间（非倒计时用）
     *
     * @param minDate 当前日期减的日期
     * @return
     */
    public static long getMinuteTimeSub(Date minDate) {
        return getTimeStamp(now(), minDate) / 1000 / 60;
    }

    /**
     * 获取某个日期和当前时间的差的毫秒数,日期 减去 当前时间
     *
     * @param maxDate 大日期
     * @param minDate 被减的日期
     * @return
     */
    public static long getTimeStamp(Date maxDate, Date minDate) {
        if (minDate == null || maxDate == null) {
            return 0;
        }
        long timeStamp = maxDate.getTime() - minDate.getTime();
        if (timeStamp < 0) {
            timeStamp = 0;
        }
        return timeStamp;
    }

    public static Date localDateTime2Date(java.time.LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        Date date = Date.from(zdt.toInstant());
        return date;
    }

    /**
     *
     * @param year
     * @param month
     * @return
     */
    public static Date getDate(int year, int month){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     *
     * @param currentTime
     * @return
     */
    public static Date getLastDate(Date currentTime){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentTime);
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);

        // DIG-8555  "-1" is not a valid value for setting "SECOND".
        calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) - 1);
        calendar.set(Calendar.SECOND, 59);
        //calendar.set(Calendar.SECOND, -1);
        return calendar.getTime();
    }

    /**
     *
     * @return
     */
    public static int getYear(){
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR);
    }

    /**
     *
     * @return
     */
    public static int getYear(Long timeStamp){
        if (timeStamp == null || timeStamp.longValue() <= 0){
            return 0;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        return calendar.get(Calendar.YEAR);
    }

    /**
     *
     * @param dateTime
     * @return
     */
    public static int getMinute(Date dateTime){
        return getDateFieldValue(dateTime, Calendar.MONTH);
    }


    /**
     *
     * @param dateTime
     * @return
     */
    public static int getHour(Date dateTime){
        return getDateFieldValue(dateTime, Calendar.HOUR_OF_DAY);
    }

    /**
     *
     * @param date
     * @return
     */
    public static Date isNullOrEmpty(Date date){
        if (date == null){
            return null;
        }
        return date;
    }
}
