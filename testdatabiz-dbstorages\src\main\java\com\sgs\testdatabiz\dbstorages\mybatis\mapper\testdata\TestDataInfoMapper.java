package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TestDataInfoMapper {
    int countByExample(TestDataInfoExample example);

    int deleteByExample(TestDataInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TestDataInfoPO record);

    int insertSelective(TestDataInfoPO record);

    List<TestDataInfoPO> selectByExampleWithBLOBs(TestDataInfoExample example);

    List<TestDataInfoPO> selectByExample(TestDataInfoExample example);

    TestDataInfoPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TestDataInfoPO record, @Param("example") TestDataInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") TestDataInfoPO record, @Param("example") TestDataInfoExample example);

    int updateByExample(@Param("record") TestDataInfoPO record, @Param("example") TestDataInfoExample example);

    int updateByPrimaryKeySelective(TestDataInfoPO record);

    int updateByPrimaryKeyWithBLOBs(TestDataInfoPO record);

    int updateByPrimaryKey(TestDataInfoPO record);

    int batchInsert(List<TestDataInfoPO> list);

    int batchUpdate(List<TestDataInfoPO> list);
}