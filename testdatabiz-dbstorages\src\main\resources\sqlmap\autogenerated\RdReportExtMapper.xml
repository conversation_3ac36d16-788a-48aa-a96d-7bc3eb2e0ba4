<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportExtMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="rd_report_id" property="rdReportId" jdbcType="BIGINT" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="update_version" property="updateVersion" jdbcType="INTEGER" />
    <result column="request_json" property="requestJson" jdbcType="LONGVARCHAR" />
    <result column="traceability_id" property="traceabilityId" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtPO" extends="BaseResultMap" >
    <result column="request_json" property="requestJson" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, rd_report_id, version, created_date, update_version,request_json,traceability_id
  </sql>
  <sql id="Blob_Column_List" >
    request_json
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_ext
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_ext
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from tb_report_ext
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_ext
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtExample" >
    delete from tb_report_ext
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtPO" >
    insert into tb_report_ext (id, rd_report_id, version,
      created_date, update_version, request_json,traceability_id
      )
    values (#{id,jdbcType=BIGINT}, #{rdReportId,jdbcType=BIGINT}, #{version,jdbcType=VARCHAR},
      now(), #{updateVersion,jdbcType=INTEGER}, #{requestJson,jdbcType=LONGVARCHAR},#{traceabilityId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtPO" >
    insert into tb_report_ext
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="rdReportId != null" >
        rd_report_id,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="updateVersion != null" >
        update_version,
      </if>
      <if test="requestJson != null" >
        request_json,
      </if>
      <if test="traceabilityId != null" >
        traceability_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="rdReportId != null" >
        #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="updateVersion != null" >
        #{updateVersion,jdbcType=INTEGER},
      </if>
      <if test="requestJson != null" >
        #{requestJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="traceabilityId != null" >
        #{traceabilityId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_ext
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_ext
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.rdReportId != null" >
        rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      </if>
      <if test="record.version != null" >
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.updateVersion != null" >
        update_version = #{record.updateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.requestJson != null" >
        request_json = #{record.requestJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_report_ext
    set id = #{record.id,jdbcType=BIGINT},
      rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      version = #{record.version,jdbcType=VARCHAR},

      update_version = #{record.updateVersion,jdbcType=INTEGER},
      request_json = #{record.requestJson,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_ext
    set id = #{record.id,jdbcType=BIGINT},
      rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      version = #{record.version,jdbcType=VARCHAR},

      update_version = #{record.updateVersion,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtPO" >
    update tb_report_ext
    <set >
      <if test="rdReportId != null" >
        rd_report_id = #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="updateVersion != null" >
        update_version = #{updateVersion,jdbcType=INTEGER},
      </if>
      <if test="requestJson != null" >
        request_json = #{requestJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtPO" >
    update tb_report_ext
    set rd_report_id = #{rdReportId,jdbcType=BIGINT},
      version = #{version,jdbcType=VARCHAR},

      update_version = #{updateVersion,jdbcType=INTEGER},
      request_json = #{requestJson,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtPO" >
    update tb_report_ext
    set rd_report_id = #{rdReportId,jdbcType=BIGINT},
      version = #{version,jdbcType=VARCHAR},

      update_version = #{updateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report_ext
      (`id`,`rd_report_id`,`version`,
      `created_date`,`update_version`,`request_json`,`traceability_id`
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
      ( #{ item.id, jdbcType=BIGINT},#{ item.rdReportId, jdbcType=BIGINT},#{ item.version, jdbcType=VARCHAR},
      #{ item.createdDate, jdbcType=TIMESTAMP},#{ item.updateVersion, jdbcType=INTEGER},#{ item.requestJson, jdbcType=LONGVARCHAR},#{ item.traceabilityId, jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" >
      update tb_report_ext
      <set>
        <if test="item.rdReportId != null">
          `rd_report_id` = #{item.rdReportId, jdbcType = BIGINT},
        </if>
        <if test="item.version != null">
          `version` = #{item.version, jdbcType = VARCHAR},
        </if>
        <if test="item.createdDate != null">
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if>
        <if test="item.updateVersion != null">
          `update_version` = #{item.updateVersion, jdbcType = INTEGER},
        </if>
        <if test="item.requestJson != null">
          `request_json` = #{item.requestJson, jdbcType = LONGVARCHAR},
        </if>
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>
