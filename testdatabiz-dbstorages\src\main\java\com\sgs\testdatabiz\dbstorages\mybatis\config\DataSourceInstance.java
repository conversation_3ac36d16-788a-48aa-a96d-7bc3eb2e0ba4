package com.sgs.testdatabiz.dbstorages.mybatis.config;

import java.util.List;
import java.util.Map;

public class DataSourceInstance {
    /**
     *
     */
    private List<String> productLines;

    /**
     *
     */
    private Map<String, Map<String, DataSourceConnection>> schema;

    public List<String> getProductLines() {
        return productLines;
    }

    public void setProductLines(List<String> productLines) {
        this.productLines = productLines;
    }

    public Map<String, Map<String, DataSourceConnection>> getSchema() {
        return schema;
    }

    public void setSchema(Map<String, Map<String, DataSourceConnection>> schema) {
        this.schema = schema;
    }
}
