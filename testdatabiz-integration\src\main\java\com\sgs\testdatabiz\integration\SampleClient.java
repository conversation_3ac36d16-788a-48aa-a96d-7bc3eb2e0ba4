package com.sgs.testdatabiz.integration;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.info.sample.TestLineSampleTypeInfo;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineInstanceIdsReq;
import com.sgs.testdatabiz.core.constant.ApiUrlConstants;
import com.sgs.testdatabiz.core.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SampleClient {

    private static final Logger logger = LoggerFactory.getLogger(SampleClient.class);
    
    @Value("${api.base.otsnotes}")
    private String otsnotesBaseUrl;
    
    @Autowired
    private TokenClient tokenClient;

    public List<TestLineSampleTypeInfo> queryTestSampleByTestLineInstanceId(List<String> testLineInstanceIds) {
        TestLineInstanceIdsReq reqObject = new TestLineInstanceIdsReq();
        reqObject.setTestLineInstanceIds(testLineInstanceIds);
        reqObject.setToken(tokenClient.getToken());
        
        try {
            String url = otsnotesBaseUrl + ApiUrlConstants.OtsNotes.Sample.QUERY_TEST_SAMPLE;
            
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("token", tokenClient.getToken());
            
            // 发送HTTP请求
            String response = HttpClientUtil.postJsonHeader(url, reqObject, headers);
            
            if (Func.isEmpty(response)) {
                logger.error("SampleClient.queryTestSampleByTestLineInstanceId error, testLineInstanceIds:{}, response is empty", 
                    StringUtil.join(testLineInstanceIds,","));
                return Lists.newArrayList();
            }
            
            // 解析响应
            BaseResponse<List<TestLineSampleTypeInfo>> baseResponse = 
                JSONObject.parseObject(response, new TypeReference<BaseResponse<List<TestLineSampleTypeInfo>>>(){});
                
            if (baseResponse == null || baseResponse.getStatus() != 200) {
                logger.error("SampleClient.queryTestSampleByTestLineInstanceId error, testLineInstanceIds:{}, response:{}", 
                    StringUtil.join(testLineInstanceIds,","), response);
                return Lists.newArrayList();
            }
            
            return baseResponse.getData();
            
        } catch (Exception e) {
            logger.error("SampleClient.queryTestSampleByTestLineInstanceId exception, testLineInstanceIds:{}", 
                StringUtil.join(testLineInstanceIds,","), e);
            return Lists.newArrayList();
        }
    }
}
