package com.sgs.testdatabiz.domain.service.validation.type.testline;

import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.core.errorcode.ErrorCode;
import com.sgs.testdatabiz.core.errorcode.ErrorCodeFactory;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.testdatabiz.core.exception.ReportDataCheckException;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.model.ReportTestDataValidationDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdPpTestLineRelDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestLineDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataBatchDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * TestLine数据完整性校验服务
 * 校验ReportDataBatchDTO中testLineList的数据完整性，包括：
 * 1. 基础字段校验
 * 2. Citation信息校验
 * 3. Conclusion信息校验
 * 4. PP关联信息校验（如果存在）
 */
@Service
public class RdTestLineValidationService implements ValidationService {

    private static final String VALIDATION_TYPE = ValidationTypeEnum.TESTLINE_DATA.getName();

    @Override
    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO) {
        if (!(validationRequestDTO instanceof ReportTestDataValidationDTO)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                ErrorCategoryEnum.REPORT_DATA_ERROR,
                ErrorBizModelEnum.REPORT_DATA_BIZ,
                ErrorFunctionTypeEnum.VALIDATION,
                ErrorTypeEnum.REQUESTNULL
            );
            throw new ReportDataCheckException(errorCode, 400, "Invalid validation request type");
        }

        ReportTestDataValidationDTO reportTestDataValidationDTO = (ReportTestDataValidationDTO) validationRequestDTO;
        ReportDataBatchDTO reportData = reportTestDataValidationDTO.getReportDataBatchDTO();

        if (reportData == null) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                ErrorCategoryEnum.REPORT_DATA_ERROR,
                ErrorBizModelEnum.REPORT_DATA_BIZ,
                ErrorFunctionTypeEnum.VALIDATION,
                ErrorTypeEnum.REQUESTNULL
            );
            throw new ReportDataCheckException(errorCode, 400, "ReportDataBatchDTO cannot be null");
        }

        if (CollectionUtils.isEmpty(reportData.getTestLineList())) {
            return ValidationResultDTO.success("No test lines to validate");
        }

        for (int i = 0; i < reportData.getTestLineList().size(); i++) {
            validateTestLine(reportData.getTestLineList().get(i), String.format("testLineList[%d]", i));
        }

        return ValidationResultDTO.success("All test lines validated successfully");
    }

    /**
     * 校验单个TestLine的数据完整性
     * @param testLine 待校验的TestLine对象
     * @param path 当前校验的数据路径
     */
    private void validateTestLine(RdTestLineDTO testLine, String path) {
        if (testLine == null) {
            throwValidationError(path + " cannot be null");
        }

        validateRequiredFields(testLine, path);
        validateCitation(testLine, path);
        //validateConclusion(testLine, path);
        //validatePPTestLineRel(testLine, path);
    }

    private void validateRequiredFields(RdTestLineDTO testLine, String path) {
        //validateNotNull(testLine.getActiveIndicator(), path + ".activeIndicator");
        validateNotBlank(testLine.getEvaluationAlias(), path + ".evaluationAlias");
        //validateNotBlank(testLine.getEvaluationName(), path + ".evaluationName");
        //validateNotNull(testLine.getLastModifiedTimestamp(), path + ".lastModifiedTimestamp");
        validateNotBlank(testLine.getOrderNo(), path + ".orderNo");
        //validateNotBlank(testLine.getProductLineAbbr(), path + ".productLineAbbr");
        validateNotNull(testLine.getSystemId(), path + ".systemId");
        validateNotNull(testLine.getTestLineId(), path + ".testLineId");
        validateNotBlank(testLine.getTestLineInstanceId(), path + ".testLineInstanceId");
        //validateNotNull(testLine.getTestLineSeq(), path + ".testLineSeq");
        //validateNotNull(testLine.getTestLineStatus(), path + ".testLineStatus");
        //validateNotNull(testLine.getTestLineType(), path + ".testLineType");
        //validateNotNull(testLine.getTestLineVersionId(), path + ".testLineVersionId");
    }

    private void validateCitation(RdTestLineDTO testLine, String path) {
        String citationPath = path + ".citation";
        if (testLine.getCitation() == null) {
            throwValidationError(citationPath + " cannot be null");
        }
        //validateNotNull(testLine.getCitation().getActiveIndicator(), citationPath + ".activeIndicator");
        validateNotBlank(testLine.getCitation().getCitationFullName(), citationPath + ".citationFullName");
        validateNotNull(testLine.getCitation().getCitationId(), citationPath + ".citationId");
        validateNotBlank(testLine.getCitation().getCitationName(), citationPath + ".citationName");
        validateNotNull(testLine.getCitation().getCitationType(), citationPath + ".citationType");
        //validateNotNull(testLine.getCitation().getCitationVersionId(), citationPath + ".citationVersionId");
        //validateNotNull(testLine.getCitation().getLastModifiedTimestamp(), citationPath + ".lastModifiedTimestamp");
    }

    private void validateConclusion(RdTestLineDTO testLine, String path) {
        String conclusionPath = path + ".conclusion";
        if (testLine.getConclusion() == null) {
            throwValidationError(conclusionPath + " cannot be null");
        }
        validateNotBlank(testLine.getConclusion().getConclusionCode(), conclusionPath + ".conclusionCode");
        validateNotBlank(testLine.getConclusion().getCustomerConclusion(), conclusionPath + ".customerConclusion");
    }

    private void validatePPTestLineRel(RdTestLineDTO testLine, String path) {
        if (!CollectionUtils.isEmpty(testLine.getPpTestLineRelList())) {
            for (int i = 0; i < testLine.getPpTestLineRelList().size(); i++) {
                String ppPath = path + ".ppTestLineRel[" + i + "]";
                RdPpTestLineRelDTO pp = testLine.getPpTestLineRelList().get(i);
                //validateNotNull(pp.getActiveIndicator(), ppPath + ".activeIndicator");
                validateNotNull(pp.getAid(), ppPath + ".aid");
                //validateNotNull(pp.getLastModifiedTimestamp(), ppPath + ".lastModifiedTimestamp");
                validateNotBlank(pp.getPpName(), ppPath + ".ppName");
                validateNotNull(pp.getPpNo(), ppPath + ".ppNo");
                validateNotNull(pp.getPpVersionId(), ppPath + ".ppVersionId");
            }
        }
    }

    private void validateNotNull(Object value, String path) {
        if (value == null) {
            throwValidationError(path + " is required");
        }
    }

    private void validateNotBlank(String value, String path) {
        if (StringUtils.isBlank(value)) {
            throwValidationError(path + " is required");
        }
    }

    private void throwValidationError(String message) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
            ErrorCategoryEnum.REPORT_DATA_ERROR,
            ErrorBizModelEnum.REPORT_DATA_BIZ,
            ErrorFunctionTypeEnum.VALIDATION,
            ErrorTypeEnum.REQUESTNULL
        );
        throw new ReportDataCheckException(errorCode, 400, String.format("[%s] %s", VALIDATION_TYPE, message));
    }

    @Override
    public String getType() {
        return ValidationTypeEnum.TESTLINE_DATA.getName();
    }

    @Override
    public Integer getOrder() {
        return 10;
    }
} 