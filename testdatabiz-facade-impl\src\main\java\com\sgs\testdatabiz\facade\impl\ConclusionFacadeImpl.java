package com.sgs.testdatabiz.facade.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.domain.service.ConclusionService;
import com.sgs.testdatabiz.facade.IConclusionFacade;
import com.sgs.testdatabiz.facade.model.req.ConclusionInfoReq;
import com.sgs.testdatabiz.facade.model.rsp.ConclusionInfoRsp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 */
@Component("conclusionFacade")
public class ConclusionFacadeImpl implements IConclusionFacade {
    @Autowired
    private ConclusionService conclusionService;

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse<List<ConclusionInfoRsp>> getConclusionInfoList(ConclusionInfoReq reqObject){
        return BaseResponse.newInstance(conclusionService.getConclusionInfoList(reqObject));
    }

    @Override
    public BaseResponse<List<ConclusionInfoRsp>> getAllConclusionInfoList(ConclusionInfoReq reqObject) {
        return BaseResponse.newInstance(conclusionService.getAllConclusionInfoList(reqObject));
    }
}
