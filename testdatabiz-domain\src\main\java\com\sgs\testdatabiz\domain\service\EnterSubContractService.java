package com.sgs.testdatabiz.domain.service;

import cn.hutool.db.handler.StringHandler;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.otsnotes.facade.model.enums.StatusEnum;
import com.sgs.otsnotes.facade.model.info.sample.TestLineSampleTypeInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.rsp.testLine.GetSubContractTestLineRsp;
import com.sgs.testdatabiz.core.annotation.SourceType;
import com.sgs.testdatabiz.core.util.*;
import com.sgs.testdatabiz.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataObjectRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubTestDataMatrixInfo;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubTestDataReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubcontractNoReq;
import com.sgs.testdatabiz.facade.model.rsp.subcontract.*;
import com.sgs.testdatabiz.facade.model.testdata.TestDataResultLangInfo;
import com.sgs.testdatabiz.integration.*;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@SourceType(sourceType = SourceTypeEnum.ENTERSUBCONTRACT)
public class EnterSubContractService extends AbstractTestDataService<SubTestDataReq> {

    private static final Logger logger = LoggerFactory.getLogger(EnterSubContractService.class);

    @Autowired
    private TestLineClient testLineClient;
    @Autowired
    private SampleClient sampleClient;
    @Autowired
    private ReportClient reportClient;
    @Autowired
    private SubcontractClient subcontractClient;
    @Autowired
    private TestDataObjectRelExtMapper objectRelExtMapper;
    @Autowired
    private TrimsLocalClient trimsLocalClient;
    @Autowired
    private PreOrderClient preOrderClient;
    @Autowired
    private TestDataMatrixInfoExtMapper matrixInfoExtMapper;
    @Autowired
    private SnowflakeIdWorker idWorker;


    public CustomResult<XSSFWorkbook> downLoadTemplate(SubcontractNoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        SubContractInfo subContractInfo = subcontractClient.getSubContractInfo(reqObject.getSubcontractNo());
        String orderNo = subContractInfo.getOrderNo();
        InputStream fileStream = Thread.currentThread().getContextClassLoader()
                .getResourceAsStream("export_template/Data Entry for Subcontract.xlsx");
        XSSFWorkbook wb = null;
        rspResult.setData(wb);
        try {
            wb = new XSSFWorkbook(fileStream);
            /* 有边框 自动换行 居左 垂直水平 */
            XSSFCellStyle unBlockStyle = wb.createCellStyle();
            unBlockStyle.setBorderBottom(BorderStyle.THIN);
            unBlockStyle.setBorderTop(BorderStyle.THIN);
            unBlockStyle.setBorderLeft(BorderStyle.THIN);
            unBlockStyle.setBorderRight(BorderStyle.THIN);
            unBlockStyle.setAlignment(HorizontalAlignment.LEFT);
            unBlockStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            unBlockStyle.setWrapText(true);//设置自动换行
            unBlockStyle.setLocked(false);
            XSSFFont font = wb.createFont();
            font.setFontHeightInPoints((short) 11);
            font.setBold(true);
            unBlockStyle.setFont(font);//选择创建的字体格式

            XSSFCellStyle blockStyle = wb.createCellStyle();
            blockStyle.setBorderBottom(BorderStyle.THIN);
            blockStyle.setBorderTop(BorderStyle.THIN);
            blockStyle.setBorderLeft(BorderStyle.THIN);
            blockStyle.setBorderRight(BorderStyle.THIN);
            blockStyle.setAlignment(HorizontalAlignment.LEFT);
            blockStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            blockStyle.setWrapText(true);//设置自动换行
            blockStyle.setLocked(true);
            blockStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
            XSSFFont bfont = wb.createFont();
            bfont.setFontHeightInPoints((short) 11);
            //bfont.setColor(new XSSFColor(new Color(255,0,0)));
            blockStyle.setFont(bfont);//选择创建的字体格式

            //SubContractInfo subContractInfo = subcontractClient.getSubContractInfo(reqObject.getSubcontractNo());


            String subcontractId = subContractInfo.getId();
            List<GetSubContractTestLineRsp> subTestLines = testLineClient.getSubContractTestLine(subcontractId, orderNo);
            List<String> testLineInstanceIds = subTestLines.stream().map(GetSubContractTestLineRsp::getTestLineInstanceId).collect(Collectors.toList());
            Set<Long> ppBaseIds = subTestLines.stream().map(GetSubContractTestLineRsp::getPpBaseId).collect(Collectors.toSet());
            List<GetPpBaseInfoRsp> ppBaseInfos = trimsLocalClient.getPpBaseInfo(ppBaseIds);
            Map<Long, GetPpBaseInfoRsp> ppMap = ppBaseInfos.stream().collect(Collectors.toMap(GetPpBaseInfoRsp::getPpBaseId, Function.identity(), (o1, o2) -> o1));
            List<TestLineSampleTypeInfo> sampleList = sampleClient.queryTestSampleByTestLineInstanceId(testLineInstanceIds);
            Map<String, List<TestLineSampleTypeInfo>> tlMap = sampleList.stream().collect(Collectors.groupingBy(TestLineSampleTypeInfo::getTestLineInstanceId));
            int index = 0;
            XSSFSheet sheet = null;
            List<String> sheetNames = Lists.newArrayList();
            for (GetSubContractTestLineRsp rsp : subTestLines) {
                if (index == 0) {
                    sheet = wb.getSheetAt(0);
                } else {
                    sheet = wb.cloneSheet(0);
                }
                //sheet.protectSheet("Aa123456");
                String sheetName = rsp.getTestItem();
                if (sheetNames.contains(sheetName)) {
                    sheetName = rsp.getTestLineId() + "_" + sheetName;
                }
                wb.setSheetName(index, sheetName);
                sheetNames.add(sheetName);
                //testLineInstanceId
                XSSFCell instanceIdCell = sheet.getRow(0).getCell(0);
                instanceIdCell.setCellStyle(blockStyle);
                instanceIdCell.setCellType(CellType.STRING);
                instanceIdCell.setCellValue(rsp.getTestLineInstanceId());
                //evaluationAlias
                XSSFCell testItemCell = sheet.getRow(0).getCell(1);
                testItemCell.setCellStyle(blockStyle);
                testItemCell.setCellType(CellType.STRING);
                testItemCell.setCellValue(rsp.getTestItem());

                XSSFCell citationIdCell = sheet.getRow(1).getCell(0);
                citationIdCell.setCellStyle(blockStyle);
                citationIdCell.setCellType(CellType.STRING);
                citationIdCell.setCellValue(rsp.getCitationId() == null ? "" : rsp.getCitationId().toString());
                XSSFCell standardCell = sheet.getRow(1).getCell(1);
                standardCell.setCellStyle(blockStyle);
                standardCell.setCellType(CellType.STRING);
                standardCell.setCellValue(rsp.getTestStandard());
                //citationVersionId
                XSSFCell citationVersionIdCell = sheet.getRow(2).getCell(1);
                citationVersionIdCell.setCellStyle(blockStyle);
                citationVersionIdCell.setCellType(CellType.STRING);
                citationVersionIdCell.setCellValue(rsp.getCitationVersionId());

                //testLineId
                XSSFCell testLineIdCell = sheet.getRow(2).getCell(2);
                testLineIdCell.setCellStyle(blockStyle);
                testLineIdCell.setCellType(CellType.STRING);
                testLineIdCell.setCellValue(rsp.getTestLineId());
                //ordNo
                XSSFCell orderNoCell = sheet.getRow(2).getCell(3);
                orderNoCell.setCellStyle(blockStyle);
                orderNoCell.setCellValue(orderNo);
                //subcontractNo
                XSSFCell subNoCell = sheet.getRow(2).getCell(4);
                subNoCell.setCellStyle(blockStyle);
                subNoCell.setCellValue(rsp.getSubContractNo());
                //subcontractId
                XSSFCell subIdCell = sheet.getRow(2).getCell(5);
                subIdCell.setCellStyle(blockStyle);
                subIdCell.setCellValue(subcontractId);
                //aid
                XSSFCell aIdCell = sheet.getRow(2).getCell(6);
                aIdCell.setCellStyle(blockStyle);
                aIdCell.setCellValue(NumberUtil.toLong(rsp.getaId()));
                //ppVersionId
                GetPpBaseInfoRsp baseInfoRsp = ppMap.get(rsp.getPpBaseId());
                Integer ppVersionId = 0;
                if (baseInfoRsp != null) {
                    ppVersionId = NumberUtil.toInt(baseInfoRsp.getPpVersionId());
                }
                XSSFCell ppVersionIddCell = sheet.getRow(2).getCell(7);
                ppVersionIddCell.setCellStyle(blockStyle);
                ppVersionIddCell.setCellType(CellType.STRING);
                ppVersionIddCell.setCellValue(ppVersionId);

                List<TestLineSampleTypeInfo> samples = tlMap.get(rsp.getTestLineInstanceId()).stream().sorted(Comparator.comparing(TestLineSampleTypeInfo::getSampleNo)).collect(Collectors.toList());
                samples = samples.stream().sorted(Comparator.comparing(TestLineSampleTypeInfo::getSampleSeq, Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());
                sheet.setColumnWidth(3 + samples.size(), 50 * 256);
                for (int x = 0; x < samples.size(); x++) {
                    TestLineSampleTypeInfo sample = samples.get(x);
                    //sampleNo
                    XSSFCell cell = sheet.getRow(4).createCell(3 + x);  //row4 Analyte title行  新增sampleNo列
                    cell.setCellStyle(blockStyle);
                    cell.setCellType(CellType.STRING);
                    cell.setCellValue(sample.getSampleNo());
                    //sampleId
                    XSSFCell sampleIdCell = sheet.getRow(5).createCell(3 + x);
                    sampleIdCell.setCellStyle(blockStyle);
                    sampleIdCell.setCellValue(sample.getSampleId());//sampleId
                    //matrixId
                    XSSFCell matrixIdCell = sheet.getRow(3).createCell(3 + x);
                    matrixIdCell.setCellStyle(blockStyle);
                    matrixIdCell.setCellType(CellType.STRING);
                    matrixIdCell.setCellValue(sample.getMatrixId());
                    //testValue输入框
                    sheet.getRow(6).createCell(3 + x).setCellType(CellType.STRING);
                    sheet.getRow(6).createCell(3 + x).setCellStyle(unBlockStyle);           //输入内容行增加行线
                }
                //Limit 列
                XSSFCell cell1 = sheet.getRow(4).createCell(3 + samples.size());
                XSSFCell cell2 = sheet.getRow(6).createCell(3 + samples.size());
                cell2.setCellStyle(unBlockStyle);
                cell1.setCellStyle(blockStyle);
                cell1.setCellValue("Requirement(样品limt不同时，请使用以下格式：A:2-3\\B:3-4)");
                index++;
            }
        } catch (Exception e) {
            logger.error("error", e);
        }
        rspResult.setSuccess(true);
        rspResult.setData(wb);
        return rspResult;
    }


    @Override
    public CustomResult doInvoke(SubTestDataReq reqObject) {
        CustomResult rspResult = new CustomResult();
        UserInfo userInfo = UserHelper.getLocalUser();
        TestDataObjectRelPO relPO = new TestDataObjectRelPO();
        relPO.setId(UUID.randomUUID().toString());
        TestDataObjectRelPO reportObjectRel = objectRelExtMapper.getReportObjectRelByObjectNo(reqObject.getObjectNo());
        if (reportObjectRel != null) {
            relPO.setId(reportObjectRel.getId());
        }
        String suffix = StringUtil.getTestDataSuffix(userInfo.getCurrentLabCode());

        relPO.setOrderNo(reqObject.getOrderNo());
        relPO.setReportNo(reqObject.getReportNo());
        relPO.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        relPO.setObjectNo(reqObject.getObjectNo());
        relPO.setSourceType(SourceTypeEnum.ENTERSUBCONTRACT.getCode());
        relPO.setActiveIndicator(StatusEnum.VALID.getCode());
        relPO.setLabCode(userInfo.getCurrentLabCode());
        relPO.setCreatedBy(userInfo.getRegionAccount());
        relPO.setCreatedDate(DateUtils.getNow());
        relPO.setModifiedBy(userInfo.getRegionAccount());
        relPO.setModifiedDate(DateUtils.getNow());
        relPO.setCompleteDate(reqObject.getCompletedDate());
        relPO.setLanguageId(LanguageType.English.getLanguageId());
        relPO.setParentOrderNo(StringUtil.defaultString(reqObject.getParentOrderNo(), reqObject.getOrderNo()));
        relPO.setBizVersionId(this.getTestDataReportObjectRelMd5(relPO));

        Map<String, Long> matrixBizMaps = Maps.newConcurrentMap();
        if (!CollectionUtils.isEmpty(reqObject.getMatrixInfos())) {
            List<SubTestDataMatrixInfo> testMatrixDTOs = reqObject.getMatrixInfos();
            Map<String, List<SubTestDataMatrixInfo>> matrixMaps = testMatrixDTOs.stream()
                    .filter(item -> StringUtil.isNotEmpty(item.getTestMatrixId()))
                    .collect(Collectors.groupingBy(SubTestDataMatrixInfo::getTestMatrixId));

            // 获取 matrix 中的 <bizVersionId, id>关系
            List<TestDataMatrixInfoPO> testDataMatrixInfoPOS = matrixInfoExtMapper.queryMatrix(relPO.getId(), suffix);
            if (!CollectionUtils.isEmpty(testDataMatrixInfoPOS)) {
                matrixBizMaps = testDataMatrixInfoPOS.stream().collect(Collectors.toMap(TestDataMatrixInfoPO::getBizVersionId, TestDataMatrixInfoPO::getId, (k1, k2) -> k1));
            }
        }
        Map<String, Long> finalMatrixBizMaps = matrixBizMaps;
        List<TestDataMatrixInfoPO> matrixPOList = reqObject.getMatrixInfos().stream().map(x -> {
            TestDataMatrixInfoPO matrixPO = new TestDataMatrixInfoPO();
            matrixPO.setObjectRelId(relPO.getId());
            BeanUtils.copyProperties(x, matrixPO);
            matrixPO.setActiveIndicator(StatusEnum.VALID.getCode());
            matrixPO.setCreatedBy(userInfo.getRegionAccount());
            matrixPO.setCreatedDate(DateUtils.getNow());
            matrixPO.setModifiedBy(userInfo.getRegionAccount());
            matrixPO.setModifiedDate(DateUtils.getNow());
            matrixPO.setBizVersionId(this.getTestDataReportMatrixMd5(matrixPO));
            // 设置 Id
            matrixPO.setId(NumberUtil.defaultIfBlank(finalMatrixBizMaps.get(matrixPO.getBizVersionId()), idWorker.nextId()));

            matrixPO.setSampleSeq(String.valueOf(x.getSampleSeq()));
            return matrixPO;
        }).collect(Collectors.toList());

        List<TestDataInfoPO> reportPOList = reqObject.getTestDataReports().stream().map(x -> {
            TestDataInfoPO reportPO = new TestDataInfoPO();
            BeanUtils.copyProperties(x, reportPO);
            reportPO.setId(idWorker.nextId());
            reportPO.setActiveIndicator(StatusEnum.VALID.getCode());
            reportPO.setCreatedBy(userInfo.getRegionAccount());
            reportPO.setCreatedDate(DateUtils.getNow());
            reportPO.setModifiedBy(userInfo.getRegionAccount());
            reportPO.setModifiedDate(DateUtils.getNow());
            reportPO.setObjectRelId(relPO.getId());
            reportPO.setBizVersionId(this.getTestDataReportMd5(reportPO));
            TestDataMatrixInfoPO matrixInfoPO = ListUtils.findFirst(matrixPOList, testDataMatrixInfoPO -> testDataMatrixInfoPO.getTestMatrixId().equals(x.getTestMatrixId()));
            if (matrixInfoPO != null) {
                reportPO.setTestDataMatrixId(matrixInfoPO.getId());
            }
            return reportPO;
        }).collect(Collectors.toList());

        TestDataDTO dataDTO = new TestDataDTO();
        dataDTO.setTestDataObjectRels(Lists.newArrayList(relPO));
        dataDTO.setTestDataMatrixInfos(matrixPOList);
        dataDTO.setTestDataInfos(reportPOList);
        dataDTO.setTestDataSuffix(com.sgs.testdatabiz.core.util.StringUtil.getTestDataSuffix(userInfo.getCurrentLabCode()));

        TestDataObjectRelDTO objectRelDTO = new TestDataObjectRelDTO();
        objectRelDTO.setObjectNo(reqObject.getObjectNo());
        objectRelDTO.setReportNo(reqObject.getReportNo());
        objectRelDTO.setModifiedDate(DateUtils.getNow());
        objectRelDTO.setModifiedBy(userInfo.getRegionAccount());
        dataDTO.setObjectRelDTO(objectRelDTO);

        rspResult = this.doDeal(dataDTO);
        return rspResult;
    }

    public CustomResult<ReportDataRsp> querySubTestData(SubcontractNoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        UserInfo userInfo = UserHelper.getLocalUser();
        if (Objects.isNull(userInfo)) {
            return rspResult.fail("登录失效，请重新登录");
        }
        if(StringUtils.isBlank(reqObject.getSubcontractNo()) && StringUtils.isBlank(reqObject.getExternalObjectNo())){
            return rspResult.fail("Parameter can't be empty.");
        }
        ReportDataRsp dataRsp = new ReportDataRsp();
        dataRsp.setSubContractNo(reqObject.getSubcontractNo());
        dataRsp.setExternalObjectNo(reqObject.getExternalObjectNo());
        List<UploadCitationInfo> list = objectRelExtMapper.queryUploadTestDataBySubcontractNo(reqObject.getSubcontractNo(),reqObject.getExternalObjectNo(),  com.sgs.testdatabiz.core.util.StringUtil.getTestDataSuffix(userInfo.getCurrentLabCode()));
        dataRsp.setList(this.convertRsp(list));
        rspResult.setSuccess(true);
        rspResult.setData(dataRsp);
        return rspResult;
    }

    public CustomResult cancelSubTestData(SubcontractNoReq reqObject) {
        logger.info("cancelSubTestData receive param,reqObject:{}", JSONObject.toJSONString(reqObject));
        CustomResult rspResult = new CustomResult();
        int count = objectRelExtMapper.cancelUploadTestData(reqObject.getSubcontractNo(), com.sgs.testdatabiz.core.util.StringUtil.getTestDataSuffix(reqObject.getLabCode()));
        rspResult.setSuccess(count > 0);
        return rspResult;
    }

    private List<SubTestDataInfo> convertRsp(List<UploadCitationInfo> list) {
        List<SubTestDataInfo> resultList = Lists.newArrayList();
        list.forEach(x -> {
            SubTestDataInfo dataInfo = new SubTestDataInfo();
            dataInfo.setCitationName(x.getCitationName());
            dataInfo.setEvaluationAlias(x.getEvaluationAlias());
            Map<String, List<UploadTestInfo>> map = x.getCitations().stream()
                    .collect(Collectors.groupingBy(uploadTestInfo -> StringUtils.defaultIfEmpty(uploadTestInfo.getAnalyteName(), "") + StringUtils.defaultIfEmpty(uploadTestInfo.getReportUnit(), "")  + uploadTestInfo.getAnalyteSeq()));
            List<AnalyteInfo> analyteInfos = Lists.newArrayList();
            map.forEach((s, uploadTestInfos) -> {
                AnalyteInfo analyteInfo = new AnalyteInfo();
                analyteInfo.setAnalyte(uploadTestInfos.get(0).getAnalyteName());
                analyteInfo.setReportUnit(uploadTestInfos.get(0).getReportUnit());
                analyteInfo.setAnalyteSeq(NumberUtil.toInt(uploadTestInfos.get(0).getAnalyteSeq()));

                String languages = uploadTestInfos.get(0).getLanguages();
                try{
                    List<TestDataResultLangInfo> languageResult = JSONArray.parseArray(languages, TestDataResultLangInfo.class);
                    for (TestDataResultLangInfo lan : languageResult) {
                        if(!LanguageType.check(lan.getLanguageId(),LanguageType.Chinese)){
                            continue;
                        }
                        String testAnalyteNameCN = lan.getTestAnalyteName();
                        String reportUnitCN = lan.getReportUnit();
                        analyteInfo.setAnalyteCN(testAnalyteNameCN);
                        analyteInfo.setReportUnitCN(reportUnitCN);
                    }
                }catch (Exception e){
                    logger.error("解析分包testdata的language 异常",e);
                }

                List<SampleTestDataInfo> dataInfos = Lists.newArrayList();
                Set<String> limits = Sets.newHashSet();
                uploadTestInfos.forEach(uploadTestInfo -> {
                    SampleTestDataInfo sampleInfo = new SampleTestDataInfo();
                    sampleInfo.setSampleNo(uploadTestInfo.getSampleNo());
                    sampleInfo.setTestValue(uploadTestInfo.getTestValue());
                    sampleInfo.setSampleSeq(uploadTestInfo.getSampleSeq());
                    dataInfos.add(sampleInfo);
                    if (StringUtils.isNotBlank(uploadTestInfo.getReportLimit())) {
                        limits.add(uploadTestInfo.getSampleNo() + ":" + uploadTestInfo.getReportLimit());
                    }
                });
                List<UploadTestInfo> limists = uploadTestInfos.stream().filter(uploadTestInfo -> StringUtils.isNotBlank(uploadTestInfo.getReportLimit())).collect(Collectors.toList());
                Set<String> limitSet = limists.stream().map(UploadTestInfo::getReportLimit).collect(Collectors.toSet());
                if (limists.size() > 1 && limitSet.size() == 1) {
                    analyteInfo.setReportLimit(limitSet.toArray()[0].toString());
                } else {
                    analyteInfo.setReportLimit(StringUtils.join(limits, "\\"));
                }
                analyteInfo.setSampleList(dataInfos.stream().sorted(Comparator.comparing(SampleTestDataInfo::getSampleSeq)).collect(Collectors.toList()));
                analyteInfos.add(analyteInfo);
            });
            dataInfo.setAnalyteList(analyteInfos.stream().sorted(Comparator.comparing(AnalyteInfo::getAnalyteSeq)).collect(Collectors.toList()));
            resultList.add(dataInfo);
        });
        return resultList;
    }

}
