/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdContactPersonInput implements Serializable{
    private String contactUsage;
    private String contactName;
    private String contactEmail;
    private String contactPhone;
    private String contactTelephone;
    private String contactMobile;
    private String contactFAX;
    private String regionAccount;
    private String responsibleTeamCode;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
