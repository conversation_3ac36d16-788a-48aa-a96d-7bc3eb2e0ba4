/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestItemMappingInput implements Serializable {
    private String testLineInstanceId;
    private List<RdAnalyteInput> analyteList;
    private RdTestItemMappingExternalInput external;


    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
