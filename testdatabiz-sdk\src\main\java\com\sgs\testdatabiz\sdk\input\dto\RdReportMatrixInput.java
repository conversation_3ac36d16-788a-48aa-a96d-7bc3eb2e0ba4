/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdReportMatrixInput implements Serializable {
    private String subReportNo;
    private String testMatrixId;
    private Integer testMatrixGroupId;
    private String testConditionGroupId;
    private List<RdConditionInput> conditionList;
    // add 230922
    private RdReportMatrixExternalInput external;
    private String testSampleInstanceId;
    private List<RdSpecimenInput> specimenList;
    private String testLineInstanceId;
//    private String analyteInstanceId;
    private List<RdPositionInput> positionList;
    private RdConclusionInput conclusion;
    private List<RdAttachmentInput> testMatrixFileList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
    //SCI-1378
    private String applicationFactor;
    private String metaData;
    private String labSectionName;
}
