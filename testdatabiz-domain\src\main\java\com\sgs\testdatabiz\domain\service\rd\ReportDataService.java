package com.sgs.testdatabiz.domain.service.rd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sgs.extsystem.facade.model.customer.req.CheckTestLineMappingReq;
import com.sgs.extsystem.facade.model.customer.req.MappingTestLineReq;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.preorder.facade.model.enums.DisplayDataEnums;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.testdatabiz.core.enums.CustomerUsage;
import com.sgs.testdatabiz.core.enums.ReportEntryModeEnum;
import com.sgs.testdatabiz.core.enums.TrfSourceType;
import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.core.errorcode.ErrorCode;
import com.sgs.testdatabiz.core.errorcode.ErrorCodeFactory;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.testdatabiz.core.exception.ReportDataCheckException;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.RdReportInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataAndMatrixDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.*;
import com.sgs.testdatabiz.dbstorages.mybatis.model.*;
import com.sgs.testdatabiz.domain.service.rd.assembler.ReportDataAssembler;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.domain.service.rd.filter.FilterContext;
import com.sgs.testdatabiz.domain.service.rd.filter.ReportDataFilterStrategyFactory;
import com.sgs.testdatabiz.domain.service.rd.manager.*;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.TableNameChecker;
import com.sgs.testdatabiz.domain.service.utils.convertor.ReportDataBuilder;
import com.sgs.testdatabiz.domain.service.utils.convertor.TestDataObjectRelBuilder;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.facade.model.enums.AttachmentObjectTypeEnum;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldInfo;
import com.sgs.testdatabiz.facade.model.req.rd.BatchExportReportDataReq;
import com.sgs.testdatabiz.facade.model.rsp.config.CheckTestLineMappingExistsDTO;
import com.sgs.testdatabiz.facade.model.testdata.TestDataResultLangInfo;
import com.sgs.testdatabiz.integration.FrameWorkClient;
import com.sgs.testdatabiz.integration.LocaliLayerClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 15:13
 */
@Slf4j
@Service
public class ReportDataService {

    private final RdReportMapper reportMapper;

    @Autowired
    private  RdReportInfoExtMapper reportInfoExtMapper;
    @Autowired
    private ReportDataAssembler reportDataAssembler;
    @Autowired
    private RdReportProductDffMapper reportProductDffMapper;
    @Autowired
    private RdReportExtMapper reportExtMapper;
    @Autowired
    private AttachmentManager attachmentManager;
    @Autowired
    private ReportDataManager reportDataManager;
    @Autowired
    private RdReportTrfRelMapper reportTrfRelMapper;
    @Autowired
    private ReportTrfRelManager reportTrfRelManager;
    @Autowired
    private ReportLangManager reportLangManager;

    @Autowired
    private TestDataObjectRelMapper objectRelMapper;
    @Autowired
    private ReportInvoiceManager reportInvoiceManager;
    @Autowired
    private QuotationManager quotationManager;
    @Autowired
    private TableNameChecker tableNameChecker;
    @Autowired
    private RdReportTestResultLangMapper testResultLangMapper;

    @Autowired
    private TestDataObjectRelExtMapper objectRelExtMapper;

    @Autowired
    private ReportProductDffManager reportProductDffManager;

    @Autowired
    private RdReportMatrixLangMapper reportMatrixLangMapper;

    @Autowired
    private ReportMatrixManager reportMatrixManager;

    @Autowired
    private ReportResultManager reportResultManager;

    @Autowired
    private ReportTrfRelManager trfRelManager;

    @Autowired
    private ReportDataBuilder reportDataBuilder;

    @Autowired
    private LocaliLayerClient localiLayerClient;

    @Autowired
    private TestDataObjectRelBuilder testDataObjectRelBuilder;

    @Resource
    private FrameWorkClient frameWorkClient;
    @Autowired
    private ReportDataFilterStrategyFactory filterStrategyFactory;

    private static final List<Integer> checkTestLineRefSystemIdList = new ArrayList<>();

    static {
        // 伊利
        checkTestLineRefSystemIdList.add(10016);
    }

    /**
     * ILayer定义 testResultType 配置为ReportConclusion 时，返回 2 ;
     */
    public static final String TEST_RESULT_TYPE_REPORT_CONCLUSION = "2";

    //    @Transactional(rollbackFor = Exception.class)
    public boolean importReportData(ReportDataBatchDO reportDataDO, String version) {
        if (reportDataDO == null) {
            log.error("[importReportData] reportDataDO is null");
            return false;
        }
        List<RdReportDO> reportList = reportDataDO.getReportList();
        if (Func.isEmpty(reportList)) {
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importReportData] reportList cannot empty!");
        }

        checkTelineMappingIfRefSystemIdInList(reportDataDO);

        if (Func.isEmpty(reportDataDO.getOrderList())) {
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importReportData] orderList cannot empty!");
        }
        Map<String, RdOrderDO> orderMap = new HashMap<>();
        for (RdOrderDO rdOrderDO : reportDataDO.getOrderList()) {
            orderMap.put(rdOrderDO.getSystemId() + rdOrderDO.getOrderNo(), rdOrderDO);
        }
        List<String> reportNos = reportList.stream().map(RdReportDO::getReportNo).distinct().collect(Collectors.toList());
        // 1.保存Report
        List<RdReportPO> rdReportPOs = reportDataBuilder.buildRdReportPO(reportDataDO, orderMap, getReportMap(reportNos, reportDataDO.getLabId()));
        if (Func.isEmpty(rdReportPOs)) {
            log.error("[importReportData] report cannot empty!");
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importReportData] report cannot empty!");
        }
        int saveReportResult = reportInfoExtMapper.batchReportInsert(rdReportPOs);
        if (saveReportResult < 1) {
            log.error("[importReportData] step1 save report failed.");
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importReportData] step1 save report failed.");
        }
        log.info("[importReportData] step1 save report success.");

        // 3.RdReportTrfRelPO
        List<RdTrfDO> trfList = reportDataDO.getTrfList();
        if (!CollectionUtils.isEmpty(trfList)) {
            List<RdReportTrfRelPO> trfRelPOList = reportDataBuilder.buildReportTrfRelPO(reportDataDO);
            boolean saveTrfRelResult = reportTrfRelManager.batchSave(trfRelPOList);
            if (!saveTrfRelResult) {
                log.error("[importReportData] step3 save ReportTrfRel failed.");
                throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importReportData] step3 save ReportTrfRel failed.");
            }
            log.info("[importReportData] step3 save ReportTrfRel success.");
        } else {
            log.info("[importReportData] step3 trfList is empty.");
        }

        // 4.保存原始request信息-按照执行系统和订单号组合处理
        List<RdReportExtPO> reportExtPOs = reportDataBuilder.buildReportExtPO(reportDataDO, version);
        boolean saveReportExtResult = reportExtMapper.batchInsert(reportExtPOs) > 0;
        if (!saveReportExtResult) {
            log.error("[importReportData] step4 save report ext failed.");
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importReportData] step4 save report ext failed.");
        }
        log.info("[importReportData] step4 save report ext success.");

        return true;
    }

    private void checkTelineMappingIfRefSystemIdInList(ReportDataBatchDO reportDataDO) {
        Integer refSystemId = 0;
        if (Func.isNotEmpty(reportDataDO.getTrfList())) {
            refSystemId = reportDataDO.getTrfList().get(0).getRefSystemId();
        }
        if (checkTestLineRefSystemIdList.contains(refSystemId)) {
            reportDataDO.getReportList().forEach(
                    v -> {
                        List<RdReportMatrixDO> reportMatrixList = v.getReportMatrixList();
                        if (Func.isEmpty(reportMatrixList)) {
                            return;
                        }
                        // 通过流处理报告矩阵列表，提取唯一的测试线实例ID集合
                        // 优化：提前计算和过滤，减少循环次数
                        Set<String> distinctTestLineInstanceIds = reportMatrixList.stream()
                                .map(RdReportMatrixDO::getTestLineInstanceId)
                                .collect(Collectors.toSet());

                        // 根据测试线实例ID过滤测试线列表，只保留存在于报告矩阵中的测试线
                        List<RdTestLineDO> filteredTestLines = reportDataDO.getTestLineList().stream()
                                .filter(line -> distinctTestLineInstanceIds.contains(line.getTestLineInstanceId()))
                                .collect(Collectors.toList());

                        // 检查测试线与测试数据对象的关系，过滤出符合条件的测试线，并统计特定测试结果类型的数量
                        List<CheckTestLineMappingExistsDTO> list = checkTestLinesMapping(filteredTestLines, reportDataDO.getBuCode(), reportDataDO.getTrfList().get(0).getRefSystemId())
                                .map(CustomResult::getData)
                                .orElseGet(ArrayList::new);
                        long count = list.stream().filter(l -> TEST_RESULT_TYPE_REPORT_CONCLUSION.equals(l.getTestResultType())).count();
                        // 校验标签审核计数否符合预期
                        validateLabelReviewCount(Math.toIntExact(count), filteredTestLines.size());
                        // SCI-436 End
                    }
            );
        }
    }

    public Optional<CustomResult<List<CheckTestLineMappingExistsDTO>>> checkTestLinesMapping(List<RdTestLineDO> line, String buCode, Integer refSystemId) {
        CheckTestLineMappingReq req = new CheckTestLineMappingReq();
        req.setRefSystemId(refSystemId);
        req.setProductLineCode(buCode);
        List<Integer> collect = line.stream().map(RdTestLineDO::getTestLineId).collect(Collectors.toList());
        List<MappingTestLineReq> reqList = new ArrayList<>();
        collect.forEach(
                id -> {
                    MappingTestLineReq mappingTestLineReq = new MappingTestLineReq();
                    mappingTestLineReq.setTestLineId(id);
                    reqList.add(mappingTestLineReq);
                }
        );
        req.setTestLines(reqList);
        try {
            CustomResult<List<CheckTestLineMappingExistsDTO>> listCustomResult = localiLayerClient.queryTestLineMappingExists(req);
            if (listCustomResult.isSuccess()) {
                return Optional.ofNullable(listCustomResult);
            }
            throw new BizException("Error querying test line mapping exists");
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    public void validateLabelReviewCount(int count, int totalLines) {
        if (count > 0 && count != totalLines) {
            throw new BizException("Label review test can only appear alone");
        }
    }

    public boolean updateNonReportInfo(ReportDataBatchDO reportDataDO, String version) {
        List<RdReportDO> reportList = reportDataDO.getReportList();
        List<RdOrderDO> orderList = reportDataDO.getOrderList();
        if (Func.isEmpty(orderList)) {
            return false;
        }

        RdOrderDO orderReq = orderList.get(0);
        Map<String, RdProductDO> productMap = new HashMap<>();
        List<RdProductDO> productList = Func.isEmpty(orderReq.getProductList()) ? new ArrayList<>() : orderReq.getProductList();
        productList.forEach(
                product -> productMap.put(product.getProductInstanceId(), product)
        );

        List<RdSampleDO> sampleList = Func.isEmpty(orderReq.getSampleList()) ? new ArrayList<>() : orderReq.getSampleList();

        Map<String, RdSampleDO> sampleMap = new HashMap<>();
        sampleList.forEach(
                sample -> sampleMap.put(sample.getTestSampleInstanceId(), sample)
        );
        reportList.forEach(
                report -> {
                    String reportNo = report.getReportNo();
                    ReportDataDO reportData = exportReportData(reportDataDO.getLabId(), reportNo);

                    RdOrderDO order = reportData.getOrder();
                    processProduct(order, productMap);
                    processSample(order, sampleMap);
                    reportData.getOrder().setProductList(productList);
                    reportData.getOrder().setSampleList(sampleList);
                    List<RdAttachmentDO> requestAttachment = orderReq.getAttachmentList();
                    reportData.getOrder().setAttachmentList(requestAttachment);
                    List<RdCustomerDO> newCustomerList = getCustomerList(orderReq, order, reportData, reportDataDO);
                    reportData.getOrder().setCustomerList(newCustomerList);
                    updateReportExt(reportData);

                }
        );

        return true;
    }

    private void processSample(RdOrderDO order, Map<String, RdSampleDO> sampleMap) {
        List<RdSampleDO> oldSampleList = order.getSampleList();
        oldSampleList.forEach(
                oldSample -> {
                    if (Func.isNotEmpty(sampleMap.get(oldSample.getTestSampleInstanceId()))) {
                        List<String> sampleCheckList = checkDffData(oldSample.getSampleAttrList(), sampleMap.get(oldSample.getTestSampleInstanceId()).getSampleAttrList());
                        if (Func.isNotEmpty(sampleCheckList)) {
                            throw new BizException(sampleCheckList.stream().collect(Collectors.joining(",")) + " cannot edit!");
                        }
                    }
                }
        );
    }

    private void processProduct(RdOrderDO order, Map<String, RdProductDO> productMap) {
        order.getProductList().forEach(
                product -> {
                    if (Func.isNotEmpty(productMap.get(product.getProductInstanceId()))) {
                        List<String> productCheckList = checkDffData(product.getProductAttrList(), productMap.get(product.getProductInstanceId()).getProductAttrList());
                        if (Func.isNotEmpty(productCheckList)) {
                            throw new BizException(productCheckList.stream().collect(Collectors.joining(",")) + " cannot edit!");
                        }
                    }
                }
        );
    }

    private void updateReportExt(ReportDataDO reportData) {
        List<RdReportExtPO> rdReportExtPOS = reportDataManager.getReportExtByReportId(reportData.getHeader().getId());
        if (Func.isNotEmpty(rdReportExtPOS)) {
            RdReportExtPO rdReportExtPO = rdReportExtPOS.stream().max(Comparator.comparingInt(RdReportExtPO::getUpdateVersion)).get();
            rdReportExtPO.setRequestJson(JSONObject.toJSONString(reportData));
            rdReportExtPO.setUpdateVersion(rdReportExtPO.getUpdateVersion() + 1);
            rdReportExtPO.setId(reportDataBuilder.getId());
            reportExtMapper.insert(rdReportExtPO);
        }
    }

    private static @NotNull List<RdCustomerDO> getCustomerList(RdOrderDO orderReq, RdOrderDO order, ReportDataDO reportData, ReportDataBatchDO reportDataDO) {
        List<RdCustomerDO> customerReq = orderReq.getCustomerList();
        List<RdCustomerDO> newCustomerList = new ArrayList<>();
        List<RdCustomerDO> oldCustomerList = order.getCustomerList();
        if (Func.isEmpty(oldCustomerList)) {
            oldCustomerList = new ArrayList<>();
        }
        if (Func.isNotEmpty(customerReq)) {
            List<RdCustomerDO> finalOldCustomerList = oldCustomerList;
            customerReq.forEach(
                    l -> {
                        if (Objects.equals(l.getCustomerUsage(), CustomerUsage.Supplier.getUsage()) || Objects.equals(l.getCustomerUsage(), CustomerUsage.Manufacture.getUsage())) {
                            newCustomerList.add(l);
                        }
                        if (Objects.equals(l.getCustomerUsage(), CustomerUsage.Buyer.getUsage())) {
                            RdCustomerDO rdCustomerDO = finalOldCustomerList.stream().filter(v -> Objects.equals(v.getCustomerUsage(), CustomerUsage.Buyer.getUsage())).findFirst().orElse(new RdCustomerDO());
                            rdCustomerDO.setMarketSegmentCode(l.getMarketSegmentCode());
                            rdCustomerDO.setMarketSegmentName(l.getMarketSegmentName());
                            newCustomerList.add(rdCustomerDO);
                        }
                    }
            );
        }
        String source = "";
        if (Func.isNotEmpty(reportData.getTrfList()) && Func.isNotBlank(reportData.getTrfList().get(0).getSource())) {
            source = reportData.getTrfList().get(0).getSource();
        } else if (Func.isNotEmpty(reportDataDO.getTrfList()) && Func.isNotBlank(reportDataDO.getTrfList().get(0).getSource())) {
            source = reportDataDO.getTrfList().get(0).getSource();
        }
        if (Func.isNotBlank(source) && Objects.equals(TrfSourceType.Order2TRF.getSourceType(), Func.toInt(source))) {
            if (Func.isNotEmpty(customerReq)) {
                RdCustomerDO rdCustomerDO = customerReq.stream().filter(custReq -> Objects.equals(custReq.getCustomerUsage(), CustomerUsage.Applicant.getUsage())).findFirst().orElse(null);
                if (Func.isNotEmpty(rdCustomerDO)) {
                    newCustomerList.add(rdCustomerDO);
                }
            }


            oldCustomerList.forEach(
                    old -> {
                        if (!Objects.equals(old.getCustomerUsage(), CustomerUsage.Applicant.getUsage()) && !Objects.equals(old.getCustomerUsage(), CustomerUsage.Buyer.getUsage())
                                && !Objects.equals(old.getCustomerUsage(), CustomerUsage.Supplier.getUsage())
                                && !Objects.equals(old.getCustomerUsage(), CustomerUsage.Manufacture.getUsage())) {
                            newCustomerList.add(old);
                        }
                    }
            );
        }
        return newCustomerList;
    }

    private List<String> checkDffData(List<RdProductSampleAttrDO> oldData, List<RdProductSampleAttrDO> newData) {
        List<String> sb = new ArrayList<>();
        if (oldData == null || newData == null) {
            return null;
        }
        Map<String, RdProductSampleAttrDO> map = new HashMap<>();
        oldData.stream().filter(l -> Objects.equals(l.getDisplayInReport(), DisplayDataEnums.Display_ONE.getCode().toString())).forEach(
                l -> map.put(l.getLabelCode(), l));

        for (RdProductSampleAttrDO newAttr : newData) {
            String labelCode = newAttr.getLabelCode();

            RdProductSampleAttrDO rdProductSampleAttrDO = map.get(labelCode);
            if (Func.isNotEmpty(rdProductSampleAttrDO)) {
                List<RdAttrLanguageDO> oldLanguageList = rdProductSampleAttrDO.getLanguageList();
                if (Func.isEmpty(oldLanguageList)) {
                    oldLanguageList = new ArrayList<>();
                }
                List<RdAttrLanguageDO> newLanguageList = newAttr.getLanguageList();
                if (Func.isEmpty(newLanguageList)) {
                    newLanguageList = new ArrayList<>();
                }
                oldLanguageList.stream().forEach(l -> l.setLastModifiedTimestamp(null));
                oldLanguageList.stream().forEach(l -> l.setActiveIndicator(null));
                newLanguageList.stream().forEach(l -> l.setLastModifiedTimestamp(null));
                newLanguageList.stream().forEach(l -> l.setActiveIndicator(null));
                boolean equals = new HashSet<>(newLanguageList).equals(new HashSet<>(oldLanguageList));
                if (!equals) {
                    sb.add(newAttr.getLabelName());
                }
            }

        }

        return sb;
    }


    private Map<String, RdReportPO> getReportMap(List<String> reportNos, Long labId) {
        RdReportExample example = new RdReportExample();
        //SCI-1475 delete cancel criteria
        example.createCriteria().andActiveIndicatorEqualTo(ActiveType.Enable.getStatus()).andLabIdEqualTo(labId).andReportNoIn(reportNos);
        List<RdReportPO> reportPOS = reportMapper.selectByExample(example);
        Map<String, RdReportPO> map = new HashMap<>();
        if (Func.isEmpty(reportPOS)) {
            return map;
        }

        reportPOS.forEach(
                l -> map.put(l.getReportNo(), l)
        );

        return map;
    }

    /**
     * 保存TestDataObjectRel数据
     *
     * @param reportDataDO
     * @return
     */
    public List<TestDataObjectRelPO> importTestDataObjectRelList(ReportDataBatchDO reportDataDO, Map<String, RdOrderDO> orderMap) {

        List<TestDataObjectRelPO> list = testDataObjectRelBuilder.buildTestDataObjectRelPO(reportDataDO, orderMap);
        if (Func.isNotEmpty(list)) {
            list.forEach(
                    l -> {
                        TestDataObjectRelPO reportObjectRelInfo = objectRelExtMapper.getReportObjectRelInfo(l);
                        if (Func.isNotEmpty(reportObjectRelInfo)) {
                            l.setId(reportObjectRelInfo.getId());
                        }
                    }
            );

            objectRelExtMapper.batchInsert(list);
            return list;
        }
        return new ArrayList<>();
    }

    public boolean existReportData(Long labId, String reportNo) {
        RdReportPO report = reportDataManager.getReport(labId, reportNo);
        if (report == null) {
            return false;
        }
        return !Objects.equals(ReportStatus.Cancelled.getCode(), report.getReportStatus());
    }
    public Map<String, Boolean> existReportData(ReportDataBatchDO reportDataDO, Long labId) {
        List<RdReportDO> reportList = reportDataDO.getReportList();
        Map<String, Boolean> resultMap = Maps.newHashMap();
        if (Func.isNotEmpty(reportList)) {
            for (RdReportDO l : reportList) {
                resultMap.put(l.getReportNo(), this.existReportData(labId, l.getReportNo()));
            }
        }
        return resultMap;
    }

    public boolean existReportTrfRel(Long labId, List<String> trfNoList) {
        if (Func.isNotEmpty(trfNoList)) {
            List<RdReportTrfRelPO> trfRelPOList = trfRelManager.getByTrfNoAndLabId(labId, trfNoList);
            if (Func.isEmpty(trfRelPOList)) {
                return false;
            } else {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取分包Matrix数据
     *
     * @return
     */
    public ReportDataDO getSubTestDataInfo(ReportDataDO dataDO) {
        String reportNo = dataDO.getHeader().getReportNo();
        String reportId = dataDO.getHeader().getReportId();
        Integer systemId = dataDO.getHeader().getSystemId();

        RdReportExample rdReportExample = new RdReportExample();
        RdReportExample.Criteria criteria = rdReportExample.createCriteria();
        criteria.andReportNoEqualTo(reportNo);
        if (Func.isNotBlank(reportId)) {
            criteria.andReportIdEqualTo(reportId);
        }
        if (Func.isNotEmpty(systemId)) {
            criteria.andSystemIdEqualTo(Func.toLong(systemId));
        }
        List<RdReportPO> reportPOS = reportMapper.selectByExample(rdReportExample);
        RdReportPO rdReportPO = CollUtil.get(reportPOS, 0);
        if (Func.isEmpty(rdReportPO)) {
            return dataDO;
        }

        List<LabInfo> labInfos = frameWorkClient.queryLabList(new HashMap<>());
        if (Func.isEmpty(labInfos)) {
            return dataDO;
        }

        LabInfo labInfo = labInfos.stream().filter(l -> Objects.equals(l.getLaboratoryID(), String.valueOf(rdReportPO.getLabId()))).findFirst().orElse(null);
        if (Func.isEmpty(labInfo)) {
            return dataDO;
        }

        String laboratoryCode = labInfo.getLaboratoryCode();
        if (Func.isEmpty(dataDO.getOrder())) {
            return dataDO;
        }
        return getSubTestDataInfo(dataDO.getOrder().getOrderNo(), laboratoryCode, dataDO);
    }

    public ReportDataDO getSubTestDataInfo(String orderNo, String labCode, ReportDataDO dataDO) {
        tableNameChecker.checkTableNameExist(labCode);
        String suffix = StringUtil.getTestDataSuffix(labCode);
        List<TestDataAndMatrixDTO> testDataAndMatrixDTOS = reportResultManager.queryTestDataInfoByOrderNo(suffix, orderNo);
        if (Func.isEmpty(testDataAndMatrixDTOS)) {
            return dataDO;
        }
        setSubData(dataDO, testDataAndMatrixDTOS);
        return dataDO;
    }



    private void setSubData(ReportDataDO dataDO, List<TestDataAndMatrixDTO> testDataAndMatrixDTOS) {
        List<RdTestLineDO> testLineList = dataDO.getTestLineList();
        dataDO.setTestLineList(testLineList);

        RdReportDO header = dataDO.getHeader();
        String mainReportTestMatrixMergeMode = header.getTestMatrixMergeMode();
        dataDO.setHeader(header);
        List<RdReportMatrixDO> reportMatrixList = header.getReportMatrixList();
        if (Func.isEmpty(reportMatrixList)) {
            reportMatrixList = new ArrayList<>();
        }
        header.setReportMatrixList(reportMatrixList);

        List<RdTestResultDO> testResultList = dataDO.getTestResultList();
        if (Func.isEmpty(testResultList)) {
            testResultList = new ArrayList<>();
        }
        dataDO.setTestResultList(testResultList);


        for (TestDataAndMatrixDTO dto : testDataAndMatrixDTOS) {
            String parentOrderNo = dto.getParentOrderNo();
            String objectNo = dto.getObjectNo();
            String testMatrixId = dto.getTestMatrixId();
            String testDataMatrixId = dto.getTestDataMatrixId();
            Integer ppVersionId = dto.getPpVersionId();
            Integer aid = dto.getAid();
            String externalCode = dto.getExternalCode();
            Integer testLineId = dto.getTestLineId();
            String evaluationAlias = dto.getEvaluationAlias();
            Integer citationId = dto.getCitationId();
            Integer citationVersionId = dto.getCitationVersionId();
            Integer citationType = dto.getCitationType();
            String citationName = dto.getCitationName();
            Integer testLineSeq = dto.getTestLineSeq();
            String matrixLanuges = dto.getMatrixLanuges();
            String methodDesc = dto.getMethodDesc();
            String sampleId = dto.getSampleId();
            String sampleNo = dto.getSampleNo();
            String externalSampleNo = dto.getExternalSampleNo();
            Integer sampleSeq = dto.getSampleSeq();
            Integer matrixConclusionId = dto.getMatrixConclusionId();
            Integer analyteId = dto.getAnalyteId();
            if (Func.isEmpty(analyteId)) {
                continue;
            }
            String analyteName = dto.getAnalyteName();
            Integer analyteConclusionId = dto.getAnalyteConclusionId();
            String position = dto.getPosition();
            String analyteCode = dto.getAnalyteCode();
            Integer analyteType = dto.getAnalyteType();
            Integer analyteSeq = dto.getAnalyteSeq();
            String reportUnit = dto.getReportUnit();
            String testValue = dto.getTestValue();
            String casNo = dto.getCasNo();
            String reportLimit = dto.getReportLimit();
            String limitUnit = dto.getLimitUnit();
            Integer testDataConclusionId = dto.getTestDataConclusionId();
            Integer sourceType = dto.getSourceType();
            String testDataLanguages = dto.getTestDataLanguages();
            String testSampleInstanceId = dto.getTestSampleInstanceId();
            String testLineInstanceId = dto.getTestLineInstanceId();
            String testResultInstanceId = dto.getTestResultInstanceId();
            String orderNoNo = dto.getOrderNo();
            String methodLimit = dto.getMethodLimit();
            String matrixExtFields = dto.getMatrixExtFields();

            RdTestResultDO testResultDO = new RdTestResultDO();
            testResultDO.setOrderNo(orderNoNo);
//            testResultDO.setSystemId();
            RdTestResultResultDO testResultResultDO = new RdTestResultResultDO();
            testResultResultDO.setTestResultFullName(analyteName);
            testResultResultDO.setAnalyteConclusionId(analyteConclusionId);
            if (Func.isNotEmpty(analyteId)) {
                RdTestResultNameDO testResultNameDO = new RdTestResultNameDO();
                testResultNameDO.setAnalyteInstanceId(analyteId.toString());
                testResultResultDO.setTestResultFullNameRel(testResultNameDO);
            }
            setTestResultLanguageList(testDataLanguages, testResultDO);

            testResultResultDO.setResultValue(testValue);
            testResultResultDO.setResultUnit(reportUnit);
            testResultDO.setTestResult(testResultResultDO);
            //SCI-1371 增加testResultInstanceId的取值逻辑
            testResultDO.setTestResultInstanceId(testResultInstanceId);
            testResultDO.setTestResultSeq(analyteSeq);
            testResultDO.setTestMatrixId(testMatrixId);
            RdReportLimitDO reportLimitDO = new RdReportLimitDO();
            reportLimitDO.setLimitValueFullName(reportLimit);
            reportLimitDO.setLimitUnit(limitUnit);
            testResultDO.setReportLimit(reportLimitDO);
            //SCI-1378
            testResultDO.setCasNo(casNo);
            setMethodLimitAndShareDataRefer(methodLimit, limitUnit, testResultDO, matrixExtFields);

            if (Func.isEmpty(testMatrixId)) {
                // 通用逻辑
                String testMatrixId1 = getTestMatrixId(
                        getTestLineInstanceIde(ppVersionId, testLineId, citationVersionId, citationType, testLineList),
                        sampleId, reportMatrixList
                );

                if (Func.isNotEmpty(mainReportTestMatrixMergeMode) && !Objects.equals(ReportEntryModeEnum.Host.getName(), mainReportTestMatrixMergeMode)) {
                    // Execute时只取SubReport.TestMatrixMergeMode  = Merge；
                    if (Objects.equals(ReportEntryModeEnum.Execute.getName(), mainReportTestMatrixMergeMode)) {
                        reportMatrixList = new ArrayList<>();
                        header.setReportMatrixList(reportMatrixList);
                        testMatrixId1 = getTestMatrixId(
                                getTestLineInstanceIde(ppVersionId, testLineId, citationVersionId, citationType, testLineList),
                                sampleId, reportMatrixList
                        );
                    }
                    if (Func.isBlank(testMatrixId1) && Func.isNotBlank(testDataMatrixId)) {
                        RdReportMatrixDO matrixDO = new RdReportMatrixDO();
                        matrixDO.setTestMatrixId(testDataMatrixId);
                        matrixDO.setTestSampleInstanceId(testSampleInstanceId);
                        matrixDO.setTestLineInstanceId(testLineInstanceId);
                        RdConclusionDO conclusionDO = new RdConclusionDO();
                        conclusionDO.setConclusionCode(Func.toStr(matrixConclusionId, ""));
                        matrixDO.setConclusion(conclusionDO);
                        matrixDO.setMatrixInstanceId(testDataMatrixId);
                        reportMatrixList.add(matrixDO);
                    }
                }
                // FAST特殊渠道
                if (Objects.equals(sourceType, SourceTypeEnum.FAST.getCode())) {
                    testMatrixId1 = getTestMatrixId(
                            getFastTestLineInstanceIde(testLineId, testLineList),
                            sampleId, reportMatrixList
                    );
                }
                if (Func.isNotBlank(testMatrixId1)) {
                    testResultDO.setTestMatrixId(
                            testMatrixId1
                    );
                }
            }
            if (Func.isNotEmpty(testResultDO.getTestMatrixId())) {
                testResultList.add(testResultDO);
            }
        }
    }

    private static void setMethodLimitAndShareDataRefer(String methodLimit, String limitUnit, RdTestResultDO testResultDO, String matrixExtFields) {
        RdMethodLimitDO methodLimitDO = new RdMethodLimitDO();
        methodLimitDO.setLimitValueFullName(methodLimit);
        methodLimitDO.setLimitUnit(limitUnit);
        testResultDO.setMethodLimit(methodLimitDO);
        if(Func.isNotEmpty(matrixExtFields)){
            RdShareDataReferDO rdShareDataReferDO = new RdShareDataReferDO();
            TestDataMatrixExtFieldInfo extField = JSONObject.parseObject(matrixExtFields, TestDataMatrixExtFieldInfo.class);
            rdShareDataReferDO.setReferFromReportNo(extField.getReferFromReportNo());
            rdShareDataReferDO.setReferFromSampleNo(extField.getReferFromSampleNo());
            RdShareDataReferDO rdShareDataRefer = testResultDO.getShareDataRefer();
            if (Func.isEmpty(rdShareDataRefer)) {
                testResultDO.setShareDataRefer(rdShareDataReferDO);
            }
        }
    }

    private static void setTestResultLanguageList(String testDataLanguages, RdTestResultDO testResultDO) {
        if(Func.isNotEmpty(testDataLanguages)){
            List<RdTestResultLanguageDO> testResultLanguageList = new ArrayList<>();
            List<TestDataResultLangInfo> testDataLanguageList = JSONObject.parseArray(testDataLanguages, TestDataResultLangInfo.class);
            if(!CollectionUtils.isEmpty(testDataLanguageList)){
                testDataLanguageList.forEach(lan -> {
                    RdTestResultLanguageDO testResultLanguageDO = new RdTestResultLanguageDO();
                    testResultLanguageDO.setLanguageId(lan.getLanguageId());
                    testResultLanguageDO.setTestAnalyteName(lan.getTestAnalyteName());
                    testResultLanguageDO.setTestResultFullName(lan.getTestAnalyteName());
                    testResultLanguageDO.setLimitValueFullName(lan.getLimitUnit());
                    testResultLanguageDO.setReportValueLimitFullName(lan.getReportUnit());
                    testResultLanguageList.add(testResultLanguageDO);
                });
            }
            testResultDO.setLanguageList(testResultLanguageList);
        }
    }

    private List<TestDataAndMatrixDTO> filter(List<RdSubReportDO> subReportList, List<TestDataAndMatrixDTO> testDataAndMatrixDTOS) {
        if (Func.isEmpty(subReportList) || Func.isEmpty(testDataAndMatrixDTOS)) {
            return testDataAndMatrixDTOS;
        }
        List<String> collect = subReportList.stream().map(l -> l.getObjectNo() + l.getSubReportNo()).distinct().collect(Collectors.toList());
        return testDataAndMatrixDTOS.stream().filter(l -> collect.contains(l.getObjectNo() + l.getExternalNo())).collect(Collectors.toList());
    }

    private String getTestMatrixId(String testLineInstanceId, String testSampleInstanceId, List<RdReportMatrixDO> matrixDOList) {
        if (Func.isEmpty(matrixDOList)) {
            return null;
        }
        RdReportMatrixDO rdReportMatrixDO = matrixDOList.stream().filter(l -> Objects.equals(testLineInstanceId, l.getTestLineInstanceId()) && Objects.equals(testSampleInstanceId, l.getTestSampleInstanceId())).findFirst().orElse(null);
        if (Func.isNotEmpty(rdReportMatrixDO)) {
            return rdReportMatrixDO.getTestMatrixId();
        }
        return null;
    }

    private String getTestLineInstanceIde(Integer ppVersionId, Integer testLineId, Integer citationVersionId, Integer citationType, List<RdTestLineDO> testLineList) {
        if (Func.isEmpty(testLineId) || Func.isEmpty(citationVersionId) || Func.isEmpty(citationType) || Func.isEmpty(testLineList)) {
            return null;
        }
        testLineList = testLineList.stream().filter(l -> Objects.equals(l.getTestLineId(), testLineId)).collect(Collectors.toList());
        if (Func.isEmpty(testLineList)) {
            return null;
        }

        for (RdTestLineDO testLine : testLineList) {
            RdCitationDO citation = testLine.getCitation();
            if (Func.isEmpty(citation)) {
                return null;
            }
            Integer versionId = citation.getCitationVersionId();
            Integer type = citation.getCitationType();
            if (Objects.equals(versionId, citationVersionId) && Objects.equals(type, citationType)) {
                if (Func.isEmpty(ppVersionId)) {
                    return testLine.getTestLineInstanceId();
                }
                List<RdPpTestLineRelDO> ppTestLineRelList = testLine.getPpTestLineRelList();
                if (Func.isEmpty(ppTestLineRelList)) {
                    return null;
                }
                List<RdPpTestLineRelDO> collect = ppTestLineRelList.stream().filter(l -> Objects.equals(l.getPpVersionId(), ppVersionId)).collect(Collectors.toList());
                if (Func.isNotEmpty(collect)) {
                    return testLine.getTestLineInstanceId();
                }
            } else {
                return null;
            }

        }
        return null;

    }

    private String getFastTestLineInstanceIde(Integer testLineId, List<RdTestLineDO> testLineList) {
        if (Func.isEmpty(testLineId)) {
            return null;
        }
        testLineList = testLineList.stream().filter(l -> Objects.equals(l.getTestLineId(), testLineId)).collect(Collectors.toList());
        if (Func.isEmpty(testLineList)) {
            return null;
        }

        return testLineList.get(0).getTestLineInstanceId();

    }

    /**
     * 查询Report Data
     *
     * @param labId
     * @param reportNo
     * @return
     */
    public ReportDataDO exportReportData(Long labId, String reportNo, String labCode) {
        List<RdReportPO> rdReportPOS = reportDataManager.getByReportNoAndLabId(labId, reportNo);
        if (Func.isNotEmpty(rdReportPOS)) {
            RdReportPO rdReportPO = rdReportPOS.get(0);
            Long reportPOId = rdReportPO.getId();
            ReportDataDO dataDO = reportDataManager.selectByReportId(reportPOId);
            if (Func.isEmpty(dataDO)) {
                return null;
            }
            String orderNo = dataDO.getOrder().getOrderNo();
            return getSubTestDataInfo(orderNo, labCode, dataDO);


            // 如下注释代码暂存，因目前数据都是从log表出去，后续此处需要重新恢复从各个结构数据查询出去
//
//            List<RdReportLangPO> reportLang = reportLangManager.getByReportId(reportPOId);
//            List<RdReportProductDffPO> productDffList = reportProductDffManager.getProductDffByReportNo(reportNo);
//            List<RdAttachmentPO> attachmentList = attachmentManager.selectByReportNo(reportNo, rdReportPO.getSystemId(), AttachmentObjectTypeEnum.REPORT.getCode());
//            List<RdReportTrfRelPO> rdReportTrfRelPOS = trfRelManager.selectByReportNoAndLabId(rdReportPO.getReportNo(), rdReportPO.getLabId());
//            List<TestDataObjectRelPO> objectRelPOs = objectRelExtMapper.getReportObjectRelByReportNoAndLabId(rdReportPO.getReportNo(), rdReportPO.getLabId());
//
//            ReportDataDO reportDataDO = reportDataAssembler.assemblerResultReportDataDO(rdReportPO,
//                    reportLang,
//                    productDffList,
//                    attachmentList,
//                    rdReportTrfRelPOS,
//                    objectRelPOs
//            );
//
//
//            String suffix = StringUtil.getTestDataSuffix(labCode);
//
//
//            List<RdReportTestResultLangPO> testResultLangList = new ArrayList<>();
//            List<RdReportMatrixLangPO> reportMatrixLangList = new ArrayList<>();
//            List<TestSamplePO> testSampleList = new ArrayList<>();
//            List<TestSampleGroupPO> testSampleGroupList = new ArrayList<>();
//            List<TestDataInfoPO> testDataList = reportResultManager.getTestDataList(suffix, reportPOId);
//            if (Func.isNotEmpty(testDataList)) {
//                List<Long> testDataIds = testDataList.stream().map(TestDataInfoPO::getId).collect(Collectors.toList());
//                testResultLangList = reportResultManager.getTestResultLangList(testDataIds);
//            }
//            List<TestDataObjectRelPO> testDataObjectRelList = getTestDataObjectRelList(labId, reportNo, null);
//            List<TestDataMatrixInfoPO> testMatrixList = reportMatrixManager.getTestMatrixList(suffix, reportPOId);
//            if (Func.isNotEmpty(testMatrixList)) {
//                List<Long> matrixIds = testMatrixList.stream().map(TestDataMatrixInfoPO::getId).collect(Collectors.toList());
//                reportMatrixLangList = reportMatrixManager.getReportMatrixLangList(matrixIds);
//                // sample和sampleGroup
//                testSampleList = reportMatrixManager.getTestSampleList(matrixIds);
//                if (Func.isNotEmpty(testSampleList)) {
//                    List<String> sampleIds = testSampleList.stream().map(TestSamplePO::getSampleInstanceId).collect(Collectors.toList());
//                    testSampleGroupList = reportMatrixManager.getTestSampleGroupList(sampleIds);
////                    if (Func.isNotEmpty(testSampleGroupList)) {
////                        Map<String, List<TestSampleGroupPO>> sampleGroupMap = testSampleGroupList.stream().collect(Collectors.groupingBy(TestSampleGroupPO::getSampleId));
////                    }
//                }
//
//            }
//            return ReportDataBuilder.buildReportDataDO(reportDataDO, testDataList, testMatrixList, testDataObjectRelList, reportMatrixLangList, testResultLangList, testSampleList, testSampleGroupList);
        }
        return null;
    }

    public ReportDataDO exportReportData(Long labId, String reportNo) {
        List<RdReportPO> rdReportPOS = reportDataManager.getByReportNoAndLabId(labId, reportNo);
        if (Func.isNotEmpty(rdReportPOS)) {
            RdReportPO rdReportPO = rdReportPOS.get(0);
            Long reportPOId = rdReportPO.getId();
            ReportDataDO dataDO = reportDataManager.selectByReportId(reportPOId);
            return dataDO;
        }
        return new ReportDataDO();
    }

    public String getReportRequestJsonByReportNo(String reportNo) {
        RdReportPO rdReportPO = reportDataManager.getReportByReportNo(reportNo);
        if (Func.isNotEmpty(rdReportPO)) {
            Long reportPOId = rdReportPO.getId();
            List<RdReportExtPO> rdReportExtPOS = reportDataManager.getReportExtByReportId(reportPOId);
            if (Func.isNotEmpty(rdReportExtPOS)) {
                RdReportExtPO rdReportExtPO = rdReportExtPOS.stream().max(Comparator.comparingInt(RdReportExtPO::getUpdateVersion)).get();
                if (Func.isNotEmpty(rdReportExtPO)) {
                    return rdReportExtPO.getRequestJson();
                }
            }
        }
        return null;
    }

    /**
     * 获取TestDataObjectRel数据
     *
     * @param labId
     * @param reportNo
     * @param orderNo
     * @return
     */
    public List<TestDataObjectRelPO> getTestDataObjectRelList(Long labId, String reportNo, String orderNo) {
        TestDataObjectRelExample objectRelExample = new TestDataObjectRelExample();
        if (Func.isBlank(orderNo)) {
            objectRelExample.createCriteria().andLabIdEqualTo(labId).andReportNoEqualTo(reportNo).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        } else {
            objectRelExample.createCriteria().andLabIdEqualTo(labId).andOrderNoEqualTo(orderNo).andReportNoEqualTo(reportNo).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        }
        return objectRelMapper.selectByExample(objectRelExample);
    }


    /**
     * 查询reportData数据
     *
     * @param labId
     * @param trfNo
     * @return
     */
    public List<ReportDataDO> exportReportDataByTrfNo(Long labId, String trfNo, String labCode) {
        String suffix = StringUtil.getTestDataSuffix(labCode);
        List<RdReportPO> rdReportPOS = reportDataManager.getByTrfNoAndLabId(labId, trfNo);
        List<ReportDataDO> list = new ArrayList<>();
        if (Func.isNotEmpty(rdReportPOS)) {
            rdReportPOS.forEach(
                    rdReportPO -> {

                        String reportNo = rdReportPO.getReportNo();
                        Long reportPOId = rdReportPO.getId();

                        ReportDataDO dataDO = reportDataManager.selectByReportId(reportPOId);
                        String orderNo = dataDO.getOrder().getOrderNo();
                        dataDO = getSubTestDataInfo(orderNo, labCode, dataDO);

//
//                        List<RdReportLangPO> reportLang = reportLangManager.getByReportId(reportPOId);
//                        List<RdReportProductDffPO> productDffList = reportProductDffManager.getProductDffByReportNo(reportNo);
//                        List<RdAttachmentPO> attachmentList = attachmentManager.selectByReportNo(reportNo, rdReportPO.getSystemId(), AttachmentObjectTypeEnum.REPORT.getCode());
//                        List<RdReportTrfRelPO> rdReportTrfRelPOS = trfRelManager.selectByReportNoAndLabId(rdReportPO.getReportNo(), rdReportPO.getLabId());
//                        List<TestDataObjectRelPO> objectRelPOs = objectRelExtMapper.getReportObjectRelByReportNoAndLabId(rdReportPO.getReportNo(), rdReportPO.getLabId());
//
//                        ReportDataDO reportDataDO = reportDataAssembler.assemblerResultReportDataDO(rdReportPO,
//                                reportLang,
//                                productDffList,
//                                attachmentList,
//                                rdReportTrfRelPOS,
//                                objectRelPOs);
//
//                        List<RdReportTestResultLangPO> testResultLangList = new ArrayList<>();
//                        List<RdReportMatrixLangPO> reportMatrixLangList = new ArrayList<>();
//                        List<TestSamplePO> testSampleList = new ArrayList<>();
//                        List<TestSampleGroupPO> testSampleGroupList = new ArrayList<>();
//
//                        List<TestDataInfoPO> testDataList = reportResultManager.getTestDataList(suffix, reportPOId);
//                        if (Func.isNotEmpty(testDataList)) {
//                            List<Long> testDataIds = testDataList.stream().map(TestDataInfoPO::getId).collect(Collectors.toList());
//                            testResultLangList = reportResultManager.getTestResultLangList(testDataIds);
//                        }
//                        List<TestDataObjectRelPO> testDataObjectRelList = getTestDataObjectRelList(labId, reportNo, null);
//                        List<TestDataMatrixInfoPO> testMatrixList = reportMatrixManager.getTestMatrixList(suffix, reportPOId);
//                        if (Func.isNotEmpty(testMatrixList)) {
//                            List<Long> matrixIds = testMatrixList.stream().map(TestDataMatrixInfoPO::getId).collect(Collectors.toList());
//                            reportMatrixLangList = reportMatrixManager.getReportMatrixLangList(matrixIds);
//                            testSampleList = reportMatrixManager.getTestSampleList(matrixIds);
//                            if (Func.isNotEmpty(testSampleList)) {
//                                List<String> sampleIds = testSampleList.stream().map(TestSamplePO::getSampleInstanceId).collect(Collectors.toList());
//                                testSampleGroupList = reportMatrixManager.getTestSampleGroupList(sampleIds);
//                            }
//                        }
//                        ReportDataDO dataDO = ReportDataBuilder.buildReportDataDO(reportDataDO, testDataList, testMatrixList, testDataObjectRelList, reportMatrixLangList, testResultLangList, testSampleList, testSampleGroupList);
                        list.add(dataDO);
                    }
            );
        }

        return list;
    }

    /**
     * 批量查询Report Data
     *
     * @param labId
     * @param reportNos
     * @return
     */
    public List<ReportDataDO> exportReportDataList(Long labId, List<String> reportNos, String labCode) {
//        List<RdReportPO> rdReportPOS = reportDataManager.getReportList(labId, reportNos);
        if (Func.isNotEmpty(reportNos)) {
            List<ReportDataDO> result = new ArrayList<>();
            reportNos.forEach(
                    l -> {
                        ReportDataDO reportDataDO = this.exportReportData(labId, l, labCode);
                        if (Func.isNotEmpty(reportDataDO)) {
                            result.add(reportDataDO);
                        }
                    }
            );
            return result;
        }
        return new ArrayList<>();
    }

    /**
     * 批量查询Report Data
     *
     * @param labId
     * @param reportNos
     * @return
     */
    public List<RdReportPO> exportReportHeaderList(Long labId, List<String> reportNos, String labCode) {
        if (Func.isNotEmpty(reportNos)) {
            return reportDataManager.getEnableReportList(labId, reportNos);
        }
        return new ArrayList<>();
    }


    public List<ReportDataDO> exportReportDataListAllStatus(Long labId, List<String> reportNos, String labCode) {
        if (Func.isNotEmpty(reportNos) && Func.isNotEmpty(labId)) {
            List<ReportDataDO> result = new ArrayList<>();
            reportNos.forEach(
                    l -> {
                        List<RdReportPO> rdReportPOS = reportDataManager.getByReportNoAndLabIdAllStatus(labId, l);
                        if (Func.isNotEmpty(rdReportPOS)) {
                            RdReportPO rdReportPO = rdReportPOS.stream().filter(v -> !Objects.equals(ReportStatus.Cancelled.getCode(), v.getReportStatus())).findFirst().orElse(rdReportPOS.get(0));
                            Long reportPOId = rdReportPO.getId();
                            ReportDataDO dataDO = reportDataManager.selectByReportId(reportPOId);
                            String orderNo = dataDO.getOrder().getOrderNo();
                            dataDO = getSubTestDataInfo(orderNo, labCode, dataDO);
                            result.add(dataDO);
                        }
                    }
            );
            return result;
        }
        return new ArrayList<>();
    }

    /**
     * 导入quotation
     *
     * @param quotationList
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    public boolean importQuotationList(List<RdQuotationDO> quotationList) {
        if (Func.isEmpty(quotationList)) {
            log.error("[importQuotation] quotationList is null");
            return false;
        }
        Long systemId = quotationList.get(0).getSystemId();
        Long labId = quotationList.get(0).getLabId();
        String reportNo = quotationList.get(0).getReportNo();
        String orderNo = quotationList.get(0).getOrderNo();

        // 清空数据
        quotationManager.deleteQuotationData(reportNo, systemId);

        RdReportPO report = reportDataManager.getReportNotByReportStatus(labId, reportNo);
        if (Func.isNotEmpty(report)) {
            Long id = report.getId();
            boolean updateResult = reportDataManager.updateQuotationListByReportId(id, quotationList);
            if (!updateResult) {
                log.error("[importQuotation] quotation update log table failed.  labId:{} ,reportNo:{}", labId, reportNo);
                throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importQuotation] quotation update log table failed.");
            }
        }

        QuotationBean quotationBean = reportDataBuilder.buildQuotationPOList(labId, systemId, reportNo, orderNo, quotationList);
        if (Func.isEmpty(quotationBean)) {
            log.error("[importQuotation] quotation build failed.  labId:{} ,reportNo:{}", labId, reportNo);
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importQuotation] quotation build failed.");
        }
        List<RdQuotationPO> quotationPOList = quotationBean.getQuotationPOList();
        boolean saveQuotationResult = quotationManager.batchInsert(quotationPOList);
        if (!saveQuotationResult) {
            log.error("[importQuotation] batchInsert quotation failed.  labId:{} ,reportNo:{}", labId, reportNo);
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importQuotation] batchInsert quotation failed.");
        }

        return true;
    }

    /**
     * 查询quotation
     *
     * @param labId
     * @return
     */
    public List<RdQuotationDO> exportQuotation(Long labId, String reportNo, String
            orderNo, List<String> quotationNos) {
        Assert.notNull(labId, "labId cannot null");
        List<RdQuotationPO> quotationPOList = quotationManager.selectByReportNo(labId, reportNo, orderNo, quotationNos);
        return reportDataAssembler.assemblerQuotationDO(quotationPOList);
    }

    /**
     * 查询quotation
     *
     * @param labId
     * @return
     */
    public List<RdQuotationDO> exportQuotation(Long labId, String reportNo) {
        Assert.notNull(labId, "labId cannot null");
        Assert.notNull(reportNo, "reportNo cannot null");
        List<RdQuotationPO> quotationPOList = quotationManager.selectByReportNo(labId, reportNo, null, null);
        return reportDataAssembler.assemblerQuotationDO(quotationPOList);
    }

    /**
     * 查询quotation
     *
     * @param labId
     * @return
     */
    public List<RdQuotationDO> exportQuotation(Long labId, List<String> reportNos) {
        Assert.notNull(labId, "labId cannot null");
        Assert.notNull(reportNos, "reportNos cannot null");
        List<RdQuotationPO> quotationPOList = quotationManager.selectByReportNo(labId, reportNos);
        return reportDataAssembler.assemblerQuotationDO(quotationPOList);
    }

    /**
     * 导入invoice
     *
     * @param invoiceList
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    public boolean importInvoiceList(List<RdInvoiceDO> invoiceList, RdOrderDTO orderDTO) {
        if (Func.isEmpty(invoiceList)) {
            log.error("[importInvoiceList] invoiceList is null");
            return false;
        }
        Long systemId = invoiceList.get(0).getSystemId();
        Long labId = invoiceList.get(0).getLabId();
        String reportNo = invoiceList.get(0).getReportNo();
        String orderNo = invoiceList.get(0).getOrderNo();
        // 清空数据
        reportInvoiceManager.deleteInvoiceData(systemId, reportNo);

        List<List<String>> quotationNosList = invoiceList.stream().map(RdInvoiceDO::getQuotationNos).collect(Collectors.toList());
        List<String> quotationNos = new ArrayList<>();
        if (Func.isNotEmpty(quotationNosList)) {
            quotationNosList.forEach(quotationNos::addAll);
        }

        // 校验quotation必须已经存在
        if (!quotationManager.existQuotationNos(quotationNos, systemId)) {
            log.error("[importInvoiceList] quotation not exist.  labId:{} ,reportNo:{}", labId, reportNo);
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "[importInvoiceList] quotation not exist.");
        }


        // 插入invoice
        RdReportPO report = reportDataManager.getReportNotByReportStatus(labId, reportNo);
       if (Func.isEmpty(report)) {
           log.error("[importInvoiceList] data Exception rd_report_id is null.  labId:{} ,reportNo:{}", labId, reportNo);
           ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.REPORT_DATA_ERROR, ErrorBizModelEnum.REPORT_DATA_BIZ, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REPORT_NOT_EXIST);
           throw new ReportDataCheckException(errorCode,ResponseCode.INTERNAL_SERVER_ERROR.getCode(),"report not exist");
       }
        List<RdReportInvoicePO> reportInvoicePOList = reportDataBuilder.buildRdReportInvoicePOList(report.getId(), orderNo, labId, reportNo, invoiceList, systemId);
        boolean saveReportInvoiceResult = reportInvoiceManager.batchInsert(reportInvoicePOList);
        if (!saveReportInvoiceResult) {
            log.error("[importInvoiceList] batchInsert report invoice failed.  labId:{} ,reportNo:{}", labId, reportNo);
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.REPORT_DATA_ERROR, ErrorBizModelEnum.REPORT_DATA_BIZ, ErrorFunctionTypeEnum.SAVE, ErrorTypeEnum.SAVE_REPORT_INVOICE_FAILED);
            throw new ReportDataCheckException(errorCode,ResponseCode.INTERNAL_SERVER_ERROR.getCode(),"batchInsert report invoice failed.");
        }

        if (Func.isNotEmpty(report)) {
            Long id = report.getId();
            boolean updateResult = reportDataManager.updateInvoiceListByReportId(id, invoiceList, orderDTO);
            if (!updateResult) {
                log.error("[importInvoiceList] invoice updateInvoiceListByReportId.  labId:{} ,reportNo:{}", labId, reportNo);
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.REPORT_DATA_ERROR, ErrorBizModelEnum.REPORT_DATA_BIZ, ErrorFunctionTypeEnum.SAVE, ErrorTypeEnum.SAVE_REPORT_QUOTATION_FAILED);
                throw new ReportDataCheckException(errorCode,ResponseCode.INTERNAL_SERVER_ERROR.getCode(),"update invoice to reportExt failed.");
            }
        }

        // 插入quotation invoice rel
        List<RdQuotationInvoiceRelPO> quotationInvoiceRelPOList = reportDataBuilder.buildQuotationInvoiceRelPOList(invoiceList);
        if (Func.isNotEmpty(quotationInvoiceRelPOList)) {
            boolean saveQuotationInvoiceResult = reportInvoiceManager.batchInsertQuotationInvoiceRel(quotationInvoiceRelPOList);
            if (!saveQuotationInvoiceResult) {
                log.error("[importInvoiceList] batchInsert quotation invoice rel failed.  labId:{} ,reportNo:{}", labId, reportNo);
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.REPORT_DATA_ERROR, ErrorBizModelEnum.REPORT_DATA_BIZ, ErrorFunctionTypeEnum.SAVE, ErrorTypeEnum.SAVE_REPORT_INVOICE_FAILED);
                throw new ReportDataCheckException(errorCode,ResponseCode.INTERNAL_SERVER_ERROR.getCode(),"batchInsert quotation invoice rel failed.");
            }
        }
        return true;
    }

    /**
     * 查询invoice
     *
     * @param labId
     * @param reportNo
     * @return
     */
    public List<RdInvoiceDO> exportInvoiceList(Long labId, String reportNo) {
        List<RdReportInvoicePO> reportInvoicePOList = reportInvoiceManager.selectReportInvoice(reportNo, labId);
        Map<String, List<RdAttachmentPO>> reportFileMap = new HashMap<>();
        if (Func.isNotEmpty(reportInvoicePOList)) {
            List<String> invoiceNos = reportInvoicePOList.stream().map(RdReportInvoicePO::getInvoiceNo).collect(Collectors.toList());

            List<RdAttachmentPO> invoiceFiles = attachmentManager.getByObjectNosAndType(invoiceNos, AttachmentObjectTypeEnum.INVOICE.getCode());
            if (Func.isNotEmpty(invoiceFiles)) {
                reportFileMap = invoiceFiles.stream().collect(Collectors.groupingBy(RdAttachmentPO::getObjectNo));
            }
        }

        return reportDataAssembler.assemblerReportInvoiceDO(reportInvoicePOList, reportFileMap);
    }

    /**
     * 查询invoice
     *
     * @param labId
     * @return
     */
    public List<RdInvoiceDO> exportInvoiceList(Long labId, List<String> invoiceNos) {
        Assert.notNull(labId, "labId cannot null");
        Assert.notNull(invoiceNos, "invoiceNos cannot null");
        List<RdReportInvoicePO> reportInvoicePOList = reportInvoiceManager.selectReportInvoice(labId, invoiceNos);
        Map<String, List<RdAttachmentPO>> reportFileMap = new HashMap<>();
        if (Func.isNotEmpty(reportInvoicePOList)) {

            List<RdAttachmentPO> invoiceFiles = attachmentManager.getByObjectNosAndType(invoiceNos, AttachmentObjectTypeEnum.INVOICE.getCode());
            if (Func.isNotEmpty(invoiceFiles)) {
                reportFileMap = invoiceFiles.stream().collect(Collectors.groupingBy(RdAttachmentPO::getObjectNo));
            }
        }

        return reportDataAssembler.assemblerReportInvoiceDO(reportInvoicePOList, reportFileMap);
    }

    /**
     * 查询invoice
     *
     * @param labId
     * @return
     */
    public List<RdInvoiceDO> exportInvoiceListByReportNos(Long labId, List<String> reportNos) {
        Assert.notNull(labId, "labId cannot null");
        Assert.notNull(reportNos, "reportNos cannot null");
        List<RdReportInvoicePO> reportInvoicePOList = reportInvoiceManager.selectReportInvoiceByReportNos(labId, reportNos);
        Map<String, List<RdAttachmentPO>> reportFileMap = new HashMap<>();
        if (Func.isNotEmpty(reportInvoicePOList)) {
            List<String> invoiceNos = reportInvoicePOList.stream().map(RdReportInvoicePO::getInvoiceNo).distinct().collect(Collectors.toList());
            List<RdAttachmentPO> invoiceFiles = attachmentManager.getByObjectNosAndType(invoiceNos, AttachmentObjectTypeEnum.INVOICE.getCode());
            if (Func.isNotEmpty(invoiceFiles)) {
                reportFileMap = invoiceFiles.stream().collect(Collectors.groupingBy(RdAttachmentPO::getObjectNo));
            }
        }
        return reportDataAssembler.assemblerReportInvoiceDO(reportInvoicePOList, reportFileMap);
    }

    /**
     * cancel
     *
     * @param labId
     * @param reportNo
     */
    public void cancel(Long labId, String reportNo, Integer reportStatus) {
        RdReportPO report = reportDataManager.getReport(labId, reportNo);
        if (Func.isNotEmpty(report)) {
            report.setReportStatus(ReportStatus.Cancelled.getCode());
            report.setModifiedDate(DateUtils.getNow());
            reportDataManager.updateById(report);
            // 更新大json
            reportDataManager.updateExtReportStatus(report.getId(), Func.isNotEmpty(reportStatus) ? reportStatus : ReportStatus.Cancelled.getCode());
        } else {
            log.error("[ReportCancel] cancel report failed.  labId:{} ,reportNo:{}", labId, reportNo);
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, StrUtil.format("[ReportCancel] cancel report failed , Report not exist.  labId:{} ,reportNo:{}", labId, reportNo));
        }
    }

    /**
     * 根据ID修改report状态
     *
     * @param reportId     reportId
     * @param reportStatus 目标状态
     */
    public void modifyReportStatusById(Long reportId, Integer reportStatus) {
        RdReportPO report = reportMapper.selectByPrimaryKey(reportId);
        if (Func.isNotEmpty(report)) {
            report.setReportStatus(reportStatus);
            report.setModifiedDate(DateUtils.getNow());
            reportDataManager.updateById(report);
            // 更新大json
            reportDataManager.updateExtReportStatus(reportId, reportStatus);
        } else {
            log.error("[ModifyReportStatus] modify report status failed. reportId:{}, targetStatus:{}", 
                    reportId, reportStatus);
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, 
                    StrUtil.format("[ModifyReportStatus] modify report status failed, Report not exist. reportId:{}", 
                            reportId));
        }
    }


    /**
     * 根据ID更新report过期状态
     *
     * @param reportId reportId
     * @param activeIndicator 状态值
     */
    public CustomResult updateReportExpireById(Long reportId, Integer activeIndicator) {
        RdReportPO report = reportMapper.selectByPrimaryKey(reportId);
        if (Func.isNotEmpty(report)) {
            report.setActiveIndicator(activeIndicator);
            report.setModifiedDate(new Date());
            reportMapper.updateByPrimaryKey(report);
            return CustomResult.newSuccessInstance();
        } else {
            log.error("[UpdateReportExpire] update report expire failed. reportId:{}", reportId);
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR,
                    StrUtil.format("[UpdateReportExpire] update report expire failed, Report not exist. reportId:{}",
                            reportId));
        }
    }

    public ReportDataService(RdReportMapper reportMapper) {
        this.reportMapper = reportMapper;
    }

    public CustomResult updateReportExpire(Long labId, List<String> reportNos, String labCode) {
        if (Func.isNotEmpty(reportNos)) {
            List<RdReportPO> rdReportPOS = reportDataManager.getReportList(labId, reportNos);
            if (Func.isNotEmpty(rdReportPOS)) {
                rdReportPOS.forEach(l -> {
                    l.setActiveIndicator(ActiveType.Disable.getStatus());
                    l.setModifiedDate(new Date());
                });

                reportMapper.batchUpdate(rdReportPOS);
            }
        }
        return CustomResult.newSuccessInstance();
    }

    public List<ReportDataDO> exportByVersion(Long labId, String reportNo, String labCode) {
        List<RdReportPO> rdReportPOS = reportDataManager.getByReportNoAndLabId(labId, reportNo);
        if (Func.isNotEmpty(rdReportPOS)) {
            RdReportPO rdReportPO = rdReportPOS.get(0);
            Long reportPOId = rdReportPO.getId();
            List<ReportDataDO> list = reportDataManager.selectByVersion(reportPOId);
            if (Func.isEmpty(list)) {
                return null;
            }
            list.forEach(
                    dataDO -> {
                        String orderNo = dataDO.getOrder().getOrderNo();
                        getSubTestDataInfo(orderNo, labCode, dataDO);
                    }
            );
            return list;
        }
        return null;
    }

    public void replaceSystemId(Long systemId, List<String> reportNos) {
        for (String reportNo : reportNos) {
            RdReportExample example = new RdReportExample();
            example.createCriteria()
                    .andSystemIdNotEqualTo(systemId).andReportNoEqualTo(reportNo);
            // 按systemId + reportNo查询 RdReport
            reportMapper.selectByExample(example).stream().findFirst().ifPresent(report -> {
                RdReportExtExample extExample = new RdReportExtExample();
                extExample.createCriteria().andRdReportIdEqualTo(report.getId());
                //按RdReport.id查询RdReportExt
                reportExtMapper.selectByExampleWithBLOBs(extExample).stream()
                        // 取updateVersion最大的那条
                        .max(Comparator.comparing(RdReportExtPO::getUpdateVersion))
                        .ifPresent(ext -> {
                            ReportDataDTO reportDataDTO = JSONObject.parseObject(ext.getRequestJson(), ReportDataDTO.class);
                            if (Objects.isNull(reportDataDTO)) {
                                return;
                            }
                            if (Objects.nonNull(reportDataDTO.getSystemId())) {
                                reportDataDTO.setSystemId(systemId);
                            }
                            Optional.ofNullable(reportDataDTO.getHeader()).ifPresent(h -> h.setSystemId(systemId.intValue()));
                            Optional.ofNullable(reportDataDTO.getOrder()).ifPresent(o -> o.setSystemId(systemId.intValue()));
                            Optional.ofNullable(reportDataDTO.getTrfList()).ifPresent(ts -> ts.forEach(t -> {
                                Optional.ofNullable(t.getOrderList()).ifPresent(os -> os.forEach(o -> o.setSystemId(systemId.intValue())));
                            }));
                            Optional.ofNullable(reportDataDTO.getTestSampleList()).ifPresent(ts -> ts.forEach(s -> s.setSystemId(systemId.intValue())));
                            Optional.ofNullable(reportDataDTO.getTestLineList()).ifPresent(ts -> ts.forEach(s -> s.setSystemId(systemId.intValue())));
                            Optional.ofNullable(reportDataDTO.getTestResultList()).ifPresent(ts -> ts.forEach(s -> s.setSystemId(systemId.intValue())));

                            //更新ext
                            ext.setRequestJson(JSONObject.toJSONString(reportDataDTO));
                            reportExtMapper.updateByPrimaryKeyWithBLOBs(ext);
                        });

                //更新report
                report.setSystemId(systemId);
                reportMapper.updateByPrimaryKey(report);
            });

        }
    }

    public List<TestDataAndMatrixDTO> querySubReportData(String labCode, String orderNo) {
        String suffix = StringUtil.getTestDataSuffix(labCode);
        return reportResultManager.queryTestDataInfoByOrderNo(suffix, orderNo);
    }

    public List<ReportDataDO> queryReportByOrderNo(String orderNo,String labCode) {
        if (Func.isBlank(orderNo) || Func.isBlank(labCode)) {
            return new ArrayList<>();
        }
        LabInfo labCodeInfoByLabCodeFromCache = frameWorkClient.getLabCodeInfoByLabCodeFromCache(labCode, null);
        if (Func.isEmpty(labCodeInfoByLabCodeFromCache)) {
            return new ArrayList<>();
        }
        return reportDataManager.getReportExtByOrderNoAndLabId(labCodeInfoByLabCodeFromCache.getLaboratoryID(), orderNo);
    }

    /**
     * 对数据进行过滤处理
     * @param data 需要过滤的数据
     * @return 过滤后的数据
     */
    public ReportDataDO applyDataFilter(ReportDataDO data) {
        if (data == null) {
            return null;
        }
        
        // 创建过滤上下文
        FilterContext context = new FilterContext();
        context.setSystemId(data.getSystemId());
        
        // 执行所有过滤器
        return filterStrategyFactory.applyFilters(data, context);
    }

    public List<ReportDataDO> exportReportDataListWithFilter(Long labId, List<String> reportNos, String labCode,boolean applyFilter, String testSampleLevel){
        if(Func.isEmpty(reportNos)){
            return Collections.emptyList();
        }
        List<ReportDataDO> reportDataDOS = exportReportDataList(labId, reportNos, labCode);
        if(Func.isEmpty(reportDataDOS)){
            return Collections.emptyList();
        }
        if(testSampleLevel.equals(BatchExportReportDataReq.TEST_SAMPLE_LEVEL_NO_MIX_AND_SHARE_LIST)){
            // Process noMixAndShareList logic
            reportDataDOS.forEach(this::processNoMixAndShareList);
        }

        // Deduplicate testLineList based on testLineInstanceId
        reportDataDOS.forEach(reportData -> {
            if (Func.isNotEmpty(reportData) && Func.isNotEmpty(reportData.getTestLineList())) {
                List<RdTestLineDO> uniqueTestLines = reportData.getTestLineList().stream()
                        .filter(testLine -> testLine != null && testLine.getTestLineInstanceId() != null)
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        RdTestLineDO::getTestLineInstanceId,
                                        testLine -> testLine,
                                        (existing, replacement) -> existing),
                                map -> new ArrayList<>(map.values())));
                reportData.setTestLineList(uniqueTestLines);
            }
        });
        if(applyFilter){
            return reportDataDOS.stream().map(this::applyDataFilter).collect(Collectors.toList());
        }
        return reportDataDOS;
    }


    /**
     * 导出带过滤的报告数据
     */
    public ReportDataDO exportReportDataWithFilter(Long labId, String reportNo, String labCode, boolean applyFilter) {
        // 获取原始数据
        ReportDataDO originalData = exportReportData(labId, reportNo, labCode);
        
        if (!applyFilter || originalData == null) {
            return originalData;
        }
        // 应用过滤器
        return applyDataFilter(originalData);
    }

    /**
     * 处理noMixAndShareList逻辑
     * @param reportDataDO 报告数据对象
     */
    private void processNoMixAndShareList(ReportDataDO reportDataDO) {
        if (Func.isEmpty(reportDataDO) || Func.isEmpty(reportDataDO.getTestSampleList())) {
            return;
        }
        
        List<RdTestSampleDO> testSampleList = reportDataDO.getTestSampleList();
        for (RdTestSampleDO testSample : testSampleList) {
            if (testSample == null || testSample.getTestSampleType() == null) {
                continue;
            }
            
            // 处理testSampleType == 105的情况
            if (testSample.getTestSampleType() == 105) {
                testSample.setTestSampleType(102);
                continue;
            }
            
            // 处理testSampleType == 104的情况
            if (testSample.getTestSampleType() == 104) {
                processTestSampleType104(reportDataDO, testSample);
            }
        }
    }

    @Data
    private static class TLAndTSKey {
        private String testSampleInstanceId;
        private String testLineInstanceId;

        public static TLAndTSKey of(String testLineInstanceId, String testSampleInstanceId) {
            TLAndTSKey entity = new TLAndTSKey();
            entity.setTestLineInstanceId(testLineInstanceId);
            entity.setTestSampleInstanceId(testSampleInstanceId);
            return entity;
        }
    }
    
    /**
     * 处理testSampleType为104的测试样本
     * @param reportDataDO 报告数据对象
     * @param mixTestSample 测试样本
     */
    private void processTestSampleType104(ReportDataDO reportDataDO, RdTestSampleDO mixTestSample) {
        List<RdTestSampleGroupDO> testSampleGroupList = mixTestSample.getTestSampleGroupList();
        List<RdReportMatrixDO> reportMatrixList = Optional.ofNullable(reportDataDO.getHeader()).map(RdReportDO::getReportMatrixList).orElse(Collections.emptyList());
        
        if (Func.isEmpty(testSampleGroupList) || Func.isEmpty(reportMatrixList)) {
            return;
        }
        
        // 创建reportMatrixList的Map，以testSampleInstanceId为key，提高查询效率
        Map<String, List<RdReportMatrixDO>> matrixMapByTestSampleInstanceId = reportMatrixList.stream()
                .filter(matrix -> matrix != null && matrix.getTestSampleInstanceId() != null && matrix.getTestLineInstanceId() !=null)
                .collect(Collectors.groupingBy(RdReportMatrixDO::getTestSampleInstanceId));

        Map<TLAndTSKey, List<RdReportMatrixDO>> matrixMapByTlAndTs = reportMatrixList.stream()
                .filter(matrix -> matrix != null && matrix.getTestSampleInstanceId() != null && matrix.getTestLineInstanceId() !=null)
                .collect(Collectors.groupingBy(matrix -> TLAndTSKey.of(matrix.getTestLineInstanceId(), matrix.getTestSampleInstanceId())));

        // 获取与testSample.testSampleInstanceId匹配的矩阵
        List<RdReportMatrixDO> mixSampleMatrices = matrixMapByTestSampleInstanceId.getOrDefault(mixTestSample.getTestSampleInstanceId(), Collections.emptyList());
        if (mixSampleMatrices.isEmpty()) {
            return;
        }


        // 找出不存在于reportMatrixList中的testSampleGroupList元素
        List<RdTestSampleGroupDO> groupTestSampleList = testSampleGroupList.stream()
                .filter(groupItem -> groupItem != null && groupItem.getTestSampleInstanceId() != null)
                .collect(Collectors.toList());
        
        if (groupTestSampleList.isEmpty()) {
            return;
        }

        // 创建testResultList的Map，以testMatrixId为key，提高查询效率
        Map<String, List<RdTestResultDO>> resultMap = Optional.ofNullable(reportDataDO.getTestResultList())
                .map(testResultList -> testResultList.stream()
                        .filter(result -> result != null && result.getTestMatrixId() != null)
                        .collect(Collectors.groupingBy(RdTestResultDO::getTestMatrixId)))
                .orElse(Collections.emptyMap());

        Map<String, List<RdReportConclusionDO>> conclusionMap = Optional.ofNullable(reportDataDO.getReportConclusionList())
                .map(reportConclusonList -> reportConclusonList.stream().filter(conclusion -> conclusion != null && conclusion.getObjectId() != null)
                        .collect(Collectors.groupingBy(RdReportConclusionDO::getObjectId)))
                .orElse(Collections.emptyMap());

        // 为每个不存在的组项和匹配的矩阵复制数据
        Set<String> mixMatricesIds = new HashSet<>();
        for (RdReportMatrixDO mixMatrix : mixSampleMatrices) {
            String testMatrixId = mixMatrix.getTestMatrixId();
            if (Func.isBlank(testMatrixId)) {
                continue;
            }
            mixMatricesIds.add(testMatrixId);
            for (RdTestSampleGroupDO groupTestSample : groupTestSampleList) {
                // group testSample + mix testLine lookup matrices
                TLAndTSKey key = TLAndTSKey.of(mixMatrix.getTestLineInstanceId(), groupTestSample.getTestSampleInstanceId());
                if (matrixMapByTlAndTs.containsKey(key)) {
                    continue;
                }
                List<RdTestResultDO> matchingResults = resultMap.getOrDefault(testMatrixId, Collections.emptyList());
                List<RdReportConclusionDO> matchingConclusions = conclusionMap.getOrDefault(testMatrixId, Collections.emptyList());
                copyMatrixAndResults(reportDataDO, mixMatrix, matchingResults,matchingConclusions, groupTestSample);
            }
        }
        List<RdReportMatrixDO> afterRemoveMatrices = reportDataDO.getHeader().getReportMatrixList().stream()
                .filter(matrix -> Objects.nonNull(matrix) && Objects.nonNull(matrix.getTestMatrixId()))
                .filter(matrix -> !mixMatricesIds.contains(matrix.getTestMatrixId()))
                .collect(Collectors.toList());
        List<RdTestResultDO> afterRemoveResults = Optional.ofNullable(reportDataDO.getTestResultList())
                .map(testResultList -> testResultList.stream()
                        .filter(result -> Objects.nonNull(result) && Objects.nonNull(result.getTestMatrixId()))
                        .filter(result -> !mixMatricesIds.contains(result.getTestMatrixId()))
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        List<RdReportConclusionDO> afterRemoveConclusions = Optional.ofNullable(reportDataDO.getReportConclusionList())
                .map(reportConclusionList -> reportConclusionList.stream()
                        .filter(result -> Objects.nonNull(result) && Objects.nonNull(result.getObjectId()))
                        .filter(result -> !mixMatricesIds.contains(result.getObjectId()))
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        reportDataDO.getHeader().setReportMatrixList(afterRemoveMatrices);
        reportDataDO.setTestResultList(afterRemoveResults);
        reportDataDO.setReportConclusionList(afterRemoveConclusions);
    }
    
    /**
     * 复制矩阵和结果
     * @param reportDataDO 报告数据对象
     * @param matrix 源矩阵
     * @param matchingResults 匹配的结果列表
     * @param notExistsGroupItem 不存在的组项
     */
    private void copyMatrixAndResults(ReportDataDO reportDataDO, RdReportMatrixDO matrix, 
                                     List<RdTestResultDO> matchingResults, List<RdReportConclusionDO> matchingConclusions, RdTestSampleGroupDO notExistsGroupItem) {
        // 复制矩阵
        RdReportMatrixDO newMatrix = copyMatrix(matrix);
        
        // 生成新的testMatrixId
        String newTestMatrixId = UUID.randomUUID().toString();
        newMatrix.setTestMatrixId(newTestMatrixId);
        
        // 替换testSampleInstanceId
        newMatrix.setTestSampleInstanceId(notExistsGroupItem.getTestSampleInstanceId());
        
        // 添加到reportMatrixList
        reportDataDO.getHeader().getReportMatrixList().add(newMatrix);
        
        // 复制并更新testResultList项
        for (RdTestResultDO result : matchingResults) {
            RdTestResultDO newResult = copyTestResult(result);
            
            // 更新testMatrixId为新的ID
            newResult.setTestMatrixId(newTestMatrixId);
            
            // 添加到testResultList
            reportDataDO.getTestResultList().add(newResult);
        }

        for (RdReportConclusionDO matchingConclusion : matchingConclusions) {
            RdReportConclusionDO newConclusion = copyConclusion(matchingConclusion);

            // 更新testMatrixId为新的ID
            newConclusion.setObjectId(newTestMatrixId);

            // 添加到testResultList
            reportDataDO.getReportConclusionList().add(newConclusion);
        }
    }
    
    /**
     * 复制矩阵对象
     * @param source 源矩阵
     * @return 复制的新矩阵
     */
    private RdReportMatrixDO copyMatrix(RdReportMatrixDO source) {
        RdReportMatrixDO target = new RdReportMatrixDO();
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.error("Error copying matrix properties", e);
        }
        return target;
    }
    
    /**
     * 复制测试结果对象
     * @param source 源测试结果
     * @return 复制的新测试结果
     */
    private RdTestResultDO copyTestResult(RdTestResultDO source) {
        RdTestResultDO target = new RdTestResultDO();
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.error("Error copying result properties", e);
        }
        return target;
    }

    /**
     * 复制测试结果对象
     * @param source 源测试结果
     * @return 复制的新测试结果
     */
    private RdReportConclusionDO copyConclusion(RdReportConclusionDO source) {
        RdReportConclusionDO target = new RdReportConclusionDO();
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.error("Error copying result properties", e);
        }
        return target;
    }


}
