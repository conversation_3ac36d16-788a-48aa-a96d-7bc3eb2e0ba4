/*
 *
 * (C) Copyright 2016 Ymatou (http://www.ymatou.com/).
 * All rights reserved.
 *
 */

package com.sgs.testdatabiz.sdk.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.core.MediaType;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class HttpClientUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    /**
     * 三次握手时间
     */
    private static final Integer CONN_TIME_OUT = 3000;
    /**
     * 响应超时时间，超过此时间不再读取响应；
     */
    private static final Integer SOCKET_TIME_OUT = 20000;
    private static final Integer DEFAULT_MAX_PER_ROUTE = 200;
    private static final Integer MAX_TOTAL = 1000;
    /**
     * http clilent中从connetcion pool中获得一个connection的超时时间；
     */
    private static final Integer CONNECTION_REQUEST_TIMEOUT = 30000;
    private static final RequestConfig requestConfig;
    private static final HttpClient httpClient;

    private static int timeout = 60;

    // 编码格式。发送编码格式统一用UTF-8
    private static String ENCODING = "UTF-8";
    //private static Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    static {
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(CONN_TIME_OUT)
                .setSocketTimeout(SOCKET_TIME_OUT).build();

        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setDefaultMaxPerRoute(DEFAULT_MAX_PER_ROUTE);//每个route默认的最大连接数
        cm.setMaxTotal(MAX_TOTAL);//整个连接池的最大连接数

        httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultRequestConfig(requestConfig).build();
    }

    public static String requestGet(String url, Map<String, String> paramsMap, Integer timeout) throws Exception {
        return sendGet(url, initParams(paramsMap), timeout);
    }

    public static String sendGet(String url, List<NameValuePair> params) throws Exception {
        return sendGet(url, params, null);
    }

    public static String sendGet(String url, List<NameValuePair> params, Integer timeout) throws Exception {
        // Get请求
        HttpGet httpGet = new HttpGet(url);

        try {
            if (timeout != null && timeout > 0) {
                httpGet.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }

            // 设置参数
            String str = EntityUtils.toString(new UrlEncodedFormEntity(params, Consts.UTF_8));
            httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + str));
            // 发送请求
            HttpResponse response = httpClient.execute(httpGet);
            //logger.info("GET request url:{} params:{} response:{} time:{}", url, paramsMap, response, System.currentTimeMillis() - start);
            // 获取返回数据
            String retStr = getSuccessRetFromResp(response, url, JSON.toJSONString(params));

            return retStr;
        } finally {
            httpGet.releaseConnection();
        }
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param host
     * @param port
     * @return
     * @throws Exception
     */
    public static String sendGet(String reqUrl, Map<String, String> paramMaps, String host, int port) throws Exception {
        StringBuffer append = new StringBuffer();
        BufferedReader reader = null;
        try {
            List<NameValuePair> params = initParams(paramMaps);
            // 设置参数
            String reqParams = EntityUtils.toString(new UrlEncodedFormEntity(params, Consts.UTF_8));

            URL client = new URL(String.format("%s?%s", reqUrl, reqParams));
            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
            URLConnection connection = client.openConnection(proxy);

            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));

            String line;
            while ((line = reader.readLine()) != null) {
                append.append(line);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
            }
        }
        return append.toString();
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T sendGet(String reqUrl, Map<String, String> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = requestGet(reqUrl, paramMaps, null);
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    public static <T> List<T> get(String reqUrl, Map<String, String> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = requestGet(reqUrl, paramMaps, null);
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseArray(jsonStr, clazz);
    }

    public static String sendGet(String reqUrl, Map<String, String> paramMaps) throws Exception {
        return requestGet(reqUrl, paramMaps, null);
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param host
     * @param port
     * @return
     * @throws Exception
     */
    public static String sendPost(String reqUrl, Map<String, Object> paramMaps, String host, int port) throws Exception {
        StringBuffer append = new StringBuffer();
        BufferedReader reader = null;
        PrintWriter out = null;
        try {
            // 设置参数
            String reqParams = JSON.toJSONString(paramMaps);
            URL client = new URL(reqUrl);
            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
            URLConnection connection = client.openConnection(proxy);

            connection.setRequestProperty("Content-Type", MediaType.APPLICATION_JSON);
            // 设置超时时间
            /*connection.setConnectTimeout(5000);
            connection.setReadTimeout(15000);*/

            // 发送POST请求必须设置如下两行
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(connection.getOutputStream());
            // 发送请求参数
            out.print(reqParams);
            // flush输出流的缓冲
            out.flush();

            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));
            String line;
            while ((line = reader.readLine()) != null) {
                append.append(line);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception  e) {
            }
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
            }
        }
        return append.toString();
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T sendPost(String reqUrl, Map<String, Object> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(paramMaps));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     *
     * @param reqUrl
     * @param reqParams
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> sendPost(String reqUrl, Object reqParams, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseArray(jsonStr, clazz);
    }

    public static <T> T doPost(String reqUrl,Object reqParams, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     *
     * @param reqUrl
     * @param reqParams
     * @param typeRef
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T sendPost(String reqUrl,Object reqParams, TypeReference<T> typeRef) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, typeRef);
    }

    /**
     *
     * @param reqUrl
     * @param reqParams
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T post(String reqUrl, Object reqParams, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> post(String reqUrl, Map<String, Object> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(paramMaps));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseArray(jsonStr, clazz);
    }

    public static <T> T post(String url, Map<String, Object> paramMaps, TypeReference<T> typeRefs) {
        try {
            String jsonStr = post(url, paramMaps);

            return JSON.parseObject(jsonStr, typeRefs);
        } catch (Exception e) {
            logger.error("post fail,url:"+url+",Exception"+e.getMessage());
        }
        return null;
    }


    /**
     * 基于HttpClient 4.5的通用POST方法
     *
     * @param url
     *            提交的URL
     * @param paramsMap
     *            提交<参数，值>Map
     * @return 提交响应
     */
    public static String post(String url, Map<String, Object> paramsMap) {
        HttpPost httpPost = new HttpPost(url);
        String rspStr = null;
        try {
            httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(SOCKET_TIME_OUT).build());

            if (paramsMap != null) {
                List<NameValuePair> paramList = new ArrayList<NameValuePair>();
                for (Map.Entry<String, Object> param : paramsMap.entrySet()) {
                    Object paramValue = param.getValue();
                    if (paramValue == null){
                        paramValue = "";
                    }
                    NameValuePair pair = new BasicNameValuePair(param.getKey(), paramValue.toString());
                    paramList.add(pair);
                }
                httpPost.setEntity(new UrlEncodedFormEntity(paramList, ENCODING));
            }
            HttpResponse response = httpClient.execute(httpPost);

            // 获取返回数据
            rspStr = getSuccessRetFromResp(response, url, JSON.toJSONString(paramsMap));
        } catch (Exception ex) {
            logger.error("post fail,url:" + url + ",Exception" + ex.getMessage());
        } finally {
            httpPost.releaseConnection();
        }
        return rspStr;
    }


    public static <T> T doPost(String reqUrl, Map<String, Object> paramMaps, Class<T> clazz){
        String jsonStr = post(reqUrl, paramMaps);
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     *
     * @param reqUrl
     * @param headerMaps
     * @param reqObject
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T doPost(String reqUrl, Map<String, String> headerMaps, Object reqObject, Class<T> clazz){
        String jsonStr = postJsonHeader(reqUrl, JSONObject.toJSONString(reqObject), headerMaps);
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        try{
            return JSONObject.parseObject(jsonStr, clazz);
        }catch (Exception ex){
            ex.printStackTrace();
            logger.error("doPost.reqObject：{}", reqObject);
            return null;
        }
        //return JSON.parseObject(jsonStr, clazz);
    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static String postJson(String url, String params) throws Exception {
        return postJson(url, params, null);
    }

    /**
     *
     * @param url
     * @param paramMaps
     * @return
     * @throws Exception
     */
    public static String doPost(String url, Map<String, Object> paramMaps) throws Exception {
        return postJson(url, JSON.toJSONString(paramMaps), null);
    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static String postJson(String url, String params, Integer timeout) throws Exception {

        //logger.info("POST request url:{} params:{}", url, params);

        //Long start = System.currentTimeMillis();

        HttpPost httpPost = new HttpPost(url);

        try {
            if (timeout != null && timeout > 0) {
                httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }

            StringEntity entity = new StringEntity(params, Consts.UTF_8);
            entity.setContentType(MediaType.APPLICATION_JSON);

            httpPost.setEntity(entity);

            HttpResponse response = httpClient.execute(httpPost);

            //logger.info("POST request url:{} time:{}", url, System.currentTimeMillis() - start);

            String retStr = getSuccessRetFromResp(response, url, params);

            return retStr;
        } finally {
            httpPost.releaseConnection();
        }

    }

    public static String postJsonHeader(String url, String params, Map<String,String> headerMaps) {
        return postJsonHeader(url,params,headerMaps,null);
    }
    /**
     * 追加header
     * @param url
     * @param params
     * @param timeout
     * @param headerMaps
     * @return
     * @throws Exception
     */
    public static String postJsonHeader(String url, String params, Map<String,String> headerMaps,Integer timeout){
        HttpPost httpPost = new HttpPost(url);
        String retStr = null;
        try {
            if (timeout != null && timeout > 0) {
                httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }
            if (headerMaps != null){
                for (Map.Entry<String, String> entry: headerMaps.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity entity = new StringEntity(params, Consts.UTF_8);
            entity.setContentType(MediaType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            retStr = getSuccessRetFromResp(response, url, params);
        } catch (Exception ex){
            logger.error("postJsonHeader, Error：{}", ex);
        } finally{
            httpPost.releaseConnection();
            // DIG-8555 Remove this return statement from this finally block.
            //return retStr;
        }

        // DIG-8555
        return retStr;
    }

    private static String getSuccessRetFromResp(HttpResponse response, String url, String params) throws IOException {
        String retStr;
        // 检验状态码，如果成功接收数据
        int code = response.getStatusLine().getStatusCode();

        if (code == 200) {
            retStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
        } else {
            throw new RuntimeException(String.format("Http request error:%s, url:%s, params:%s", response, url, params));
        }

        //logger.info("Http request url:{} params={} retStr:{}. ", url, params, retStr);
        return retStr;
    }


    private static List<NameValuePair> initParams(Map<String, String> paramsMap) {
        List<NameValuePair> params = new ArrayList<>();
        if (paramsMap == null)
            return params;
        Iterator<String> iterator = paramsMap.keySet().iterator();


        while (iterator.hasNext()) {
            String key = iterator.next();
            params.add(new BasicNameValuePair(key, paramsMap.get(key)));
        }
        return params;
    }


    private static List<NameValuePair> getParams(Map<String, Object> paramMaps) {

        List<NameValuePair> params = new ArrayList<>();
        if (paramMaps == null)
            return params;
        Iterator<String> iterator = paramMaps.keySet().iterator();


        while (iterator.hasNext()) {
            String key = iterator.next();
            Object value = paramMaps.get(key);
            params.add(new BasicNameValuePair(key, value == null ? "" : value.toString()));
        }
        return params;
    }

    public static String get(String url){
        // Get请求
        HttpGet httpGet = new HttpGet(url);
        try {
            httpGet.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(CONN_TIME_OUT).build());
            // 设置参数
            httpGet.setURI(new URI(httpGet.getURI().toString()));
            // 发送请求
            HttpResponse response = httpClient.execute(httpGet);
            //logger.info("GET request url:{} params:{} response:{} time:{}", url, paramsMap, response, System.currentTimeMillis() - start);
            // 获取返回数据
            String retStr = getSuccessRetFromResp(response, url,null);

            return retStr;
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            httpGet.releaseConnection();
        }
        return null;
    }

}

