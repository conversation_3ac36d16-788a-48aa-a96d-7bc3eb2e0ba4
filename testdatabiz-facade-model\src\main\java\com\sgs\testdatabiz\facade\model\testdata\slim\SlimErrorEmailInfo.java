package com.sgs.testdatabiz.facade.model.testdata.slim;

import com.sgs.testdatabiz.facade.model.enums.EmailTypeEnum;

import java.util.LinkedHashMap;
import java.util.LinkedHashSet;

public final class SlimErrorEmailInfo {
    /**
     *
     */
    private EmailTypeEnum emailType;

    /**
     *
     */
    private LinkedHashMap<String, LinkedHashSet<String>> errorMsgs;

    public EmailTypeEnum getEmailType() {
        return emailType;
    }

    public void setEmailType(EmailTypeEnum emailType) {
        this.emailType = emailType;
    }

    public LinkedHashMap<String, LinkedHashSet<String>> getErrorMsgs() {
        return errorMsgs;
    }

    public void setErrorMsgs(LinkedHashMap<String, LinkedHashSet<String>> errorMsgs) {
        this.errorMsgs = errorMsgs;
    }
}
