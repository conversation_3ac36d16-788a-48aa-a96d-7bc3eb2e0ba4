package com.sgs.testdatabiz.facade.model.info.starlims;

import com.sgs.framework.core.common.PrintFriendliness;

public final class TestDataLangInfo extends PrintFriendliness {
    /**
     *
     */
    private String analyteAlias;

    /**
     *
     */
    private String testAnalyteName;

    /**
     *
     */
    private String reportUnit;

    /**
     *
     */
    private Integer languageId;

    public String getAnalyteAlias() {
        return analyteAlias;
    }

    public void setAnalyteAlias(String analyteAlias) {
        this.analyteAlias = analyteAlias;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }
}
