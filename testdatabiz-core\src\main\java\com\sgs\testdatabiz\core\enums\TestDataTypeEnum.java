package com.sgs.testdatabiz.core.enums;

public enum TestDataTypeEnum {
    Default(0,"Defalut"),
    <PERSON>(1, "<PERSON>"),
    <PERSON>(2, "Fast"),
    StarLims(3, "StarLims");

    private int type;
    private String text;

    TestDataTypeEnum(int type, String text) {
        this.type = type;
        this.text = text;
    }

    public int getType() {
        return type;
    }

    public String getText() {
        return text;
    }
}
