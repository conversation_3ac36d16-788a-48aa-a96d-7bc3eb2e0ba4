package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportMatrixLangMapper {
    int countByExample(RdReportMatrixLangExample example);

    int deleteByExample(RdReportMatrixLangExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportMatrixLangPO record);

    int insertSelective(RdReportMatrixLangPO record);

    List<RdReportMatrixLangPO> selectByExample(RdReportMatrixLangExample example);

    RdReportMatrixLangPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportMatrixLangPO record, @Param("example") RdReportMatrixLangExample example);

    int updateByExample(@Param("record") RdReportMatrixLangPO record, @Param("example") RdReportMatrixLangExample example);

    int updateByPrimaryKeySelective(RdReportMatrixLangPO record);

    int updateByPrimaryKey(RdReportMatrixLangPO record);

    int batchInsert(List<RdReportMatrixLangPO> list);

    int batchUpdate(List<RdReportMatrixLangPO> list);
}