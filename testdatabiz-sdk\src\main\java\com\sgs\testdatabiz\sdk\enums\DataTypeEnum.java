package com.sgs.testdatabiz.sdk.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/1/5 11:38
 */
public enum DataTypeEnum {
    Input("Input", "Input", "输入框"),
    Select("Select", "Select", "选择框"),
    Regulation("Select2", "Select2", "选择框"),
    TextArea("TextArea", "TextArea", "文本域"),
    RichText("RichText", "RichText", "富文本"),
    Label("Label", "Label", "显示信息");

    private String type;
    private String message;
    private String desc;
    static Map<String, DataTypeEnum> maps = new HashMap();

    private DataTypeEnum(String type, String message, String desc) {
        this.type = type;
        this.message = message;
        this.desc = desc;
    }


    public String getType() {
        return this.type;
    }

    public String getMessage() {
        return this.message;
    }

    public String getDesc() {
        return desc;
    }

    public static DataTypeEnum findType(String type) {
        return type != null && maps.containsKey(type) ? (DataTypeEnum) maps.get(type) : null;
    }

    public static boolean check(String type, DataTypeEnum citationType) {
        if (type != null && maps.containsKey(type)) {
            return maps.get(type) == citationType;
        } else {
            return false;
        }
    }

    static {
        DataTypeEnum[] var0 = values();
        int var1 = var0.length;

        for (int var2 = 0; var2 < var1; ++var2) {
            DataTypeEnum type = var0[var2];
            maps.put(type.getType(), type);
        }

    }
}

