package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class TestLineMappingInfoPO {
    /**
     * Id INTEGER(10) 必填<br>
     * ID,Primary key
     */
    private Integer id;

    /**
     * TestLineId INTEGER(10) 必填<br>
     *
     */
    private Integer ppNo;

    /**
     * TestLineId INTEGER(10) 必填<br>
     * 
     */
    private Integer testLineId;

    /**
     * SlimCode VARCHAR(200)<br>
     * 
     */
    private String slimCode;

    /**
     * LabCode VARCHAR(64)<br>
     * 
     */
    private String labCode;

    /**
     * ProductLineCode VARCHAR(50) 必填<br>
     * 
     */
    private String productLineCode;

    /**
     * TestLineEvaluation VARCHAR(500)<br>
     * 
     */
    private String testLineEvaluation;

    /**
     * StandardId INTEGER(10)<br>
     * 
     */
    private Integer standardId;

    /**
     * StandardName VARCHAR(500)<br>
     * 
     */
    private String standardName;

    /**
     * SystemId INTEGER(10)<br>
     * 1:slim   2:fast
     */
    private Integer systemId;

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Boolean activeIndicator;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * CreatedDate
     */
    private Date createdDate;

    /**
     * CreatedBy VARCHAR(50)<br>
     * CreatedBy
     */
    private String createdBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * ModifiedDate
     */
    private Date modifiedDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * ModifiedBy
     */
    private String modifiedBy;

    /**
     * LastModifiedTimeStamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 
     */
    private Date lastModifiedTimeStamp;
    /**
     * 临时使用 excelId
     */
    private Integer testExcelId;

    /**
     * Id INTEGER(10) 必填<br>
     * 获得 ID,Primary key
     */
    public Integer getId() {
        return id;
    }

    /**
     * Id INTEGER(10) 必填<br>
     * 设置 ID,Primary key
     */
    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPpNo() {
        return ppNo;
    }

    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    /**
     * TestLineId INTEGER(10) 必填<br>
     * 获得 
     */
    public Integer getTestLineId() {
        return testLineId;
    }

    /**
     * TestLineId INTEGER(10) 必填<br>
     * 设置 
     */
    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    /**
     * SlimCode VARCHAR(200)<br>
     * 获得 
     */
    public String getSlimCode() {
        return slimCode;
    }

    /**
     * SlimCode VARCHAR(200)<br>
     * 设置 
     */
    public void setSlimCode(String slimCode) {
        this.slimCode = slimCode == null ? null : slimCode.trim();
    }

    /**
     * LabCode VARCHAR(64)<br>
     * 获得 
     */
    public String getLabCode() {
        return labCode;
    }

    /**
     * LabCode VARCHAR(64)<br>
     * 设置 
     */
    public void setLabCode(String labCode) {
        this.labCode = labCode == null ? null : labCode.trim();
    }

    /**
     * ProductLineCode VARCHAR(50) 必填<br>
     * 获得 
     */
    public String getProductLineCode() {
        return productLineCode;
    }

    /**
     * ProductLineCode VARCHAR(50) 必填<br>
     * 设置 
     */
    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode == null ? null : productLineCode.trim();
    }

    /**
     * TestLineEvaluation VARCHAR(500)<br>
     * 获得 
     */
    public String getTestLineEvaluation() {
        return testLineEvaluation;
    }

    /**
     * TestLineEvaluation VARCHAR(500)<br>
     * 设置 
     */
    public void setTestLineEvaluation(String testLineEvaluation) {
        this.testLineEvaluation = testLineEvaluation == null ? null : testLineEvaluation.trim();
    }

    /**
     * StandardId INTEGER(10)<br>
     * 获得 
     */
    public Integer getStandardId() {
        return standardId;
    }

    /**
     * StandardId INTEGER(10)<br>
     * 设置 
     */
    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }

    /**
     * StandardName VARCHAR(500)<br>
     * 获得 
     */
    public String getStandardName() {
        return standardName;
    }

    /**
     * StandardName VARCHAR(500)<br>
     * 设置 
     */
    public void setStandardName(String standardName) {
        this.standardName = standardName == null ? null : standardName.trim();
    }

    /**
     * SystemId INTEGER(10)<br>
     * 获得 1:slim   2:fast
     */
    public Integer getSystemId() {
        return systemId;
    }

    /**
     * SystemId INTEGER(10)<br>
     * 设置 1:slim   2:fast
     */
    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Boolean getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Boolean activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 CreatedDate
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 CreatedDate
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 CreatedBy
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 CreatedBy
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 ModifiedDate
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 ModifiedDate
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 ModifiedBy
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 ModifiedBy
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * LastModifiedTimeStamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 获得 
     */
    public Date getLastModifiedTimeStamp() {
        return lastModifiedTimeStamp;
    }

    /**
     * LastModifiedTimeStamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 设置 
     */
    public void setLastModifiedTimeStamp(Date lastModifiedTimeStamp) {
        this.lastModifiedTimeStamp = lastModifiedTimeStamp;
    }

    public Integer getTestExcelId() {
        return testExcelId;
    }

    public void setTestExcelId(Integer testExcelId) {
        this.testExcelId = testExcelId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", testLineId=").append(testLineId);
        sb.append(", slimCode=").append(slimCode);
        sb.append(", labCode=").append(labCode);
        sb.append(", productLineCode=").append(productLineCode);
        sb.append(", testLineEvaluation=").append(testLineEvaluation);
        sb.append(", standardId=").append(standardId);
        sb.append(", standardName=").append(standardName);
        sb.append(", systemId=").append(systemId);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", lastModifiedTimeStamp=").append(lastModifiedTimeStamp);
        sb.append("]");
        return sb.toString();
    }
}