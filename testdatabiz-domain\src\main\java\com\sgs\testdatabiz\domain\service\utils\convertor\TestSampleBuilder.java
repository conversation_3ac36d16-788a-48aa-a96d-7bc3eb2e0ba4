package com.sgs.testdatabiz.domain.service.utils.convertor;

import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdMaterialAttrDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestSampleDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestSampleGroupDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public final class TestSampleBuilder {

    @Autowired
    private IdService ID_GENERATOR;

    public List<RdTestSampleDO> buildTestSampleDO(List<TestSamplePO> testSampleList, Map<String, List<TestSampleGroupPO>> sampleGroupMap, String extFieldsJson) {
        List<RdTestSampleDO> list = new ArrayList<>();
        testSampleList.forEach(
                sample -> {
                    RdTestSampleDO sampleDO = new RdTestSampleDO();
                    sampleDO.setTestSampleInstanceId(sample.getSampleInstanceId());
                    sampleDO.setParentTestSampleId(sample.getSampleParentId());
                    List<TestSampleGroupPO> sampleGroupPOList = sampleGroupMap.get(sample.getSampleInstanceId());
                    if (Func.isNotEmpty(sampleGroupPOList)) {
                        List<RdTestSampleGroupDO> sampleGroupDOS = new ArrayList<>();
                        sampleGroupPOList.forEach(
                                testSampleGroupPO -> {
                                    RdTestSampleGroupDO testSampleGroupDO = new RdTestSampleGroupDO();
                                    testSampleGroupDO.setTestSampleInstanceId(testSampleGroupPO.getSampleGroupId());
                                    testSampleGroupDO.setMainSampleFlag(testSampleGroupPO.getMainSampleFlag());
                                    sampleGroupDOS.add(testSampleGroupDO);
                                }
                        );

                        sampleDO.setTestSampleGroupList(sampleGroupDOS);
                    }
                    sampleDO.setTestSampleNo(sample.getSampleNo());
                    // fixme 此处无法赋值
//                    sampleDO.setExternalSampleNo();

                    sampleDO.setTestSampleType(sample.getSampleType());
                    sampleDO.setCategory(sample.getCategory());
                    sampleDO.setTestSampleSeq(sample.getSampleSeq());
                    RdMaterialAttrDO materialAttrDO = new RdMaterialAttrDO();
                    materialAttrDO.setMaterialDescription(sample.getDescription());
                    materialAttrDO.setMaterialOtherSampleInfo(sample.getOtherSampleInfo());
                    materialAttrDO.setMaterialEndUse(sample.getEndUse());
                    materialAttrDO.setApplicableFlag(sample.getApplicableFlag());
                    materialAttrDO.setMaterialRemark(sample.getSampleRemark());
                    materialAttrDO.setMaterialName(sample.getMaterial());
                    materialAttrDO.setMaterialColor(sample.getColor());
                    materialAttrDO.setMaterialTexture(sample.getComposition());

                    materialAttrDO.setExtFields(JSONObject.parseObject(extFieldsJson));


                    sampleDO.setMaterialAttr(materialAttrDO);

                    // fixme
//                    sampleDO.setConclusion();
//                    sampleDO.setTestSamplePhoto();

                    list.add(sampleDO);
                }
        );
        return list;
    }
}
