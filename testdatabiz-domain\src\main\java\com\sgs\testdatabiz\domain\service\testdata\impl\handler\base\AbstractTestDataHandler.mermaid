flowchart TD
    A[开始] --> B{数据来源类型检查}
    B -->|是| C[跳过校验，返回true]
    B -->|否| D[初始化校验条件]
    D --> E[获取测试矩阵]
    E --> F{测试矩阵为空}
    F -->|是| G[记录错误，返回false]
    F -->|否| H[获取SubContract测试矩阵]
    H --> I[获取测试线映射关系]
    I --> J[获取客户配置]
    J --> K[获取客户特定规则]
    K --> L{是否需要客户校验}
    L -->|是| M{是否需要验证Matrix}
    M -->|是| N[校验通道数据]
    N --> O{校验通过}
    O -->|否| P[返回false]
    O -->|是| Q[遍历测试矩阵]
    M -->|否| Q[遍历测试矩阵]
    L -->|否| Q[遍历测试矩阵]
    Q --> R{是否需要客户校验}
    R -->|是| S{样本编号为空}
    S -->|是| T[记录错误，继续]
    S -->|否| U{校验分析物代码}
    U -->|否| V[记录错误，继续]
    U -->|是| W{是否需要验证Matrix}
    W -->|是| X[获取SubContract测试矩阵信息]
    X --> Y{是否存在对应Matrix}
    Y -->|否| Z[记录错误，继续]
    Y -->|是| AA[设置Matrix ID]
    W -->|否| AB[计算哈希值]
    R -->|否| AB[计算哈希值]
    AB --> AC{哈希值重复}
    AC -->|是| AD{是否需要客户校验}
    AD -->|是| AE[记录错误，继续]
    AD -->|否| AF[继续]
    AC -->|否| AG[检查Matrix是否已存在]
    AG --> AH{是否已存在}
    AH -->|是| AI{是否需要客户校验}
    AI -->|是| AJ[记录错误，继续]
    AI -->|否| AK[继续]
    AH -->|否| AL[校验Test Data数据]
    AL --> AM{校验通过}
    AM -->|否| AN[记录错误，继续]
    AM -->|是| AO[继续]
    Q --> AP[结束遍历]
    AP --> AQ{是否有错误信息}
    AQ -->|是| AR[返回false]
    AQ -->|否| AS[返回true]
