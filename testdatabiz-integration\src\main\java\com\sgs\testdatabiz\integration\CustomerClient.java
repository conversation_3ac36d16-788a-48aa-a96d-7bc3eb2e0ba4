package com.sgs.testdatabiz.integration;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.preorder.facade.CustomerFacade;
import com.sgs.preorder.facade.model.enums.CustomerType;
import com.sgs.preorder.facade.model.req.customer.CustomerSimplifyInfoReq;
import com.sgs.preorder.facade.model.rsp.customer.CustomerSimplifyInfoRsp;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
public class CustomerClient {
    private static final Logger logger = LoggerFactory.getLogger(CustomerClient.class);
    @Autowired
    private CustomerFacade customerFacade;

    /**
     *
     * @param orderNo
     * @param productLineCode
     * @return
     */
    public BaseResponse<CustomerSimplifyInfoRsp> getCustomerInfo(String orderNo, String productLineCode){
        BaseResponse rspResult = new BaseResponse();
        if(StringUtils.isBlank(orderNo)){
            rspResult.setMessage("请求的orderNo不能为空");
            return rspResult;
        }
        CustomerSimplifyInfoReq req = new CustomerSimplifyInfoReq();
        req.setOrderNo(orderNo);
        req.setCustomerType(CustomerType.Buyer.getCode());
        req.setProductLineCode(productLineCode);
        com.sgs.preorder.facade.model.common.BaseResponse<CustomerSimplifyInfoRsp> resp = customerFacade.getCustomerSimplifyInfoByOrderId(req);

        rspResult.setData(resp.getData());
        rspResult.setStatus(resp.getStatus());
        rspResult.setMessage(resp.getMessage());

        return rspResult;
    }
}
