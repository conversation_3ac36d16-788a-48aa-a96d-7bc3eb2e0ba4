package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.status;

import com.sgs.testdatabiz.core.enums.FilterType;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.rd.filter.AbstractReportDataFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.FilterContext;
import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import org.springframework.stereotype.Component;
import java.util.stream.Collectors;

@DefaultFilter(order = 2)
@Component
public class InvalidStatusFilter extends AbstractReportDataFilter {

    @Override
    protected String getFilterType() {
        return FilterType.INVALID_STATUS.name();
    }

    @Override
    protected ReportDataDO doFilterInternal(ReportDataDO reportData, FilterContext context) {
        if (reportData == null) {
            return null;
        }

        // 过滤TestLine，如果testline的ActiveIndicator为空认为有效
        if (reportData.getTestLineList() != null) {
            int beforeCount = reportData.getTestLineList().size();
            reportData.setTestLineList(
                reportData.getTestLineList().stream()
                    .filter(tl -> tl.getActiveIndicator() == null || tl.getActiveIndicator() == 1)
                    .collect(Collectors.toList())
            );
            context.addFilterRecord(
                "InvalidStatusFilter", 
                "testLines",
                beforeCount,
                reportData.getTestLineList().size(),
                "Filtered test lines with invalid status"
            );
        }

        // 过滤TestSample, 如果testSample的ActiveIndicator为空认为有效
        if (reportData.getTestSampleList() != null) {
            int beforeCount = reportData.getTestSampleList().size();
            reportData.setTestSampleList(
                reportData.getTestSampleList().stream()
                    .filter(sample -> sample.getActiveIndicator() == null || sample.getActiveIndicator() == 1)
                    .collect(Collectors.toList())
            );
            context.addFilterRecord(
                "InvalidStatusFilter",
                "testSamples",
                beforeCount,
                reportData.getTestSampleList().size(),
                "Filtered test samples with invalid status"
            );
        }

        return reportData;
    }
} 