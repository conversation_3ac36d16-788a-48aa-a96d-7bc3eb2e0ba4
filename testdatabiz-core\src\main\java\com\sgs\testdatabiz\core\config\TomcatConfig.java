package com.sgs.testdatabiz.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Created by <PERSON> on 2019/05/09.
 * Description:
 */
@Configuration
public class TomcatConfig {
    @Value("${tomcat.port}")
    private int port;
    @Value("${tomcat.connectionTimeout}")
    private int connectionTimeout;
    @Value("${tomcat.maxConnections}")
    private int maxConnections;
    @Value("${tomcat.maxThreads}")
    private int maxThreads;
    @Value("${tomcat.acceptCount}")
    private int acceptCount;
    @Value("${tomcat.uriEncoding}")
    private String uriEncoding;
    @Value("${tomcat.protocol}")
    private String protocol;

    /**
     * @return the port
     */
    public int getPort() {
        return port;
    }

    /**
     * @return the connectionTimeout
     */
    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    /**
     * @return the maxConnections
     */
    public int getMaxConnections() {
        return maxConnections;
    }

    /**
     * @return the maxThreads
     */
    public int getMaxThreads() {
        return maxThreads;
    }

    /**
     * @return the acceptCount
     */
    public int getAcceptCount() {
        return acceptCount;
    }

    /**
     * @return the uriEncoding
     */
    public String getUriEncoding() {
        return uriEncoding;
    }

    /**
     * @return the protocol
     */
    public String getProtocol() {
        return protocol;
    }
}
