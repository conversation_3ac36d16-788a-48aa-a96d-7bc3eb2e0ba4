package com.sgs.testdatabiz.sdk.util;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jayway.jsonpath.*;
import com.sgs.testdatabiz.sdk.output.dto.RdReportDataDTO;

import java.io.File;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ConvertJsonValueUtil {

//    public static String convert(RdReportDataDTO rdReportDataDTO, Map<String, String> jsonPathReplacements) {
//        if (rdReportDataDTO == null) {
//            throw new IllegalArgumentException("rdReportDataDTO cannot be null");
//        }
//
//        JSONObject jsonObject = new JSONObject();
//
//        try {
//            // 获取所有字段及其值
//            Map<String, Object> fieldValues = getFieldValues(rdReportDataDTO);
//
//            for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
//                String fieldName = entry.getKey();
//                Object value = entry.getValue();
//                // 将字段值替换为 $ 加上字段名的形式
//                if (value instanceof List || value instanceof Map) {
//                    jsonObject.put(fieldName, processComplexType(value));
//                } else {
//                    jsonObject.put(fieldName, "$" + fieldName);
//                }
//            }
//
//            // 应用 JSON Path 替换
//            applyJsonPathReplacements(jsonObject, jsonPathReplacements);
//        } catch (Exception e) {
//            throw new RuntimeException("Error converting RdReportDataDTO to JSON template", e);
//        }
//
//        // 使用 fastjson 将 JSONObject 转换为 JSON 字符串
//        return JSON.toJSONString(jsonObject);
//    }
//
//    private static Map<String, Object> getFieldValues(RdReportDataDTO rdReportDataDTO) throws IllegalAccessException {
//        Map<String, Object> fieldValues = new HashMap<>();
//        Class<?> clazz = rdReportDataDTO.getClass();
//
//        for (Field field : clazz.getDeclaredFields()) {
//            field.setAccessible(true);
//            fieldValues.put(field.getName(), field.get(rdReportDataDTO));
//        }
//
//        return fieldValues;
//    }
//
//    private static Object processComplexType(Object value) {
//        if (value instanceof List) {
//            List<?> list = (List<?>) value;
//            return list.stream().map(ConvertTemplateUtil::processComplexType);
//        } else if (value instanceof Map) {
//            Map<?, ?> map = (Map<?, ?>) value;
//            Map<String, Object> result = new HashMap<>();
//            for (Map.Entry<?, ?> entry : map.entrySet()) {
//                result.put(entry.getKey().toString(), processComplexType(entry.getValue()));
//            }
//            return result;
//        } else if (value instanceof RdReportDataDTO) {
//            return processRdReportDataDTO((RdReportDataDTO) value);
//        } else {
//            return value;
//        }
//    }
//    private static JSONObject processRdReportDataDTO(RdReportDataDTO rdReportDataDTO) {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            Map<String, Object> fieldValues = getFieldValues(rdReportDataDTO);
//            for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
//                String fieldName = entry.getKey();
//                Object value = entry.getValue();
//                if (value instanceof List || value instanceof Map) {
//                    jsonObject.put(fieldName, processComplexType(value));
//                } else {
//                    jsonObject.put(fieldName, "$" + fieldName);
//                }
//            }
//        } catch (IllegalAccessException e) {
//            throw new RuntimeException("Error processing RdReportDataDTO", e);
//        }
//        return jsonObject;
//    }
//    private static void applyJsonPathReplacements(JSONObject jsonObject, Map<String, String> jsonPathReplacements) {
//        for (Map.Entry<String, String> entry : jsonPathReplacements.entrySet()) {
//            String jsonPath = entry.getKey();
//            String replacementValue = entry.getValue();
//
//            try {
//                Object value = JsonPath.read(jsonObject.toJSONString(), jsonPath);
//                if (value != null) {
//                    // 替换指定路径的值
//                    JsonPath.parse(jsonObject.toJSONString()).set(jsonPath, replacementValue).json();
//                    // 处理该路径下的其他字段
//                    handleSubFields(jsonObject, jsonPath);
//                }
//            } catch (PathNotFoundException e) {
//                // 忽略不存在的路径
//            }
//        }
//    }
//    private static void handleSubFields(JSONObject jsonObject, String jsonPath) {
//        String[] pathParts = jsonPath.split("\\.");
//        JSONObject current = jsonObject;
//        for (int i = 0; i < pathParts.length - 1; i++) {
//            current = current.getJSONObject(pathParts[i]);
//        }
//        if (current != null && current instanceof JSONObject) {
//            for (String key : ((JSONObject) current).keySet()) {
//                if (!key.equals(pathParts[pathParts.length - 1])) {
//                    ((JSONObject) current).put(key, "$" + key);
//                }
//            }
//        }
//    }


    public static String convert(RdReportDataDTO rdReportDataDTO, Map<String, String> jsonPathReplacements) {
        if (rdReportDataDTO == null) {
            throw new IllegalArgumentException("rdReportDataDTO cannot be null");
        }

        JSONObject jsonObject = new JSONObject();

        try {
            // 获取所有字段及其值
            Map<String, Object> fieldValues = getFieldValues(rdReportDataDTO);

            for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
                String fieldName = entry.getKey();
                Object value = entry.getValue();
                // 将字段值替换为 $ 加上字段名的形式
                if (value instanceof List || value instanceof Map) {
                    jsonObject.put(fieldName, processComplexType(value));
                } else {
                    jsonObject.put(fieldName, "$" + fieldName);
                }
            }

            // 应用 JSON Path 替换
            applyJsonPathReplacements(jsonObject, jsonPathReplacements);
        } catch (Exception e) {
            throw new RuntimeException("Error converting RdReportDataDTO to JSON template", e);
        }

        // 使用 fastjson 将 JSONObject 转换为 JSON 字符串
        return JSON.toJSONString(jsonObject);
    }

    private static Map<String, Object> getFieldValues(Object obj) throws IllegalAccessException {
        Map<String, Object> fieldValues = new HashMap<>();
        Class<?> clazz = obj.getClass();

        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            fieldValues.put(field.getName(), field.get(obj));
        }

        return fieldValues;
    }

    private static Object processComplexType(Object value) {
        if (value instanceof List) {
            List<?> list = (List<?>) value;
            return list.stream().map(ConvertJsonValueUtil::processComplexType).collect(Collectors.toList());
        } else if (value instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) value;
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                result.put(entry.getKey().toString(), processComplexType(entry.getValue()));
            }
            return result;
        } else if (value instanceof RdReportDataDTO) {
            return processRdReportDataDTO((RdReportDataDTO) value);
        } else {
            return value;
        }
    }

    private static JSONObject processRdReportDataDTO(RdReportDataDTO rdReportDataDTO) {
        JSONObject jsonObject = new JSONObject();
        try {
            Map<String, Object> fieldValues = getFieldValues(rdReportDataDTO);
            for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
                String fieldName = entry.getKey();
                Object value = entry.getValue();
                if (value instanceof List || value instanceof Map) {
                    jsonObject.put(fieldName, processComplexType(value));
                } else {
                    jsonObject.put(fieldName, "$" + fieldName);
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Error processing RdReportDataDTO", e);
        }
        return jsonObject;
    }

    private static void applyJsonPathReplacements(JSONObject jsonObject, Map<String, String> jsonPathReplacements) {
        for (Map.Entry<String, String> entry : jsonPathReplacements.entrySet()) {
            String jsonPath = entry.getKey();
            String replacementValue = entry.getValue();

            try {
                Object value = JsonPath.read(jsonObject.toJSONString(), jsonPath);
                if (value != null) {
                    // 替换指定路径的值
                    JsonPath.parse(jsonObject.toJSONString()).set(jsonPath, replacementValue).json();
                    // 处理该路径下的其他字段
                    handleSubFields(jsonObject, jsonPath);
                }
            } catch (PathNotFoundException e) {
                // 忽略不存在的路径
            }
        }
    }

    private static void handleSubFields(JSONObject jsonObject, String jsonPath) {
        String[] pathParts = jsonPath.split("\\.");
        JSONObject current = jsonObject;
        for (int i = 0; i < pathParts.length - 1; i++) {
            current = current.getJSONObject(pathParts[i]);
        }
        if (current != null && current instanceof JSONObject) {
            for (String key : ((JSONObject) current).keySet()) {
                if (!key.equals(pathParts[pathParts.length - 1])) {
                    ((JSONObject) current).put(key, "$" + key);
                }
            }
        }
    }

//    public static void main(String[] args) {
//        RdReportDataDTO rdReportDataDTO = new RdReportDataDTO();
//        rdReportDataDTO.setHeader(new RdReportDTO());
//        rdReportDataDTO.getHeader().setReportNo("123456");
//
//        Map<String, String> jsonPathReplacements = new HashMap<>();
//        jsonPathReplacements.put("$.header.reportNo", "customFunction");
//
//        System.out.println(convert(rdReportDataDTO, jsonPathReplacements));
//    }


//    public static String transformJson(String json, Map<String, String> replacements) {
//        // 将JSON字符串解析为JSONObject
//        JSONObject rootNode = JSON.parseObject(json);
//
//        // 遍历并替换指定路径的值
//        for (Map.Entry<String, String> entry : replacements.entrySet()) {
//            String path = entry.getKey();
//            String value = entry.getValue();
//
//            // 使用JSONPath获取节点
//            Object node = JsonPath.read(json, path);
//            if (node instanceof String) {
//                // 更新指定路径的值
//                JsonPath.parse(rootNode.toJSONString()).set(path, value).json();
//            }
//        }
//
//        // 将剩余的值替换为$key格式
//        replaceRemainingValues(rootNode);
//
//        // 将JSONObject转换回JSON字符串
//        return JSON.toJSONString(rootNode, true);
//    }
//
//    private static void replaceRemainingValues(JSONObject node) {
//        for (String key : node.keySet()) {
//            Object value = node.get(key);
//            if (value instanceof JSONObject) {
//                replaceRemainingValues((JSONObject) value);
//            } else if (value instanceof JSONArray) {
//                JSONArray array = (JSONArray) value;
//                for (int i = 0; i < array.size(); i++) {
//                    Object item = array.get(i);
//                    if (item instanceof JSONObject) {
//                        replaceRemainingValues((JSONObject) item);
//                    }
//                }
//            } else if (value instanceof String) {
//                node.put(key, "$" + key);
//            }
//        }
//    }
//
//    public static void main(String[] args) {
//        try {
//            String json = "{\"header\":{\"orderNo\":\"12345\"},\"body\":{\"data\":[{\"id\":\"1\",\"value\":\"test1\"},{\"id\":\"2\",\"value\":\"test2\"}]}}";
//            Map<String, String> replacements = new HashMap<>();
//            replacements.put("$.header.orderNo", "func");
//
//            String transformedJson = transformJson(json, replacements);
//            System.out.println(transformedJson);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }


//    public static String transformJson(String json, String specifiedPath, Object replacementValue) {
//        // 解析JSON字符串为DocumentContext
//        DocumentContext context = JsonPath.parse(json);
//        // 替换指定路径的值
//        context.set(specifiedPath, replacementValue);
//        // 获取修改后的JSON字符串
//        return context.jsonString();
//    }
//
//
//    public static String transformJson(String json, Map<String, String> replacements) {
//        // 解析JSON字符串为JSONObject
//        JSONObject jsonObject = JSON.parseObject(json);
//
//        // 递归替换其他所有值
//        replaceValuesWithKeys(jsonObject);
//
//        // 遍历替换映射，对每个路径进行检查和替换
//        for (Map.Entry<String, String> entry : replacements.entrySet()) {
//            String specifiedPath = entry.getKey();
//            String replacementValue = entry.getValue();
//
//            try {
//                Object existingValue = JsonPath.read(jsonObject.toJSONString(), specifiedPath);
//                if (existingValue != null) {
//                    JsonPath.parse(jsonObject).set(specifiedPath, replacementValue);
//                } else {
//                    System.out.println("指定路径 " + specifiedPath + " 不存在，无法进行替换。");
//                }
//            } catch (com.jayway.jsonpath.PathNotFoundException e) {
//                System.out.println("指定路径 " + specifiedPath + " 不存在，无法进行替换。");
//            }
//        }
//
//        // 返回修改后的JSON字符串
//        return jsonObject.toJSONString();
//    }
//
//    private static void replaceValuesWithKeys(Object json) {
//        if (json instanceof JSONObject) {
//            JSONObject jsonObject = (JSONObject) json;
//            for (String key : jsonObject.keySet()) {
//                Object value = jsonObject.get(key);
//                if (value instanceof JSONObject || value instanceof JSONArray) {
//                    replaceValuesWithKeys(value);
//                } else {
//                    jsonObject.put(key, "$." + key);
//                }
//            }
//        } else if (json instanceof JSONArray) {
//            JSONArray jsonArray = (JSONArray) json;
//            for (int i = 0; i < jsonArray.size(); i++) {
//                Object arrayElement = jsonArray.get(i);
//                if (arrayElement instanceof JSONObject || arrayElement instanceof JSONArray) {
//                    replaceValuesWithKeys(arrayElement);
//                }
//            }
//        }
//    }
//
//    public static void main(String[] args) {
//        String json = "{\"header\":{\"orderNo\":\"12345\"},\"body\":{\"data\":[{\"id\":\"1\",\"value\":\"test1\"},{\"id\":\"2\",\"value\":\"test2\"}]}}";
//        RdReportDataDTO dataOutput = new RdReportDataDTO();
//        File file = new File("C:\\Users\\<USER>\\work\\test.txt");
//        String requestJson = FileUtil.readFile(file, "UTF-8");
//
//        String specifiedPath = "$.header.orderNo";
//        String replacementValue = "func";
//
//        // 创建一个包含多个路径和对应值的映射
//
//        // 创建一个包含多个路径和对应值的映射
//        Map<String, String> replacements = new HashMap<>();
//        replacements.put("$.header.orderNo", "Jane");
//        replacements.put("$.header.reportId", "25");
//        replacements.put("$.header.systemId", "456 Elm St");
//
//        String transformedJson = transformJson(requestJson, replacements);
//        System.out.println(transformedJson);
//    }

    public static String transformJson(String json, Map<String, String> replacements) {
        // 解析原始 JSON 字符串为 JSONObject
        JSONObject originalJson = JSON.parseObject(json);

        // 创建一个新的 JSONObject 用于存储最终的模板
        JSONObject templateJson = new JSONObject();

        // 将原始 JSON 的所有路径和值复制到新的 JSON 对象中
        copyJson(originalJson, templateJson);

        // 遍历替换映射，对每个路径进行检查和替换
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            String specifiedPath = entry.getKey();
            String replacementValue = entry.getValue();

            try {
                // 使用 JsonPath 进行替换
                JsonPath.parse(templateJson).set(specifiedPath, replacementValue);
            } catch (com.jayway.jsonpath.PathNotFoundException e) {
                System.out.println("指定路径 " + specifiedPath + " 不存在，无法进行替换。");
            }
        }

        // 返回修改后的 JSON 字符串
        return templateJson.toJSONString();
    }

    private static void copyJson(JSONObject source, JSONObject target) {
        for (String key : source.keySet()) {
            Object value = source.get(key);
            if (value instanceof JSONObject) {
                target.put(key, new JSONObject());
                copyJson((JSONObject) value, target.getJSONObject(key));
            } else if (value instanceof JSONArray) {
                target.put(key, new JSONArray());
                copyJsonArray((JSONArray) value, target.getJSONArray(key));
            } else {
                target.put(key, value);
            }
        }
    }
    public static Map<String, String> getJsonValuesByPaths(String json, List<String> jsonPaths) {
        Map<String, String> result = new HashMap<>();

        for (String jsonPath : jsonPaths) {
            try {
                // 使用 JsonPath 读取指定路径下的所有值
                Object value = JsonPath.read(json, jsonPath);

                if (value instanceof List) {
                    // 如果是列表，遍历所有值，构建结果 Map
                    List<Object> values = (List<Object>) value;
                    if (values.size() == 1) {
                        result.put(jsonPath, values.get(0).toString());
                    } else {
                        int index = 0;
                        for (Object v : values) {
                            result.put(jsonPath + "[" + index + "]", v.toString());
                            index++;
                        }
                    }
                } else {
                    // 如果不是列表，直接添加到结果 Map
                    result.put(jsonPath, value.toString());
                }
            } catch (PathNotFoundException e) {
                // 处理路径不存在的情况
                System.out.println("路径 " + jsonPath + " 不存在: " + e.getMessage());
            }
        }

        return result;
    }

    private static void copyJsonArray(JSONArray source, JSONArray target) {
        for (int i = 0; i < source.size(); i++) {
            Object value = source.get(i);
            if (value instanceof JSONObject) {
                target.add(new JSONObject());
                copyJson((JSONObject) value, target.getJSONObject(i));
            } else if (value instanceof JSONArray) {
                target.add(new JSONArray());
                copyJsonArray((JSONArray) value, target.getJSONArray(i));
            } else {
                target.add(value);
            }
        }
    }

    public static void main(String[] args) {
        String json = "{\"header\": {\"name\": \"John\", \"age\": 30}, \"body\": {\"address\": \"123 Main St\"}}";
        File file = new File("C:\\Users\\<USER>\\work\\test.txt");
        String requestJson = FileUtil.readFile(file, "UTF-8");
        // 创建一个包含多个路径和对应值的映射
        Map<String, String> replacements = new HashMap<>();
        replacements.put("$.header.orderNo", "Jane");
        replacements.put("$.header.systemId", "25");
        replacements.put("$.body.address", "456 Elm St");

        String transformedJson = transformJson(requestJson, replacements);
        System.out.println(transformedJson);
        Map<String, String> jsonValues =getJsonValuesByPaths(transformedJson, Arrays.asList("$.header.orderNo", "$.header.systemId", "$.body.address"));
        System.out.println(jsonValues);
    }
}







