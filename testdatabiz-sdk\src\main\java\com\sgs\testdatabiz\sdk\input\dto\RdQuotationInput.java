/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationRelationshipDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdQuotationInput implements Serializable{
    // add 20230529
    private String orderNo;
    // add 20230529
    private Integer systemId;
    private String quotationNo;
    private RdQuotationRelationshipDTO relationship;
    private RdCustomerInput payer;
    private String currency;
    private BigDecimal netAmount;
    private BigDecimal vatAmount;
    private BigDecimal totalAmount;
    private BigDecimal discount;
    private BigDecimal adjustmentAmount;
    private BigDecimal finalAmount;
    // add 20230529 version -> quotationVersionId
    private String quotationVersionId;
    private Integer quotationStatus;
    private List<RdServiceItemInput> serviceItemList;
    private List<RdAttachmentInput> quotationFileList;

    private String quotationInstanceId;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
