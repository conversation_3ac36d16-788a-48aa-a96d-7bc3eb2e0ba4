package com.sgs.testdatabiz.sdk.input.dto;

import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/31 20:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportDataInput extends BaseModel implements Serializable {
    private RdReportInput header;
    private List<RdTestSampleInput> testSampleList;
    private List<RdTestLineInput> testLineList;
//    private List<RdReportMatrixDTO> reportMatrixList;
    private List<RdTestResultInput> testResultList;
    private List<RdReportConclusionInput> reportConclusionList;
    private List<RdAttachmentInput> reportFileList;
//    private List<RdSubReportDTO> subReportList;
    private List<RdConditionGroupInput> conditionGroupList;
    private List<RdTrfInput> trfList;
    private RdOrderDTO order;
    private List<RdQuotationInput> quotationList;
    private List<RdInvoiceDTO> invoiceList;
}
