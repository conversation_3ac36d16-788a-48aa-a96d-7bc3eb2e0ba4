package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TestDataObjectRelMapper {
    int countByExample(TestDataObjectRelExample example);

    int deleteByExample(TestDataObjectRelExample example);

    int deleteByPrimaryKey(String id);

    int insert(TestDataObjectRelPO record);

    int insertSelective(TestDataObjectRelPO record);

    List<TestDataObjectRelPO> selectByExample(TestDataObjectRelExample example);

    TestDataObjectRelPO selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") TestDataObjectRelPO record, @Param("example") TestDataObjectRelExample example);

    int updateByExample(@Param("record") TestDataObjectRelPO record, @Param("example") TestDataObjectRelExample example);

    int updateByPrimaryKeySelective(TestDataObjectRelPO record);

    int updateByPrimaryKey(TestDataObjectRelPO record);

    int batchInsert(List<TestDataObjectRelPO> list);

    int batchUpdate(List<TestDataObjectRelPO> list);
}