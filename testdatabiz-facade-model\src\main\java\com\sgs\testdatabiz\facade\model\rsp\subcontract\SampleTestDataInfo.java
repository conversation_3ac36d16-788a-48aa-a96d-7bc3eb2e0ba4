package com.sgs.testdatabiz.facade.model.rsp.subcontract;

import com.sgs.framework.core.common.PrintFriendliness;

public class SampleTestDataInfo extends PrintFriendliness {

    private String sampleNo;

    private String testValue;

    private Integer sampleSeq;

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public Integer getSampleSeq() {
        return sampleSeq;
    }

    public void setSampleSeq(Integer sampleSeq) {
        this.sampleSeq = sampleSeq;
    }
}
