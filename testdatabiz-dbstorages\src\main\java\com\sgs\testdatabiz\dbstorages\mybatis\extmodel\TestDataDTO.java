package com.sgs.testdatabiz.dbstorages.mybatis.extmodel;

import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;

import java.util.List;


public class TestDataDTO extends PrintFriendliness {

    private List<TestDataObjectRelPO> testDataObjectRels;

    private List<TestDataMatrixInfoPO> testDataMatrixInfos;

    private List<TestDataInfoPO> testDataInfos;

    private String testDataSuffix;

    private TestDataObjectRelDTO objectRelDTO;

    private TestDataObjectRelPO testDataObjectRelPO;

    public List<TestDataObjectRelPO> getTestDataObjectRels() {
        return testDataObjectRels;
    }

    public void setTestDataObjectRels(List<TestDataObjectRelPO> testDataObjectRels) {
        this.testDataObjectRels = testDataObjectRels;
    }

    public List<TestDataMatrixInfoPO> getTestDataMatrixInfos() {
        return testDataMatrixInfos;
    }

    public void setTestDataMatrixInfos(List<TestDataMatrixInfoPO> testDataMatrixInfos) {
        this.testDataMatrixInfos = testDataMatrixInfos;
    }

    public TestDataObjectRelPO getTestDataObjectRelPO() {
        return testDataObjectRelPO;
    }

    public void setTestDataObjectRelPO(TestDataObjectRelPO testDataObjectRelPO) {
        this.testDataObjectRelPO = testDataObjectRelPO;
    }

    public List<TestDataInfoPO> getTestDataInfos() {
        return testDataInfos;
    }

    public void setTestDataInfos(List<TestDataInfoPO> testDataInfos) {
        this.testDataInfos = testDataInfos;
    }

    public String getTestDataSuffix() {
        return testDataSuffix;
    }

    public void setTestDataSuffix(String testDataSuffix) {
        this.testDataSuffix = testDataSuffix;
    }

    public TestDataObjectRelDTO getObjectRelDTO() {
        return objectRelDTO;
    }

    public void setObjectRelDTO(TestDataObjectRelDTO objectRelDTO) {
        this.objectRelDTO = objectRelDTO;
    }
}
