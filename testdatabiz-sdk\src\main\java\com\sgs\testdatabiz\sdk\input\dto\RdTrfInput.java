/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTrfInput implements Serializable {
    private Integer refSystemId;
    private String trfNo;
    private Integer serviceLevel;
    private String trfTemplateOwner;
    private String trfTemplateId;
    private String trfTemplateName;
    private Date trfSubmissionDate;
    private Date serviceStartDate;
    private Date serviceEndDate;
    private Integer interfaceExclude;
    private String source;

    // add 20230529
    private List<RdOrderRelInput> orderList;


    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
