package com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.OrderIdRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataGroupDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMappingDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataOldDTO;
import com.sgs.testdatabiz.facade.model.info.starlims.ColumnInfo;
import com.sgs.testdatabiz.facade.model.req.TestDataConfigReq;
import com.sgs.testdatabiz.facade.model.rsp.TestDataConfigRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ConfigExtMapper {

    Integer checkTable(@Param("tableName") String tableName);

    Integer createTestDataConfigTable(@Param("sourceName") String sourceName, @Param("newTableName") String newTableName);


    List<String> getTableInfoList(@Param("tableName") String tableName);

    List<ColumnInfo> getColumnInfoList(String masterTable);

    int alterTableColumn(ColumnInfo column);

    /**
     * 获取所有索引
     * @param tableName
     * @return
     */
    List<String> getAllIndexNameFromTableName(@Param("tableName") String tableName);

    /**
     *
     * @param tableName
     * @param idxName
     * @return
     */
    List<String> getAllIndexFromTableName(@Param("tableName") String tableName, @Param("idxName") String idxName);

    List<String> findIndexFromTableName(@Param("tableName") String tableName, @Param("idxName") String idxName);

    /**
     *
     * @param tableName
     * @param idxName
     * @param indexList
     * @return
     */
    int commonCreatIndex(@Param("tableName") String tableName, @Param("idxName") String idxName, @Param("list")List<String> indexList);


    List<TestDataMappingDTO> queryTestLineMappingByLabCode(@Param("labCode") String labCode);


    List<OrderIdRelDTO> queryTestDataGroup(@Param("id") Long id, @Param("suffix") String suffix, @Param("createDate") String createDate, @Param("endDate") String endDate, @Param("orderNo") String orderNo);

    /**
     *
     * @param orderNos
     * @param suffix
     * @return
     */
    List<TestDataOldDTO> queryTestData(@Param("orderNos") List<String> orderNos, @Param("suffix") String suffix);

    List<TestDataConfigRsp> queryTestDataConfig(@Param("req") TestDataConfigReq req);

}
