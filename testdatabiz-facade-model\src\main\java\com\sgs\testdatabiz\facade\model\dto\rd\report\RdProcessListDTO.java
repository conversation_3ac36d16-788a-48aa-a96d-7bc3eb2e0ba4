/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RdProcessListDTO implements Serializable{

    private RdProcessDTO create;

    private RdProcessDTO generate;

    private RdProcessDTO confirm;

    private RdProcessDTO delivery;

    private RdProcessDTO sampleReceive;

    private RdProcessDTO testingStart;

    private RdProcessDTO testingEnd;

    private RdProcessDTO cancel;
}
