package com.sgs.testdatabiz.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class StarLimsConfig {
    /**
     *
     */
    @Value("${starLims.url}")
    private String starLimsUrl;

    /**
     *
     */
    @Value("${starLims.authId}")
    private String starLimsAuthId;

    @Value("${starLims.header.user.key}")
    private String headerUserKey;

    @Value("${starLims.header.user.val}")
    private String headerUserVal;

    @Value("${starLims.header.pass.key}")
    private String headerPassKey;

    @Value("${starLims.header.pass.val}")
    private String headerPassVal;

    public String getStarLimsUrl() {
        return starLimsUrl;
    }

    public void setStarLimsUrl(String starLimsUrl) {
        this.starLimsUrl = starLimsUrl;
    }

    public String getStarLimsAuthId() {
        return starLimsAuthId;
    }

    public void setStarLimsAuthId(String starLimsAuthId) {
        this.starLimsAuthId = starLimsAuthId;
    }

    public String getHeaderUserKey() {
        return headerUserKey;
    }

    public void setHeaderUserKey(String headerUserKey) {
        this.headerUserKey = headerUserKey;
    }

    public String getHeaderUserVal() {
        return headerUserVal;
    }

    public void setHeaderUserVal(String headerUserVal) {
        this.headerUserVal = headerUserVal;
    }

    public String getHeaderPassKey() {
        return headerPassKey;
    }

    public void setHeaderPassKey(String headerPassKey) {
        this.headerPassKey = headerPassKey;
    }

    public String getHeaderPassVal() {
        return headerPassVal;
    }

    public void setHeaderPassVal(String headerPassVal) {
        this.headerPassVal = headerPassVal;
    }
}
