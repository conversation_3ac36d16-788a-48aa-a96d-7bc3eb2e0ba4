package com.sgs.testdatabiz.facade.model.info;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.testdatabiz.facade.model.testdata.TestDataConditionInfo;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

public class TestDataInfo extends PrintFriendliness {
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("多语言（1：英文、2：中文")
    private Integer languageId;
    @ApiModelProperty("分包单号/JobNos")
    private String objectNo;
    @ApiModelProperty("slimJobNo")
    private String externalNo;
    @ApiModelProperty("外部号")
    private String externalCode;
    @ApiModelProperty("完成时间")
    private Date completedDate;
    @ApiModelProperty("sampleNo")
    private String sampleNo;

    @ApiModelProperty("testDataMatrixId")
    private Long testDataMatrixId;

    @ApiModelProperty("testLineMappingId")
    private Integer testLineMappingId;

    @ApiModelProperty("matrixConclusionId")
    private Integer matrixConclusionId;

    @ApiModelProperty("conclusionDisplay")
    private String conclusionDisplay;

    @ApiModelProperty("外部 SampleNo")
    private String externalSampleNo;
    @ApiModelProperty("ReportLimit")
    private String reportLimit;
    @ApiModelProperty("analyteCode")
    private String analyteCode;
    @ApiModelProperty("analyteType；0：General、1：Conclusion")
    private String analyteType;
    @ApiModelProperty("extFields")
    private String extFields;
    @ApiModelProperty("condition")
    private String condition;
    @ApiModelProperty("testConditions")
    private List<TestDataConditionInfo> testConditions;
    @ApiModelProperty("TestAnalyteName")
    private String testAnalyteName;
    @ApiModelProperty("TestAnalyteName 中文")
    private String testAnalyteNameCN;
    @ApiModelProperty("reportUnit")
    private String reportUnit;
    @ApiModelProperty("reportUnitCN 中文")
    private String reportUnitCN;
    @ApiModelProperty("TestValue")
    private String testValue;
    @ApiModelProperty("analyte排序字段 analyteSeq")
    private String analyteSeq;
    @ApiModelProperty("MaterialName")
    private String materialName;
    @ApiModelProperty("MaterialTexture")
    private String materialTexture;
    @ApiModelProperty("UsedPosition")
    private String usedPosition;
    @ApiModelProperty("MaterialColor")
    private String materialColor;
    @ApiModelProperty("testLineId")
    private Integer testLineId;
    @ApiModelProperty("citationId")
    private Integer citationId;
    @ApiModelProperty("testDataLanguages")
    private String testDataLanguages;
    @ApiModelProperty("sourceType; 1:slim   2:fast")
    private Integer sourceType;

    private Integer ppNo;

    private Integer ppVersionId;

    private Long testLineSeq;

    @JsonIgnore
    private Integer testConditionId;
    @JsonIgnore
    private String testConditionDesc;
    @JsonIgnore
    private Integer testConditionSeq;

    // add 231114
    private String excludeCustomerInterface;

    private List<TestDataMatrixExtFieldLangInfo> languageList;

    public List<TestDataMatrixExtFieldLangInfo> getLanguageList() {
        return languageList;
    }

    public String getExcludeCustomerInterface() {
        return excludeCustomerInterface;
    }

    public void setExcludeCustomerInterface(String excludeCustomerInterface) {
        this.excludeCustomerInterface = excludeCustomerInterface;
    }

    public void setLanguageList(List<TestDataMatrixExtFieldLangInfo> languageList) {
        this.languageList = languageList;
    }

    public Integer getTestConditionId() {
        return testConditionId;
    }

    public void setTestConditionId(Integer testConditionId) {
        this.testConditionId = testConditionId;
    }

    public String getTestConditionDesc() {
        return testConditionDesc;
    }

    public void setTestConditionDesc(String testConditionDesc) {
        this.testConditionDesc = testConditionDesc;
    }

    public Integer getTestConditionSeq() {
        return testConditionSeq;
    }

    public void setTestConditionSeq(Integer testConditionSeq) {
        this.testConditionSeq = testConditionSeq;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public Long getTestDataMatrixId() {
        return testDataMatrixId;
    }

    public void setTestDataMatrixId(Long testDataMatrixId) {
        this.testDataMatrixId = testDataMatrixId;
    }

    public Integer getTestLineMappingId() {
        return testLineMappingId;
    }

    public void setTestLineMappingId(Integer testLineMappingId) {
        this.testLineMappingId = testLineMappingId;
    }

    public Integer getMatrixConclusionId() {
        return matrixConclusionId;
    }

    public void setMatrixConclusionId(Integer matrixConclusionId) {
        this.matrixConclusionId = matrixConclusionId;
    }

    public String getConclusionDisplay() {
        return conclusionDisplay;
    }

    public void setConclusionDisplay(String conclusionDisplay) {
        this.conclusionDisplay = conclusionDisplay;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getAnalyteCode() {
        return analyteCode;
    }

    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode;
    }

    public String getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(String analyteType) {
        this.analyteType = analyteType;
    }

    public String getExtFields() {
        return extFields;
    }

    public void setExtFields(String extFields) {
        this.extFields = extFields;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public List<TestDataConditionInfo> getTestConditions() {
        return testConditions;
    }

    public void setTestConditions(List<TestDataConditionInfo> testConditions) {
        this.testConditions = testConditions;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getTestAnalyteNameCN() {
        return testAnalyteNameCN;
    }

    public void setTestAnalyteNameCN(String testAnalyteNameCN) {
        this.testAnalyteNameCN = testAnalyteNameCN;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportUnitCN() {
        return reportUnitCN;
    }

    public void setReportUnitCN(String reportUnitCN) {
        this.reportUnitCN = reportUnitCN;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public String getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(String analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public String getTestDataLanguages() {
        return testDataLanguages;
    }

    public void setTestDataLanguages(String testDataLanguages) {
        this.testDataLanguages = testDataLanguages;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getPpNo() {
        return ppNo;
    }

    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    public Long getTestLineSeq() {
        return testLineSeq;
    }

    public void setTestLineSeq(Long testLineSeq) {
        this.testLineSeq = testLineSeq;
    }
}
