package com.sgs.testdatabiz.integration;

import com.sgs.extsystem.facade.CustomerConfigFacade;
import com.sgs.extsystem.facade.model.customer.req.OrderNoReq;
import com.sgs.extsystem.facade.model.customer.rsp.CustomerConfigRsp;
import com.sgs.framework.core.base.BaseResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class CustomerConfClient {
    private static Logger logger = LoggerFactory.getLogger(CustomerConfClient.class);
    @Autowired
    private CustomerConfigFacade customerConfigFacade;

    /**
     *
     * @param orderNo
     * @param productLineCode
     * @return
     */
    public CustomerConfigRsp getCustomerConfigByOrderNo(String orderNo, String productLineCode){
        if(StringUtils.isBlank(orderNo)){
            logger.error("请求的orderNo不能为空");
            return null;
        }
        OrderNoReq req = new OrderNoReq();
        req.setOrderNo(orderNo);
        req.setProductLineCode(productLineCode);
        BaseResponse<CustomerConfigRsp> resp = customerConfigFacade.getCustomerConfigByOrderNo(req);
        if(resp.getStatus() != 200 || Objects.isNull(resp.getData())){
            return null;
        }
        return resp.getData();
    }

}
