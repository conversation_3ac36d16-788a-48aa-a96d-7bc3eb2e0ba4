/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdAnalyteDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestItemMappingDO implements Serializable {
    private String testLineInstanceId;
    private List<RdAnalyteDO> analyteList;
    private RdTestItemMappingExternalDO external;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
