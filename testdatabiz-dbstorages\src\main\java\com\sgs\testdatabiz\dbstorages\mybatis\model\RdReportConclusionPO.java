package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdReportConclusionPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 标识
     */
    private Long id;

    /**
     * lab_id BIGINT(19)<br>
     * Trims系统实验室标识
     */
    private Long labId;

    /**
     * order_no VARCHAR(50)<br>
     * 执行系统订单号
     */
    private String orderNo;

    /**
     * report_no VARCHAR(50)<br>
     * 执行系统报告号
     */
    private String reportNo;

    /**
     * conclusion_instance_id VARCHAR(50)<br>
     * 执行系统结论标识
     */
    private String conclusionInstanceId;

    /**
     * object_id VARCHAR(36)<br>
     * 基于Level分别对应 Report、Matrix 等ID
     */
    private String objectId;

    /**
     * pp_artifact_rel_id BIGINT(19)<br>
     * PP TL 关联标识
     */
    private Long ppArtifactRelId;

    /**
     * pp_sample_rel_id VARCHAR(50)<br>
     * PP Sample关联标识
     */
    private String ppSampleRelId;

    /**
     * section_id INTEGER(10)<br>
     * PP Section 标识
     */
    private Integer sectionId;

    /**
     * test_line_instance_id VARCHAR(50)<br>
     * 测试项实例标识
     */
    private String testLineInstanceId;

    /**
     * sample_instance_id VARCHAR(50)<br>
     * 测试样实例标识
     */
    private String sampleInstanceId;

    /**
     * conclusion_level_id INTEGER(10)<br>
     * 结论级别标识
     */
    private Integer conclusionLevelId;

    /**
     * conclusion_code VARCHAR(500)<br>
     * SGS 结论编码
     */
    private String conclusionCode;

    /**
     * customer_conclusion VARCHAR(4000)<br>
     * 客户结论
     */
    private String customerConclusion;

    /**
     * conclusion_remark VARCHAR(1000)<br>
     * 结论备注信息
     */
    private String conclusionRemark;

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 获得 Trims系统实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 设置 Trims系统实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 获得 执行系统订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 设置 执行系统订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * report_no VARCHAR(50)<br>
     * 获得 执行系统报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50)<br>
     * 设置 执行系统报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * conclusion_instance_id VARCHAR(50)<br>
     * 获得 执行系统结论标识
     */
    public String getConclusionInstanceId() {
        return conclusionInstanceId;
    }

    /**
     * conclusion_instance_id VARCHAR(50)<br>
     * 设置 执行系统结论标识
     */
    public void setConclusionInstanceId(String conclusionInstanceId) {
        this.conclusionInstanceId = conclusionInstanceId == null ? null : conclusionInstanceId.trim();
    }

    /**
     * object_id VARCHAR(36)<br>
     * 获得 基于Level分别对应 Report、Matrix 等ID
     */
    public String getObjectId() {
        return objectId;
    }

    /**
     * object_id VARCHAR(36)<br>
     * 设置 基于Level分别对应 Report、Matrix 等ID
     */
    public void setObjectId(String objectId) {
        this.objectId = objectId == null ? null : objectId.trim();
    }

    /**
     * pp_artifact_rel_id BIGINT(19)<br>
     * 获得 PP TL 关联标识
     */
    public Long getPpArtifactRelId() {
        return ppArtifactRelId;
    }

    /**
     * pp_artifact_rel_id BIGINT(19)<br>
     * 设置 PP TL 关联标识
     */
    public void setPpArtifactRelId(Long ppArtifactRelId) {
        this.ppArtifactRelId = ppArtifactRelId;
    }

    /**
     * pp_sample_rel_id VARCHAR(50)<br>
     * 获得 PP Sample关联标识
     */
    public String getPpSampleRelId() {
        return ppSampleRelId;
    }

    /**
     * pp_sample_rel_id VARCHAR(50)<br>
     * 设置 PP Sample关联标识
     */
    public void setPpSampleRelId(String ppSampleRelId) {
        this.ppSampleRelId = ppSampleRelId == null ? null : ppSampleRelId.trim();
    }

    /**
     * section_id INTEGER(10)<br>
     * 获得 PP Section 标识
     */
    public Integer getSectionId() {
        return sectionId;
    }

    /**
     * section_id INTEGER(10)<br>
     * 设置 PP Section 标识
     */
    public void setSectionId(Integer sectionId) {
        this.sectionId = sectionId;
    }

    /**
     * test_line_instance_id VARCHAR(50)<br>
     * 获得 测试项实例标识
     */
    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    /**
     * test_line_instance_id VARCHAR(50)<br>
     * 设置 测试项实例标识
     */
    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId == null ? null : testLineInstanceId.trim();
    }

    /**
     * sample_instance_id VARCHAR(50)<br>
     * 获得 测试样实例标识
     */
    public String getSampleInstanceId() {
        return sampleInstanceId;
    }

    /**
     * sample_instance_id VARCHAR(50)<br>
     * 设置 测试样实例标识
     */
    public void setSampleInstanceId(String sampleInstanceId) {
        this.sampleInstanceId = sampleInstanceId == null ? null : sampleInstanceId.trim();
    }

    /**
     * conclusion_level_id INTEGER(10)<br>
     * 获得 结论级别标识
     */
    public Integer getConclusionLevelId() {
        return conclusionLevelId;
    }

    /**
     * conclusion_level_id INTEGER(10)<br>
     * 设置 结论级别标识
     */
    public void setConclusionLevelId(Integer conclusionLevelId) {
        this.conclusionLevelId = conclusionLevelId;
    }

    /**
     * conclusion_code VARCHAR(500)<br>
     * 获得 SGS 结论编码
     */
    public String getConclusionCode() {
        return conclusionCode;
    }

    /**
     * conclusion_code VARCHAR(500)<br>
     * 设置 SGS 结论编码
     */
    public void setConclusionCode(String conclusionCode) {
        this.conclusionCode = conclusionCode == null ? null : conclusionCode.trim();
    }

    /**
     * customer_conclusion VARCHAR(4000)<br>
     * 获得 客户结论
     */
    public String getCustomerConclusion() {
        return customerConclusion;
    }

    /**
     * customer_conclusion VARCHAR(4000)<br>
     * 设置 客户结论
     */
    public void setCustomerConclusion(String customerConclusion) {
        this.customerConclusion = customerConclusion == null ? null : customerConclusion.trim();
    }

    /**
     * conclusion_remark VARCHAR(1000)<br>
     * 获得 结论备注信息
     */
    public String getConclusionRemark() {
        return conclusionRemark;
    }

    /**
     * conclusion_remark VARCHAR(1000)<br>
     * 设置 结论备注信息
     */
    public void setConclusionRemark(String conclusionRemark) {
        this.conclusionRemark = conclusionRemark == null ? null : conclusionRemark.trim();
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", labId=").append(labId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", conclusionInstanceId=").append(conclusionInstanceId);
        sb.append(", objectId=").append(objectId);
        sb.append(", ppArtifactRelId=").append(ppArtifactRelId);
        sb.append(", ppSampleRelId=").append(ppSampleRelId);
        sb.append(", sectionId=").append(sectionId);
        sb.append(", testLineInstanceId=").append(testLineInstanceId);
        sb.append(", sampleInstanceId=").append(sampleInstanceId);
        sb.append(", conclusionLevelId=").append(conclusionLevelId);
        sb.append(", conclusionCode=").append(conclusionCode);
        sb.append(", customerConclusion=").append(customerConclusion);
        sb.append(", conclusionRemark=").append(conclusionRemark);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}