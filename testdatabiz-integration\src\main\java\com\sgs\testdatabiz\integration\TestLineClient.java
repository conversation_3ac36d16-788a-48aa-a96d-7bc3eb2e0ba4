package com.sgs.testdatabiz.integration;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.otsnotes.facade.TestLineFacade;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.req.testLine.GetSubContractTestLineReq;
import com.sgs.otsnotes.facade.model.rsp.testLine.GetSubContractTestLineRsp;
import com.sgs.testdatabiz.core.constant.ApiUrlConstants;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TestLineClient {

    private static final Logger logger = LoggerFactory.getLogger(TestLineClient.class);

    @Value("${api.base.otsnotes}")
    private String otsnotesBaseUrl;

    @Autowired
    private TokenClient tokenClient;

    public List<GetSubContractTestLineRsp> getSubContractTestLine(String subContractId, String orderNo) {
        try {
            String url = otsnotesBaseUrl + ApiUrlConstants.OtsNotes.TestLine.GET_SUBCONTRACT_TEST_LINE;
            
            GetSubContractTestLineReq reqObject = new GetSubContractTestLineReq();
            reqObject.setSubContractId(subContractId);
            reqObject.setOrderNo(orderNo);
            reqObject.setAllflag(0);
            reqObject.setToken(tokenClient.getToken());

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("token", tokenClient.getToken());
            
            String response = HttpClientUtil.postJsonHeader(url, reqObject, headers);
            
            if (Func.isEmpty(response)) {
                logger.error("TestLineClient.getSubContractTestLine error, response is empty");
                return Lists.newArrayList();
            }
            
            BaseResponse<List<GetSubContractTestLineRsp>> baseResponse = 
                JSONObject.parseObject(response, new TypeReference<BaseResponse<List<GetSubContractTestLineRsp>>>(){});
                
            if (baseResponse == null || baseResponse.getStatus() != 200) {
                logger.error("TestLineClient.getSubContractTestLine error, response:{}", response);
                return Lists.newArrayList();
            }
            
            return baseResponse.getData();
            
        } catch (Exception e) {
            logger.error("TestLineClient.getSubContractTestLine exception", e);
            return Lists.newArrayList();
        }
    }
}
