package com.sgs.testdatabiz.facade.model.testdata;

import com.google.common.collect.Sets;
import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class TestDataTestMatrixInfo extends PrintFriendliness {
    /**
     *
     */
    @ApiModelProperty("测试项Id")
    private String testMatrixId;

    /**
     *
     */
    @ApiModelProperty("测试包下测试项Id")
    private Long aid;

    /**
     *
     */
    @ApiModelProperty("测试包编号")
    private Integer ppNo;

    /**
     *
     */
    @ApiModelProperty("测试包版本Id")
    private Integer ppVersionId;

    /**
     *
     */
    @ApiModelProperty("测试样品Id 取值规则： \n \n " +
            "   StarLims：取externalId  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String testSampleId;
    /**
     * testLineInstanceId SCI-1378
     */
    private String testLineInstanceId;

    /**
     * matrixSource SCI-1378
     */
    @ApiModelProperty("测点的来源 取值规则： \n \n " +
            "   StarLims  \n "+
            "   ExecSystem  \n "+
            "")
    private String matrixSource;
    /**
     *
     */
    @ApiModelProperty("测试样品编号 取值规则： \n \n " +
            "   StarLims：取materialNumber  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String testSampleNo;

    /**
     * slim Code（SHA22-000036-01）
     */
    @ApiModelProperty("externalCode 取值规则： \n \n " +
            "   SLIM： SLIM Code \n "+
            "")
    private String externalCode;

    /**
     *
     */
    @ApiModelProperty("测试项Id")
    private Integer testLineId;

    /**
     *
     */
    @ApiModelProperty("测试项版本Id")
    private Integer testLineVersionId;

    /**
     *
     */
    @ApiModelProperty("测试标准Id")
    private Integer citationId;

    /**
     *
     */
    @ApiModelProperty("测试标准版本Id")
    private Integer citationVersionId;

    /**
     * 0：None、1：Method、2：Regulation、3：Standard
     */
    @ApiModelProperty("测试项类型（0：None、1：Method、2：Regulation、3：Standard）")
    private Integer citationType;

    /**
     *
     */
    @ApiModelProperty("测试项名称")
    private String citationName;

    /**
     *
     */
    @ApiModelProperty("External Id，目前该值只有SLIM场景有值，对应的值是id")
    private String externalId;

    /**
     *
     */
    @ApiModelProperty("外部样品编号 取值规则： \n \n " +
            "   StarLims： sampleNo \n "+
            "   SLIM： \n "+
            "   FAST：  \n "+
            "")
    private String externalSampleNo;

    /**
     *
     */
    @ApiModelProperty("外部识别码（SLIM特有）")
    private String externalident;

    /**
     *
     */
    @ApiModelProperty("材质名称")
    private String materialName;

    /**
     *
     */
    @ApiModelProperty("材质纹理")
    private String materialTexture;

    /**
     *
     */
    @ApiModelProperty("使用位置")
    private String usedPosition;

    /**
     *
     */
    @ApiModelProperty("材质颜色")
    private String materialColor;

    /**
     *
     */
    @ApiModelProperty("testConditions")
    private List<TestDataConditionInfo> testConditions;

    /**
     *
     */
    @ApiModelProperty("测试排序 取值规则： \n \n " +
            "   StarLims： testLineSeq \n "+
            "   SLIM： \n "+
            "   FAST：  \n "+
            "")
    private Long testLineSeq;

    /**
     *
     */
    @ApiModelProperty("样品排序")
    private String sampleSeq;

    /**
     *
     */
    @ApiModelProperty("evaluationAlias 取值规则： \n \n " +
            "   StarLims： testReportName \n "+
            "   SLIM： \n "+
            "   FAST：  \n "+
            "")
    private String evaluationAlias;

    /**
     *
     */
    @ApiModelProperty("测试方法描述 取值规则： \n \n " +
            "   StarLims：取testMethod  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String methodDesc;

    /**
     *
     */
    @ApiModelProperty("测试结论Id")
    private String conclusionId;

    /**
     *
     */
    @ApiModelProperty("测试结论别名 取值规则： \n \n " +
            "   StarLims：取conclusionAlias  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String conclusionDisplay;

    /**
     *
     */
    @ApiModelProperty("testLineMappingId")
    private Integer testLineMappingId;

    /**
     *
     */
    @ApiModelProperty("appFactorId")
    private Integer appFactorId;

    /**
     *
     */
    @ApiModelProperty("appFactorName")
    private String appFactorName;

    /**
     *
     */
    @ApiModelProperty("测试结果")
    private List<TestDataResultInfo> testResults;

    /**
     *
     */
    @ApiModelProperty("多语言")
    private List<TestDataTestMatrixLangInfo> languages;

    /**
     * 回传的数据与订单数据无法Mapping
     */
    @ApiModelProperty("禁用Matrix")
    private boolean disableMatrix;

    //SCI-1378
    @ApiModelProperty("分享的SampleNo")
    private String referFromSampleNo;
    //SCI-1378
    @ApiModelProperty("分享的ReportNo")
    private List<String> referFromReportNo;

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }

    public String getMatrixSource() {
        return matrixSource;
    }

    public void setMatrixSource(String matrixSource) {
        this.matrixSource = matrixSource;
    }

    public String getReferFromSampleNo() {
        return referFromSampleNo;
    }

    public void setReferFromSampleNo(String referFromSampleNo) {
        this.referFromSampleNo = referFromSampleNo;
    }

    public List<String> getReferFromReportNo() {
        return referFromReportNo;
    }

    public void setReferFromReportNo(List<String> referFromReportNo) {
        this.referFromReportNo = referFromReportNo;
    }

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public Long getAid() {
        return aid;
    }

    public void setAid(Long aid) {
        this.aid = aid;
    }

    public Integer getPpNo() {
        return ppNo;
    }

    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    public String getTestSampleId() {
        return testSampleId;
    }

    public void setTestSampleId(String testSampleId) {
        this.testSampleId = testSampleId;
    }

    public String getTestSampleNo() {
        return testSampleNo;
    }

    public void setTestSampleNo(String testSampleNo) {
        this.testSampleNo = testSampleNo;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getTestLineVersionId() {
        return testLineVersionId;
    }

    public void setTestLineVersionId(Integer testLineVersionId) {
        this.testLineVersionId = testLineVersionId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public Integer getCitationVersionId() {
        return citationVersionId;
    }

    public void setCitationVersionId(Integer citationVersionId) {
        this.citationVersionId = citationVersionId;
    }

    public Integer getCitationType() {
        return citationType;
    }

    public void setCitationType(Integer citationType) {
        this.citationType = citationType;
    }

    public String getCitationName() {
        return citationName;
    }

    public void setCitationName(String citationName) {
        this.citationName = citationName;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public String getExternalident() {
        return externalident;
    }

    public void setExternalident(String externalident) {
        this.externalident = externalident;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public List<TestDataConditionInfo> getTestConditions() {
        return testConditions;
    }

    public void setTestConditions(List<TestDataConditionInfo> testConditions) {
        this.testConditions = testConditions;
    }

    public Long getTestLineSeq() {
        return testLineSeq;
    }

    public void setTestLineSeq(Long testLineSeq) {
        this.testLineSeq = testLineSeq;
    }

    public String getSampleSeq() {
        return sampleSeq;
    }

    public void setSampleSeq(String sampleSeq) {
        this.sampleSeq = sampleSeq;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getMethodDesc() {
        return methodDesc;
    }

    public void setMethodDesc(String methodDesc) {
        this.methodDesc = methodDesc;
    }

    public String getConclusionId() {
        return conclusionId;
    }

    public void setConclusionId(String conclusionId) {
        this.conclusionId = conclusionId;
    }

    public String getConclusionDisplay() {
        return conclusionDisplay;
    }

    public void setConclusionDisplay(String conclusionDisplay) {
        this.conclusionDisplay = conclusionDisplay;
    }

    public Integer getTestLineMappingId() {
        return testLineMappingId;
    }

    public void setTestLineMappingId(Integer testLineMappingId) {
        this.testLineMappingId = testLineMappingId;
    }

    public Integer getAppFactorId() {
        return appFactorId;
    }

    public void setAppFactorId(Integer appFactorId) {
        this.appFactorId = appFactorId;
    }

    public String getAppFactorName() {
        return appFactorName;
    }

    public void setAppFactorName(String appFactorName) {
        this.appFactorName = appFactorName;
    }

    public List<TestDataResultInfo> getTestResults() {
        return testResults;
    }

    public void setTestResults(List<TestDataResultInfo> testResults) {
        this.testResults = testResults;
    }

    public List<TestDataTestMatrixLangInfo> getLanguages() {
        return languages;
    }

    public void setLanguages(List<TestDataTestMatrixLangInfo> languages) {
        this.languages = languages;
    }

    public boolean isDisableMatrix() {
        return disableMatrix;
    }

    public void setDisableMatrix(boolean disableMatrix) {
        this.disableMatrix = disableMatrix;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((aid == null) ? 0 : aid.hashCode());
        result = prime * result + ((externalCode == null) ? 0 : externalCode.hashCode());
        result = prime * result + ((appFactorId == null) ? 0 : appFactorId.hashCode());
        Set<Integer> testConditionIds = Sets.newHashSet();
        if (testConditions != null){
            testConditionIds = testConditions.stream().map(tc -> NumberUtils.toInt(String.valueOf(tc.getTestConditionId()))).collect(Collectors.toSet());
        }
        result = prime * result + ((testConditionIds == null) ? 0 : testConditionIds.hashCode());
        result = prime * result + ((testSampleId == null) ? 0 : testSampleId.hashCode());
        result = prime * result + ((testSampleNo == null) ? 0 : testSampleNo.hashCode());
        result = prime * result + ((testLineId == null) ? 0 : testLineId.hashCode());
        result = prime * result + ((citationId == null) ? 0 : citationId.hashCode());
        result = prime * result + ((citationVersionId == null) ? 0 : citationVersionId.hashCode());
        result = prime * result + ((citationType == null) ? 0 : citationType.hashCode());
        return result;
    }
}
