package com.sgs.testdatabiz.facade.impl.exportReportData.cmd;

import com.alibaba.cola.extension.Extension;
import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.domain.service.rd.ReportDataService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.facade.impl.exportReportData.ExportReportDataActionExtPt;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
@Extension
public class NormalExportReportDataActionExtPt implements ExportReportDataActionExtPt {

    @Autowired
    private ReportDataService reportDataService;

    @Override
    public ReportDataDTO exportReportData(@NotNull Long labId, @NotEmpty String reportNo, @NotEmpty String labCode) {
        ReportDataDO reportDataDO = reportDataService.exportReportData(labId, reportNo,labCode);
        String jsonString = JSONObject.toJSONString(reportDataDO);
        ReportDataDTO convert = JSONObject.parseObject(jsonString, ReportDataDTO.class);
        return convert;
    }

    @Override
    public List<ReportDataDTO> exportReportDataByTrfNo(@NotNull Long labId, @NotEmpty String trfNo, @NotEmpty String labCode) {
        return buildReportDataDTO(reportDataService.exportReportDataByTrfNo(labId, trfNo,labCode));
    }

    @Override
    public List<ReportDataDTO> exportReportDataList(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode) {
        List<ReportDataDO> reportDataDOS = reportDataService.exportReportDataList(labId, reportNos,labCode);
        return buildReportDataDTO(reportDataDOS);
    }

    private List<ReportDataDTO> buildReportDataDTO(List<ReportDataDO> reportDataDOS) {
        List<ReportDataDTO> list = new ArrayList<>();
        if (Func.isNotEmpty(reportDataDOS)) {
            reportDataDOS.forEach(
                    reportDataDO -> {
//                        ReportDataDTO convert = ReportDataConvertor.convert(reportDataDO);

                        String jsonString = JSONObject.toJSONString(reportDataDO);
                        ReportDataDTO convert = JSONObject.parseObject(jsonString, ReportDataDTO.class);

                        if (Func.isNotEmpty(convert)) {
                            list.add(convert);
                        }
                    }
            );
        }
        return list;
    }
}
