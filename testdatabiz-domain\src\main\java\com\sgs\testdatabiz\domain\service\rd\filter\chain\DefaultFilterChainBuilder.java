package com.sgs.testdatabiz.domain.service.rd.filter.chain;

import com.sgs.testdatabiz.domain.service.rd.filter.ReportDataFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Comparator;
import java.util.stream.Collectors;

@Component
public class DefaultFilterChainBuilder implements FilterChainBuilder {
    
    @Autowired
    private List<ReportDataFilter> allFilters;
    
    @Override
    public List<ReportDataFilter> buildFilters() {
        return allFilters.stream()
            .filter(filter -> filter.getClass().isAnnotationPresent(DefaultFilter.class))
            .sorted(Comparator.comparingInt(filter -> 
                filter.getClass().getAnnotation(DefaultFilter.class).order()))
            .collect(Collectors.toList());
    }
} 