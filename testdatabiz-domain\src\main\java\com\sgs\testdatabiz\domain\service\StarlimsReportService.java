package com.sgs.testdatabiz.domain.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.CitationType;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.testdatabiz.core.annotation.SourceType;
import com.sgs.testdatabiz.core.enums.ConclusionType;
import com.sgs.testdatabiz.core.util.*;
import com.sgs.testdatabiz.facade.model.enums.AnalyteTypeEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataObjectRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.info.starlims.StarLimsRelData;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataLangInfo;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataMatrixLangInfo;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataObjectRelInfo;
import com.sgs.testdatabiz.facade.model.req.starlims.*;
import com.sgs.testdatabiz.integration.StarlimsClient;
import com.sgs.testdatabiz.integration.TrimsLocalClient;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRsp;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
@SourceType(sourceType = SourceTypeEnum.STARLIMS)
public class StarlimsReportService extends AbstractTestDataService<ReceiveStarLimsReportReq> {
    private static final Logger logger = LoggerFactory.getLogger(StarlimsReportService.class);
    @Autowired
    private TestDataObjectRelExtMapper testDataReportObjectRelExtMapper;
    @Autowired
    private TestDataMatrixInfoExtMapper testDataReportMatrixExtMapper;
    @Autowired
    private TestDataInfoExtMapper testDataReporExtMapper;
    @Autowired
    private TrimsLocalClient trimsLocalClient;
    @Autowired
    private SnowflakeIdWorker idWorker;
    @Autowired
    private StarlimsClient starlimsClient;

    /**
     * @param reqObject
     * @return
     */
    @Override
    protected CustomResult doInvoke(ReceiveStarLimsReportReq reqObject) {
        String suffix = StringUtil.getTestDataSuffix(reqObject.getLabCode());
        CustomResult<TestDataDTO> rspResult = new CustomResult();
        List<ReceiveReportData> reportDatas = reqObject.getReportDatas();
        if (CollectionUtils.isEmpty(reportDatas)) {
            return rspResult.fail("PLEASE CHECK PARAMETER!");
        }
        //？
        Set<Long> aids = reportDatas.stream().map(ReceiveReportData::getaId).collect(Collectors.toSet());
        List<PpArtifactRsp> ppArtifacts = trimsLocalClient.getPpArtifactRelListByArtifactIds(aids);
        if (ppArtifacts == null) {
            ppArtifacts = Lists.newArrayList();
        }
        Map<Long, PpArtifactRsp> ppArtifactMaps = Maps.newHashMap();
        ppArtifacts.forEach(ppArtifact -> {
            ppArtifactMaps.put(ppArtifact.getArtifactId(), ppArtifact);
        });
        List<TestDataObjectRelPO> objectRels = Lists.newArrayList();
        List<TestDataMatrixInfoPO> testDataMatrixs = Lists.newArrayList();
        List<TestDataInfoPO> testDatas = Lists.newArrayList();

        TestDataObjectRelPO objectRel = this.getTestDataObjectRelInfo(reqObject);

        TestDataObjectRelPO rel = new TestDataObjectRelPO();
        rel.setReportNo(reqObject.getReportNo());
        rel.setObjectNo(reqObject.getSubContractNo());
        rel.setExternalNo(reqObject.getExternalNo());
        rel.setSourceType(SourceTypeEnum.STARLIMS.getCode());
        rel.setCompleteDate(reqObject.getCompletedDate());
        if (StringUtils.isNotBlank(reqObject.getOriginalReportNo())) {
            rel.setExternalObjectNo(reqObject.getOriginalReportNo());
            TestDataObjectRelPO originalObjectRel = testDataReportObjectRelExtMapper.getReportObjectRelInfo(rel);
            if (originalObjectRel != null) {
                //置为无效，更新DB
                originalObjectRel.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
                objectRels.add(originalObjectRel);
            }
        }
        rel.setExternalObjectNo(reqObject.getExternalObjectNo());
        TestDataObjectRelPO oldObjectRel = testDataReportObjectRelExtMapper.getReportObjectRelInfo(rel);
        if (oldObjectRel != null) {
            objectRel.setId(oldObjectRel.getId());
        }
        objectRels.add(objectRel);

        Map<String, TestDataMatrixInfoPO> testDataMatrixMaps = Maps.newHashMap();
        testDataReportMatrixExtMapper.queryMatrix(objectRel.getId(), suffix).forEach(testDataMatrix -> {
            testDataMatrixMaps.put(testDataMatrix.getBizVersionId(), testDataMatrix);
        });

        Map<String, TestDataInfoPO> testDataMaps = Maps.newHashMap();
        testDataReporExtMapper.queryTestDataInfo(objectRel.getId(), suffix).forEach(testData -> {
            testDataMaps.put(testData.getBizVersionId(), testData);
        });

        for (ReceiveReportData reportData : reportDatas) {
            TestDataMatrixInfoPO testDataMatrix = this.getTestDataMatrixInfo(reportData, objectRel.getId(), ppArtifactMaps);
            TestDataMatrixInfoPO oldTestDataMatrix = testDataMatrixMaps.get(testDataMatrix.getBizVersionId());
            if (oldTestDataMatrix != null) {
                Long testDataMatrixId = oldTestDataMatrix.getId();
                testDataMatrix.setId(testDataMatrixId);
                testDataMatrixMaps.remove(oldTestDataMatrix.getBizVersionId());
            }
            testDataMatrixs.add(testDataMatrix);
            List<ReceiveTestResult> testResults = reportData.getTestResults();
            if (testResults == null) {
                testResults = Lists.newArrayList();
            }
            for (ReceiveTestResult testResult : testResults) {
                TestDataInfoPO testData = this.getTestDataInfo(testResult, objectRel.getId(), testDataMatrix.getId());
                if (testData == null) {
                    continue;
                }
                TestDataInfoPO oldTestData = testDataMaps.get(testData.getBizVersionId());
                if (oldTestData != null) {
                    Long testDataId = oldTestData.getId();
                    testData.setId(testDataId);
                    testDataMaps.remove(oldTestData.getBizVersionId());
                }
                testDatas.add(testData);
            }
        }

        TestDataDTO testDataDTO = new TestDataDTO();
        testDataDTO.setTestDataInfos(testDatas);
        testDataDTO.setTestDataObjectRels(objectRels);
        testDataDTO.setTestDataMatrixInfos(testDataMatrixs);
        testDataDTO.setTestDataSuffix(StringUtil.getTestDataSuffix(reqObject.getLabCode()));
        TestDataObjectRelDTO objectRelDTO = new TestDataObjectRelDTO();
        objectRelDTO.setObjectNo(reqObject.getObjectNo());
        objectRelDTO.setReportNo(reqObject.getReportNo());
        objectRelDTO.setExternalObjectNo(rel.getExternalObjectNo());
        objectRelDTO.setModifiedDate(DateUtils.getNow());
        objectRelDTO.setModifiedBy(SourceTypeEnum.STARLIMS.getDesc());
        testDataDTO.setObjectRelDTO(objectRelDTO);
        return this.doDeal(testDataDTO);
    }

    /**
     * @param reqObject
     * @return
     */
    private TestDataObjectRelPO getTestDataObjectRelInfo(ReceiveStarLimsReportReq reqObject) {
        String regionAccount = SourceTypeEnum.STARLIMS.getDesc();
        TestDataObjectRelPO objectRel = new TestDataObjectRelPO();
        objectRel.setId(UUID.randomUUID().toString());
        objectRel.setProductLineCode(reqObject.getProductLineCode());
        objectRel.setOrderNo(reqObject.getOrderNo());
        objectRel.setParentOrderNo(reqObject.getParentOrderNo());
        objectRel.setReportNo(reqObject.getReportNo());
        objectRel.setObjectNo(reqObject.getSubContractNo());
        objectRel.setExternalId(reqObject.getExternalId());//外部系统主键Id.对方的外部系统主键id其实是我们自己的id，我们存储的外部系统主键id是对方系统主键id
        objectRel.setExternalNo(reqObject.getObjectNo());//当Starlims数据时，该值为folderNo。objectNo属性是starlims对应的字段FolderNo
        objectRel.setExternalObjectNo(reqObject.getExternalObjectNo());
        LanguageType languageType = LanguageType.findStarLimsCode(reqObject.getLanguageId());
        if (languageType != null) {
            objectRel.setLanguageId(languageType.getLanguageId());
        }
        objectRel.setCompleteDate(reqObject.getCompletedDate());
        objectRel.setSourceType(SourceTypeEnum.STARLIMS.getCode());//1、slim 2、job 3、starlims 4、fast 5、subcontract 6、new

        objectRel.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());//0: inactive, 1: active
        objectRel.setLabCode(reqObject.getLabCode());//必填 订单所在的Lab，不能从请求头里面sgstoken获取labcode，外部系统无法获取sgstoken
        objectRel.setBizVersionId(this.getTestDataReportObjectRelMd5(objectRel));
        objectRel.setCreatedBy(regionAccount);//写死
        objectRel.setCreatedDate(DateUtils.getNow());
        objectRel.setModifiedBy(regionAccount);
        objectRel.setModifiedDate(DateUtils.getNow());

        return objectRel;
    }

    /**
     * @param reportData
     * @param objectRelId
     * @param ppArtifactMaps
     * @return
     */
    private TestDataMatrixInfoPO getTestDataMatrixInfo(ReceiveReportData reportData, String objectRelId, Map<Long, PpArtifactRsp> ppArtifactMaps) {
        TestDataMatrixInfoPO testDataMatrix = new TestDataMatrixInfoPO();
        Long artifactId = NumberUtil.toLong(reportData.getaId());
        testDataMatrix.setAid(artifactId);
        testDataMatrix.setCitationVersionId(NumberUtil.toInt(reportData.getCitationExternalId()));
        testDataMatrix.setCitationType(CitationType.None.getType());
        PpArtifactRsp ppArtifact = ppArtifactMaps.get(artifactId);
        if (ppArtifact != null) {
            testDataMatrix.setCitationId(ppArtifact.getCitationId());
            testDataMatrix.setCitationVersionId(ppArtifact.getCitationVersionId());
            testDataMatrix.setCitationType(ppArtifact.getCitationType());
            testDataMatrix.setCitationName(ppArtifact.getCitationName());
        }
        testDataMatrix.setId(idWorker.nextId());
        testDataMatrix.setObjectRelId(objectRelId);
        testDataMatrix.setPpVersionId(NumberUtil.toInt(reportData.getPpVersionId()));
        testDataMatrix.setTestLineId(NumberUtil.toInt(reportData.getTestLineId()));
        testDataMatrix.setMethodDesc(Transcoding.rtfStrEsc(reportData.getTestMethod()));//对应Starlims字段：testMethod
        testDataMatrix.setSampleId(reportData.getExternalId());
        testDataMatrix.setSampleNo(reportData.getMaterialNumber());
        testDataMatrix.setExternalSampleNo(reportData.getSampleNo());
        //testDataMatrix.setSampleSeq(reportData.getTestSampleSeq());
        testDataMatrix.setTestLineSeq(reportData.getSorter());//1、对应Starlims字段：sorter
        //testDataMatrix.setExtFields(null);//示例{     "materialName":"C1+C2",     "materialTexture":"0.8cm塑料调节扣",     "usedPosition":"上身",     "materialColor":"黑色" }
        testDataMatrix.setEvaluationAlias(Transcoding.rtfStrEsc(reportData.getTestReportName()));

        testDataMatrix.setConclusionId(!StringUtils.isBlank(reportData.getConclusion()) ? ConclusionType.getEnumType(reportData.getConclusion()).getConclusion() : null);//1、这里需要Mapper转换成对应SODA的conclusion。conclusion属性对应的starlims 对应字段是testConclusion
        testDataMatrix.setConclusionDisplay(reportData.getConclusion());
        testDataMatrix.setExternalId(String.valueOf(reportData.getId()));
        List<ReceiveReportLanguages> languages = reportData.getLanguages();
        if (languages == null) {
            languages = Lists.newArrayList();
        }
        List<TestDataMatrixLangInfo> langs = Lists.newArrayList();
        for (ReceiveReportLanguages language : languages) {
            LanguageType languageType = LanguageType.findStarLimsCode(language.getLanguageId());
            if (languageType == null || languageType.getLanguageId() <= 0) {
                continue;
            }
            TestDataMatrixLangInfo lang = new TestDataMatrixLangInfo();
            lang.setLanguageId(languageType.getLanguageId());
            lang.setEvaluationAlias(Transcoding.rtfStrEsc(language.getTestReportName()));

            String conclusionAlias = language.getConclusionAlias();
            String unicode = Transcoding.unicodeToChar(conclusionAlias);
            if (StringUtil.isNotEmpty(unicode)) {
                lang.setConclusionDisplay(unicode);
            } else {
                lang.setConclusionDisplay(conclusionAlias);
            }
            lang.setMethodDesc(Transcoding.rtfStrEsc(language.getTestMethod()));

            langs.add(lang);
        }
        if (!langs.isEmpty()) {
            testDataMatrix.setLanguages(JSONObject.toJSONString(langs));
        }

        testDataMatrix.setBizVersionId(this.getTestDataReportMatrixMd5(testDataMatrix));
        testDataMatrix.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
        testDataMatrix.setCreatedBy(SourceTypeEnum.STARLIMS.getDesc());//写死
        testDataMatrix.setCreatedDate(DateUtils.getNow());
        testDataMatrix.setModifiedBy(SourceTypeEnum.STARLIMS.getDesc());
        testDataMatrix.setModifiedDate(DateUtils.getNow());
        return testDataMatrix;
    }

    /**
     * @param testResult
     * @param objectRelId
     * @param testDataMatrixId
     * @return
     */
    private TestDataInfoPO getTestDataInfo(ReceiveTestResult testResult, String objectRelId, Long testDataMatrixId) {
        if (testResult == null) {
            return null;
        }
        TestDataInfoPO testData = new TestDataInfoPO();

        testData.setId(idWorker.nextId());
        testData.setObjectRelId(objectRelId);
        testData.setTestDataMatrixId(testDataMatrixId);
        testData.setAnalyteId(String.valueOf(testResult.getId()));//reportDatas.testResults.analyteId
        testData.setAnalyteCode(testResult.getAnalyte());
        testData.setAnalyteName(testResult.getAnalyteAlias());

        testData.setAnalyteType(AnalyteTypeEnum.General.getType());// 定义枚举 0
        if (StringUtils.startsWithIgnoreCase(testResult.getAnalyte(), AnalyteTypeEnum.Conclusion.getText())) {
            testData.setAnalyteType(AnalyteTypeEnum.Conclusion.getType());
        }
        testData.setAnalyteSeq(NumberUtil.toInt(testResult.getSorter()));//reportDatas.testResults.analyteSeq
        testData.setReportUnit(testResult.getUnit());//reportDatas.testResults.reportUnit
        testData.setLimitUnit(testResult.getUnit());//reportDatas.testResults.reportUnit
        testData.setTestValue(testResult.getResult());//reportDatas.testResults.testValue
        testData.setCasNo(testResult.getCasNo());
        testData.setReportLimit(testResult.getLimit());//reportDatas.testResults.limit
        testData.setConclusionId(testResult.getConclusionId());//reportDatas.testResults.analyteConclusion

        testData.setBizVersionId(this.getTestDataReportMd5(testData));
        testData.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
        testData.setCreatedBy(SourceTypeEnum.STARLIMS.getDesc());
        testData.setCreatedDate(DateUtils.getNow());
        testData.setModifiedBy(SourceTypeEnum.STARLIMS.getDesc());
        testData.setModifiedDate(DateUtils.getNow());

        List<ReceiveTestLanguages> languages = testResult.getLanguages();
        if (languages == null) {
            languages = Lists.newArrayList();
        }
        List<TestDataLangInfo> langs = Lists.newArrayList();
        for (ReceiveTestLanguages language : languages) {
            LanguageType languageType = LanguageType.findStarLimsCode(language.getLanguageId());
            if (languageType == null || languageType.getLanguageId() <= 0) {
                continue;
            }
            TestDataLangInfo lang = new TestDataLangInfo();
            lang.setLanguageId(languageType.getLanguageId());
            //lang.setAnalyteAlias(language.getAnalyteName());
            String analyte = language.getAnalyteAlias();
            String analyteName = Transcoding.unicodeToChar(analyte);
            if (StringUtil.isNotEmpty(analyteName)) {
                lang.setTestAnalyteName(analyteName);
            } else {
                lang.setTestAnalyteName(analyte);
            }
            langs.add(lang);
        }
        if (!langs.isEmpty()) {
            testData.setLanguages(JSONObject.toJSONString(langs));
        }
        return testData;
    }

    /**
     * @param reqObject
     * @return
     */
    public CustomResult cleanStarlimsData(FolderReportInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        do {

            List<TestDataObjectRelInfo> objectRels = testDataReportObjectRelExtMapper.getStarlimsObjectRelList(reqObject);
            if (objectRels == null || objectRels.isEmpty()) {
                if (StringUtils.isBlank(reqObject.getFolderNo())) {
                    return rspResult.fail("StarLims 接口返回对象为空.");
                }
                TestDataObjectRelInfo objectRel = new TestDataObjectRelInfo();
                objectRel.setOrderNo(reqObject.getExternalId());
                objectRel.setProductLineCode(reqObject.getProductLineCode());
                objectRel.setObjectNo(reqObject.getExternalId());
                objectRel.setExternalNo(reqObject.getFolderNo());

                // DIG-8555  A "NullPointerException" could be thrown; "objectRels" is nullable here.
                objectRels = Lists.newArrayList();
                objectRels.add(objectRel);
            }
            // TODO 多线程处理
            for (TestDataObjectRelInfo objectRel : objectRels) {
                reqObject.setLastModifiedTime(objectRel.getLastModifiedTime());
                FolderReportInfoReq folderReport = new FolderReportInfoReq();
                folderReport.setExternalParentId(objectRel.getOrderNo());
                folderReport.setExternalId(objectRel.getObjectNo());
                folderReport.setFolderNo(objectRel.getExternalNo());

                /*folderReport.setExternalParentId("SL923092600403TX");
                folderReport.setExternalId("GZSL2360040301GZCCL");
                folderReport.setFolderNo("CAN23-0000441");*/

                FolderReportInfoRsp rspObject = starlimsClient.getFolderReportInfoList(folderReport);
                if (rspObject == null) {
                    if (StringUtils.isNotBlank(reqObject.getFolderNo())) {
                        return rspResult.fail("StarLims 接口返回对象为空.");
                    }
                    continue;
                }
                List<ReceiveStarLimsReportReq> folderReports = rspObject.getReport();
                if (folderReports == null || folderReports.isEmpty()) {
                    if (StringUtils.isNotBlank(reqObject.getFolderNo())) {
                        return rspResult.fail(String.format("StarLims未返回数据(%s_%s).", rspObject.getStatus(), rspObject.getDetails()));
                    }
                    continue;
                }

                folderReports.forEach(report -> {
                    report.setParentOrderNo(objectRel.getParentOrderNo());
                    report.setLabCode(objectRel.getLabCode());
                    report.setReportNo(objectRel.getReportNo());
                    report.setObjectNo(objectRel.getObjectNo());
                    report.setCompletedDate(objectRel.getCompleteDate());

                    CustomResult customResult = this.doInvoke(report);
                    if (customResult.isSuccess()) {

                    }
                });
            }
        } while (StringUtils.isBlank(reqObject.getFolderNo()));

        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult washStarlimsData(JSONObject reqObject) {
        CustomResult rspResult = new CustomResult();
        if (Func.isEmpty(reqObject) || Func.isEmpty(reqObject.get("labCode"))) {
            return rspResult.fail("labCode cannot null");
        }

        String suffix = StringUtil.getTestDataSuffix(reqObject.get("labCode").toString());
        List<StarLimsRelData> objectRels = testDataReportObjectRelExtMapper.getAbnormalStarlimsData(suffix);
        if (objectRels == null || objectRels.isEmpty()) {
            return rspResult.fail("当前Lab暂无需要修复的Starlims数据！");
        }
        for (StarLimsRelData objectRel : objectRels) {
            FolderReportInfoReq folderReport = new FolderReportInfoReq();
            folderReport.setExternalParentId(objectRel.getOrderNo());
            folderReport.setExternalId(objectRel.getObjectNo());
            folderReport.setFolderNo(objectRel.getExternalNo());

            FolderStarlimsReportInfoRsp rspObject = starlimsClient.getFolderReportInfoList(folderReport, reqObject.get("labCode").toString());
            if (rspObject == null) {
                continue;
            }
            List<FolderStarlimsReportRsp> folderReports = rspObject.getReport();
            if (folderReports == null || folderReports.isEmpty()) {
                continue;
            }

            folderReports.forEach(report -> {
                List<ReceiveReportData> reportDatas = report.getData();
                if (Func.isNotEmpty(reportDatas)) {
                    reportDatas = reportDatas.stream().filter(l -> Objects.equals(l.getId(), objectRel.getExternalId())).collect(Collectors.toList());
                    if (Func.isNotEmpty(reportDatas)) {
                        reportDatas.forEach(
                                reportData -> {
                                    String conclusion = reportData.getConclusion();
                                    String conclusionId = null;
                                    String condition = objectRel.getCondition();
                                    if (Func.isNotEmpty(condition) && Func.isNotEmpty(reportData.getCondition())) {
                                        JSONArray jsonArray = JSONArray.parseArray(condition);
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        Object testConditionIdObj = jsonObject.get("testConditionId");
                                        int testConditionId = NumberUtil.toInt(testConditionIdObj);
                                        List<ReceiveConditionInfoReq> reportDataCondition = reportData.getCondition();
                                        if (Objects.equals(testConditionId, reportDataCondition.get(0).getConditionId())) {
                                            conclusionId = ConclusionType.getEnumType(conclusion).getConclusion();
                                        }
                                    } else {
                                        conclusionId = ConclusionType.getEnumType(conclusion).getConclusion();
                                    }
                                    if (Func.isNotEmpty(conclusionId)) {
                                        testDataReportMatrixExtMapper.updateConclusionIdById(conclusionId, objectRel.getTestMatrixId(), suffix);
                                    }
                                }
                        );
                    }
                }


            });
        }
        rspResult.setSuccess(true);
        return rspResult;
    }
}
