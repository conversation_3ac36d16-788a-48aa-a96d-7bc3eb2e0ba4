package com.sgs.testdatabiz.web;

import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.KafkaMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;

import com.sgs.testdatabiz.facade.impl.prop.ReportDataBizProps;

/**
 *
 */
@SpringBootApplication(exclude={
    DataSourceAutoConfiguration.class
    ,KafkaAutoConfiguration.class
    ,KafkaMetricsAutoConfiguration.class
    ,RedissonAutoConfiguration.class
    ,RedisAutoConfiguration.class
    ,RedisRepositoriesAutoConfiguration.class
})
@ImportResource("classpath:applicationContext.xml")
@EnableAspectJAutoProxy
@EnableConfigurationProperties({
        ReportDataBizProps.class
})
@ComponentScan(basePackages = {"com.sgs.tools", "com.sgs.testdatabiz"})
public class TestDataBizApplication {
    public static void main(String[] args) {
        SpringApplication.run(TestDataBizApplication.class, args);
    }

//    /**
//     *
//     * @return
//     */
//    @Bean
//    public SnowflakeIdWorker snowflakeIdWorker() {
//        long dataCenterId = IdUtil.getDataCenterId(SnowflakeIdWorker.maxDatacenterId);
//        long workerId = IdUtil.getWorkerId(dataCenterId, SnowflakeIdWorker.maxWorkerId);
//        return new SnowflakeIdWorker(workerId,dataCenterId);
//    }
}
