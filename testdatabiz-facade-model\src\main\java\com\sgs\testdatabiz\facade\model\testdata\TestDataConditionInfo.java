package com.sgs.testdatabiz.facade.model.testdata;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 *
 */
public class TestDataConditionInfo extends PrintFriendliness {
    /**
     *
     */
    @ApiModelProperty("testConditionId")
    private Integer testConditionId;

    /**
     *
     */
    @ApiModelProperty("testConditionName")
    private String testConditionName;

    /**
     *
     */
    @ApiModelProperty("testConditionSeq")
    private Integer testConditionSeq;

    /**
     *
     */
    @ApiModelProperty("languages")
    private List<TestDataConditionLangInfo> languages;

    public Integer getTestConditionId() {
        return testConditionId;
    }

    public void setTestConditionId(Integer testConditionId) {
        this.testConditionId = testConditionId;
    }

    public String getTestConditionName() {
        return testConditionName;
    }

    public void setTestConditionName(String testConditionName) {
        this.testConditionName = testConditionName;
    }

    public Integer getTestConditionSeq() {
        return testConditionSeq;
    }

    public void setTestConditionSeq(Integer testConditionSeq) {
        this.testConditionSeq = testConditionSeq;
    }

    public List<TestDataConditionLangInfo> getLanguages() {
        return languages;
    }

    public void setLanguages(List<TestDataConditionLangInfo> languages) {
        this.languages = languages;
    }
}
