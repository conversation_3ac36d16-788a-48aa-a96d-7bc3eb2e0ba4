package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdQuotationLangDTO extends BaseModel{

    private Integer languageId;
    private String payerCustomerName; // 最大长度300
    private String citationName; // 最大长度255
    private String citationFullName; // 最大长度255
    private String serviceItemName; // 最大长度2000

    private String quotationInstanceId;

    private Integer activeIndicator;

    private Date lastModifiedTimestamp;

}
