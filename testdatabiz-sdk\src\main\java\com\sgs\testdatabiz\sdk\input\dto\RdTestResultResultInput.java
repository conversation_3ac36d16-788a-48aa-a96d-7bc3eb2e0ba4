/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestResultResultInput implements Serializable {

    private String testResultFullName;

    private RdTestResultNameInput testResultFullNameRel;

    private String resultValue;
    private String resultValueRemark;
    private String resultUnit;
    private Integer failFlag;
    //SCI-1378
    private String failRemark;
    private String reportRemark;
    private Integer analyteConclusionId;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
