package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportLangMapper {
    int countByExample(RdReportLangExample example);

    int deleteByExample(RdReportLangExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportLangPO record);

    int insertSelective(RdReportLangPO record);

    List<RdReportLangPO> selectByExample(RdReportLangExample example);

    RdReportLangPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportLangPO record, @Param("example") RdReportLangExample example);

    int updateByExample(@Param("record") RdReportLangPO record, @Param("example") RdReportLangExample example);

    int updateByPrimaryKeySelective(RdReportLangPO record);

    int updateByPrimaryKey(RdReportLangPO record);

    int batchInsert(List<RdReportLangPO> list);

    int batchUpdate(List<RdReportLangPO> list);
}