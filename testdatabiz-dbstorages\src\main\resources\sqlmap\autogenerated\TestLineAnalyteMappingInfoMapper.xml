<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestLineAnalyteMappingInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoPO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="testLineMappingId" property="testLineMappingId" jdbcType="INTEGER" />
    <result column="analyteCode" property="analyteCode" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="createdBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="createdDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, testLineMappingId, analyteCode, `status`, createdBy, createdDate, modifiedBy, 
    modifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_line_analyte_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_line_analyte_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tb_test_line_analyte_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoExample" >
    delete from tb_test_line_analyte_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoPO" >
    insert into tb_test_line_analyte_mapping (id, testLineMappingId, analyteCode, 
      `status`, createdBy, createdDate, 
      modifiedBy, modifiedDate)
    values (#{id,jdbcType=INTEGER}, #{testLineMappingId,jdbcType=INTEGER}, #{analyteCode,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoPO" >
    insert into tb_test_line_analyte_mapping
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="testLineMappingId != null" >
        testLineMappingId,
      </if>
      <if test="analyteCode != null" >
        analyteCode,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="createdBy != null" >
        createdBy,
      </if>
      <if test="createdDate != null" >
        createdDate,
      </if>
      <if test="modifiedBy != null" >
        modifiedBy,
      </if>
      <if test="modifiedDate != null" >
        modifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="testLineMappingId != null" >
        #{testLineMappingId,jdbcType=INTEGER},
      </if>
      <if test="analyteCode != null" >
        #{analyteCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_line_analyte_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_line_analyte_mapping
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.testLineMappingId != null" >
        testLineMappingId = #{record.testLineMappingId,jdbcType=INTEGER},
      </if>
      <if test="record.analyteCode != null" >
        analyteCode = #{record.analyteCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        createdBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        createdDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_line_analyte_mapping
    set id = #{record.id,jdbcType=INTEGER},
      testLineMappingId = #{record.testLineMappingId,jdbcType=INTEGER},
      analyteCode = #{record.analyteCode,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      createdBy = #{record.createdBy,jdbcType=VARCHAR},
      createdDate = #{record.createdDate,jdbcType=TIMESTAMP},
      modifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      modifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoPO" >
    update tb_test_line_analyte_mapping
    <set >
      <if test="testLineMappingId != null" >
        testLineMappingId = #{testLineMappingId,jdbcType=INTEGER},
      </if>
      <if test="analyteCode != null" >
        analyteCode = #{analyteCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        createdBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        createdDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoPO" >
    update tb_test_line_analyte_mapping
    set testLineMappingId = #{testLineMappingId,jdbcType=INTEGER},
      analyteCode = #{analyteCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      createdBy = #{createdBy,jdbcType=VARCHAR},
      createdDate = #{createdDate,jdbcType=TIMESTAMP},
      modifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      modifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>