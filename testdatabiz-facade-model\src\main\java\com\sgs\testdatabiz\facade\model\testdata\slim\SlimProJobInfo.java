package com.sgs.testdatabiz.facade.model.testdata.slim;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.testdatabiz.facade.model.info.order.OrderTestMatrixInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;
import java.util.Set;

@ApiModel
public final class SlimProJobInfo extends BaseRequest {
    /**
     *
     */
    private String orderNo;

    /**
     * 发包方订单号
     */
    @ApiModelProperty("发包方订单号")
    private String parentOrderNo;

    /**
     *
     */
    private String reportNo;

    /**
     *
     */
    private String subContractNo;

    /**
     * SHA22-000036
     */
    private String slimJobNo;

    /**
     *
     */
    private String labCode;

    /**
     *
     */
    private Date completedDate;

    /**
     *
     */
    private boolean slimCheck;

    /**
     *
     */
    private List<OrderTestMatrixInfo> orderTestMatrixs;

    /**
     * 始终拆分以：TestLineId+CitationId分组
     */
    private Set<String> alwaysSplits;

    /**
     *
     */
    private List<SlimTestMatrixInfo> testMatrixs;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public String getSlimJobNo() {
        return slimJobNo;
    }

    public void setSlimJobNo(String slimJobNo) {
        this.slimJobNo = slimJobNo;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }

    public boolean isSlimCheck() {
        return slimCheck;
    }

    public void setSlimCheck(boolean slimCheck) {
        this.slimCheck = slimCheck;
    }

    public List<OrderTestMatrixInfo> getOrderTestMatrixs() {
        return orderTestMatrixs;
    }

    public void setOrderTestMatrixs(List<OrderTestMatrixInfo> orderTestMatrixs) {
        this.orderTestMatrixs = orderTestMatrixs;
    }

    public Set<String> getAlwaysSplits() {
        return alwaysSplits;
    }

    public void setAlwaysSplits(Set<String> alwaysSplits) {
        this.alwaysSplits = alwaysSplits;
    }

    public List<SlimTestMatrixInfo> getTestMatrixs() {
        return testMatrixs;
    }

    public void setTestMatrixs(List<SlimTestMatrixInfo> testMatrixs) {
        this.testMatrixs = testMatrixs;
    }
}
