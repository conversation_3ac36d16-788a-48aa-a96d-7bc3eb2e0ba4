/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPositionInput implements Serializable {

    private String positionInstanceId;
    private Integer usageTypeId;
    private String usageTypeName;
    private String positionName;
    private String positionDescription;
    private Integer positionSeq;
    private List<RdPositionLanguageInput> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
