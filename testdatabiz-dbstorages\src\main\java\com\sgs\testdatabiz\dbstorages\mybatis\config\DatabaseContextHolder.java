package com.sgs.testdatabiz.dbstorages.mybatis.config;

import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.DatabaseTypeEnum;

/**
 * 保存一个线程安全的 DatabaseTypeEnum 容器
 * 用于保存数据源类型
 * Created by <PERSON> on 2019/4/28.
 */
public class DatabaseContextHolder {
    private static final ThreadLocal<ThreadDataSourceInfo> contextHolder = new ThreadLocal<>();

    public static void setDataSource(ThreadDataSourceInfo dataSource) {
        contextHolder.remove();
        contextHolder.set(dataSource);
    }

    public static ThreadDataSourceInfo getDataSource() {
        return contextHolder.get();
    }

    public static void clear() {
        contextHolder.remove();
    }

    /**
     * @param databaseType
     */
    public static void setDatabaseContext(DatabaseTypeEnum databaseType) {
        setDatabaseContext(null, null, databaseType);
    }

    /**
     * @param dbName
     * @param databaseType
     */
    public static void setDatabaseContext(String dbName, DatabaseTypeEnum databaseType) {
        setDatabaseContext(dbName, null, databaseType);
    }

    /**
     * @param dbName
     * @param productLineType
     * @param databaseType
     */
    public static void setDatabaseContext(String dbName, ProductLineType productLineType, DatabaseTypeEnum databaseType) {
        ThreadDataSourceInfo dataSource = new ThreadDataSourceInfo();
        dataSource.setDbName(dbName);
        dataSource.setProductLineType(productLineType);
        dataSource.setDatabaseType(databaseType);

        setDataSource(dataSource);
    }
}
