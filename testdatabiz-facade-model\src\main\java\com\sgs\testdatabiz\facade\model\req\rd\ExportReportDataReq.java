package com.sgs.testdatabiz.facade.model.req.rd;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.req.rd.validgroup.ExportReportDataGroup;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ExportReportDataReq extends BaseModel {

    private String reportNo;

    @Override
    public String getExtId() {
        return reportNo;
    }
}
