/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdAnalyteLanguageDTO implements Serializable {
    private Integer languageId;
    private String analyteName;
    private String reportUnit;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
