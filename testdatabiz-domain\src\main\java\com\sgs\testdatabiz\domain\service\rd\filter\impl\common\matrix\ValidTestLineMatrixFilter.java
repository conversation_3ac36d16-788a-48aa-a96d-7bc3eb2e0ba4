package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.matrix;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.FilterType;
import com.sgs.testdatabiz.domain.service.rd.filter.AbstractReportDataFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.FilterContext;
import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdReportMatrixDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestLineDO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@DefaultFilter(order = 10)
@Component
public class ValidTestLineMatrixFilter extends AbstractReportDataFilter {

    @Override
    protected ReportDataDO doFilterInternal(ReportDataDO reportData, FilterContext context) {
        if (reportData == null || reportData.getHeader() == null) {
            return reportData;
        }

        // 获取所有有效的test line instance IDs
        List<RdTestLineDO> testLineList = reportData.getTestLineList();
        final Set<String> validTestLineInstanceIds = Func.isNotEmpty(testLineList) ? 
                testLineList.stream()
                    .filter(testLine -> testLine != null && testLine.getTestLineInstanceId() != null)
                    .map(RdTestLineDO::getTestLineInstanceId)
                    .collect(Collectors.toSet()) : 
                Collections.emptySet();

        // 获取原始matrix数量
        int originalMatrixCount = 0;
        int filteredMatrixCount = 0;

        // 过滤每个report中的matrix
        List<RdReportMatrixDO> originalMatrixList = reportData.getHeader().getReportMatrixList();
        if (Func.isNotEmpty(originalMatrixList)) {
            originalMatrixCount = originalMatrixList.size();
            
            // 过滤matrix，只保留关联有效test line的matrix
            List<RdReportMatrixDO> filteredMatrixList = originalMatrixList.stream()
                    .filter(matrix -> matrix != null 
                            && matrix.getTestLineInstanceId() != null
                            && validTestLineInstanceIds.contains(matrix.getTestLineInstanceId()))
                    .collect(Collectors.toList());
            
            filteredMatrixCount = filteredMatrixList.size();
            reportData.getHeader().setReportMatrixList(filteredMatrixList);
        }

        // 记录过滤操作
        if (originalMatrixCount != filteredMatrixCount) {
            context.addFilterRecord(
                getFilterName(),
                "reportMatrixList",
                originalMatrixCount,
                filteredMatrixCount,
                getFilterMessage()
            );
        }

        return reportData;
    }

    @Override
    protected String getFilterType() {
        return FilterType.MATRIX_TEST_LINE.name();
    }

    
    protected String getFilterName() {
        return "ValidTestLineMatrixFilter";
    }

    protected String getFilterMessage() {
        return "Filtered matrices without valid test line instance ID";
    }
} 