/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestSampleLimitGroupDTO implements Serializable {

    private String limitGroupInstanceId;

    private Integer limitGroupId;
    private Integer pPBaseId;
    private Integer limitGroupTypeId;
    private String limitGroupName;
    private String limitGroupTypeName;
    private String conclusionMode;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private List<RdTestSampleLimitGroupLangDTO> languageList;

    private List<RdProductAttributeDTO> productAttributeList;

    @Data
    public static class RdTestSampleLimitGroupLangDTO implements Serializable{
        private Integer languageId;
        private String limitGroupName;
        private String limitGroupTypeName;
    }
}
