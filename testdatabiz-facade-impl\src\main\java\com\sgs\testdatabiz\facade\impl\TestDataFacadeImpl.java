package com.sgs.testdatabiz.facade.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.core.enums.TestDataTypeEnum;
import com.sgs.testdatabiz.domain.service.TestDataConfigService;
import com.sgs.testdatabiz.domain.service.TestDataService;
import com.sgs.testdatabiz.domain.service.TestLineAnalyteService;
import com.sgs.testdatabiz.domain.service.testdata.factory.TestDataFactory;
import com.sgs.testdatabiz.facade.TestDataFacade;
import com.sgs.testdatabiz.facade.model.info.TestDataInfo;
import com.sgs.testdatabiz.facade.model.req.TestDataConfigReq;
import com.sgs.testdatabiz.facade.model.req.TestDataDeleteReq;
import com.sgs.testdatabiz.facade.model.req.TestDataQueryReq;
import com.sgs.testdatabiz.facade.model.req.TestLineAnalyteMappingReq;
import com.sgs.testdatabiz.facade.model.rsp.TestDataConfigRsp;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixRsp;
import com.sgs.testdatabiz.facade.model.rsp.TestLineAnalyteMappingRsp;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.slim.SlimProJobInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 */
@Component("testDataFacade")
public class TestDataFacadeImpl implements TestDataFacade {
    @Autowired
    private TestDataService testDataService;
    @Autowired
    private TestLineAnalyteService testLineAnalyteService;
    @Autowired
    private TestDataFactory testDataFactory;
    @Autowired
    private TestDataConfigService testDataConfigService;

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse slimTestData(SlimProJobInfo reqObject) {
        return BaseResponse.newInstance(testDataFactory.doInvoke(reqObject, TestDataTypeEnum.Slim));
    }

    @Override
    public BaseResponse<List<TestDataInfo>> queryTestData(TestDataQueryReq reqObject) {
        return BaseResponse.newInstance(testDataService.queryTestData(reqObject));
    }

    @Override
    public BaseResponse deleteTestData(TestDataDeleteReq reqObject) {
        return BaseResponse.newInstance(testDataService.deleteTestData(reqObject));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse saveTestData(ReportTestDataInfo reqObject){
        return BaseResponse.newInstance(null);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse batchInsert(TestLineAnalyteMappingReq reqObject){
        return BaseResponse.newInstance(testLineAnalyteService.batchInsert(reqObject));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse<List<TestLineAnalyteMappingRsp>> getTestLineAnalyteInfoList(TestLineAnalyteMappingReq reqObject){
        return BaseResponse.newInstance(testLineAnalyteService.getTestLineAnalyteInfoList(reqObject));
    }

    @Override
    public BaseResponse<List<TestDataConfigRsp>> queryTestDataConfig(TestDataConfigReq req) {
        //返回general数据
        List<TestDataConfigRsp> list = testDataConfigService.queryTestDataConfig(req);
        return BaseResponse.newInstance(list);
    }

    @Override
    public BaseResponse<List<TestDataTestMatrixRsp>> getTestDataInfoList(TestDataQueryReq reqObject) {
        return BaseResponse.newInstance(testDataService.getTestDataInfoList(reqObject));
    }
}
