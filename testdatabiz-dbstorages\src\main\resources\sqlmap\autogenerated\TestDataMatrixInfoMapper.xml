<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestDataMatrixInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="ObjectRelId" property="objectRelId" jdbcType="VARCHAR" />
    <result column="TestMatrixId" property="testMatrixId" jdbcType="VARCHAR" />
    <result column="TestLineMappingId" property="testLineMappingId" jdbcType="INTEGER" />
    <result column="ExternalId" property="externalId" jdbcType="VARCHAR" />
    <result column="ExternalCode" property="externalCode" jdbcType="VARCHAR" />
    <result column="PpVersionId" property="ppVersionId" jdbcType="INTEGER" />
    <result column="Aid" property="aid" jdbcType="BIGINT" />
    <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
    <result column="CitationId" property="citationId" jdbcType="INTEGER" />
    <result column="CitationVersionId" property="citationVersionId" jdbcType="INTEGER" />
    <result column="CitationType" property="citationType" jdbcType="INTEGER" />
    <result column="CitationName" property="citationName" jdbcType="VARCHAR" />
    <result column="SampleId" property="sampleId" jdbcType="VARCHAR" />
    <result column="SampleNo" property="sampleNo" jdbcType="VARCHAR" />
    <result column="ExternalSampleNo" property="externalSampleNo" jdbcType="VARCHAR" />
    <result column="TestLineSeq" property="testLineSeq" jdbcType="BIGINT" />
    <result column="SampleSeq" property="sampleSeq" jdbcType="VARCHAR" />
    <result column="EvaluationAlias" property="evaluationAlias" jdbcType="VARCHAR" />
    <result column="MethodDesc" property="methodDesc" jdbcType="VARCHAR" />
    <result column="ConclusionId" property="conclusionId" jdbcType="VARCHAR" />
    <result column="ConclusionDisplay" property="conclusionDisplay" jdbcType="VARCHAR" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="test_matrix_group_id" property="testMatrixGroupId" jdbcType="INTEGER" />
    <result column="test_line_instance_id" property="testLineInstanceId" jdbcType="VARCHAR" />
    <result column="evaluation_name" property="evaluationName" jdbcType="VARCHAR" />
    <result column="test_line_status" property="testLineStatus" jdbcType="VARCHAR" />
    <result column="test_line_remark" property="testLineRemark" jdbcType="VARCHAR" />
    <result column="citation_full_name" property="citationFullName" jdbcType="VARCHAR" />
    <result column="sample_instance_id" property="sampleInstanceId" jdbcType="VARCHAR" />
    <result column="sample_parent_id" property="sampleParentId" jdbcType="VARCHAR" />
    <result column="sample_type" property="sampleType" jdbcType="VARCHAR" />
    <result column="category" property="category" jdbcType="VARCHAR" />
    <result column="material_color" property="materialColor" jdbcType="VARCHAR" />
    <result column="composition" property="composition" jdbcType="VARCHAR" />
    <result column="material_description" property="materialDescription" jdbcType="VARCHAR" />
    <result column="material_end_use" property="materialEndUse" jdbcType="VARCHAR" />
    <result column="applicable_flag" property="applicableFlag" jdbcType="VARCHAR" />
    <result column="material_other_sample_info" property="materialOtherSampleInfo" jdbcType="VARCHAR" />
    <result column="material_remark" property="materialRemark" jdbcType="VARCHAR" />
    <result column="conclusion_code" property="conclusionCode" jdbcType="VARCHAR" />
    <result column="customer_conclusion" property="customerConclusion" jdbcType="VARCHAR" />
    <result column="conclusion_remark" property="conclusionRemark" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" extends="BaseResultMap" >
    <result column="ExtFields" property="extFields" jdbcType="LONGVARCHAR" />
    <result column="Condition" property="condition" jdbcType="LONGVARCHAR" />
    <result column="Languages" property="languages" jdbcType="LONGVARCHAR" />
    <result column="sample_group" property="sampleGroup" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, ObjectRelId, TestMatrixId, TestLineMappingId, ExternalId, ExternalCode, PpVersionId, 
    Aid, TestLineId, CitationId, CitationVersionId, CitationType, CitationName, SampleId, 
    SampleNo, ExternalSampleNo, TestLineSeq, SampleSeq, EvaluationAlias, MethodDesc, 
    ConclusionId, ConclusionDisplay, BizVersionId, ActiveIndicator, CreatedBy, CreatedDate, 
    ModifiedBy, ModifiedDate, lab_id, order_no, report_no, test_matrix_group_id, test_line_instance_id, 
    evaluation_name, test_line_status, test_line_remark, citation_full_name, sample_instance_id, 
    sample_parent_id, sample_type, category, material_color, composition, material_description, 
    material_end_use, applicable_flag, material_other_sample_info, material_remark, conclusion_code, 
    customer_conclusion, conclusion_remark, active_indicator, last_modified_timestamp
  </sql>
  <sql id="Blob_Column_List" >
    ExtFields, `Condition`, Languages, sample_group
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_data_matrix_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_data_matrix_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_data_matrix_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_test_data_matrix_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoExample" >
    delete from tb_test_data_matrix_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" >
    insert into tb_test_data_matrix_info (id, ObjectRelId, TestMatrixId, 
      TestLineMappingId, ExternalId, ExternalCode, 
      PpVersionId, Aid, TestLineId, 
      CitationId, CitationVersionId, CitationType, 
      CitationName, SampleId, SampleNo, 
      ExternalSampleNo, TestLineSeq, SampleSeq, 
      EvaluationAlias, MethodDesc, ConclusionId, 
      ConclusionDisplay, BizVersionId, ActiveIndicator, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate, lab_id, order_no, 
      report_no, test_matrix_group_id, test_line_instance_id, 
      evaluation_name, test_line_status, test_line_remark, 
      citation_full_name, sample_instance_id, sample_parent_id, 
      sample_type, category, material_color, 
      composition, material_description, material_end_use, 
      applicable_flag, material_other_sample_info, 
      material_remark, conclusion_code, customer_conclusion, 
      conclusion_remark, active_indicator, last_modified_timestamp, 
      ExtFields, `Condition`, Languages, 
      sample_group)
    values (#{id,jdbcType=BIGINT}, #{objectrelid,jdbcType=VARCHAR}, #{testmatrixid,jdbcType=VARCHAR}, 
      #{testlinemappingid,jdbcType=INTEGER}, #{externalid,jdbcType=VARCHAR}, #{externalcode,jdbcType=VARCHAR}, 
      #{ppversionid,jdbcType=INTEGER}, #{aid,jdbcType=BIGINT}, #{testlineid,jdbcType=INTEGER}, 
      #{citationid,jdbcType=INTEGER}, #{citationversionid,jdbcType=INTEGER}, #{citationtype,jdbcType=INTEGER}, 
      #{citationname,jdbcType=VARCHAR}, #{sampleid,jdbcType=VARCHAR}, #{sampleno,jdbcType=VARCHAR}, 
      #{externalsampleno,jdbcType=VARCHAR}, #{testlineseq,jdbcType=BIGINT}, #{sampleseq,jdbcType=VARCHAR}, 
      #{evaluationalias,jdbcType=VARCHAR}, #{methoddesc,jdbcType=VARCHAR}, #{conclusionid,jdbcType=VARCHAR}, 
      #{conclusiondisplay,jdbcType=VARCHAR}, #{bizversionid,jdbcType=CHAR}, #{activeindicator,jdbcType=INTEGER}, 
      #{createdby,jdbcType=VARCHAR}, #{createddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, 
      #{modifieddate,jdbcType=TIMESTAMP}, #{labId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{testMatrixGroupId,jdbcType=INTEGER}, #{testLineInstanceId,jdbcType=VARCHAR},
      #{evaluationName,jdbcType=VARCHAR}, #{testLineStatus,jdbcType=VARCHAR}, #{testLineRemark,jdbcType=VARCHAR}, 
      #{citationFullName,jdbcType=VARCHAR}, #{sampleInstanceId,jdbcType=VARCHAR}, #{sampleParentId,jdbcType=VARCHAR}, 
      #{sampleType,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{materialColor,jdbcType=VARCHAR}, 
      #{composition,jdbcType=VARCHAR}, #{materialDescription,jdbcType=VARCHAR}, #{materialEndUse,jdbcType=VARCHAR}, 
      #{applicableFlag,jdbcType=VARCHAR}, #{materialOtherSampleInfo,jdbcType=VARCHAR}, 
      #{materialRemark,jdbcType=VARCHAR}, #{conclusionCode,jdbcType=VARCHAR}, #{customerConclusion,jdbcType=VARCHAR}, 
      #{conclusionRemark,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=TINYINT}, #{lastModifiedTimestamp,jdbcType=TIMESTAMP}, 
      #{extfields,jdbcType=LONGVARCHAR}, #{condition,jdbcType=LONGVARCHAR}, #{languages,jdbcType=LONGVARCHAR}, 
      #{sampleGroup,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" >
    insert into tb_test_data_matrix_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="objectrelid != null" >
        ObjectRelId,
      </if>
      <if test="testmatrixid != null" >
        TestMatrixId,
      </if>
      <if test="testlinemappingid != null" >
        TestLineMappingId,
      </if>
      <if test="externalid != null" >
        ExternalId,
      </if>
      <if test="externalcode != null" >
        ExternalCode,
      </if>
      <if test="ppversionid != null" >
        PpVersionId,
      </if>
      <if test="aid != null" >
        Aid,
      </if>
      <if test="testlineid != null" >
        TestLineId,
      </if>
      <if test="citationid != null" >
        CitationId,
      </if>
      <if test="citationversionid != null" >
        CitationVersionId,
      </if>
      <if test="citationtype != null" >
        CitationType,
      </if>
      <if test="citationname != null" >
        CitationName,
      </if>
      <if test="sampleid != null" >
        SampleId,
      </if>
      <if test="sampleno != null" >
        SampleNo,
      </if>
      <if test="externalsampleno != null" >
        ExternalSampleNo,
      </if>
      <if test="testlineseq != null" >
        TestLineSeq,
      </if>
      <if test="sampleseq != null" >
        SampleSeq,
      </if>
      <if test="evaluationalias != null" >
        EvaluationAlias,
      </if>
      <if test="methoddesc != null" >
        MethodDesc,
      </if>
      <if test="conclusionid != null" >
        ConclusionId,
      </if>
      <if test="conclusiondisplay != null" >
        ConclusionDisplay,
      </if>
      <if test="bizversionid != null" >
        BizVersionId,
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdby != null" >
        CreatedBy,
      </if>
      <if test="createddate != null" >
        CreatedDate,
      </if>
      <if test="modifiedby != null" >
        ModifiedBy,
      </if>
      <if test="modifieddate != null" >
        ModifiedDate,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="testMatrixGroupId != null" >
        test_matrix_group_id,
      </if>
      <if test="testLineInstanceId != null" >
        test_line_instance_id,
      </if>
      <if test="evaluationName != null" >
        evaluation_name,
      </if>
      <if test="testLineStatus != null" >
        test_line_status,
      </if>
      <if test="testLineRemark != null" >
        test_line_remark,
      </if>
      <if test="citationFullName != null" >
        citation_full_name,
      </if>
      <if test="sampleInstanceId != null" >
        sample_instance_id,
      </if>
      <if test="sampleParentId != null" >
        sample_parent_id,
      </if>
      <if test="sampleType != null" >
        sample_type,
      </if>
      <if test="category != null" >
        category,
      </if>
      <if test="materialColor != null" >
        material_color,
      </if>
      <if test="composition != null" >
        composition,
      </if>
      <if test="materialDescription != null" >
        material_description,
      </if>
      <if test="materialEndUse != null" >
        material_end_use,
      </if>
      <if test="applicableFlag != null" >
        applicable_flag,
      </if>
      <if test="materialOtherSampleInfo != null" >
        material_other_sample_info,
      </if>
      <if test="materialRemark != null" >
        material_remark,
      </if>
      <if test="conclusionCode != null" >
        conclusion_code,
      </if>
      <if test="customerConclusion != null" >
        customer_conclusion,
      </if>
      <if test="conclusionRemark != null" >
        conclusion_remark,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
      <if test="extfields != null" >
        ExtFields,
      </if>
      <if test="condition != null" >
        `Condition`,
      </if>
      <if test="languages != null" >
        Languages,
      </if>
      <if test="sampleGroup != null" >
        sample_group,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="objectrelid != null" >
        #{objectrelid,jdbcType=VARCHAR},
      </if>
      <if test="testmatrixid != null" >
        #{testmatrixid,jdbcType=VARCHAR},
      </if>
      <if test="testlinemappingid != null" >
        #{testlinemappingid,jdbcType=INTEGER},
      </if>
      <if test="externalid != null" >
        #{externalid,jdbcType=VARCHAR},
      </if>
      <if test="externalcode != null" >
        #{externalcode,jdbcType=VARCHAR},
      </if>
      <if test="ppversionid != null" >
        #{ppversionid,jdbcType=INTEGER},
      </if>
      <if test="aid != null" >
        #{aid,jdbcType=BIGINT},
      </if>
      <if test="testlineid != null" >
        #{testlineid,jdbcType=INTEGER},
      </if>
      <if test="citationid != null" >
        #{citationid,jdbcType=INTEGER},
      </if>
      <if test="citationversionid != null" >
        #{citationversionid,jdbcType=INTEGER},
      </if>
      <if test="citationtype != null" >
        #{citationtype,jdbcType=INTEGER},
      </if>
      <if test="citationname != null" >
        #{citationname,jdbcType=VARCHAR},
      </if>
      <if test="sampleid != null" >
        #{sampleid,jdbcType=VARCHAR},
      </if>
      <if test="sampleno != null" >
        #{sampleno,jdbcType=VARCHAR},
      </if>
      <if test="externalsampleno != null" >
        #{externalsampleno,jdbcType=VARCHAR},
      </if>
      <if test="testlineseq != null" >
        #{testlineseq,jdbcType=BIGINT},
      </if>
      <if test="sampleseq != null" >
        #{sampleseq,jdbcType=VARCHAR},
      </if>
      <if test="evaluationalias != null" >
        #{evaluationalias,jdbcType=VARCHAR},
      </if>
      <if test="methoddesc != null" >
        #{methoddesc,jdbcType=VARCHAR},
      </if>
      <if test="conclusionid != null" >
        #{conclusionid,jdbcType=VARCHAR},
      </if>
      <if test="conclusiondisplay != null" >
        #{conclusiondisplay,jdbcType=VARCHAR},
      </if>
      <if test="bizversionid != null" >
        #{bizversionid,jdbcType=CHAR},
      </if>
      <if test="activeindicator != null" >
        #{activeindicator,jdbcType=INTEGER},
      </if>
      <if test="createdby != null" >
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null" >
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="testMatrixGroupId != null" >
        #{testMatrixGroupId,jdbcType=INTEGER},
      </if>
      <if test="testLineInstanceId != null" >
        #{testLineInstanceId,jdbcType=INTEGER},
      </if>
      <if test="evaluationName != null" >
        #{evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="testLineStatus != null" >
        #{testLineStatus,jdbcType=VARCHAR},
      </if>
      <if test="testLineRemark != null" >
        #{testLineRemark,jdbcType=VARCHAR},
      </if>
      <if test="citationFullName != null" >
        #{citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="sampleInstanceId != null" >
        #{sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="sampleParentId != null" >
        #{sampleParentId,jdbcType=VARCHAR},
      </if>
      <if test="sampleType != null" >
        #{sampleType,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="materialColor != null" >
        #{materialColor,jdbcType=VARCHAR},
      </if>
      <if test="composition != null" >
        #{composition,jdbcType=VARCHAR},
      </if>
      <if test="materialDescription != null" >
        #{materialDescription,jdbcType=VARCHAR},
      </if>
      <if test="materialEndUse != null" >
        #{materialEndUse,jdbcType=VARCHAR},
      </if>
      <if test="applicableFlag != null" >
        #{applicableFlag,jdbcType=VARCHAR},
      </if>
      <if test="materialOtherSampleInfo != null" >
        #{materialOtherSampleInfo,jdbcType=VARCHAR},
      </if>
      <if test="materialRemark != null" >
        #{materialRemark,jdbcType=VARCHAR},
      </if>
      <if test="conclusionCode != null" >
        #{conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusion != null" >
        #{customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="conclusionRemark != null" >
        #{conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="extfields != null" >
        #{extfields,jdbcType=LONGVARCHAR},
      </if>
      <if test="condition != null" >
        #{condition,jdbcType=LONGVARCHAR},
      </if>
      <if test="languages != null" >
        #{languages,jdbcType=LONGVARCHAR},
      </if>
      <if test="sampleGroup != null" >
        #{sampleGroup,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_data_matrix_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_data_matrix_info
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.objectrelid != null" >
        ObjectRelId = #{record.objectrelid,jdbcType=VARCHAR},
      </if>
      <if test="record.testmatrixid != null" >
        TestMatrixId = #{record.testmatrixid,jdbcType=VARCHAR},
      </if>
      <if test="record.testlinemappingid != null" >
        TestLineMappingId = #{record.testlinemappingid,jdbcType=INTEGER},
      </if>
      <if test="record.externalid != null" >
        ExternalId = #{record.externalid,jdbcType=VARCHAR},
      </if>
      <if test="record.externalcode != null" >
        ExternalCode = #{record.externalcode,jdbcType=VARCHAR},
      </if>
      <if test="record.ppversionid != null" >
        PpVersionId = #{record.ppversionid,jdbcType=INTEGER},
      </if>
      <if test="record.aid != null" >
        Aid = #{record.aid,jdbcType=BIGINT},
      </if>
      <if test="record.testlineid != null" >
        TestLineId = #{record.testlineid,jdbcType=INTEGER},
      </if>
      <if test="record.citationid != null" >
        CitationId = #{record.citationid,jdbcType=INTEGER},
      </if>
      <if test="record.citationversionid != null" >
        CitationVersionId = #{record.citationversionid,jdbcType=INTEGER},
      </if>
      <if test="record.citationtype != null" >
        CitationType = #{record.citationtype,jdbcType=INTEGER},
      </if>
      <if test="record.citationname != null" >
        CitationName = #{record.citationname,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleid != null" >
        SampleId = #{record.sampleid,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleno != null" >
        SampleNo = #{record.sampleno,jdbcType=VARCHAR},
      </if>
      <if test="record.externalsampleno != null" >
        ExternalSampleNo = #{record.externalsampleno,jdbcType=VARCHAR},
      </if>
      <if test="record.testlineseq != null" >
        TestLineSeq = #{record.testlineseq,jdbcType=BIGINT},
      </if>
      <if test="record.sampleseq != null" >
        SampleSeq = #{record.sampleseq,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluationalias != null" >
        EvaluationAlias = #{record.evaluationalias,jdbcType=VARCHAR},
      </if>
      <if test="record.methoddesc != null" >
        MethodDesc = #{record.methoddesc,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionid != null" >
        ConclusionId = #{record.conclusionid,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusiondisplay != null" >
        ConclusionDisplay = #{record.conclusiondisplay,jdbcType=VARCHAR},
      </if>
      <if test="record.bizversionid != null" >
        BizVersionId = #{record.bizversionid,jdbcType=CHAR},
      </if>
      <if test="record.activeindicator != null" >
        ActiveIndicator = #{record.activeindicator,jdbcType=INTEGER},
      </if>
      <if test="record.createdby != null" >
        CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.createddate != null" >
        CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedby != null" >
        ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="record.modifieddate != null" >
        ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.testMatrixGroupId != null" >
        test_matrix_group_id = #{record.testMatrixGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.testLineInstanceId != null" >
        test_line_instance_id = #{record.testLineInstanceId,jdbcType=INTEGER},
      </if>
      <if test="record.evaluationName != null" >
        evaluation_name = #{record.evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineStatus != null" >
        test_line_status = #{record.testLineStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineRemark != null" >
        test_line_remark = #{record.testLineRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.citationFullName != null" >
        citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleInstanceId != null" >
        sample_instance_id = #{record.sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleParentId != null" >
        sample_parent_id = #{record.sampleParentId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleType != null" >
        sample_type = #{record.sampleType,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null" >
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.materialColor != null" >
        material_color = #{record.materialColor,jdbcType=VARCHAR},
      </if>
      <if test="record.composition != null" >
        composition = #{record.composition,jdbcType=VARCHAR},
      </if>
      <if test="record.materialDescription != null" >
        material_description = #{record.materialDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.materialEndUse != null" >
        material_end_use = #{record.materialEndUse,jdbcType=VARCHAR},
      </if>
      <if test="record.applicableFlag != null" >
        applicable_flag = #{record.applicableFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.materialOtherSampleInfo != null" >
        material_other_sample_info = #{record.materialOtherSampleInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.materialRemark != null" >
        material_remark = #{record.materialRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionCode != null" >
        conclusion_code = #{record.conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerConclusion != null" >
        customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionRemark != null" >
        conclusion_remark = #{record.conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extfields != null" >
        ExtFields = #{record.extfields,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.condition != null" >
        `Condition` = #{record.condition,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.languages != null" >
        Languages = #{record.languages,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.sampleGroup != null" >
        sample_group = #{record.sampleGroup,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_test_data_matrix_info
    set id = #{record.id,jdbcType=BIGINT},
      ObjectRelId = #{record.objectrelid,jdbcType=VARCHAR},
      TestMatrixId = #{record.testmatrixid,jdbcType=VARCHAR},
      TestLineMappingId = #{record.testlinemappingid,jdbcType=INTEGER},
      ExternalId = #{record.externalid,jdbcType=VARCHAR},
      ExternalCode = #{record.externalcode,jdbcType=VARCHAR},
      PpVersionId = #{record.ppversionid,jdbcType=INTEGER},
      Aid = #{record.aid,jdbcType=BIGINT},
      TestLineId = #{record.testlineid,jdbcType=INTEGER},
      CitationId = #{record.citationid,jdbcType=INTEGER},
      CitationVersionId = #{record.citationversionid,jdbcType=INTEGER},
      CitationType = #{record.citationtype,jdbcType=INTEGER},
      CitationName = #{record.citationname,jdbcType=VARCHAR},
      SampleId = #{record.sampleid,jdbcType=VARCHAR},
      SampleNo = #{record.sampleno,jdbcType=VARCHAR},
      ExternalSampleNo = #{record.externalsampleno,jdbcType=VARCHAR},
      TestLineSeq = #{record.testlineseq,jdbcType=BIGINT},
      SampleSeq = #{record.sampleseq,jdbcType=VARCHAR},
      EvaluationAlias = #{record.evaluationalias,jdbcType=VARCHAR},
      MethodDesc = #{record.methoddesc,jdbcType=VARCHAR},
      ConclusionId = #{record.conclusionid,jdbcType=VARCHAR},
      ConclusionDisplay = #{record.conclusiondisplay,jdbcType=VARCHAR},
      BizVersionId = #{record.bizversionid,jdbcType=CHAR},
      ActiveIndicator = #{record.activeindicator,jdbcType=INTEGER},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      lab_id = #{record.labId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      test_matrix_group_id = #{record.testMatrixGroupId,jdbcType=INTEGER},
      test_line_instance_id = #{record.testLineInstanceId,jdbcType=VARCHAR},
      evaluation_name = #{record.evaluationName,jdbcType=VARCHAR},
      test_line_status = #{record.testLineStatus,jdbcType=VARCHAR},
      test_line_remark = #{record.testLineRemark,jdbcType=VARCHAR},
      citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      sample_instance_id = #{record.sampleInstanceId,jdbcType=VARCHAR},
      sample_parent_id = #{record.sampleParentId,jdbcType=VARCHAR},
      sample_type = #{record.sampleType,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      material_color = #{record.materialColor,jdbcType=VARCHAR},
      composition = #{record.composition,jdbcType=VARCHAR},
      material_description = #{record.materialDescription,jdbcType=VARCHAR},
      material_end_use = #{record.materialEndUse,jdbcType=VARCHAR},
      applicable_flag = #{record.applicableFlag,jdbcType=VARCHAR},
      material_other_sample_info = #{record.materialOtherSampleInfo,jdbcType=VARCHAR},
      material_remark = #{record.materialRemark,jdbcType=VARCHAR},
      conclusion_code = #{record.conclusionCode,jdbcType=VARCHAR},
      customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      conclusion_remark = #{record.conclusionRemark,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      ExtFields = #{record.extfields,jdbcType=LONGVARCHAR},
      `Condition` = #{record.condition,jdbcType=LONGVARCHAR},
      Languages = #{record.languages,jdbcType=LONGVARCHAR},
      sample_group = #{record.sampleGroup,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_data_matrix_info
    set id = #{record.id,jdbcType=BIGINT},
      ObjectRelId = #{record.objectrelid,jdbcType=VARCHAR},
      TestMatrixId = #{record.testmatrixid,jdbcType=VARCHAR},
      TestLineMappingId = #{record.testlinemappingid,jdbcType=INTEGER},
      ExternalId = #{record.externalid,jdbcType=VARCHAR},
      ExternalCode = #{record.externalcode,jdbcType=VARCHAR},
      PpVersionId = #{record.ppversionid,jdbcType=INTEGER},
      Aid = #{record.aid,jdbcType=BIGINT},
      TestLineId = #{record.testlineid,jdbcType=INTEGER},
      CitationId = #{record.citationid,jdbcType=INTEGER},
      CitationVersionId = #{record.citationversionid,jdbcType=INTEGER},
      CitationType = #{record.citationtype,jdbcType=INTEGER},
      CitationName = #{record.citationname,jdbcType=VARCHAR},
      SampleId = #{record.sampleid,jdbcType=VARCHAR},
      SampleNo = #{record.sampleno,jdbcType=VARCHAR},
      ExternalSampleNo = #{record.externalsampleno,jdbcType=VARCHAR},
      TestLineSeq = #{record.testlineseq,jdbcType=BIGINT},
      SampleSeq = #{record.sampleseq,jdbcType=VARCHAR},
      EvaluationAlias = #{record.evaluationalias,jdbcType=VARCHAR},
      MethodDesc = #{record.methoddesc,jdbcType=VARCHAR},
      ConclusionId = #{record.conclusionid,jdbcType=VARCHAR},
      ConclusionDisplay = #{record.conclusiondisplay,jdbcType=VARCHAR},
      BizVersionId = #{record.bizversionid,jdbcType=CHAR},
      ActiveIndicator = #{record.activeindicator,jdbcType=INTEGER},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      lab_id = #{record.labId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      test_matrix_group_id = #{record.testMatrixGroupId,jdbcType=INTEGER},
      test_line_instance_id = #{record.testLineInstanceId,jdbcType=VARCHAR},
      evaluation_name = #{record.evaluationName,jdbcType=VARCHAR},
      test_line_status = #{record.testLineStatus,jdbcType=VARCHAR},
      test_line_remark = #{record.testLineRemark,jdbcType=VARCHAR},
      citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      sample_instance_id = #{record.sampleInstanceId,jdbcType=VARCHAR},
      sample_parent_id = #{record.sampleParentId,jdbcType=VARCHAR},
      sample_type = #{record.sampleType,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      material_color = #{record.materialColor,jdbcType=VARCHAR},
      composition = #{record.composition,jdbcType=VARCHAR},
      material_description = #{record.materialDescription,jdbcType=VARCHAR},
      material_end_use = #{record.materialEndUse,jdbcType=VARCHAR},
      applicable_flag = #{record.applicableFlag,jdbcType=VARCHAR},
      material_other_sample_info = #{record.materialOtherSampleInfo,jdbcType=VARCHAR},
      material_remark = #{record.materialRemark,jdbcType=VARCHAR},
      conclusion_code = #{record.conclusionCode,jdbcType=VARCHAR},
      customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      conclusion_remark = #{record.conclusionRemark,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" >
    update tb_test_data_matrix_info
    <set >
      <if test="objectrelid != null" >
        ObjectRelId = #{objectrelid,jdbcType=VARCHAR},
      </if>
      <if test="testmatrixid != null" >
        TestMatrixId = #{testmatrixid,jdbcType=VARCHAR},
      </if>
      <if test="testlinemappingid != null" >
        TestLineMappingId = #{testlinemappingid,jdbcType=INTEGER},
      </if>
      <if test="externalid != null" >
        ExternalId = #{externalid,jdbcType=VARCHAR},
      </if>
      <if test="externalcode != null" >
        ExternalCode = #{externalcode,jdbcType=VARCHAR},
      </if>
      <if test="ppversionid != null" >
        PpVersionId = #{ppversionid,jdbcType=INTEGER},
      </if>
      <if test="aid != null" >
        Aid = #{aid,jdbcType=BIGINT},
      </if>
      <if test="testlineid != null" >
        TestLineId = #{testlineid,jdbcType=INTEGER},
      </if>
      <if test="citationid != null" >
        CitationId = #{citationid,jdbcType=INTEGER},
      </if>
      <if test="citationversionid != null" >
        CitationVersionId = #{citationversionid,jdbcType=INTEGER},
      </if>
      <if test="citationtype != null" >
        CitationType = #{citationtype,jdbcType=INTEGER},
      </if>
      <if test="citationname != null" >
        CitationName = #{citationname,jdbcType=VARCHAR},
      </if>
      <if test="sampleid != null" >
        SampleId = #{sampleid,jdbcType=VARCHAR},
      </if>
      <if test="sampleno != null" >
        SampleNo = #{sampleno,jdbcType=VARCHAR},
      </if>
      <if test="externalsampleno != null" >
        ExternalSampleNo = #{externalsampleno,jdbcType=VARCHAR},
      </if>
      <if test="testlineseq != null" >
        TestLineSeq = #{testlineseq,jdbcType=BIGINT},
      </if>
      <if test="sampleseq != null" >
        SampleSeq = #{sampleseq,jdbcType=VARCHAR},
      </if>
      <if test="evaluationalias != null" >
        EvaluationAlias = #{evaluationalias,jdbcType=VARCHAR},
      </if>
      <if test="methoddesc != null" >
        MethodDesc = #{methoddesc,jdbcType=VARCHAR},
      </if>
      <if test="conclusionid != null" >
        ConclusionId = #{conclusionid,jdbcType=VARCHAR},
      </if>
      <if test="conclusiondisplay != null" >
        ConclusionDisplay = #{conclusiondisplay,jdbcType=VARCHAR},
      </if>
      <if test="bizversionid != null" >
        BizVersionId = #{bizversionid,jdbcType=CHAR},
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator = #{activeindicator,jdbcType=INTEGER},
      </if>
      <if test="createdby != null" >
        CreatedBy = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null" >
        CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="testMatrixGroupId != null" >
        test_matrix_group_id = #{testMatrixGroupId,jdbcType=INTEGER},
      </if>
      <if test="testLineInstanceId != null" >
        test_line_instance_id = #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="evaluationName != null" >
        evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="testLineStatus != null" >
        test_line_status = #{testLineStatus,jdbcType=VARCHAR},
      </if>
      <if test="testLineRemark != null" >
        test_line_remark = #{testLineRemark,jdbcType=VARCHAR},
      </if>
      <if test="citationFullName != null" >
        citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="sampleInstanceId != null" >
        sample_instance_id = #{sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="sampleParentId != null" >
        sample_parent_id = #{sampleParentId,jdbcType=VARCHAR},
      </if>
      <if test="sampleType != null" >
        sample_type = #{sampleType,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="materialColor != null" >
        material_color = #{materialColor,jdbcType=VARCHAR},
      </if>
      <if test="composition != null" >
        composition = #{composition,jdbcType=VARCHAR},
      </if>
      <if test="materialDescription != null" >
        material_description = #{materialDescription,jdbcType=VARCHAR},
      </if>
      <if test="materialEndUse != null" >
        material_end_use = #{materialEndUse,jdbcType=VARCHAR},
      </if>
      <if test="applicableFlag != null" >
        applicable_flag = #{applicableFlag,jdbcType=VARCHAR},
      </if>
      <if test="materialOtherSampleInfo != null" >
        material_other_sample_info = #{materialOtherSampleInfo,jdbcType=VARCHAR},
      </if>
      <if test="materialRemark != null" >
        material_remark = #{materialRemark,jdbcType=VARCHAR},
      </if>
      <if test="conclusionCode != null" >
        conclusion_code = #{conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusion != null" >
        customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="conclusionRemark != null" >
        conclusion_remark = #{conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="extfields != null" >
        ExtFields = #{extfields,jdbcType=LONGVARCHAR},
      </if>
      <if test="condition != null" >
        `Condition` = #{condition,jdbcType=LONGVARCHAR},
      </if>
      <if test="languages != null" >
        Languages = #{languages,jdbcType=LONGVARCHAR},
      </if>
      <if test="sampleGroup != null" >
        sample_group = #{sampleGroup,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" >
    update tb_test_data_matrix_info
    set ObjectRelId = #{objectrelid,jdbcType=VARCHAR},
      TestMatrixId = #{testmatrixid,jdbcType=VARCHAR},
      TestLineMappingId = #{testlinemappingid,jdbcType=INTEGER},
      ExternalId = #{externalid,jdbcType=VARCHAR},
      ExternalCode = #{externalcode,jdbcType=VARCHAR},
      PpVersionId = #{ppversionid,jdbcType=INTEGER},
      Aid = #{aid,jdbcType=BIGINT},
      TestLineId = #{testlineid,jdbcType=INTEGER},
      CitationId = #{citationid,jdbcType=INTEGER},
      CitationVersionId = #{citationversionid,jdbcType=INTEGER},
      CitationType = #{citationtype,jdbcType=INTEGER},
      CitationName = #{citationname,jdbcType=VARCHAR},
      SampleId = #{sampleid,jdbcType=VARCHAR},
      SampleNo = #{sampleno,jdbcType=VARCHAR},
      ExternalSampleNo = #{externalsampleno,jdbcType=VARCHAR},
      TestLineSeq = #{testlineseq,jdbcType=BIGINT},
      SampleSeq = #{sampleseq,jdbcType=VARCHAR},
      EvaluationAlias = #{evaluationalias,jdbcType=VARCHAR},
      MethodDesc = #{methoddesc,jdbcType=VARCHAR},
      ConclusionId = #{conclusionid,jdbcType=VARCHAR},
      ConclusionDisplay = #{conclusiondisplay,jdbcType=VARCHAR},
      BizVersionId = #{bizversionid,jdbcType=CHAR},
      ActiveIndicator = #{activeindicator,jdbcType=INTEGER},
      CreatedBy = #{createdby,jdbcType=VARCHAR},
      CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      lab_id = #{labId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      test_matrix_group_id = #{testMatrixGroupId,jdbcType=INTEGER},
      test_line_instance_id = #{testLineInstanceId,jdbcType=VARCHAR},
      evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      test_line_status = #{testLineStatus,jdbcType=VARCHAR},
      test_line_remark = #{testLineRemark,jdbcType=VARCHAR},
      citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      sample_instance_id = #{sampleInstanceId,jdbcType=VARCHAR},
      sample_parent_id = #{sampleParentId,jdbcType=VARCHAR},
      sample_type = #{sampleType,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      material_color = #{materialColor,jdbcType=VARCHAR},
      composition = #{composition,jdbcType=VARCHAR},
      material_description = #{materialDescription,jdbcType=VARCHAR},
      material_end_use = #{materialEndUse,jdbcType=VARCHAR},
      applicable_flag = #{applicableFlag,jdbcType=VARCHAR},
      material_other_sample_info = #{materialOtherSampleInfo,jdbcType=VARCHAR},
      material_remark = #{materialRemark,jdbcType=VARCHAR},
      conclusion_code = #{conclusionCode,jdbcType=VARCHAR},
      customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      conclusion_remark = #{conclusionRemark,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      ExtFields = #{extfields,jdbcType=LONGVARCHAR},
      `Condition` = #{condition,jdbcType=LONGVARCHAR},
      Languages = #{languages,jdbcType=LONGVARCHAR},
      sample_group = #{sampleGroup,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO" >
    update tb_test_data_matrix_info
    set ObjectRelId = #{objectrelid,jdbcType=VARCHAR},
      TestMatrixId = #{testmatrixid,jdbcType=VARCHAR},
      TestLineMappingId = #{testlinemappingid,jdbcType=INTEGER},
      ExternalId = #{externalid,jdbcType=VARCHAR},
      ExternalCode = #{externalcode,jdbcType=VARCHAR},
      PpVersionId = #{ppversionid,jdbcType=INTEGER},
      Aid = #{aid,jdbcType=BIGINT},
      TestLineId = #{testlineid,jdbcType=INTEGER},
      CitationId = #{citationid,jdbcType=INTEGER},
      CitationVersionId = #{citationversionid,jdbcType=INTEGER},
      CitationType = #{citationtype,jdbcType=INTEGER},
      CitationName = #{citationname,jdbcType=VARCHAR},
      SampleId = #{sampleid,jdbcType=VARCHAR},
      SampleNo = #{sampleno,jdbcType=VARCHAR},
      ExternalSampleNo = #{externalsampleno,jdbcType=VARCHAR},
      TestLineSeq = #{testlineseq,jdbcType=BIGINT},
      SampleSeq = #{sampleseq,jdbcType=VARCHAR},
      EvaluationAlias = #{evaluationalias,jdbcType=VARCHAR},
      MethodDesc = #{methoddesc,jdbcType=VARCHAR},
      ConclusionId = #{conclusionid,jdbcType=VARCHAR},
      ConclusionDisplay = #{conclusiondisplay,jdbcType=VARCHAR},
      BizVersionId = #{bizversionid,jdbcType=CHAR},
      ActiveIndicator = #{activeindicator,jdbcType=INTEGER},
      CreatedBy = #{createdby,jdbcType=VARCHAR},
      CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      lab_id = #{labId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      test_matrix_group_id = #{testMatrixGroupId,jdbcType=INTEGER},
      test_line_instance_id = #{testLineInstanceId,jdbcType=VARCHAR},
      evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      test_line_status = #{testLineStatus,jdbcType=VARCHAR},
      test_line_remark = #{testLineRemark,jdbcType=VARCHAR},
      citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      sample_instance_id = #{sampleInstanceId,jdbcType=VARCHAR},
      sample_parent_id = #{sampleParentId,jdbcType=VARCHAR},
      sample_type = #{sampleType,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      material_color = #{materialColor,jdbcType=VARCHAR},
      composition = #{composition,jdbcType=VARCHAR},
      material_description = #{materialDescription,jdbcType=VARCHAR},
      material_end_use = #{materialEndUse,jdbcType=VARCHAR},
      applicable_flag = #{applicableFlag,jdbcType=VARCHAR},
      material_other_sample_info = #{materialOtherSampleInfo,jdbcType=VARCHAR},
      material_remark = #{materialRemark,jdbcType=VARCHAR},
      conclusion_code = #{conclusionCode,jdbcType=VARCHAR},
      customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      conclusion_remark = #{conclusionRemark,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_data_matrix_info
      (`id`,`ObjectRelId`,`TestMatrixId`,
      `TestLineMappingId`,`ExternalId`,`ExternalCode`,
      `PpVersionId`,`Aid`,`TestLineId`,
      `CitationId`,`CitationVersionId`,`CitationType`,
      `CitationName`,`SampleId`,`SampleNo`,
      `ExternalSampleNo`,`TestLineSeq`,`SampleSeq`,
      `EvaluationAlias`,`MethodDesc`,`ConclusionId`,
      `ConclusionDisplay`,`BizVersionId`,`ActiveIndicator`,
      `CreatedBy`,`CreatedDate`,`ModifiedBy`,
      `ModifiedDate`,`lab_id`,`order_no`,
      `report_no`,`test_matrix_group_id`,`test_line_instance_id`,
      `evaluation_name`,`test_line_status`,`test_line_remark`,
      `citation_full_name`,`sample_instance_id`,`sample_parent_id`,
      `sample_type`,`category`,`material_color`,
      `composition`,`material_description`,`material_end_use`,
      `applicable_flag`,`material_other_sample_info`,`material_remark`,
      `conclusion_code`,`customer_conclusion`,`conclusion_remark`,
      `active_indicator`,`last_modified_timestamp`,`ExtFields`,
      `Condition`,`Languages`,`sample_group`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.objectrelid, jdbcType=VARCHAR},#{ item.testmatrixid, jdbcType=VARCHAR},
      #{ item.testlinemappingid, jdbcType=INTEGER},#{ item.externalid, jdbcType=VARCHAR},#{ item.externalcode, jdbcType=VARCHAR},
      #{ item.ppversionid, jdbcType=INTEGER},#{ item.aid, jdbcType=BIGINT},#{ item.testlineid, jdbcType=INTEGER},
      #{ item.citationid, jdbcType=INTEGER},#{ item.citationversionid, jdbcType=INTEGER},#{ item.citationtype, jdbcType=INTEGER},
      #{ item.citationname, jdbcType=VARCHAR},#{ item.sampleid, jdbcType=VARCHAR},#{ item.sampleno, jdbcType=VARCHAR},
      #{ item.externalsampleno, jdbcType=VARCHAR},#{ item.testlineseq, jdbcType=BIGINT},#{ item.sampleseq, jdbcType=VARCHAR},
      #{ item.evaluationalias, jdbcType=VARCHAR},#{ item.methoddesc, jdbcType=VARCHAR},#{ item.conclusionid, jdbcType=VARCHAR},
      #{ item.conclusiondisplay, jdbcType=VARCHAR},#{ item.bizversionid, jdbcType=CHAR},#{ item.activeindicator, jdbcType=INTEGER},
      #{ item.createdby, jdbcType=VARCHAR},#{ item.createddate, jdbcType=TIMESTAMP},#{ item.modifiedby, jdbcType=VARCHAR},
      #{ item.modifieddate, jdbcType=TIMESTAMP},#{ item.labId, jdbcType=BIGINT},#{ item.orderNo, jdbcType=VARCHAR},
      #{ item.reportNo, jdbcType=VARCHAR},#{ item.testMatrixGroupId, jdbcType=INTEGER},#{ item.testLineInstanceId, jdbcType=VARCHAR},
      #{ item.evaluationName, jdbcType=VARCHAR},#{ item.testLineStatus, jdbcType=VARCHAR},#{ item.testLineRemark, jdbcType=VARCHAR},
      #{ item.citationFullName, jdbcType=VARCHAR},#{ item.sampleInstanceId, jdbcType=VARCHAR},#{ item.sampleParentId, jdbcType=VARCHAR},
      #{ item.sampleType, jdbcType=VARCHAR},#{ item.category, jdbcType=VARCHAR},#{ item.materialColor, jdbcType=VARCHAR},
      #{ item.composition, jdbcType=VARCHAR},#{ item.materialDescription, jdbcType=VARCHAR},#{ item.materialEndUse, jdbcType=VARCHAR},
      #{ item.applicableFlag, jdbcType=VARCHAR},#{ item.materialOtherSampleInfo, jdbcType=VARCHAR},#{ item.materialRemark, jdbcType=VARCHAR},
      #{ item.conclusionCode, jdbcType=VARCHAR},#{ item.customerConclusion, jdbcType=VARCHAR},#{ item.conclusionRemark, jdbcType=VARCHAR},
      #{ item.activeIndicator, jdbcType=TINYINT},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP},#{ item.extfields, jdbcType=LONGVARCHAR},
      #{ item.condition, jdbcType=LONGVARCHAR},#{ item.languages, jdbcType=LONGVARCHAR},#{ item.sampleGroup, jdbcType=LONGVARCHAR}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_data_matrix_info 
      <set>
        <if test="item.objectrelid != null"> 
          `ObjectRelId` = #{item.objectrelid, jdbcType = VARCHAR},
        </if> 
        <if test="item.testmatrixid != null"> 
          `TestMatrixId` = #{item.testmatrixid, jdbcType = VARCHAR},
        </if> 
        <if test="item.testlinemappingid != null"> 
          `TestLineMappingId` = #{item.testlinemappingid, jdbcType = INTEGER},
        </if> 
        <if test="item.externalid != null"> 
          `ExternalId` = #{item.externalid, jdbcType = VARCHAR},
        </if> 
        <if test="item.externalcode != null"> 
          `ExternalCode` = #{item.externalcode, jdbcType = VARCHAR},
        </if> 
        <if test="item.ppversionid != null"> 
          `PpVersionId` = #{item.ppversionid, jdbcType = INTEGER},
        </if> 
        <if test="item.aid != null"> 
          `Aid` = #{item.aid, jdbcType = BIGINT},
        </if> 
        <if test="item.testlineid != null"> 
          `TestLineId` = #{item.testlineid, jdbcType = INTEGER},
        </if> 
        <if test="item.citationid != null"> 
          `CitationId` = #{item.citationid, jdbcType = INTEGER},
        </if> 
        <if test="item.citationversionid != null"> 
          `CitationVersionId` = #{item.citationversionid, jdbcType = INTEGER},
        </if> 
        <if test="item.citationtype != null"> 
          `CitationType` = #{item.citationtype, jdbcType = INTEGER},
        </if> 
        <if test="item.citationname != null"> 
          `CitationName` = #{item.citationname, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleid != null"> 
          `SampleId` = #{item.sampleid, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleno != null"> 
          `SampleNo` = #{item.sampleno, jdbcType = VARCHAR},
        </if> 
        <if test="item.externalsampleno != null"> 
          `ExternalSampleNo` = #{item.externalsampleno, jdbcType = VARCHAR},
        </if> 
        <if test="item.testlineseq != null"> 
          `TestLineSeq` = #{item.testlineseq, jdbcType = BIGINT},
        </if> 
        <if test="item.sampleseq != null"> 
          `SampleSeq` = #{item.sampleseq, jdbcType = VARCHAR},
        </if> 
        <if test="item.evaluationalias != null"> 
          `EvaluationAlias` = #{item.evaluationalias, jdbcType = VARCHAR},
        </if> 
        <if test="item.methoddesc != null"> 
          `MethodDesc` = #{item.methoddesc, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionid != null"> 
          `ConclusionId` = #{item.conclusionid, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusiondisplay != null"> 
          `ConclusionDisplay` = #{item.conclusiondisplay, jdbcType = VARCHAR},
        </if> 
        <if test="item.bizversionid != null"> 
          `BizVersionId` = #{item.bizversionid, jdbcType = CHAR},
        </if> 
        <if test="item.activeindicator != null"> 
          `ActiveIndicator` = #{item.activeindicator, jdbcType = INTEGER},
        </if> 
        <if test="item.createdby != null"> 
          `CreatedBy` = #{item.createdby, jdbcType = VARCHAR},
        </if> 
        <if test="item.createddate != null"> 
          `CreatedDate` = #{item.createddate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedby != null"> 
          `ModifiedBy` = #{item.modifiedby, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifieddate != null"> 
          `ModifiedDate` = #{item.modifieddate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.testMatrixGroupId != null"> 
          `test_matrix_group_id` = #{item.testMatrixGroupId, jdbcType = INTEGER},
        </if> 
        <if test="item.testLineInstanceId != null"> 
          `test_line_instance_id` = #{item.testLineInstanceId, jdbcType = VARCHAR},
        </if> 
        <if test="item.evaluationName != null"> 
          `evaluation_name` = #{item.evaluationName, jdbcType = VARCHAR},
        </if> 
        <if test="item.testLineStatus != null"> 
          `test_line_status` = #{item.testLineStatus, jdbcType = VARCHAR},
        </if> 
        <if test="item.testLineRemark != null"> 
          `test_line_remark` = #{item.testLineRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.citationFullName != null"> 
          `citation_full_name` = #{item.citationFullName, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleInstanceId != null"> 
          `sample_instance_id` = #{item.sampleInstanceId, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleParentId != null"> 
          `sample_parent_id` = #{item.sampleParentId, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleType != null"> 
          `sample_type` = #{item.sampleType, jdbcType = VARCHAR},
        </if> 
        <if test="item.category != null"> 
          `category` = #{item.category, jdbcType = VARCHAR},
        </if> 
        <if test="item.materialColor != null"> 
          `material_color` = #{item.materialColor, jdbcType = VARCHAR},
        </if> 
        <if test="item.composition != null"> 
          `composition` = #{item.composition, jdbcType = VARCHAR},
        </if> 
        <if test="item.materialDescription != null"> 
          `material_description` = #{item.materialDescription, jdbcType = VARCHAR},
        </if> 
        <if test="item.materialEndUse != null"> 
          `material_end_use` = #{item.materialEndUse, jdbcType = VARCHAR},
        </if> 
        <if test="item.applicableFlag != null"> 
          `applicable_flag` = #{item.applicableFlag, jdbcType = VARCHAR},
        </if> 
        <if test="item.materialOtherSampleInfo != null"> 
          `material_other_sample_info` = #{item.materialOtherSampleInfo, jdbcType = VARCHAR},
        </if> 
        <if test="item.materialRemark != null"> 
          `material_remark` = #{item.materialRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionCode != null"> 
          `conclusion_code` = #{item.conclusionCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerConclusion != null"> 
          `customer_conclusion` = #{item.customerConclusion, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionRemark != null"> 
          `conclusion_remark` = #{item.conclusionRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.extfields != null"> 
          `ExtFields` = #{item.extfields, jdbcType = LONGVARCHAR},
        </if> 
        <if test="item.condition != null"> 
          `Condition` = #{item.condition, jdbcType = LONGVARCHAR},
        </if> 
        <if test="item.languages != null"> 
          `Languages` = #{item.languages, jdbcType = LONGVARCHAR},
        </if> 
        <if test="item.sampleGroup != null"> 
          `sample_group` = #{item.sampleGroup, jdbcType = LONGVARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>