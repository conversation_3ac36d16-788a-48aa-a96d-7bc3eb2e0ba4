package com.sgs.testdatabiz.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Configuration for API Request Recorder
 */
@Data
@Component
@ConfigurationProperties(prefix = "api.request.recorder")
public class ApiRequestRecorderConfig {

    /**
     * List of exclusion methods that should not be recorded
     */
    private List<ExclusionMethod> exclusionMethods;

    /**
     * Exclusion method configuration
     */
    @Data
    public static class ExclusionMethod {
        /**
         * Method name to exclude
         */
        private String methodName;
        
        /**
         * System ID to match for exclusion
         */
        private String systemId;
    }
}
