package com.sgs.testdatabiz.facade.model.rsp;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName TestDataConfigRsp
 * @Description 配置输出类
 * <AUTHOR>
 * @Date 2023/5/6
 */
public class TestDataConfigRsp extends PrintFriendliness {

    @ApiModelProperty("customerGroupCode")
    private String customerGroupCode;
    @ApiModelProperty("citationId")
    private Integer citationId;
    @ApiModelProperty("testLineId")
    private Integer testLineId;
    @ApiModelProperty("conditionId")
    private Integer conditionId;
    @ApiModelProperty("相关规则JSON字符串")
    private String rule;

    public String getCustomerGroupCode() {
        return customerGroupCode;
    }

    public void setCustomerGroupCode(String customerGroupCode) {
        this.customerGroupCode = customerGroupCode;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getConditionId() {
        return conditionId;
    }

    public void setConditionId(Integer conditionId) {
        this.conditionId = conditionId;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }
}
