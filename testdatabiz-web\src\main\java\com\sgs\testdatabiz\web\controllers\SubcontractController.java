package com.sgs.testdatabiz.web.controllers;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.SubContractFacade;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.facade.model.req.backsubcontract.SubCompleteTestDataReq;
import com.sgs.testdatabiz.facade.v2.ReportDataBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/subContract")
@Api(value = "/subContract", tags = "subContract")
public class SubcontractController {

    private static final Logger logger = LoggerFactory.getLogger(SubcontractController.class);

    @Autowired
    private SubContractFacade subContractFacade;

    @Autowired
    private ReportDataBizService reportDataBizService;


    @PostMapping("/saveSubCompleteTestData")
    @ApiOperation(value = "分包回传 保存 testData")
    public BaseResponse saveSubCompleteTestData(@RequestBody SubCompleteTestDataReq reqObject) {
        return subContractFacade.saveSubCompleteTestData(reqObject);
    }



    @PostMapping("/getSubTestDataInfo")
    @ApiOperation(value = "分包数据补充")
    public BaseResponse<?> getSubTestDataInfo(@RequestBody ReportDataDTO req) {
        return reportDataBizService.getSubTestDataInfo(req);
    }


}
