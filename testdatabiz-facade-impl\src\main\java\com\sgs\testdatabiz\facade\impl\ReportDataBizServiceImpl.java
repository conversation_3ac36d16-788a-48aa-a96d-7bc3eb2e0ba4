package com.sgs.testdatabiz.facade.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.ReportStatus;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.ReportSourceTypeEnum;
import com.sgs.testdatabiz.core.errorcode.ErrorCode;
import com.sgs.testdatabiz.core.errorcode.ErrorCodeFactory;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.testdatabiz.core.exception.ReportDataCheckException;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportPO;
import com.sgs.testdatabiz.domain.service.rd.ReportDataService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdInvoiceDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdOrderDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdQuotationDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataBatchDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.ReportDataChecker;
import com.sgs.testdatabiz.domain.service.utils.convertor.ReportDataConvertMapper;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.facade.impl.prop.ReportDataBizProps;
import com.sgs.testdatabiz.facade.impl.utils.ValidateReportDataUtil;
import com.sgs.testdatabiz.facade.model.dto.ValidationStatsDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.rsp.RdInvoiceExistRsp;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.rsp.RdInvoiceRsp;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.rsp.RdQuotationExistRsp;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.rsp.RdQuotationRsp;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdReportDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataBatchDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.facade.model.req.rd.ExistQuotationReq;
import com.sgs.testdatabiz.facade.model.req.rd.ExistReportInvoiceReq;
import com.sgs.testdatabiz.facade.model.req.rd.ExportQuotationReq;
import com.sgs.testdatabiz.facade.model.req.rd.ExportReportInvoiceByNosReq;
import com.sgs.testdatabiz.facade.model.req.rd.ExportReportInvoiceReq;
import com.sgs.testdatabiz.facade.model.req.rd.ImportQuotationReq;
import com.sgs.testdatabiz.facade.model.req.rd.ImportReportInvoiceReq;
import com.sgs.testdatabiz.facade.model.req.rd.QuerySubReportDataReq;
import com.sgs.testdatabiz.facade.model.rsp.rd.BatchExistReportDataResp;
import com.sgs.testdatabiz.facade.model.rsp.rd.ExistReportDataResp;
import com.sgs.testdatabiz.facade.v2.ReportDataBizService;
import com.sgs.testdatabiz.integration.CustomerBizClient;
import com.sgs.testdatabiz.integration.model.validation.ValidationRequest;
import com.sgs.testdatabiz.integration.model.validation.ValidationResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 15:09
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReportDataBizServiceImpl implements ReportDataBizService {
    public static final long SYSTEM_ID_OF_RD = 62L;
    //    private ExtensionExecutor extensionExecutor;

    @Autowired
    private ReportDataService reportDataService;

    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private CustomerBizClient customerBizClient;

    private final RedissonClient redissonClient;

    private final ReportDataBizProps props;
    @Autowired
    private ReportDataChecker reportDataChecker;

    @Override
    public BaseResponse<Void> importReportData(ReportDataBatchDTO reportDataDTO, Long labId, String labCode, String version, Boolean flag) throws InterruptedException {
        log.info("ReportData.importReportData: [{}]", Func.isEmpty(reportDataDTO) ? null : JSONObject.toJSONString(reportDataDTO));
        RLock lock = redissonClient.getLock("SCI:REPORT:IMPORT:"+labId + ":" + genKey(reportDataDTO));
        // convert ReportDataDTO --> ReportDataDO
        try {
            safeValidateReportStatus(reportDataDTO, labId, labCode);
            validateReportData(reportDataDTO);
            //做数据转换，其中特殊映射，将OrderNo和RootOrderNo做了映射，将OrderNo作为RootOrderNo
            ReportDataBatchDO reportDataDO = ReportDataConvertMapper.INSTANCE.convert2ReportDataBatchDO(reportDataDTO);

            reportDataDO.setLabId(labId);
            reportDataDO.setLabCode(labCode);

            ReportDataBizProps.LockConfig importConfig = props.getImportConfig();
            boolean isLocked = lock.tryLock(importConfig.getNumOfSecondOfWaitLock(), importConfig.getMaxOfSecondOfOperation(), TimeUnit.SECONDS);
            Assert.isTrue(isLocked, ResponseCode.INTERNAL_SERVER_ERROR, "The Report Importing, please do not execute continuously！");
            try {
                // check reportData exist
                return transactionTemplate.execute(tranStatus -> {
                    boolean importResult = false;
                    if (flag) {
                        // ReportData不存在执行import
                        importResult = reportDataService.importReportData(reportDataDO, version);
                    } else {
                        importResult = reportDataService.updateNonReportInfo(reportDataDO, version);
                    }
                    return importResult ? BaseResponse.newSuccessInstance(true) : BaseResponse.newFailInstance(ResponseCode.FAIL);
                });
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            // 中断异常重新抛出
            Thread.currentThread().interrupt(); // 推荐先恢复中断状态
            throw e;
        }catch(ReportDataCheckException ex){
            log.error("ReportData.importReportData exception: [{}]", ex);
            BaseResponse baseResponse = new BaseResponse(ex.getCode(), ex.getMessage());
            String errorCode = ((ReportDataCheckException) ex).getStdCode();
            baseResponse.setRespCode(errorCode);
            return baseResponse;
        } catch (Exception e) {
            log.error("ReportData.importReportData exception: [{}]", e);
            BaseResponse response = BaseResponse.newFailInstance(ResponseCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
    }

    @Override
    public BaseResponse<Void> cancel(@NotNull Long labId, @NotEmpty String reportNo, Integer reportStatus) {
        return transactionTemplate.execute(
                tranStatus -> {
                    reportDataService.cancel(labId, reportNo, reportStatus);
                    return BaseResponse.newSuccessInstance("success");
                });
    }

    @Override
    public BaseResponse<Void> modifyReportStatusById(@NotNull Long reportId, @NotNull Integer reportStatus) {
        return transactionTemplate.execute(
                tranStatus -> {
                    reportDataService.modifyReportStatusById(reportId, reportStatus);
                    return BaseResponse.newSuccessInstance("success");
                });
    }

    @Override
    public BaseResponse<?> updateReportExpireById(@NotNull Long reportId, @NotNull Integer activeIndicator) {
        return transactionTemplate.execute(transactionStatus -> BaseResponse
                .newInstance(reportDataService.updateReportExpireById(reportId, activeIndicator)));
    }

    @Override
    public BaseResponse<?> updateReportExpire(Long labId, List<String> reportNos, String labCode) {
        return transactionTemplate.execute(transactionStatus -> BaseResponse
                .newInstance(reportDataService.updateReportExpire(labId, reportNos, labCode)));
    }

    private void validateReportData(ReportDataBatchDTO reportDataDTO) {
        ValidationRequest request = ValidateReportDataUtil.getValidationRequestByReportDataBatchDTO(reportDataDTO);
        if(reportDataDTO.isNeedCheck()){
            ValidationResultDTO result = reportDataChecker.check(reportDataDTO);
            if (Func.isNotEmpty(result) && !result.isValidFlag()) {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.REPORT_DATA_ERROR,
                        ErrorBizModelEnum.REPORT_DATA_VALIDATION, ErrorFunctionTypeEnum.VALIDATION,
                        ErrorTypeEnum.DATA_VALIDATE_FAIL);
                throw new ReportDataCheckException(errorCode.getCode(), result.getResultMessage());
            }
        }
        validateReportDataByRemote(request);
    }

    private void safeValidateReportStatus(ReportDataBatchDTO reportDataDTO, Long labId, String labCode) {
        try {
            Optional.ofNullable(reportDataDTO.getReportList())
                    .map(rl -> rl.stream()
                            .filter(Objects::nonNull)
                            .filter(r -> Objects.isNull(r.getReportSourceType()) || Objects.equals(r.getReportSourceType(), ReportSourceTypeEnum.ExeSystemReport.getSourceType()))
                            .map(RdReportDTO::getReportNo)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList())
                    )
                    .filter(CollectionUtils::isNotEmpty)
                    .map(reportNoList ->
                            reportDataService.exportReportHeaderList(labId, reportNoList, labCode)
                    )
                    .filter(CollectionUtils::isNotEmpty)
                    .map(reportHeaderList ->
                            reportHeaderList.stream()
                                    .filter(Objects::nonNull)
                                    .filter(h -> Objects.isNull(h.getReportSourceType()) || Objects.equals(h.getReportSourceType(), ReportSourceTypeEnum.ExeSystemReport.getSourceType()))
                                    .filter(h -> Objects.nonNull(h.getReportStatus()))
                                    .filter(h -> ReportStatus.check(h.getReportStatus(), ReportStatus.Approved, ReportStatus.Completed))
                                    .collect(Collectors.toList())
                    )
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(completedList -> {
                        int systemId = Long.valueOf(SYSTEM_ID_OF_RD).intValue();
                        ValidationStatsDTO dto = new ValidationStatsDTO();
                        dto.setStatsDate(new Date());
                        dto.setSystemIdOfData(Optional.ofNullable(reportDataDTO.getSystemId()).map(Long::intValue).orElse(0));
                        dto.setSystemIfOfHeader(systemId);
                        dto.setMode("Completed");
                        dto.setValidateResult(completedList.stream().map(RdReportPO::getReportNo).filter(Objects::nonNull).distinct().collect(Collectors.joining(",")) + " has Approved");
                        dto.setStatsCount(1);
                        customerBizClient.addStatsRecord(systemId, labCode, dto);
                    });
        } catch (Throwable t) {
            //ignore
        }
    }

    private static String genKey(ReportDataBatchDTO reportDataDTO) {
        return Optional.ofNullable(reportDataDTO.getReportList())
                .map(reportList -> reportList.stream()
                        .map(RdReportDTO::getReportNo)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining("_"))
                )
                .orElse("");
    }

    private boolean validateReportDataByRemote(ValidationRequest reqs) {
        Long originalSystemId = reqs.getSystemId();
        reqs.setSystemId(SYSTEM_ID_OF_RD);
        boolean validateFlag = true;
        try {
            CustomResult<ValidationResponse> customResult = customerBizClient.validationReportData(reqs);
            if (!customResult.isSuccess()) {
                ValidationResponse response = customResult.getData();
                if (Func.isNotEmpty(response)) {
                    // 使用ReportNoLengthValidationService处理报告编号长度校验
                    ValidationResultDTO validationResult = reportDataChecker.validateResponse(response);
                    if (!validationResult.isValidFlag()) {
                        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                            ErrorCategoryEnum.REPORT_DATA_ERROR,
                            ErrorBizModelEnum.REPORT_DATA_VALIDATION,
                            ErrorFunctionTypeEnum.VALIDATION,
                            ErrorTypeEnum.DATA_VALIDATE_FAIL
                        );
                        throw new ReportDataCheckException(errorCode, 400, validationResult.getResultMessage());
                    }
                    
                    // 记录其他错误信息 降低级别
                    log.warn("ReportData.importReportData validate failed, originalSystemId: {} requestData: [{}],result: [{}]",
                        originalSystemId, reqs, response);
                } else {
                    log.warn("ReportData.importReportData validate failed, originalSystemId: {}: meet invoke exception,requestData: [{}],result: [{}]",
                        originalSystemId, reqs, customResult.getMsg());
                }
                validateFlag = false;
            }
        } catch (ReportDataCheckException e) {
            // 直接抛出已经包装好的异常
            throw e;
        } catch (Exception e) {
            log.error("ReportData.importReportData validate failed, originalSystemId: {} meet invoke exception,requestData: [{}]", 
                originalSystemId, reqs, e);
            validateFlag = false;
        }
        return validateFlag;
    }

    // report data query

    @Override
    public BaseResponse<ReportDataDTO> exportReportData(@NotNull Long labId, @NotEmpty String reportNo, @NotEmpty String labCode) {
        return BaseResponse.newInstance(reportDataService.exportReportData(labId, reportNo, labCode));
    }

    @Override
    public BaseResponse<ReportDataDTO> exportReportDataWithFilter(@NotNull Long labId, @NotEmpty String reportNos, @NotEmpty String labCode) {
        return BaseResponse.newInstance(reportDataService.exportReportDataWithFilter(labId, reportNos, labCode,true));
    }

    @Override
    public BaseResponse<List<ReportDataDTO>> exportReportDataListWithFilter(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode, @NotEmpty String testSampleLevel) {
        return BaseResponse.newInstance(reportDataService.exportReportDataListWithFilter(labId, reportNos, labCode,true, testSampleLevel));
    }

    @Override
    public BaseResponse<?> exportReportDataList(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode) {
        return BaseResponse.newInstance(reportDataService.exportReportDataList(labId, reportNos, labCode));
    }

    @Override
    public BaseResponse<?> exportReportHeaderList(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode) {
        return BaseResponse.newInstance(reportDataService.exportReportHeaderList(labId, reportNos, labCode));
    }

    @Override
    public BaseResponse<?> getSubTestDataInfo(@NotNull ReportDataDTO reportDataDO) {
        return BaseResponse.newInstance(reportDataService.getSubTestDataInfo(JSONObject.parseObject(JSONObject.toJSONString(reportDataDO), ReportDataDO.class)));
    }

    @Override
    public BaseResponse<?> exportReportDataListAllStatus(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode) {
        return BaseResponse.newInstance(reportDataService.exportReportDataListAllStatus(labId, reportNos, labCode));
    }

    @Override
    public BaseResponse<?> exportByVersion(Long labId, String reportNo, String labCode) {
        return BaseResponse.newInstance(reportDataService.exportByVersion(labId, reportNo, labCode));
    }

    @Override
    public BaseResponse<?> querySubReportData(QuerySubReportDataReq subReportDataReq) {
        return BaseResponse.newInstance(reportDataService.querySubReportData(subReportDataReq.getLabCode(), subReportDataReq.getOrderNo()));
    }

    @Override
    public BaseResponse<?> exportReportDataByTrfNo(@NotNull Long labId, @NotBlank String trfNo, @NotEmpty String labCode) {
        return BaseResponse.newInstance(reportDataService.exportReportDataByTrfNo(labId, trfNo, labCode));
    }

    @Override
    public BaseResponse<ExistReportDataResp> existReportData(@NotNull Long labId, @NotEmpty String reportNo, List<String> trfList) {
        boolean exist = reportDataService.existReportData(labId, reportNo);
        return BaseResponse.newSuccessInstance(new ExistReportDataResp(exist));
    }

    @Override
    public BaseResponse<BatchExistReportDataResp> batchExistReportData(@NotNull Long labId, @NotEmpty List<String> reportNos) {
        Map<String, Boolean> resultMap = new HashMap<>();
        reportNos.forEach(l -> {
            boolean exist = reportDataService.existReportData(labId, l);
            resultMap.put(l, exist);
        });
        return BaseResponse.newSuccessInstance(new BatchExistReportDataResp(resultMap));
    }


    // invoice operation

    @Override
    public BaseResponse<?> importInvoice(ImportReportInvoiceReq request) {

        try {
            ValidationRequest reqs = ValidateReportDataUtil.getValidationRequestByImportReportInvoiceReq(request);
            validateReportDataByRemote(reqs);
            return transactionTemplate.execute(transactionStatus -> {
                ImportQuotationReq quotationReq = new ImportQuotationReq();
                quotationReq.setReportNo(request.getReportNo());
                quotationReq.setQuotationList(request.getQuotationList());
                quotationReq.setLabCode(request.getLabCode());
                quotationReq.setSystemId(request.getSystemId());
                quotationReq.setLabId(request.getLabId());
                this.importQuotation(quotationReq);
                List<RdInvoiceDTO> invoiceList = request.getInvoiceList();
                List<RdInvoiceDO> invoiceDOList = new ArrayList<>();
                invoiceList.forEach(l -> {
                    //做数据转换，其中特殊映射，将OrderNo和RootOrderNo做了映射，将OrderNo作为RootOrderNo
                    RdInvoiceDO invoiceDO = ReportDataConvertMapper.INSTANCE.convert2RdInvoiceDO(l);
                    RdOrderDO orderDO = ReportDataConvertMapper.INSTANCE.convert2RdOrderDO(request.getOrder());
                    //RdInvoiceDO invoiceDO = JSON.parseObject(JSON.toJSONString(l), RdInvoiceDO.class);
                    invoiceDO.setReportNo(request.getReportNo());
                    invoiceDO.setLabId(request.getLabId());
                    invoiceDO.setSystemId(request.getSystemId());
                    invoiceDO.setOrderNo(orderDO.getOrderNo());
                    invoiceDOList.add(invoiceDO);
                });
                boolean result = reportDataService.importInvoiceList(invoiceDOList, request.getOrder());
                return BaseResponse.newSuccessInstance(result);
            });
        } catch (Exception e) {
            log.error("ReportData.importInvoice exception: [{}]", e);
            BaseResponse response = BaseResponse.newFailInstance(ResponseCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
    }

    @Override
    public BaseResponse<?> exportInvoice(ExportReportInvoiceReq request) {
        List<RdInvoiceDO> list = reportDataService.exportInvoiceListByReportNos(request.getLabId(), request.getReportNos());
        if (Func.isNotEmpty(list)) {
            List<RdInvoiceRsp> resultList = new ArrayList<>();
            list.forEach(l -> {
                RdInvoiceRsp rdInvoiceRsp = JSON.parseObject(JSON.toJSONString(l), RdInvoiceRsp.class);
                resultList.add(rdInvoiceRsp);
            });
            return BaseResponse.newSuccessInstance(resultList);
        }
        return BaseResponse.newSuccessInstance("");
    }

    @Override
    public BaseResponse<?> existInvoice(ExistReportInvoiceReq request) {

        List<String> reportNos = request.getReportNos();
        Long labId = request.getLabId();
        Assert.notNull(reportNos, "reportNos不能为空！");
        Assert.notNull(labId, "labId不能为空！");
        List<RdInvoiceExistRsp> list = new ArrayList<>();
        reportNos.forEach(reportNo -> {
            RdInvoiceExistRsp existRsp = new RdInvoiceExistRsp();
            List<RdInvoiceDO> invoiceDOS = reportDataService.exportInvoiceList(labId, reportNo);
            if (Func.isEmpty(invoiceDOS)) {
                existRsp.setExist(false);
            } else {
                existRsp.setExist(true);
            }
            existRsp.setReportNo(reportNo);
            list.add(existRsp);
        });
        return BaseResponse.newSuccessInstance(list);
    }

    @Override
    public BaseResponse<?> exportInvoice(ExportReportInvoiceByNosReq request) {
        List<RdInvoiceDO> list = reportDataService.exportInvoiceList(request.getLabId(), request.getInvoiceNos());
        if (Func.isNotEmpty(list)) {
            List<RdInvoiceDTO> resultList = new ArrayList<>();
            list.forEach(l -> {
                RdInvoiceDTO rdInvoiceDTO = JSON.parseObject(JSON.toJSONString(l), RdInvoiceDTO.class);
                resultList.add(rdInvoiceDTO);
            });
            return BaseResponse.newSuccessInstance(resultList);
        }
        return BaseResponse.newSuccessInstance("");
    }

    // quoation operation

    @Override
    public BaseResponse<?> importQuotation(ImportQuotationReq request) {
        try {
            ValidationRequest reqs = ValidateReportDataUtil.getValidationRequestByImportReportInvoiceReq(request);
            validateReportDataByRemote(reqs);
            List<RdQuotationDTO> quotationList = request.getQuotationList();
            return transactionTemplate.execute(transactionStatus -> {
                if (Func.isNotEmpty(quotationList)) {
                    List<RdQuotationDO> list = new ArrayList<>();
                    quotationList.forEach(l -> {
                        //做数据转换，其中特殊映射，将OrderNo和RootOrderNo做了映射，将OrderNo作为RootOrderNo
                        RdQuotationDO quotationDO = ReportDataConvertMapper.INSTANCE.convert2RdQuotationDO(l);
                        //RdQuotationDO quotationDO = JSON.parseObject(JSON.toJSONString(l), RdQuotationDO.class);
                        quotationDO.setReportNo(request.getReportNo());
                        quotationDO.setSystemId(request.getSystemId());
                        quotationDO.setLabId(request.getLabId());
                        list.add(quotationDO);
                    });
                    boolean result = reportDataService.importQuotationList(list);
                    return BaseResponse.newSuccessInstance(result);
                }
                return BaseResponse.newSuccessInstance("");
            });
        } catch (Exception e) {
            log.error("ReportData.importQuotation exception: [{}]", e);
            BaseResponse response = BaseResponse.newFailInstance(ResponseCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
    }

    @Override
    public BaseResponse<?> exportQuotation(ExportQuotationReq request) {

        List<RdQuotationDO> quotationDOS = reportDataService.exportQuotation(request.getLabId(), request.getReportNos());
        if (Func.isNotEmpty(quotationDOS)) {
            List<RdQuotationRsp> resultList = new ArrayList<>();
            quotationDOS.forEach(l -> {
                RdQuotationRsp rdQuotationDTO = JSON.parseObject(JSON.toJSONString(l), RdQuotationRsp.class);
                resultList.add(rdQuotationDTO);
            });
            return BaseResponse.newSuccessInstance(resultList);
        }
        return BaseResponse.newSuccessInstance("");
    }

    @Override
    public BaseResponse<?> existQuotation(ExistQuotationReq request) {
        List<RdQuotationExistRsp> list = new ArrayList<>();
        request.getReportNos().forEach(reportNo -> {
            RdQuotationExistRsp existRsp = new RdQuotationExistRsp();
            List<RdQuotationDO> quotationDOS = reportDataService.exportQuotation(request.getLabId(), reportNo);
            if (Func.isEmpty(quotationDOS)) {
                existRsp.setExist(false);
            } else {
                existRsp.setExist(true);
            }
            existRsp.setReportNo(reportNo);
            list.add(existRsp);
        });
        return BaseResponse.newSuccessInstance(list);
    }
}
