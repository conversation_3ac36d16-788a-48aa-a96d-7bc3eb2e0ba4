package com.sgs.testdatabiz.dbstorages.mybatis.config;

import com.sgs.framework.model.enums.ProductLineType;

/**
 *
 */
public class ProductLineContextHolder {
    private static final ThreadLocal<ProductLineType> contextHolder = new ThreadLocal();

    /**
     *
     * @param productLineCode
     */
    public static void setProductLineCode(String productLineCode) {
        setProductLineType(ProductLineType.findProductLineAbbr(productLineCode));
    }

    /**
     *
     * @param productLineType
     */
    public static void setProductLineType(ProductLineType productLineType) {
        contextHolder.remove();
        contextHolder.set(productLineType);
    }

    /**
     *
     * @return
     */
    public static ProductLineType getProductLineType() {
        return contextHolder.get();
    }

    public static String getProductLineCode() {
        ProductLineType productLineType = contextHolder.get();
        if (productLineType == null){
            return null;
        }
        return productLineType.getProductLineAbbr();
    }

    /**
     *
     */
    public static void clear() {
        contextHolder.remove();
    }

}
