package com.sgs.testdatabiz.facade.model.testdata;

import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldLangInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataConclusionRsp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel(value = "TestDataTestMatrixRsp", description = "TestDataTestMatrixRsp")
public class TestDataTestMatrixRsp extends PrintFriendliness {
    /**
     *
     */
    @ApiModelProperty("分包单号/JobNos")
    private String objectNo;

    /**
     *
     */
    @ApiModelProperty("slimJobNo")
    private String externalNo;

    /**
     *
     */
    @ApiModelProperty("外部号")
    private String externalCode;

    /**
     *
     */
    @ApiModelProperty("sampleNo")
    private String sampleNo;

    /**
     *
     */
    @ApiModelProperty("外部 SampleNo")
    private String externalSampleNo;

    /**
     *
     */
    @ApiModelProperty("MaterialName")
    private String materialName;

    /**
     *
     */
    @ApiModelProperty("MaterialTexture")
    private String materialTexture;

    /**
     *
     */
    @ApiModelProperty("UsedPosition")
    private String usedPosition;

    /**
     *
     */
    @ApiModelProperty("conclusion")
    private TestDataConclusionRsp conclusion;

    /**
     *
     */
    @ApiModelProperty("MaterialColor")
    private String materialColor;

    /**
     *
     */
    @ApiModelProperty("testLineId")
    private Integer testLineId;

    /**
     *
     */
    @ApiModelProperty("standardId")
    private Integer standardId;

    /**
     *
     */
    @ApiModelProperty("SystemId; 1:slim   2:fast")
    private Integer systemId;

    /**
     *
     */
    @ApiModelProperty("Test Results")
    private List<TestDataTestResultRsp> testResults;

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public TestDataConclusionRsp getConclusion() {
        return conclusion;
    }

    public void setConclusion(TestDataConclusionRsp conclusion) {
        this.conclusion = conclusion;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    public List<TestDataTestResultRsp> getTestResults() {
        return testResults;
    }

    public void setTestResults(List<TestDataTestResultRsp> testResults) {
        this.testResults = testResults;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()){
            return false;
        }
        TestDataTestMatrixRsp that = (TestDataTestMatrixRsp) o;
        if (objectNo != null ? !objectNo.equals(that.objectNo) : that.objectNo != null){
            return false;
        }
        if (externalNo != null ? !externalNo.equals(that.externalNo) : that.externalNo != null){
            return false;
        }
        if (externalCode != null ? !externalCode.equals(that.externalCode) : that.externalCode != null){
            return false;
        }
        if (sampleNo != null ? !sampleNo.equals(that.sampleNo) : that.sampleNo != null){
            return false;
        }
        if (externalSampleNo != null ? !externalSampleNo.equals(that.externalSampleNo) : that.externalSampleNo != null){
            return false;
        }
        if (materialName != null ? !materialName.equals(that.materialName) : that.materialName != null){
            return false;
        }
        if (materialTexture != null ? !materialTexture.equals(that.materialTexture) : that.materialTexture != null){
            return false;
        }
        if (usedPosition != null ? !usedPosition.equals(that.usedPosition) : that.usedPosition != null){
            return false;
        }
        if (materialColor != null ? !materialColor.equals(that.materialColor) : that.materialColor != null){
            return false;
        }
        if (testLineId != null ? !testLineId.equals(that.testLineId) : that.testLineId != null){
            return false;
        }
        if (standardId != null ? !standardId.equals(that.standardId) : that.standardId != null){
            return false;
        }
        return systemId != null ? !systemId.equals(that.systemId) : that.systemId != null;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((objectNo == null) ? 0 : objectNo.hashCode());
        result = prime * result + ((externalNo == null) ? 0 : externalNo.hashCode());
        result = prime * result + ((externalCode == null) ? 0 : externalCode.hashCode());
        result = prime * result + ((sampleNo == null) ? 0 : sampleNo.hashCode());
        result = prime * result + ((externalSampleNo == null) ? 0 : externalSampleNo.hashCode());
        result = prime * result + ((materialName == null) ? 0 : materialName.hashCode());
        result = prime * result + ((materialTexture == null) ? 0 : materialTexture.hashCode());
        result = prime * result + ((usedPosition == null) ? 0 : usedPosition.hashCode());
        result = prime * result + ((materialColor == null) ? 0 : materialColor.hashCode());
        result = prime * result + ((testLineId == null) ? 0 : testLineId.hashCode());
        result = prime * result + ((standardId == null) ? 0 : standardId.hashCode());
        result = prime * result + ((systemId == null) ? 0 : systemId.hashCode());
        return result;
    }
}
