package com.sgs.testdatabiz.facade.model.req.starlims;

import com.sgs.framework.core.base.BaseRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2022/10/18 0018 15:33
 */
public class ReceiveReportData extends BaseRequest {
    /**
     *
     */
    private Long aId;

    /**
     *
     */
    private Integer citationExternalId;

    /**
     * applicationFactorId、appFactorId
     */
    private Integer applicationFactorId;

    /**
     * appFactorName
     */
    private String applicationFactor;
    /**
     * referFromSampleNo
     * SCI-1378
     */
    private String referFromSampleNo;
    /**
     * referFromReportNo
     * SCI-1378
     */
    private List<String> referFromReportNo;

    /**
     * methodDesc
     */
    private String testMethod;

    /**
     *
     */
    private String id;

    /**
     *
     */
    private String notes;

    /**
     *
     */
    private Integer ppVersionId;

    /**
     * testMatrixId
     */
    private String matrixId;
    /**
     * starLims 回传我们ToStarLims时的matrixId
     */
    private String externalMatrixId;

    /**
     * testLineSeq
     */
    private Long sorter;

    /**
     *
     */
    private Long subPPAID;

    /**
     * testConclusion
     */
    private String conclusion;

    private String conclusionId;

    /**
     *
     */
    private Integer testLineId;

    /**
     * evaluationAlias
     */
    private String testReportName;

    /**
     *
     */
    private List<ReceiveTestResult> testResults;

    /**
     * testSampleNo
     */
    private String materialNumber;

    /**
     * testSampleId
     */
    private String externalId;
    /**
     * testLineInstanceId SCI-1378
     */
    private String testLineInstanceId;

    /**
     * externalSampleNo
     */
    private String sampleNo;

    /**
     * materialName
     */
    private String sampleDescription;

    /**
     * materialColor
     */
    private String sampleColor;

    /**
     * materialTexture
     */
    private String sampleMaterial;

    /**
     *
     */
    private String conclusionAlias;

    /**
     *
     */
    private List<ReceiveConditionInfoReq> condition;

    /**
     *
     */
    private String ppNumber;

    /**
     *  DIG-9653 starlims　字符定义类型　int
     */
    private Integer specNo;
    /**
     * DIG-9653 starlims　字义类型 String 长度　250
     */
    private String specName;

    /**
     *
     */
    private List<ReceiveReportLanguages> languages;

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }

    public String getReferFromSampleNo() {
        return referFromSampleNo;
    }

    public void setReferFromSampleNo(String referFromSampleNo) {
        this.referFromSampleNo = referFromSampleNo;
    }

    public List<String> getReferFromReportNo() {
        return referFromReportNo;
    }

    public void setReferFromReportNo(List<String> referFromReportNo) {
        this.referFromReportNo = referFromReportNo;
    }

    public String getConclusionId() {
        return conclusionId;
    }

    public void setConclusionId(String conclusionId) {
        this.conclusionId = conclusionId;
    }

    public String getExternalMatrixId() {
        return externalMatrixId;
    }

    public void setExternalMatrixId(String externalMatrixId) {
        this.externalMatrixId = externalMatrixId;
    }

    public Long getaId() {
        return aId;
    }

    public void setaId(Long aId) {
        this.aId = aId;
    }

    public Integer getCitationExternalId() {
        return citationExternalId;
    }

    public void setCitationExternalId(Integer citationExternalId) {
        this.citationExternalId = citationExternalId;
    }

    public Integer getApplicationFactorId() {
        return applicationFactorId;
    }

    public void setApplicationFactorId(Integer applicationFactorId) {
        this.applicationFactorId = applicationFactorId;
    }

    public String getApplicationFactor() {
        return applicationFactor;
    }

    public void setApplicationFactor(String applicationFactor) {
        this.applicationFactor = applicationFactor;
    }

    public String getTestMethod() {
        return testMethod;
    }

    public void setTestMethod(String testMethod) {
        this.testMethod = testMethod;
    }

    public Integer getSpecNo() {
        return specNo;
    }

    public void setSpecNo(Integer specNo) {
        this.specNo = specNo;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    public String getMatrixId() {
        return matrixId;
    }

    public void setMatrixId(String matrixId) {
        this.matrixId = matrixId;
    }

    public Long getSorter() {
        return sorter;
    }

    public void setSorter(Long sorter) {
        this.sorter = sorter;
    }

    public Long getSubPPAID() {
        return subPPAID;
    }

    public void setSubPPAID(Long subPPAID) {
        this.subPPAID = subPPAID;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public String getTestReportName() {
        return testReportName;
    }

    public void setTestReportName(String testReportName) {
        this.testReportName = testReportName;
    }

    public List<ReceiveTestResult> getTestResults() {
        return testResults;
    }

    public void setTestResults(List<ReceiveTestResult> testResults) {
        this.testResults = testResults;
    }

    public String getMaterialNumber() {
        return materialNumber;
    }

    public void setMaterialNumber(String materialNumber) {
        this.materialNumber = materialNumber;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getSampleDescription() {
        return sampleDescription;
    }

    public void setSampleDescription(String sampleDescription) {
        this.sampleDescription = sampleDescription;
    }

    public String getSampleColor() {
        return sampleColor;
    }

    public void setSampleColor(String sampleColor) {
        this.sampleColor = sampleColor;
    }

    public String getSampleMaterial() {
        return sampleMaterial;
    }

    public void setSampleMaterial(String sampleMaterial) {
        this.sampleMaterial = sampleMaterial;
    }

    public String getConclusionAlias() {
        return conclusionAlias;
    }

    public void setConclusionAlias(String conclusionAlias) {
        this.conclusionAlias = conclusionAlias;
    }

    public List<ReceiveConditionInfoReq> getCondition() {
        return condition;
    }

    public void setCondition(List<ReceiveConditionInfoReq> condition) {
        this.condition = condition;
    }

    public String getPpNumber() {
        return ppNumber;
    }

    public void setPpNumber(String ppNumber) {
        this.ppNumber = ppNumber;
    }

    public List<ReceiveReportLanguages> getLanguages() {
        return languages;
    }

    public void setLanguages(List<ReceiveReportLanguages> languages) {
        this.languages = languages;
    }
}
