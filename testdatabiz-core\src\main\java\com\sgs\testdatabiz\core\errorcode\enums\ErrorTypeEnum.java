package com.sgs.testdatabiz.core.errorcode.enums;

public enum ErrorTypeEnum {
    REQUESTNULL("01", "请求为空"),
    REQUIREDMISSING("02", "缺少必需字段"),
    TABLE_NAME_NOT_EXIST("03", "数据库表名不存在"),
    DATA_VALIDATE_FAIL("04", "数据校验失败"),
    REPORT_NOT_EXIST("05", "报告不存在"),
    SAVE_REPORT_INVOICE_FAILED("06", "插入报告发票失败"),
    SAVE_REPORT_QUOTATION_FAILED("07", "插入报告报价失败"),
    ;
    private final String code;
    private final String description;

    ErrorTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    public static ErrorTypeEnum fromCode(String code) {
        for (ErrorTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
