package com.sgs.testdatabiz.facade.impl.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("report.biz")
@Data
public class ReportDataBizProps {

    private LockConfig importConfig = new LockConfig();

    @Data
    public static class LockConfig {

        private long numOfSecondOfWaitLock = -1;

        private long maxOfSecondOfOperation = -1;
    }

}
