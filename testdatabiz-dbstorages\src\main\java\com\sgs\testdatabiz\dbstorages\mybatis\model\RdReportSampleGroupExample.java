package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RdReportSampleGroupExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RdReportSampleGroupExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdIsNull() {
            addCriterion("SampleGroupInstanceId is null");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdIsNotNull() {
            addCriterion("SampleGroupInstanceId is not null");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdEqualTo(String value) {
            addCriterion("SampleGroupInstanceId =", value, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdNotEqualTo(String value) {
            addCriterion("SampleGroupInstanceId <>", value, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdGreaterThan(String value) {
            addCriterion("SampleGroupInstanceId >", value, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("SampleGroupInstanceId >=", value, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdLessThan(String value) {
            addCriterion("SampleGroupInstanceId <", value, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("SampleGroupInstanceId <=", value, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdLike(String value) {
            addCriterion("SampleGroupInstanceId like", value, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdNotLike(String value) {
            addCriterion("SampleGroupInstanceId not like", value, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdIn(List<String> values) {
            addCriterion("SampleGroupInstanceId in", values, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdNotIn(List<String> values) {
            addCriterion("SampleGroupInstanceId not in", values, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdBetween(String value1, String value2) {
            addCriterion("SampleGroupInstanceId between", value1, value2, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupInstanceIdNotBetween(String value1, String value2) {
            addCriterion("SampleGroupInstanceId not between", value1, value2, "sampleGroupInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleIdIsNull() {
            addCriterion("SampleId is null");
            return (Criteria) this;
        }

        public Criteria andSampleIdIsNotNull() {
            addCriterion("SampleId is not null");
            return (Criteria) this;
        }

        public Criteria andSampleIdEqualTo(String value) {
            addCriterion("SampleId =", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdNotEqualTo(String value) {
            addCriterion("SampleId <>", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdGreaterThan(String value) {
            addCriterion("SampleId >", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdGreaterThanOrEqualTo(String value) {
            addCriterion("SampleId >=", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdLessThan(String value) {
            addCriterion("SampleId <", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdLessThanOrEqualTo(String value) {
            addCriterion("SampleId <=", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdLike(String value) {
            addCriterion("SampleId like", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdNotLike(String value) {
            addCriterion("SampleId not like", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdIn(List<String> values) {
            addCriterion("SampleId in", values, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdNotIn(List<String> values) {
            addCriterion("SampleId not in", values, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdBetween(String value1, String value2) {
            addCriterion("SampleId between", value1, value2, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdNotBetween(String value1, String value2) {
            addCriterion("SampleId not between", value1, value2, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdIsNull() {
            addCriterion("SampleGroupId is null");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdIsNotNull() {
            addCriterion("SampleGroupId is not null");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdEqualTo(String value) {
            addCriterion("SampleGroupId =", value, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdNotEqualTo(String value) {
            addCriterion("SampleGroupId <>", value, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdGreaterThan(String value) {
            addCriterion("SampleGroupId >", value, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdGreaterThanOrEqualTo(String value) {
            addCriterion("SampleGroupId >=", value, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdLessThan(String value) {
            addCriterion("SampleGroupId <", value, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdLessThanOrEqualTo(String value) {
            addCriterion("SampleGroupId <=", value, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdLike(String value) {
            addCriterion("SampleGroupId like", value, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdNotLike(String value) {
            addCriterion("SampleGroupId not like", value, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdIn(List<String> values) {
            addCriterion("SampleGroupId in", values, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdNotIn(List<String> values) {
            addCriterion("SampleGroupId not in", values, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdBetween(String value1, String value2) {
            addCriterion("SampleGroupId between", value1, value2, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andSampleGroupIdNotBetween(String value1, String value2) {
            addCriterion("SampleGroupId not between", value1, value2, "sampleGroupId");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("ActiveIndicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("ActiveIndicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Byte value) {
            addCriterion("ActiveIndicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Byte value) {
            addCriterion("ActiveIndicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Byte value) {
            addCriterion("ActiveIndicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Byte value) {
            addCriterion("ActiveIndicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Byte value) {
            addCriterion("ActiveIndicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Byte value) {
            addCriterion("ActiveIndicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Byte> values) {
            addCriterion("ActiveIndicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Byte> values) {
            addCriterion("ActiveIndicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Byte value1, Byte value2) {
            addCriterion("ActiveIndicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Byte value1, Byte value2) {
            addCriterion("ActiveIndicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("ReportNo is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("ReportNo is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("ReportNo =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("ReportNo <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("ReportNo >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("ReportNo >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("ReportNo <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("ReportNo <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("ReportNo like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("ReportNo not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("ReportNo in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("ReportNo not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("ReportNo between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("ReportNo not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("OrderNo is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("OrderNo is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("OrderNo =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("OrderNo <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("OrderNo >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("OrderNo >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("OrderNo <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("OrderNo <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("OrderNo like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("OrderNo not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("OrderNo in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("OrderNo not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("OrderNo between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("OrderNo not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("LastModifiedTimestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("LastModifiedTimestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("LastModifiedTimestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("LastModifiedTimestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("LastModifiedTimestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("LastModifiedTimestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("LastModifiedTimestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("LastModifiedTimestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeIsNull() {
            addCriterion("CeatedTime is null");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeIsNotNull() {
            addCriterion("CeatedTime is not null");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeEqualTo(Date value) {
            addCriterion("CeatedTime =", value, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeNotEqualTo(Date value) {
            addCriterion("CeatedTime <>", value, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeGreaterThan(Date value) {
            addCriterion("CeatedTime >", value, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CeatedTime >=", value, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeLessThan(Date value) {
            addCriterion("CeatedTime <", value, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeLessThanOrEqualTo(Date value) {
            addCriterion("CeatedTime <=", value, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeIn(List<Date> values) {
            addCriterion("CeatedTime in", values, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeNotIn(List<Date> values) {
            addCriterion("CeatedTime not in", values, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeBetween(Date value1, Date value2) {
            addCriterion("CeatedTime between", value1, value2, "ceatedTime");
            return (Criteria) this;
        }

        public Criteria andCeatedTimeNotBetween(Date value1, Date value2) {
            addCriterion("CeatedTime not between", value1, value2, "ceatedTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}