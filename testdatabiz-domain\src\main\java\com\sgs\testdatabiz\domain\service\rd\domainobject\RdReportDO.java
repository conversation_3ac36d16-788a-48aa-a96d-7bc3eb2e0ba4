/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCertificateDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProductDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdSampleDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportCertificateDTO;
import com.sgs.testdatabiz.facade.model.enums.ReportType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdReportDO {

    private Long id;

    private Integer systemId;
    // add 20230529
    private String orderNo;
    private String realOrderNo;
    private String reportId;
    private String reportNo;
    private Date reportDueDate;
    private String approveBy;
    private Date approveDate;
    private Long actualTat;
    private Date softCopyDeliveryDate;
    private String originalReportNo;
    private String rootReportNo;
    private String oldReportNo;
    private String reportVersion;
    private String rslstatus;
    private String failCode;
    private Integer reportStatus;
    private String certificateName;
    private String createBy;
    private Date createDate;
    private String testMatrixMergeMode;
    private RdLabDO lab;
    private RdConclusionDO conclusion;
    private List<RdReportMatrixDO> reportMatrixList;
    private List<RdSubReportDO> subReportList;
    private List<RdAttachmentDO> reportFileList;
    private List<ReportCertificateDO> reportCertificateList;
    private RdEfilingDO eFiling;
    private RdRelationshipDO relationship;
    private List<RdProductDO> productList;
    private List<RdSampleDO> sampleList;
    // add 20231103
    private String excludeCustomerInterface;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String reportRemark;
    private Integer reportSourceType;
    //SCI-1378 添加证书信息
    private RdCertificateDTO certificate;
    //SCI-1378 json字符串
    private String signList;
    private ReportType reportType;
    //SubReport 的情况下追加，例如Starlims、内部分包等
    private String reportSource;
    private String testingType;
    private String countryOfDestination;
    private String metaData;
}
