/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCustomerDO {
    private Integer customerUsage;
    private Long bossNo;
    private String customerGroupCode;
    private String customerGroupName;
    private String customerName;
    private String customerAddress;
    private String marketSegmentCode;
    private String marketSegmentName;
    private String customerRefId;
    private List<RdCustomerLanguageDO> languageList;
    private List<RdCustomerContactDO> customerContactList;

    private String customerInstanceId;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
