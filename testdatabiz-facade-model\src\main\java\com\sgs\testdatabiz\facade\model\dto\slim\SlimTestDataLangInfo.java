package com.sgs.testdatabiz.facade.model.dto.slim;

import com.sgs.framework.core.common.PrintFriendliness;

/**
 * <AUTHOR>
 * @date 2022/11/29 14:29
 */
public class SlimTestDataLangInfo extends PrintFriendliness {
    private Integer languageId;
    private String testAnalyteName;
    private String reportUnit;

    private String analyteAlias;
    private String analyteName;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getAnalyteAlias() {
        return analyteAlias;
    }

    public void setAnalyteAlias(String analyteAlias) {
        this.analyteAlias = analyteAlias;
    }

    public String getAnalyteName() {
        return analyteName;
    }

    public void setAnalyteName(String analyteName) {
        this.analyteName = analyteName;
    }
}
