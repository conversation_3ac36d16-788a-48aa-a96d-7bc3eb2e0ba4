/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestLineDO {

    // add 20230529
    private String orderNo;
    private String realOrderNo;
    // SCI-1490
    private String reportNo;
    // add 20230529
    private Integer systemId;
    private String testLineInstanceId;
    private String testItemNo;
    private Integer testLineType;
    private Long testLineBaseId;
    private Integer testLineId;
    private Integer testLineVersionId;
    private String evaluationAlias;
    private String evaluationName;
    private Integer testLineStatus;
    private Integer testLineSeq;
    private Long labSectionBaseId;
    private String labTeam;
    private String productLineAbbr;
    private String testLineRemark;
    //SCI-1378 增加labSectionName
    private String labSectionName;
    private RdTestLineExternalDO external;
    private RdCitationDO citation;
    private RdWiDO wi;
    private List<RdAnalyteDO> analyteList;
    private List<RdPpTestLineRelDO> ppTestLineRelList;
    private RdConclusionDO conclusion;
    private List<RdTestLineLanguageDO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
