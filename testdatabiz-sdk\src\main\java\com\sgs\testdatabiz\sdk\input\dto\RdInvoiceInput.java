/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdInvoiceInput implements Serializable{
    // add 20230529
    private String orderNo;
    // add 20230529
    private Integer systemId;
    private String invoiceNo;
    private List<String> quotationNos;
    private String currency;
    private BigDecimal netAmount;
    private BigDecimal vatAmount;
    private BigDecimal totalAmount;
    // add 20230720
    private BigDecimal prePaidAmount;
    private Integer invoiceStatus;
    private List<RdAttachmentInput> invoiceFileList;

    private String reportNo;

    private String invoiceInstanceId;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
