/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCustomerContactDTO implements Serializable{

    private String customerContactId;
    private String customerContactAddressId;
    private Long bossContactId;
    private Long bossSiteUseId;
    private String contactName;
    private String contactTelephone;
    private String contactMobile;
    private String contactFAX;
    private String contactEmail;
    private String sgsUserId;
    private String sgsAccountCode;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
