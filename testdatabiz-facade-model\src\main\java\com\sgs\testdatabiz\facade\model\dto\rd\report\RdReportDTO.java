/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import com.sgs.testdatabiz.facade.model.enums.ReportType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdReportDTO extends RdOrderHeaderDTO implements Serializable{
    @ApiModelProperty(value = "systemId",dataType = "integer", required = true)
    private Integer systemId;

    @ApiModelProperty(value = "reportId",dataType = "string", required = true)
    private String reportId;
    private Long actualTat;
    @ApiModelProperty(value = "reportNo",dataType = "string", required = true)
    private String reportNo;
    @ApiModelProperty(value = "reportDueDate",dataType = "date", required = true)
    private Date reportDueDate;
    private String approveBy;
    private Date approveDate;
    private Date softCopyDeliveryDate;
    private String originalReportNo;
    private String rootReportNo;
    private String oldReportNo;
    private String reportVersion;
    private String rslstatus;
    private String failCode;
    @ApiModelProperty(value = "reportStatus",dataType = "integer", required = true)
    private Integer reportStatus;
    //SCI-1366 reportSourceType，1 执行系统 2：CustomerReport
    private Integer reportSourceType;
    private String certificateName;
    @ApiModelProperty(value = "createBy",dataType = "string", required = true)
    private String createBy;
    @ApiModelProperty(value = "createDate",dataType = "date", required = true)
    private Date createDate;
    @ApiModelProperty(value="lab",dataType = "object", required = true)
    private String testMatrixMergeMode;
    private RdLabDTO lab;
    private RdConclusionDTO conclusion;
    private List<ReportCertificateDTO> reportCertificateList;
    private RdEfilingDTO eFiling;
    @ApiModelProperty(value = "reportMatrixList",dataType = "List", required = true)
    private List<RdReportMatrixDTO> reportMatrixList;
    private List<RdSubReportDTO> subReportList;
    @ApiModelProperty(value = "reportFileList",dataType = "List", required = true)
    private List<RdAttachmentDTO> reportFileList;
    private List<RdProductDTO> productList;
    private List<RdSampleDTO> sampleList;
    private RdRelationshipDTO relationship;

    // add 20231103
    private String excludeCustomerInterface;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
    @ApiModelProperty(value = "reportInstanceId",dataType = "string", required = true)
    private String reportInstanceId;

    private String reportRemark;
    //SCI-1378 添加证书信息
    private RdCertificateDTO certificateRequirement;
    //SCI-1378 json字符串
    private String signList;
    private ReportType reportType;
    //SubReport 的情况下追加，例如Starlims、内部分包等
    private String reportSource;
    private String testingType;
    private String countryOfDestination;
    //json字符串
    private String metaData;
}
