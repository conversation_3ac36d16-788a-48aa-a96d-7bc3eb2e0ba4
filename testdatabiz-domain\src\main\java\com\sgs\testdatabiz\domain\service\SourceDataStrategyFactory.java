package com.sgs.testdatabiz.domain.service;

import com.google.common.collect.Maps;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.core.annotation.SourceType;
import com.sgs.testdatabiz.domain.service.testdata.info.AbstractTestDataDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.SortedMap;

@Component
public class SourceDataStrategyFactory<TInput, TOutput> implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(SourceDataStrategyFactory.class);
    private final SortedMap<Integer, AbstractTestDataDto> eventMaps = Maps.newTreeMap();
    @Autowired
    private List<AbstractTestDataService> abstractTestDatas;

    @Override
    public void afterPropertiesSet() {
        for (AbstractTestDataService abstractTestDataService : this.abstractTestDatas) {
            Class<?> targetClass = AopUtils.getTargetClass(abstractTestDataService);
            SourceType eventType = AnnotationUtils.getAnnotation(targetClass, SourceType.class);
            if (eventType == null) {
                return;
            }
            SourceTypeEnum sourceTypeEnum = eventType.sourceType();
            if (sourceTypeEnum == null) {
                return;
            }
            AbstractTestDataDto abstractTestDataDto = new AbstractTestDataDto();
            abstractTestDataDto.setSourceTypeEnum(sourceTypeEnum);
            abstractTestDataDto.setTestDataService(abstractTestDataService);

            this.eventMaps.put(sourceTypeEnum.getCode(), abstractTestDataDto);
        }
    }

    /**
     * @param sourceTypeEnum
     * @return
     */
    public CustomResult doInvoke(TInput tInput, SourceTypeEnum sourceTypeEnum) {
        CustomResult rspResult = new CustomResult(true);

        if (sourceTypeEnum == null || !this.eventMaps.containsKey(sourceTypeEnum.getCode())) {
            return rspResult.fail("请检查数据来源");
        }
        AbstractTestDataDto abstractTestDataDto = this.eventMaps.get(sourceTypeEnum.getCode());

        CustomResult customResult = abstractTestDataDto.getTestDataService().doInvoke(tInput);
        if (!customResult.isSuccess()) {
            if (customResult.isIgnore()) {
                return CustomResult.newSuccessInstance();
            }
            logger.info("sourceTypeEnum.{} 处理终止.", sourceTypeEnum);
            return customResult;
        }

        rspResult.setSuccess(true);
        return rspResult;

    }


}
