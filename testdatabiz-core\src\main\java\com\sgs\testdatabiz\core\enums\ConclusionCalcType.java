package com.sgs.testdatabiz.core.enums;

public enum ConclusionCalcType {
    None(0, "None"),
    TestLine(1, "TestLine"),
    OriginalSample(2, "OriginalSample"),
    Report(3, "Report"),
    Section(4, "Section"),
    PP(5, "PP");

    private int code;
    private String message;

    ConclusionCalcType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
} 