package com.sgs.testdatabiz.domain.service.validation.model;

import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataBatchDTO;
import com.sgs.testdatabiz.integration.model.validation.ValidationResponse;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报告测试数据校验请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportTestDataValidationDTO extends ValidationRequestDTO<ReportDataBatchDTO> {
    /**
     * 报告批量数据
     */
    private ReportDataBatchDTO reportDataBatchDTO;

    /**
     * 校验响应
     */
    private ValidationResponse validationResponse;
}
