/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdProductAttributeDTO implements Serializable {

    private String productAttributeInstanceId;
    private Integer productAttributeId;
    private String productAttributeName;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
    private List<RdProductAttributeLangDTO> languageList;
    @Data
    public static class RdProductAttributeLangDTO implements Serializable {
        private Integer languageId;
        private String productAttributeName;
    }
}
