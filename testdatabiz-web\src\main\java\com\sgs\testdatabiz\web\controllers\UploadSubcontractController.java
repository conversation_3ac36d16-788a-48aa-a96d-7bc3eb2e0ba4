package com.sgs.testdatabiz.web.controllers;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.SubContractFacade;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubTestDataReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubcontractNoReq;
import com.sgs.testdatabiz.facade.model.rsp.subcontract.ReportDataRsp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/uploadSubContract")
@Api(value = "/uploadSubContract", tags = "uploadSubContract")
public class UploadSubcontractController {

    private static final Logger logger = LoggerFactory.getLogger(UploadSubcontractController.class);

    @Autowired
    private SubContractFacade subContractFacade;



    @PostMapping("/querySubTestData")
    @ApiOperation(value = "查询分包testData")
    public BaseResponse<ReportDataRsp> querySubTestData(@RequestBody SubcontractNoReq reqObject){
        return subContractFacade.querySubTestData(reqObject);
    }

    @PostMapping("/cancelSubTestData")
    @ApiOperation(value = "")
    public BaseResponse cancelSubTestData(@RequestBody SubcontractNoReq reqObject){
        return subContractFacade.cancelSubTestData(reqObject);
    }

    @PostMapping("/saveReportData")
    @ApiOperation(value = "分包上传 保存testData数据")
    public BaseResponse saveReportData(@RequestBody SubTestDataReq reqObject){
        return subContractFacade.saveEnterSubContractTestData(reqObject);
    }

}
