package com.sgs.testdatabiz.core.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.alicp.jetcache.CacheBuilder;
import com.alicp.jetcache.anno.CacheConsts;
import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.anno.support.GlobalCacheConfig;
import com.alicp.jetcache.anno.support.SpringConfigProvider;
import com.alicp.jetcache.embedded.LinkedHashMapCacheBuilder;
import com.alicp.jetcache.support.FastjsonKeyConvertor;

/**
 * JetCache配置类
 * 配置本地缓存
 */
@Configuration
@EnableMethodCache(basePackages = "com.sgs.testdatabiz")
@EnableCreateCacheAnnotation
public class JetCacheConfig {

    /**
     * 配置本地缓存
     */
    @Bean
    public GlobalCacheConfig globalCacheConfig() {
        Map<String, CacheBuilder> localBuilders = new HashMap<>();
        
        // 配置本地缓存
        CacheBuilder localBuilder = LinkedHashMapCacheBuilder
                .createLinkedHashMapCacheBuilder()
                .keyConvertor(FastjsonKeyConvertor.INSTANCE)
                .limit(100);
        
        localBuilders.put(CacheConsts.DEFAULT_AREA, localBuilder);

        GlobalCacheConfig globalCacheConfig = new GlobalCacheConfig();
        globalCacheConfig.setLocalCacheBuilders(localBuilders);
        // 区域名称不作为缓存key前缀
        globalCacheConfig.setAreaInCacheName(false);
        
        return globalCacheConfig;
    }

    @Bean
    public SpringConfigProvider springConfigProvider() {
        return new SpringConfigProvider();
    }
} 