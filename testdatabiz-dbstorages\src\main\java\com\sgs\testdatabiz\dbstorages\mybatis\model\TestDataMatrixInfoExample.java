package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestDataMatrixInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TestDataMatrixInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andObjectrelidIsNull() {
            addCriterion("ObjectRelId is null");
            return (Criteria) this;
        }

        public Criteria andObjectrelidIsNotNull() {
            addCriterion("ObjectRelId is not null");
            return (Criteria) this;
        }

        public Criteria andObjectrelidEqualTo(String value) {
            addCriterion("ObjectRelId =", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidNotEqualTo(String value) {
            addCriterion("ObjectRelId <>", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidGreaterThan(String value) {
            addCriterion("ObjectRelId >", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidGreaterThanOrEqualTo(String value) {
            addCriterion("ObjectRelId >=", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidLessThan(String value) {
            addCriterion("ObjectRelId <", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidLessThanOrEqualTo(String value) {
            addCriterion("ObjectRelId <=", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidLike(String value) {
            addCriterion("ObjectRelId like", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidNotLike(String value) {
            addCriterion("ObjectRelId not like", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidIn(List<String> values) {
            addCriterion("ObjectRelId in", values, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidNotIn(List<String> values) {
            addCriterion("ObjectRelId not in", values, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidBetween(String value1, String value2) {
            addCriterion("ObjectRelId between", value1, value2, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidNotBetween(String value1, String value2) {
            addCriterion("ObjectRelId not between", value1, value2, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidIsNull() {
            addCriterion("TestMatrixId is null");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidIsNotNull() {
            addCriterion("TestMatrixId is not null");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidEqualTo(String value) {
            addCriterion("TestMatrixId =", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidNotEqualTo(String value) {
            addCriterion("TestMatrixId <>", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidGreaterThan(String value) {
            addCriterion("TestMatrixId >", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidGreaterThanOrEqualTo(String value) {
            addCriterion("TestMatrixId >=", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidLessThan(String value) {
            addCriterion("TestMatrixId <", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidLessThanOrEqualTo(String value) {
            addCriterion("TestMatrixId <=", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidLike(String value) {
            addCriterion("TestMatrixId like", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidNotLike(String value) {
            addCriterion("TestMatrixId not like", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidIn(List<String> values) {
            addCriterion("TestMatrixId in", values, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidNotIn(List<String> values) {
            addCriterion("TestMatrixId not in", values, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidBetween(String value1, String value2) {
            addCriterion("TestMatrixId between", value1, value2, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidNotBetween(String value1, String value2) {
            addCriterion("TestMatrixId not between", value1, value2, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidIsNull() {
            addCriterion("TestLineMappingId is null");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidIsNotNull() {
            addCriterion("TestLineMappingId is not null");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidEqualTo(Integer value) {
            addCriterion("TestLineMappingId =", value, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidNotEqualTo(Integer value) {
            addCriterion("TestLineMappingId <>", value, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidGreaterThan(Integer value) {
            addCriterion("TestLineMappingId >", value, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestLineMappingId >=", value, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidLessThan(Integer value) {
            addCriterion("TestLineMappingId <", value, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidLessThanOrEqualTo(Integer value) {
            addCriterion("TestLineMappingId <=", value, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidIn(List<Integer> values) {
            addCriterion("TestLineMappingId in", values, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidNotIn(List<Integer> values) {
            addCriterion("TestLineMappingId not in", values, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidBetween(Integer value1, Integer value2) {
            addCriterion("TestLineMappingId between", value1, value2, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andTestlinemappingidNotBetween(Integer value1, Integer value2) {
            addCriterion("TestLineMappingId not between", value1, value2, "testlinemappingid");
            return (Criteria) this;
        }

        public Criteria andExternalidIsNull() {
            addCriterion("ExternalId is null");
            return (Criteria) this;
        }

        public Criteria andExternalidIsNotNull() {
            addCriterion("ExternalId is not null");
            return (Criteria) this;
        }

        public Criteria andExternalidEqualTo(String value) {
            addCriterion("ExternalId =", value, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidNotEqualTo(String value) {
            addCriterion("ExternalId <>", value, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidGreaterThan(String value) {
            addCriterion("ExternalId >", value, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidGreaterThanOrEqualTo(String value) {
            addCriterion("ExternalId >=", value, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidLessThan(String value) {
            addCriterion("ExternalId <", value, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidLessThanOrEqualTo(String value) {
            addCriterion("ExternalId <=", value, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidLike(String value) {
            addCriterion("ExternalId like", value, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidNotLike(String value) {
            addCriterion("ExternalId not like", value, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidIn(List<String> values) {
            addCriterion("ExternalId in", values, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidNotIn(List<String> values) {
            addCriterion("ExternalId not in", values, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidBetween(String value1, String value2) {
            addCriterion("ExternalId between", value1, value2, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalidNotBetween(String value1, String value2) {
            addCriterion("ExternalId not between", value1, value2, "externalid");
            return (Criteria) this;
        }

        public Criteria andExternalcodeIsNull() {
            addCriterion("ExternalCode is null");
            return (Criteria) this;
        }

        public Criteria andExternalcodeIsNotNull() {
            addCriterion("ExternalCode is not null");
            return (Criteria) this;
        }

        public Criteria andExternalcodeEqualTo(String value) {
            addCriterion("ExternalCode =", value, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeNotEqualTo(String value) {
            addCriterion("ExternalCode <>", value, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeGreaterThan(String value) {
            addCriterion("ExternalCode >", value, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeGreaterThanOrEqualTo(String value) {
            addCriterion("ExternalCode >=", value, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeLessThan(String value) {
            addCriterion("ExternalCode <", value, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeLessThanOrEqualTo(String value) {
            addCriterion("ExternalCode <=", value, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeLike(String value) {
            addCriterion("ExternalCode like", value, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeNotLike(String value) {
            addCriterion("ExternalCode not like", value, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeIn(List<String> values) {
            addCriterion("ExternalCode in", values, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeNotIn(List<String> values) {
            addCriterion("ExternalCode not in", values, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeBetween(String value1, String value2) {
            addCriterion("ExternalCode between", value1, value2, "externalcode");
            return (Criteria) this;
        }

        public Criteria andExternalcodeNotBetween(String value1, String value2) {
            addCriterion("ExternalCode not between", value1, value2, "externalcode");
            return (Criteria) this;
        }

        public Criteria andPpversionidIsNull() {
            addCriterion("PpVersionId is null");
            return (Criteria) this;
        }

        public Criteria andPpversionidIsNotNull() {
            addCriterion("PpVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andPpversionidEqualTo(Integer value) {
            addCriterion("PpVersionId =", value, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidNotEqualTo(Integer value) {
            addCriterion("PpVersionId <>", value, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidGreaterThan(Integer value) {
            addCriterion("PpVersionId >", value, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidGreaterThanOrEqualTo(Integer value) {
            addCriterion("PpVersionId >=", value, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidLessThan(Integer value) {
            addCriterion("PpVersionId <", value, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidLessThanOrEqualTo(Integer value) {
            addCriterion("PpVersionId <=", value, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidIn(List<Integer> values) {
            addCriterion("PpVersionId in", values, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidNotIn(List<Integer> values) {
            addCriterion("PpVersionId not in", values, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidBetween(Integer value1, Integer value2) {
            addCriterion("PpVersionId between", value1, value2, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andPpversionidNotBetween(Integer value1, Integer value2) {
            addCriterion("PpVersionId not between", value1, value2, "ppversionid");
            return (Criteria) this;
        }

        public Criteria andAidIsNull() {
            addCriterion("Aid is null");
            return (Criteria) this;
        }

        public Criteria andAidIsNotNull() {
            addCriterion("Aid is not null");
            return (Criteria) this;
        }

        public Criteria andAidEqualTo(Long value) {
            addCriterion("Aid =", value, "aid");
            return (Criteria) this;
        }

        public Criteria andAidNotEqualTo(Long value) {
            addCriterion("Aid <>", value, "aid");
            return (Criteria) this;
        }

        public Criteria andAidGreaterThan(Long value) {
            addCriterion("Aid >", value, "aid");
            return (Criteria) this;
        }

        public Criteria andAidGreaterThanOrEqualTo(Long value) {
            addCriterion("Aid >=", value, "aid");
            return (Criteria) this;
        }

        public Criteria andAidLessThan(Long value) {
            addCriterion("Aid <", value, "aid");
            return (Criteria) this;
        }

        public Criteria andAidLessThanOrEqualTo(Long value) {
            addCriterion("Aid <=", value, "aid");
            return (Criteria) this;
        }

        public Criteria andAidIn(List<Long> values) {
            addCriterion("Aid in", values, "aid");
            return (Criteria) this;
        }

        public Criteria andAidNotIn(List<Long> values) {
            addCriterion("Aid not in", values, "aid");
            return (Criteria) this;
        }

        public Criteria andAidBetween(Long value1, Long value2) {
            addCriterion("Aid between", value1, value2, "aid");
            return (Criteria) this;
        }

        public Criteria andAidNotBetween(Long value1, Long value2) {
            addCriterion("Aid not between", value1, value2, "aid");
            return (Criteria) this;
        }

        public Criteria andTestlineidIsNull() {
            addCriterion("TestLineId is null");
            return (Criteria) this;
        }

        public Criteria andTestlineidIsNotNull() {
            addCriterion("TestLineId is not null");
            return (Criteria) this;
        }

        public Criteria andTestlineidEqualTo(Integer value) {
            addCriterion("TestLineId =", value, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidNotEqualTo(Integer value) {
            addCriterion("TestLineId <>", value, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidGreaterThan(Integer value) {
            addCriterion("TestLineId >", value, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestLineId >=", value, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidLessThan(Integer value) {
            addCriterion("TestLineId <", value, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidLessThanOrEqualTo(Integer value) {
            addCriterion("TestLineId <=", value, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidIn(List<Integer> values) {
            addCriterion("TestLineId in", values, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidNotIn(List<Integer> values) {
            addCriterion("TestLineId not in", values, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidBetween(Integer value1, Integer value2) {
            addCriterion("TestLineId between", value1, value2, "testlineid");
            return (Criteria) this;
        }

        public Criteria andTestlineidNotBetween(Integer value1, Integer value2) {
            addCriterion("TestLineId not between", value1, value2, "testlineid");
            return (Criteria) this;
        }

        public Criteria andCitationidIsNull() {
            addCriterion("CitationId is null");
            return (Criteria) this;
        }

        public Criteria andCitationidIsNotNull() {
            addCriterion("CitationId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationidEqualTo(Integer value) {
            addCriterion("CitationId =", value, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidNotEqualTo(Integer value) {
            addCriterion("CitationId <>", value, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidGreaterThan(Integer value) {
            addCriterion("CitationId >", value, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationId >=", value, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidLessThan(Integer value) {
            addCriterion("CitationId <", value, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidLessThanOrEqualTo(Integer value) {
            addCriterion("CitationId <=", value, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidIn(List<Integer> values) {
            addCriterion("CitationId in", values, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidNotIn(List<Integer> values) {
            addCriterion("CitationId not in", values, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidBetween(Integer value1, Integer value2) {
            addCriterion("CitationId between", value1, value2, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationidNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationId not between", value1, value2, "citationid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidIsNull() {
            addCriterion("CitationVersionId is null");
            return (Criteria) this;
        }

        public Criteria andCitationversionidIsNotNull() {
            addCriterion("CitationVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationversionidEqualTo(Integer value) {
            addCriterion("CitationVersionId =", value, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidNotEqualTo(Integer value) {
            addCriterion("CitationVersionId <>", value, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidGreaterThan(Integer value) {
            addCriterion("CitationVersionId >", value, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationVersionId >=", value, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidLessThan(Integer value) {
            addCriterion("CitationVersionId <", value, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidLessThanOrEqualTo(Integer value) {
            addCriterion("CitationVersionId <=", value, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidIn(List<Integer> values) {
            addCriterion("CitationVersionId in", values, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidNotIn(List<Integer> values) {
            addCriterion("CitationVersionId not in", values, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidBetween(Integer value1, Integer value2) {
            addCriterion("CitationVersionId between", value1, value2, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationversionidNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationVersionId not between", value1, value2, "citationversionid");
            return (Criteria) this;
        }

        public Criteria andCitationtypeIsNull() {
            addCriterion("CitationType is null");
            return (Criteria) this;
        }

        public Criteria andCitationtypeIsNotNull() {
            addCriterion("CitationType is not null");
            return (Criteria) this;
        }

        public Criteria andCitationtypeEqualTo(Integer value) {
            addCriterion("CitationType =", value, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeNotEqualTo(Integer value) {
            addCriterion("CitationType <>", value, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeGreaterThan(Integer value) {
            addCriterion("CitationType >", value, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationType >=", value, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeLessThan(Integer value) {
            addCriterion("CitationType <", value, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeLessThanOrEqualTo(Integer value) {
            addCriterion("CitationType <=", value, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeIn(List<Integer> values) {
            addCriterion("CitationType in", values, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeNotIn(List<Integer> values) {
            addCriterion("CitationType not in", values, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeBetween(Integer value1, Integer value2) {
            addCriterion("CitationType between", value1, value2, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationtypeNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationType not between", value1, value2, "citationtype");
            return (Criteria) this;
        }

        public Criteria andCitationnameIsNull() {
            addCriterion("CitationName is null");
            return (Criteria) this;
        }

        public Criteria andCitationnameIsNotNull() {
            addCriterion("CitationName is not null");
            return (Criteria) this;
        }

        public Criteria andCitationnameEqualTo(String value) {
            addCriterion("CitationName =", value, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameNotEqualTo(String value) {
            addCriterion("CitationName <>", value, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameGreaterThan(String value) {
            addCriterion("CitationName >", value, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameGreaterThanOrEqualTo(String value) {
            addCriterion("CitationName >=", value, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameLessThan(String value) {
            addCriterion("CitationName <", value, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameLessThanOrEqualTo(String value) {
            addCriterion("CitationName <=", value, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameLike(String value) {
            addCriterion("CitationName like", value, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameNotLike(String value) {
            addCriterion("CitationName not like", value, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameIn(List<String> values) {
            addCriterion("CitationName in", values, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameNotIn(List<String> values) {
            addCriterion("CitationName not in", values, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameBetween(String value1, String value2) {
            addCriterion("CitationName between", value1, value2, "citationname");
            return (Criteria) this;
        }

        public Criteria andCitationnameNotBetween(String value1, String value2) {
            addCriterion("CitationName not between", value1, value2, "citationname");
            return (Criteria) this;
        }

        public Criteria andSampleidIsNull() {
            addCriterion("SampleId is null");
            return (Criteria) this;
        }

        public Criteria andSampleidIsNotNull() {
            addCriterion("SampleId is not null");
            return (Criteria) this;
        }

        public Criteria andSampleidEqualTo(String value) {
            addCriterion("SampleId =", value, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidNotEqualTo(String value) {
            addCriterion("SampleId <>", value, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidGreaterThan(String value) {
            addCriterion("SampleId >", value, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidGreaterThanOrEqualTo(String value) {
            addCriterion("SampleId >=", value, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidLessThan(String value) {
            addCriterion("SampleId <", value, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidLessThanOrEqualTo(String value) {
            addCriterion("SampleId <=", value, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidLike(String value) {
            addCriterion("SampleId like", value, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidNotLike(String value) {
            addCriterion("SampleId not like", value, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidIn(List<String> values) {
            addCriterion("SampleId in", values, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidNotIn(List<String> values) {
            addCriterion("SampleId not in", values, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidBetween(String value1, String value2) {
            addCriterion("SampleId between", value1, value2, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSampleidNotBetween(String value1, String value2) {
            addCriterion("SampleId not between", value1, value2, "sampleid");
            return (Criteria) this;
        }

        public Criteria andSamplenoIsNull() {
            addCriterion("SampleNo is null");
            return (Criteria) this;
        }

        public Criteria andSamplenoIsNotNull() {
            addCriterion("SampleNo is not null");
            return (Criteria) this;
        }

        public Criteria andSamplenoEqualTo(String value) {
            addCriterion("SampleNo =", value, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoNotEqualTo(String value) {
            addCriterion("SampleNo <>", value, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoGreaterThan(String value) {
            addCriterion("SampleNo >", value, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoGreaterThanOrEqualTo(String value) {
            addCriterion("SampleNo >=", value, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoLessThan(String value) {
            addCriterion("SampleNo <", value, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoLessThanOrEqualTo(String value) {
            addCriterion("SampleNo <=", value, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoLike(String value) {
            addCriterion("SampleNo like", value, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoNotLike(String value) {
            addCriterion("SampleNo not like", value, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoIn(List<String> values) {
            addCriterion("SampleNo in", values, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoNotIn(List<String> values) {
            addCriterion("SampleNo not in", values, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoBetween(String value1, String value2) {
            addCriterion("SampleNo between", value1, value2, "sampleno");
            return (Criteria) this;
        }

        public Criteria andSamplenoNotBetween(String value1, String value2) {
            addCriterion("SampleNo not between", value1, value2, "sampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoIsNull() {
            addCriterion("ExternalSampleNo is null");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoIsNotNull() {
            addCriterion("ExternalSampleNo is not null");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoEqualTo(String value) {
            addCriterion("ExternalSampleNo =", value, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoNotEqualTo(String value) {
            addCriterion("ExternalSampleNo <>", value, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoGreaterThan(String value) {
            addCriterion("ExternalSampleNo >", value, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoGreaterThanOrEqualTo(String value) {
            addCriterion("ExternalSampleNo >=", value, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoLessThan(String value) {
            addCriterion("ExternalSampleNo <", value, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoLessThanOrEqualTo(String value) {
            addCriterion("ExternalSampleNo <=", value, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoLike(String value) {
            addCriterion("ExternalSampleNo like", value, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoNotLike(String value) {
            addCriterion("ExternalSampleNo not like", value, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoIn(List<String> values) {
            addCriterion("ExternalSampleNo in", values, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoNotIn(List<String> values) {
            addCriterion("ExternalSampleNo not in", values, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoBetween(String value1, String value2) {
            addCriterion("ExternalSampleNo between", value1, value2, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andExternalsamplenoNotBetween(String value1, String value2) {
            addCriterion("ExternalSampleNo not between", value1, value2, "externalsampleno");
            return (Criteria) this;
        }

        public Criteria andTestlineseqIsNull() {
            addCriterion("TestLineSeq is null");
            return (Criteria) this;
        }

        public Criteria andTestlineseqIsNotNull() {
            addCriterion("TestLineSeq is not null");
            return (Criteria) this;
        }

        public Criteria andTestlineseqEqualTo(Long value) {
            addCriterion("TestLineSeq =", value, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqNotEqualTo(Long value) {
            addCriterion("TestLineSeq <>", value, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqGreaterThan(Long value) {
            addCriterion("TestLineSeq >", value, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqGreaterThanOrEqualTo(Long value) {
            addCriterion("TestLineSeq >=", value, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqLessThan(Long value) {
            addCriterion("TestLineSeq <", value, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqLessThanOrEqualTo(Long value) {
            addCriterion("TestLineSeq <=", value, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqIn(List<Long> values) {
            addCriterion("TestLineSeq in", values, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqNotIn(List<Long> values) {
            addCriterion("TestLineSeq not in", values, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqBetween(Long value1, Long value2) {
            addCriterion("TestLineSeq between", value1, value2, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andTestlineseqNotBetween(Long value1, Long value2) {
            addCriterion("TestLineSeq not between", value1, value2, "testlineseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqIsNull() {
            addCriterion("SampleSeq is null");
            return (Criteria) this;
        }

        public Criteria andSampleseqIsNotNull() {
            addCriterion("SampleSeq is not null");
            return (Criteria) this;
        }

        public Criteria andSampleseqEqualTo(String value) {
            addCriterion("SampleSeq =", value, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqNotEqualTo(String value) {
            addCriterion("SampleSeq <>", value, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqGreaterThan(String value) {
            addCriterion("SampleSeq >", value, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqGreaterThanOrEqualTo(String value) {
            addCriterion("SampleSeq >=", value, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqLessThan(String value) {
            addCriterion("SampleSeq <", value, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqLessThanOrEqualTo(String value) {
            addCriterion("SampleSeq <=", value, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqLike(String value) {
            addCriterion("SampleSeq like", value, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqNotLike(String value) {
            addCriterion("SampleSeq not like", value, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqIn(List<String> values) {
            addCriterion("SampleSeq in", values, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqNotIn(List<String> values) {
            addCriterion("SampleSeq not in", values, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqBetween(String value1, String value2) {
            addCriterion("SampleSeq between", value1, value2, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andSampleseqNotBetween(String value1, String value2) {
            addCriterion("SampleSeq not between", value1, value2, "sampleseq");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasIsNull() {
            addCriterion("EvaluationAlias is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasIsNotNull() {
            addCriterion("EvaluationAlias is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasEqualTo(String value) {
            addCriterion("EvaluationAlias =", value, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasNotEqualTo(String value) {
            addCriterion("EvaluationAlias <>", value, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasGreaterThan(String value) {
            addCriterion("EvaluationAlias >", value, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasGreaterThanOrEqualTo(String value) {
            addCriterion("EvaluationAlias >=", value, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasLessThan(String value) {
            addCriterion("EvaluationAlias <", value, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasLessThanOrEqualTo(String value) {
            addCriterion("EvaluationAlias <=", value, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasLike(String value) {
            addCriterion("EvaluationAlias like", value, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasNotLike(String value) {
            addCriterion("EvaluationAlias not like", value, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasIn(List<String> values) {
            addCriterion("EvaluationAlias in", values, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasNotIn(List<String> values) {
            addCriterion("EvaluationAlias not in", values, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasBetween(String value1, String value2) {
            addCriterion("EvaluationAlias between", value1, value2, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andEvaluationaliasNotBetween(String value1, String value2) {
            addCriterion("EvaluationAlias not between", value1, value2, "evaluationalias");
            return (Criteria) this;
        }

        public Criteria andMethoddescIsNull() {
            addCriterion("MethodDesc is null");
            return (Criteria) this;
        }

        public Criteria andMethoddescIsNotNull() {
            addCriterion("MethodDesc is not null");
            return (Criteria) this;
        }

        public Criteria andMethoddescEqualTo(String value) {
            addCriterion("MethodDesc =", value, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescNotEqualTo(String value) {
            addCriterion("MethodDesc <>", value, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescGreaterThan(String value) {
            addCriterion("MethodDesc >", value, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescGreaterThanOrEqualTo(String value) {
            addCriterion("MethodDesc >=", value, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescLessThan(String value) {
            addCriterion("MethodDesc <", value, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescLessThanOrEqualTo(String value) {
            addCriterion("MethodDesc <=", value, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescLike(String value) {
            addCriterion("MethodDesc like", value, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescNotLike(String value) {
            addCriterion("MethodDesc not like", value, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescIn(List<String> values) {
            addCriterion("MethodDesc in", values, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescNotIn(List<String> values) {
            addCriterion("MethodDesc not in", values, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescBetween(String value1, String value2) {
            addCriterion("MethodDesc between", value1, value2, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andMethoddescNotBetween(String value1, String value2) {
            addCriterion("MethodDesc not between", value1, value2, "methoddesc");
            return (Criteria) this;
        }

        public Criteria andConclusionidIsNull() {
            addCriterion("ConclusionId is null");
            return (Criteria) this;
        }

        public Criteria andConclusionidIsNotNull() {
            addCriterion("ConclusionId is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionidEqualTo(String value) {
            addCriterion("ConclusionId =", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidNotEqualTo(String value) {
            addCriterion("ConclusionId <>", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidGreaterThan(String value) {
            addCriterion("ConclusionId >", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidGreaterThanOrEqualTo(String value) {
            addCriterion("ConclusionId >=", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidLessThan(String value) {
            addCriterion("ConclusionId <", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidLessThanOrEqualTo(String value) {
            addCriterion("ConclusionId <=", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidLike(String value) {
            addCriterion("ConclusionId like", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidNotLike(String value) {
            addCriterion("ConclusionId not like", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidIn(List<String> values) {
            addCriterion("ConclusionId in", values, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidNotIn(List<String> values) {
            addCriterion("ConclusionId not in", values, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidBetween(String value1, String value2) {
            addCriterion("ConclusionId between", value1, value2, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidNotBetween(String value1, String value2) {
            addCriterion("ConclusionId not between", value1, value2, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayIsNull() {
            addCriterion("ConclusionDisplay is null");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayIsNotNull() {
            addCriterion("ConclusionDisplay is not null");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayEqualTo(String value) {
            addCriterion("ConclusionDisplay =", value, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayNotEqualTo(String value) {
            addCriterion("ConclusionDisplay <>", value, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayGreaterThan(String value) {
            addCriterion("ConclusionDisplay >", value, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayGreaterThanOrEqualTo(String value) {
            addCriterion("ConclusionDisplay >=", value, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayLessThan(String value) {
            addCriterion("ConclusionDisplay <", value, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayLessThanOrEqualTo(String value) {
            addCriterion("ConclusionDisplay <=", value, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayLike(String value) {
            addCriterion("ConclusionDisplay like", value, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayNotLike(String value) {
            addCriterion("ConclusionDisplay not like", value, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayIn(List<String> values) {
            addCriterion("ConclusionDisplay in", values, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayNotIn(List<String> values) {
            addCriterion("ConclusionDisplay not in", values, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayBetween(String value1, String value2) {
            addCriterion("ConclusionDisplay between", value1, value2, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andConclusiondisplayNotBetween(String value1, String value2) {
            addCriterion("ConclusionDisplay not between", value1, value2, "conclusiondisplay");
            return (Criteria) this;
        }

        public Criteria andBizversionidIsNull() {
            addCriterion("BizVersionId is null");
            return (Criteria) this;
        }

        public Criteria andBizversionidIsNotNull() {
            addCriterion("BizVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andBizversionidEqualTo(String value) {
            addCriterion("BizVersionId =", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidNotEqualTo(String value) {
            addCriterion("BizVersionId <>", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidGreaterThan(String value) {
            addCriterion("BizVersionId >", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidGreaterThanOrEqualTo(String value) {
            addCriterion("BizVersionId >=", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidLessThan(String value) {
            addCriterion("BizVersionId <", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidLessThanOrEqualTo(String value) {
            addCriterion("BizVersionId <=", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidLike(String value) {
            addCriterion("BizVersionId like", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidNotLike(String value) {
            addCriterion("BizVersionId not like", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidIn(List<String> values) {
            addCriterion("BizVersionId in", values, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidNotIn(List<String> values) {
            addCriterion("BizVersionId not in", values, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidBetween(String value1, String value2) {
            addCriterion("BizVersionId between", value1, value2, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidNotBetween(String value1, String value2) {
            addCriterion("BizVersionId not between", value1, value2, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorIsNull() {
            addCriterion("ActiveIndicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorIsNotNull() {
            addCriterion("ActiveIndicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorEqualTo(Integer value) {
            addCriterion("ActiveIndicator =", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorNotEqualTo(Integer value) {
            addCriterion("ActiveIndicator <>", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorGreaterThan(Integer value) {
            addCriterion("ActiveIndicator >", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("ActiveIndicator >=", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorLessThan(Integer value) {
            addCriterion("ActiveIndicator <", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorLessThanOrEqualTo(Integer value) {
            addCriterion("ActiveIndicator <=", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorIn(List<Integer> values) {
            addCriterion("ActiveIndicator in", values, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorNotIn(List<Integer> values) {
            addCriterion("ActiveIndicator not in", values, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorBetween(Integer value1, Integer value2) {
            addCriterion("ActiveIndicator between", value1, value2, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("ActiveIndicator not between", value1, value2, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLike(String value) {
            addCriterion("CreatedBy like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreateddateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreateddateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreateddateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createddate");
            return (Criteria) this;
        }

        public Criteria andModifiedbyIsNull() {
            addCriterion("ModifiedBy is null");
            return (Criteria) this;
        }

        public Criteria andModifiedbyIsNotNull() {
            addCriterion("ModifiedBy is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedbyEqualTo(String value) {
            addCriterion("ModifiedBy =", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyNotEqualTo(String value) {
            addCriterion("ModifiedBy <>", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyGreaterThan(String value) {
            addCriterion("ModifiedBy >", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyGreaterThanOrEqualTo(String value) {
            addCriterion("ModifiedBy >=", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyLessThan(String value) {
            addCriterion("ModifiedBy <", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyLessThanOrEqualTo(String value) {
            addCriterion("ModifiedBy <=", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyLike(String value) {
            addCriterion("ModifiedBy like", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyNotLike(String value) {
            addCriterion("ModifiedBy not like", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyIn(List<String> values) {
            addCriterion("ModifiedBy in", values, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyNotIn(List<String> values) {
            addCriterion("ModifiedBy not in", values, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyBetween(String value1, String value2) {
            addCriterion("ModifiedBy between", value1, value2, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyNotBetween(String value1, String value2) {
            addCriterion("ModifiedBy not between", value1, value2, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifieddateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifieddateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifieddateEqualTo(Date value) {
            addCriterion("ModifiedDate =", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateNotEqualTo(Date value) {
            addCriterion("ModifiedDate <>", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateGreaterThan(Date value) {
            addCriterion("ModifiedDate >", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateGreaterThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate >=", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateLessThan(Date value) {
            addCriterion("ModifiedDate <", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateLessThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate <=", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateIn(List<Date> values) {
            addCriterion("ModifiedDate in", values, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateNotIn(List<Date> values) {
            addCriterion("ModifiedDate not in", values, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateNotBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNull() {
            addCriterion("lab_id is null");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNotNull() {
            addCriterion("lab_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabIdEqualTo(Long value) {
            addCriterion("lab_id =", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotEqualTo(Long value) {
            addCriterion("lab_id <>", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThan(Long value) {
            addCriterion("lab_id >", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThanOrEqualTo(Long value) {
            addCriterion("lab_id >=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThan(Long value) {
            addCriterion("lab_id <", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThanOrEqualTo(Long value) {
            addCriterion("lab_id <=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdIn(List<Long> values) {
            addCriterion("lab_id in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotIn(List<Long> values) {
            addCriterion("lab_id not in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdBetween(Long value1, Long value2) {
            addCriterion("lab_id between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotBetween(Long value1, Long value2) {
            addCriterion("lab_id not between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("report_no is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("report_no is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("report_no =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("report_no <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("report_no >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("report_no >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("report_no <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("report_no <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("report_no like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("report_no not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("report_no in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("report_no not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("report_no between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("report_no not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdIsNull() {
            addCriterion("test_matrix_group_id is null");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdIsNotNull() {
            addCriterion("test_matrix_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdEqualTo(Integer value) {
            addCriterion("test_matrix_group_id =", value, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdNotEqualTo(Integer value) {
            addCriterion("test_matrix_group_id <>", value, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdGreaterThan(Integer value) {
            addCriterion("test_matrix_group_id >", value, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("test_matrix_group_id >=", value, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdLessThan(Integer value) {
            addCriterion("test_matrix_group_id <", value, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdLessThanOrEqualTo(Integer value) {
            addCriterion("test_matrix_group_id <=", value, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdIn(List<Integer> values) {
            addCriterion("test_matrix_group_id in", values, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdNotIn(List<Integer> values) {
            addCriterion("test_matrix_group_id not in", values, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdBetween(Integer value1, Integer value2) {
            addCriterion("test_matrix_group_id between", value1, value2, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestMatrixGroupIdNotBetween(Integer value1, Integer value2) {
            addCriterion("test_matrix_group_id not between", value1, value2, "testMatrixGroupId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIsNull() {
            addCriterion("test_line_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIsNotNull() {
            addCriterion("test_line_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdEqualTo(String value) {
            addCriterion("test_line_instance_id =", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotEqualTo(String value) {
            addCriterion("test_line_instance_id <>", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdGreaterThan(String value) {
            addCriterion("test_line_instance_id >", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("test_line_instance_id >=", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdLessThan(String value) {
            addCriterion("test_line_instance_id <", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("test_line_instance_id <=", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIn(List<String> values) {
            addCriterion("test_line_instance_id in", values, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotIn(List<String> values) {
            addCriterion("test_line_instance_id not in", values, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdBetween(String value1, String value2) {
            addCriterion("test_line_instance_id between", value1, value2, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotBetween(String value1, String value2) {
            addCriterion("test_line_instance_id not between", value1, value2, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIsNull() {
            addCriterion("evaluation_name is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIsNotNull() {
            addCriterion("evaluation_name is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameEqualTo(String value) {
            addCriterion("evaluation_name =", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotEqualTo(String value) {
            addCriterion("evaluation_name <>", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameGreaterThan(String value) {
            addCriterion("evaluation_name >", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameGreaterThanOrEqualTo(String value) {
            addCriterion("evaluation_name >=", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLessThan(String value) {
            addCriterion("evaluation_name <", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLessThanOrEqualTo(String value) {
            addCriterion("evaluation_name <=", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLike(String value) {
            addCriterion("evaluation_name like", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotLike(String value) {
            addCriterion("evaluation_name not like", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIn(List<String> values) {
            addCriterion("evaluation_name in", values, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotIn(List<String> values) {
            addCriterion("evaluation_name not in", values, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameBetween(String value1, String value2) {
            addCriterion("evaluation_name between", value1, value2, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotBetween(String value1, String value2) {
            addCriterion("evaluation_name not between", value1, value2, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIsNull() {
            addCriterion("test_line_status is null");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIsNotNull() {
            addCriterion("test_line_status is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusEqualTo(String value) {
            addCriterion("test_line_status =", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotEqualTo(String value) {
            addCriterion("test_line_status <>", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusGreaterThan(String value) {
            addCriterion("test_line_status >", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusGreaterThanOrEqualTo(String value) {
            addCriterion("test_line_status >=", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusLessThan(String value) {
            addCriterion("test_line_status <", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusLessThanOrEqualTo(String value) {
            addCriterion("test_line_status <=", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusLike(String value) {
            addCriterion("test_line_status like", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotLike(String value) {
            addCriterion("test_line_status not like", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIn(List<String> values) {
            addCriterion("test_line_status in", values, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotIn(List<String> values) {
            addCriterion("test_line_status not in", values, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusBetween(String value1, String value2) {
            addCriterion("test_line_status between", value1, value2, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotBetween(String value1, String value2) {
            addCriterion("test_line_status not between", value1, value2, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkIsNull() {
            addCriterion("test_line_remark is null");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkIsNotNull() {
            addCriterion("test_line_remark is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkEqualTo(String value) {
            addCriterion("test_line_remark =", value, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkNotEqualTo(String value) {
            addCriterion("test_line_remark <>", value, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkGreaterThan(String value) {
            addCriterion("test_line_remark >", value, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("test_line_remark >=", value, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkLessThan(String value) {
            addCriterion("test_line_remark <", value, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkLessThanOrEqualTo(String value) {
            addCriterion("test_line_remark <=", value, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkLike(String value) {
            addCriterion("test_line_remark like", value, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkNotLike(String value) {
            addCriterion("test_line_remark not like", value, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkIn(List<String> values) {
            addCriterion("test_line_remark in", values, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkNotIn(List<String> values) {
            addCriterion("test_line_remark not in", values, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkBetween(String value1, String value2) {
            addCriterion("test_line_remark between", value1, value2, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andTestLineRemarkNotBetween(String value1, String value2) {
            addCriterion("test_line_remark not between", value1, value2, "testLineRemark");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIsNull() {
            addCriterion("citation_full_name is null");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIsNotNull() {
            addCriterion("citation_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameEqualTo(String value) {
            addCriterion("citation_full_name =", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotEqualTo(String value) {
            addCriterion("citation_full_name <>", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameGreaterThan(String value) {
            addCriterion("citation_full_name >", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("citation_full_name >=", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLessThan(String value) {
            addCriterion("citation_full_name <", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLessThanOrEqualTo(String value) {
            addCriterion("citation_full_name <=", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLike(String value) {
            addCriterion("citation_full_name like", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotLike(String value) {
            addCriterion("citation_full_name not like", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIn(List<String> values) {
            addCriterion("citation_full_name in", values, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotIn(List<String> values) {
            addCriterion("citation_full_name not in", values, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameBetween(String value1, String value2) {
            addCriterion("citation_full_name between", value1, value2, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotBetween(String value1, String value2) {
            addCriterion("citation_full_name not between", value1, value2, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIsNull() {
            addCriterion("sample_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIsNotNull() {
            addCriterion("sample_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdEqualTo(String value) {
            addCriterion("sample_instance_id =", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotEqualTo(String value) {
            addCriterion("sample_instance_id <>", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdGreaterThan(String value) {
            addCriterion("sample_instance_id >", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("sample_instance_id >=", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLessThan(String value) {
            addCriterion("sample_instance_id <", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("sample_instance_id <=", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLike(String value) {
            addCriterion("sample_instance_id like", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotLike(String value) {
            addCriterion("sample_instance_id not like", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIn(List<String> values) {
            addCriterion("sample_instance_id in", values, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotIn(List<String> values) {
            addCriterion("sample_instance_id not in", values, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdBetween(String value1, String value2) {
            addCriterion("sample_instance_id between", value1, value2, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotBetween(String value1, String value2) {
            addCriterion("sample_instance_id not between", value1, value2, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdIsNull() {
            addCriterion("sample_parent_id is null");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdIsNotNull() {
            addCriterion("sample_parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdEqualTo(String value) {
            addCriterion("sample_parent_id =", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdNotEqualTo(String value) {
            addCriterion("sample_parent_id <>", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdGreaterThan(String value) {
            addCriterion("sample_parent_id >", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdGreaterThanOrEqualTo(String value) {
            addCriterion("sample_parent_id >=", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdLessThan(String value) {
            addCriterion("sample_parent_id <", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdLessThanOrEqualTo(String value) {
            addCriterion("sample_parent_id <=", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdLike(String value) {
            addCriterion("sample_parent_id like", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdNotLike(String value) {
            addCriterion("sample_parent_id not like", value, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdIn(List<String> values) {
            addCriterion("sample_parent_id in", values, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdNotIn(List<String> values) {
            addCriterion("sample_parent_id not in", values, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdBetween(String value1, String value2) {
            addCriterion("sample_parent_id between", value1, value2, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleParentIdNotBetween(String value1, String value2) {
            addCriterion("sample_parent_id not between", value1, value2, "sampleParentId");
            return (Criteria) this;
        }

        public Criteria andSampleTypeIsNull() {
            addCriterion("sample_type is null");
            return (Criteria) this;
        }

        public Criteria andSampleTypeIsNotNull() {
            addCriterion("sample_type is not null");
            return (Criteria) this;
        }

        public Criteria andSampleTypeEqualTo(String value) {
            addCriterion("sample_type =", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeNotEqualTo(String value) {
            addCriterion("sample_type <>", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeGreaterThan(String value) {
            addCriterion("sample_type >", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sample_type >=", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeLessThan(String value) {
            addCriterion("sample_type <", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeLessThanOrEqualTo(String value) {
            addCriterion("sample_type <=", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeLike(String value) {
            addCriterion("sample_type like", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeNotLike(String value) {
            addCriterion("sample_type not like", value, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeIn(List<String> values) {
            addCriterion("sample_type in", values, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeNotIn(List<String> values) {
            addCriterion("sample_type not in", values, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeBetween(String value1, String value2) {
            addCriterion("sample_type between", value1, value2, "sampleType");
            return (Criteria) this;
        }

        public Criteria andSampleTypeNotBetween(String value1, String value2) {
            addCriterion("sample_type not between", value1, value2, "sampleType");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andMaterialColorIsNull() {
            addCriterion("material_color is null");
            return (Criteria) this;
        }

        public Criteria andMaterialColorIsNotNull() {
            addCriterion("material_color is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialColorEqualTo(String value) {
            addCriterion("material_color =", value, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorNotEqualTo(String value) {
            addCriterion("material_color <>", value, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorGreaterThan(String value) {
            addCriterion("material_color >", value, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorGreaterThanOrEqualTo(String value) {
            addCriterion("material_color >=", value, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorLessThan(String value) {
            addCriterion("material_color <", value, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorLessThanOrEqualTo(String value) {
            addCriterion("material_color <=", value, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorLike(String value) {
            addCriterion("material_color like", value, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorNotLike(String value) {
            addCriterion("material_color not like", value, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorIn(List<String> values) {
            addCriterion("material_color in", values, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorNotIn(List<String> values) {
            addCriterion("material_color not in", values, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorBetween(String value1, String value2) {
            addCriterion("material_color between", value1, value2, "materialColor");
            return (Criteria) this;
        }

        public Criteria andMaterialColorNotBetween(String value1, String value2) {
            addCriterion("material_color not between", value1, value2, "materialColor");
            return (Criteria) this;
        }

        public Criteria andCompositionIsNull() {
            addCriterion("composition is null");
            return (Criteria) this;
        }

        public Criteria andCompositionIsNotNull() {
            addCriterion("composition is not null");
            return (Criteria) this;
        }

        public Criteria andCompositionEqualTo(String value) {
            addCriterion("composition =", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotEqualTo(String value) {
            addCriterion("composition <>", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThan(String value) {
            addCriterion("composition >", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThanOrEqualTo(String value) {
            addCriterion("composition >=", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLessThan(String value) {
            addCriterion("composition <", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLessThanOrEqualTo(String value) {
            addCriterion("composition <=", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLike(String value) {
            addCriterion("composition like", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotLike(String value) {
            addCriterion("composition not like", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionIn(List<String> values) {
            addCriterion("composition in", values, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotIn(List<String> values) {
            addCriterion("composition not in", values, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionBetween(String value1, String value2) {
            addCriterion("composition between", value1, value2, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotBetween(String value1, String value2) {
            addCriterion("composition not between", value1, value2, "composition");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionIsNull() {
            addCriterion("material_description is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionIsNotNull() {
            addCriterion("material_description is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionEqualTo(String value) {
            addCriterion("material_description =", value, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionNotEqualTo(String value) {
            addCriterion("material_description <>", value, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionGreaterThan(String value) {
            addCriterion("material_description >", value, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("material_description >=", value, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionLessThan(String value) {
            addCriterion("material_description <", value, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionLessThanOrEqualTo(String value) {
            addCriterion("material_description <=", value, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionLike(String value) {
            addCriterion("material_description like", value, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionNotLike(String value) {
            addCriterion("material_description not like", value, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionIn(List<String> values) {
            addCriterion("material_description in", values, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionNotIn(List<String> values) {
            addCriterion("material_description not in", values, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionBetween(String value1, String value2) {
            addCriterion("material_description between", value1, value2, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialDescriptionNotBetween(String value1, String value2) {
            addCriterion("material_description not between", value1, value2, "materialDescription");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseIsNull() {
            addCriterion("material_end_use is null");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseIsNotNull() {
            addCriterion("material_end_use is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseEqualTo(String value) {
            addCriterion("material_end_use =", value, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseNotEqualTo(String value) {
            addCriterion("material_end_use <>", value, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseGreaterThan(String value) {
            addCriterion("material_end_use >", value, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseGreaterThanOrEqualTo(String value) {
            addCriterion("material_end_use >=", value, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseLessThan(String value) {
            addCriterion("material_end_use <", value, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseLessThanOrEqualTo(String value) {
            addCriterion("material_end_use <=", value, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseLike(String value) {
            addCriterion("material_end_use like", value, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseNotLike(String value) {
            addCriterion("material_end_use not like", value, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseIn(List<String> values) {
            addCriterion("material_end_use in", values, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseNotIn(List<String> values) {
            addCriterion("material_end_use not in", values, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseBetween(String value1, String value2) {
            addCriterion("material_end_use between", value1, value2, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andMaterialEndUseNotBetween(String value1, String value2) {
            addCriterion("material_end_use not between", value1, value2, "materialEndUse");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagIsNull() {
            addCriterion("applicable_flag is null");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagIsNotNull() {
            addCriterion("applicable_flag is not null");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagEqualTo(String value) {
            addCriterion("applicable_flag =", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagNotEqualTo(String value) {
            addCriterion("applicable_flag <>", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagGreaterThan(String value) {
            addCriterion("applicable_flag >", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagGreaterThanOrEqualTo(String value) {
            addCriterion("applicable_flag >=", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagLessThan(String value) {
            addCriterion("applicable_flag <", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagLessThanOrEqualTo(String value) {
            addCriterion("applicable_flag <=", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagLike(String value) {
            addCriterion("applicable_flag like", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagNotLike(String value) {
            addCriterion("applicable_flag not like", value, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagIn(List<String> values) {
            addCriterion("applicable_flag in", values, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagNotIn(List<String> values) {
            addCriterion("applicable_flag not in", values, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagBetween(String value1, String value2) {
            addCriterion("applicable_flag between", value1, value2, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andApplicableFlagNotBetween(String value1, String value2) {
            addCriterion("applicable_flag not between", value1, value2, "applicableFlag");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoIsNull() {
            addCriterion("material_other_sample_info is null");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoIsNotNull() {
            addCriterion("material_other_sample_info is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoEqualTo(String value) {
            addCriterion("material_other_sample_info =", value, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoNotEqualTo(String value) {
            addCriterion("material_other_sample_info <>", value, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoGreaterThan(String value) {
            addCriterion("material_other_sample_info >", value, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoGreaterThanOrEqualTo(String value) {
            addCriterion("material_other_sample_info >=", value, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoLessThan(String value) {
            addCriterion("material_other_sample_info <", value, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoLessThanOrEqualTo(String value) {
            addCriterion("material_other_sample_info <=", value, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoLike(String value) {
            addCriterion("material_other_sample_info like", value, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoNotLike(String value) {
            addCriterion("material_other_sample_info not like", value, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoIn(List<String> values) {
            addCriterion("material_other_sample_info in", values, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoNotIn(List<String> values) {
            addCriterion("material_other_sample_info not in", values, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoBetween(String value1, String value2) {
            addCriterion("material_other_sample_info between", value1, value2, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialOtherSampleInfoNotBetween(String value1, String value2) {
            addCriterion("material_other_sample_info not between", value1, value2, "materialOtherSampleInfo");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkIsNull() {
            addCriterion("material_remark is null");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkIsNotNull() {
            addCriterion("material_remark is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkEqualTo(String value) {
            addCriterion("material_remark =", value, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkNotEqualTo(String value) {
            addCriterion("material_remark <>", value, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkGreaterThan(String value) {
            addCriterion("material_remark >", value, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("material_remark >=", value, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkLessThan(String value) {
            addCriterion("material_remark <", value, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkLessThanOrEqualTo(String value) {
            addCriterion("material_remark <=", value, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkLike(String value) {
            addCriterion("material_remark like", value, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkNotLike(String value) {
            addCriterion("material_remark not like", value, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkIn(List<String> values) {
            addCriterion("material_remark in", values, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkNotIn(List<String> values) {
            addCriterion("material_remark not in", values, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkBetween(String value1, String value2) {
            addCriterion("material_remark between", value1, value2, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andMaterialRemarkNotBetween(String value1, String value2) {
            addCriterion("material_remark not between", value1, value2, "materialRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIsNull() {
            addCriterion("conclusion_code is null");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIsNotNull() {
            addCriterion("conclusion_code is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeEqualTo(String value) {
            addCriterion("conclusion_code =", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotEqualTo(String value) {
            addCriterion("conclusion_code <>", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeGreaterThan(String value) {
            addCriterion("conclusion_code >", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeGreaterThanOrEqualTo(String value) {
            addCriterion("conclusion_code >=", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLessThan(String value) {
            addCriterion("conclusion_code <", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLessThanOrEqualTo(String value) {
            addCriterion("conclusion_code <=", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLike(String value) {
            addCriterion("conclusion_code like", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotLike(String value) {
            addCriterion("conclusion_code not like", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIn(List<String> values) {
            addCriterion("conclusion_code in", values, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotIn(List<String> values) {
            addCriterion("conclusion_code not in", values, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeBetween(String value1, String value2) {
            addCriterion("conclusion_code between", value1, value2, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotBetween(String value1, String value2) {
            addCriterion("conclusion_code not between", value1, value2, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIsNull() {
            addCriterion("customer_conclusion is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIsNotNull() {
            addCriterion("customer_conclusion is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionEqualTo(String value) {
            addCriterion("customer_conclusion =", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotEqualTo(String value) {
            addCriterion("customer_conclusion <>", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionGreaterThan(String value) {
            addCriterion("customer_conclusion >", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("customer_conclusion >=", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLessThan(String value) {
            addCriterion("customer_conclusion <", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLessThanOrEqualTo(String value) {
            addCriterion("customer_conclusion <=", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLike(String value) {
            addCriterion("customer_conclusion like", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotLike(String value) {
            addCriterion("customer_conclusion not like", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIn(List<String> values) {
            addCriterion("customer_conclusion in", values, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotIn(List<String> values) {
            addCriterion("customer_conclusion not in", values, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionBetween(String value1, String value2) {
            addCriterion("customer_conclusion between", value1, value2, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotBetween(String value1, String value2) {
            addCriterion("customer_conclusion not between", value1, value2, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIsNull() {
            addCriterion("conclusion_remark is null");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIsNotNull() {
            addCriterion("conclusion_remark is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkEqualTo(String value) {
            addCriterion("conclusion_remark =", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotEqualTo(String value) {
            addCriterion("conclusion_remark <>", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkGreaterThan(String value) {
            addCriterion("conclusion_remark >", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("conclusion_remark >=", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLessThan(String value) {
            addCriterion("conclusion_remark <", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLessThanOrEqualTo(String value) {
            addCriterion("conclusion_remark <=", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLike(String value) {
            addCriterion("conclusion_remark like", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotLike(String value) {
            addCriterion("conclusion_remark not like", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIn(List<String> values) {
            addCriterion("conclusion_remark in", values, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotIn(List<String> values) {
            addCriterion("conclusion_remark not in", values, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkBetween(String value1, String value2) {
            addCriterion("conclusion_remark between", value1, value2, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotBetween(String value1, String value2) {
            addCriterion("conclusion_remark not between", value1, value2, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("last_modified_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("last_modified_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("last_modified_timestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("last_modified_timestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("last_modified_timestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("last_modified_timestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("last_modified_timestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("last_modified_timestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}