package com.sgs.testdatabiz.sdk.config;

import com.sgs.testdatabiz.sdk.convert.RdConvertService;
import com.sgs.testdatabiz.sdk.convert.RdConvertServiceServiceImpl;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/4/10 11:36
 */
public class RdConverterFactory {

    private static Map<String,RdConvertService> convertServiceMap;


    static {
        convertServiceMap = new HashMap<>();
        registerConverter("v1",new RdConvertServiceServiceImpl());
    }

    private static void registerConverter(String version,RdConvertService rdConvertService){
        convertServiceMap.putIfAbsent(version,rdConvertService);
    }



    public static RdConvertService get(String version) {
        return convertServiceMap.get(version);
    }
}
