/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdContactInput implements Serializable{

    private String customerContactId;
    private Long bossContactId;
    private Long bossSiteUseId;
    private String contactName;
    private String telephone;
    private String email;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
