package com.sgs.testdatabiz.domain.service.validation;


import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;

public interface ValidationService {

    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO);

    public String getType();

    public Integer getOrder();
}
