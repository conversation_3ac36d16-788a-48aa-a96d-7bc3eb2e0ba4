package com.sgs.testdatabiz.sdk.util;

import com.sgs.testdatabiz.sdk.client.FrameWorkClient;
import com.sgs.testdatabiz.sdk.client.FrameWorkReq;
import com.sgs.testdatabiz.sdk.client.FrameWorkResp;
import com.sgs.testdatabiz.sdk.enums.FrameKeyGroupEnum;
import com.sgs.testdatabiz.sdk.model.DictEnumInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import static java.util.Arrays.stream;

public class FrameWorkHandler {

    private static final String SYSTEM_ID = "2";
    private static final String KEY_GROUP = "fw:category";

    public static ConcurrentHashMap<String, List<DictEnumInfo>> getDictionaryMap(String env){
        List<FrameWorkReq> frameWorkReqList = new ArrayList<>();
        stream(FrameKeyGroupEnum.values()).forEach(frameGroupKeyEnum -> {
            FrameWorkReq frameWorkReq = new FrameWorkReq();
            frameWorkReq.setSysKeyGroup(frameGroupKeyEnum.getGroupKey());
            frameWorkReq.setSystemID(frameGroupKeyEnum.getSystemId());
            frameWorkReqList.add(frameWorkReq);
        });
        FrameWorkResp frameWorkResp = FrameWorkClient.getDataDictionaryMap(frameWorkReqList,env);
        if(Func.isEmpty(frameWorkResp)){
            return null;
        }
        return frameWorkResp.getDataDictionaryMap();
    }


    public static ConcurrentHashMap<String, List<DictEnumInfo>> getDictionaryMap(List<FrameWorkReq> frameWorkReqList,String env){
        FrameWorkResp frameWorkResp =  FrameWorkClient.getDataDictionaryMap(frameWorkReqList,env);
        if(Func.isEmpty(frameWorkResp)){
            return null;
        }
        return frameWorkResp.getDataDictionaryMap();
    }

    public static  List<DictEnumInfo>  getDictionaryValue(String env){
        FrameWorkReq frameWorkReq = new FrameWorkReq();
        frameWorkReq.setSysKeyGroup(KEY_GROUP);
        frameWorkReq.setSystemID(SYSTEM_ID);
        //env = "TEST";
        return  FrameWorkClient.getDataDictionaryMap(frameWorkReq,env);
    }
}
