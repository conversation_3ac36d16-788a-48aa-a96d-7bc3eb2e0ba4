package com.sgs.testdatabiz.integration.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class URLConfiguration {
    private static final Logger logger = LoggerFactory.getLogger(URLConfiguration.class);

    /**
     *
     */
    @Value("${user.management.url}")
    private String userManagementUrl;

    /**
     *
     */
    @Value("${notification.url}")
    private String notificationUrl;

    /**
     *
     */
    @Value("${frameWorkApi.url}")
    private String frameWorkApiUrl;

    /**
     *
     */
    @Value("${localiLayer.url}")
    private String localiLayerUrl;

    public String getFrameWorkApiUrl() {
        return frameWorkApiUrl;
    }

    public String getNotificationUrl() {
        return notificationUrl;
    }

    public String getLocaliLayerUrl() {
        return localiLayerUrl;
    }

    public String getUserManagementUrl() {
        return userManagementUrl;
    }
}