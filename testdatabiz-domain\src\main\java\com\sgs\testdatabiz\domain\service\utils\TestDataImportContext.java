package com.sgs.testdatabiz.domain.service.utils;

import com.sgs.framework.model.enums.SourceTypeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: shawn.yang
 * @create: 2023-03-29 16:07
 */
public final class TestDataImportContext {
    private static final String CURRENT_CHANNEL_KEY = "channel";
    private static final ThreadLocal<Map<String, Object>> CONTEXT = ThreadLocal.withInitial(HashMap::new);

    public static Map<String, Object> getContext(){
        return CONTEXT.get();
    }

    public static void clear(){
        // DIG-8555  Call "remove()" on "CONTEXT".
        CONTEXT.remove();
        //CONTEXT.set(new HashMap<>());
    }


    public static void setChannel(SourceTypeEnum sourceType){
        CONTEXT.get().put(CURRENT_CHANNEL_KEY,sourceType);
    }

    public static SourceTypeEnum getChannel(){
        return (SourceTypeEnum)CONTEXT.get().get(CURRENT_CHANNEL_KEY);
    }

}
