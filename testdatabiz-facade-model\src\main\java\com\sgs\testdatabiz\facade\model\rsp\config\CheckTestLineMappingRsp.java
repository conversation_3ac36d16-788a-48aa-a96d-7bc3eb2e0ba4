package com.sgs.testdatabiz.facade.model.rsp.config;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/14 16:23
 */
public class CheckTestLineMappingRsp extends PrintFriendliness {
    /**
     *
     */
    private Integer ppNo;

    /**
     *
     */
    private Integer testLineId;

    /**
     *
     */
    private Integer citationId;

    /**
     *
     */
    private Integer citationType;

    /**
     *
     */
    private Integer checkTestLine;

    /**
     *
     */
    private String itemCode;

    /**
     *
     */
    private Integer photoRequired;

    /**
     *
     */
    private Integer checkType;

    /**
     *
     */
    private Map<String,Integer> splitMixSetting;

    /**
     *
     */
    private Map<String,Boolean> allowToAssign;

    public Integer getPpNo() {
        return ppNo;
    }

    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public Integer getCitationType() {
        return citationType;
    }

    public void setCitationType(Integer citationType) {
        this.citationType = citationType;
    }

    public Integer getCheckTestLine() {
        return checkTestLine;
    }

    public void setCheckTestLine(Integer checkTestLine) {
        this.checkTestLine = checkTestLine;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public Integer getPhotoRequired() {
        return photoRequired;
    }

    public void setPhotoRequired(Integer photoRequired) {
        this.photoRequired = photoRequired;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public Map<String, Integer> getSplitMixSetting() {
        return splitMixSetting;
    }

    public void setSplitMixSetting(Map<String, Integer> splitMixSetting) {
        this.splitMixSetting = splitMixSetting;
    }

    public Map<String, Boolean> getAllowToAssign() {
        return allowToAssign;
    }

    public void setAllowToAssign(Map<String, Boolean> allowToAssign) {
        this.allowToAssign = allowToAssign;
    }
}
