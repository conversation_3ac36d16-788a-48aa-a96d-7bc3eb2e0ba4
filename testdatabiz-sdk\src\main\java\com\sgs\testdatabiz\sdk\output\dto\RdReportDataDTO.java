package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/8 17:32
 */
@Data
public class RdReportDataDTO {

    private List<RdReportTrfRelDTO> reportTrfRelList;

    private List<RdTestDataObjectRelDTO> testDataObjectRelList;

    private RdReportDTO header;

    private RdReportExtDTO reportExt;

    private List<RdReportProductDffDTO> reportProductDffList;

    private List<RdReportMatrixDTO> reportMatrixList;

    private List<RdReportTestResultDTO> reportTestResultList;

    private List<RdQuotationDTO> quotationList;

    private List<RdReportInvoiceDTO> reportInvoiceList;

    private List<RdQuotationInvoiceRelDTO> quotationInvoiceRelList;

    private List<RdAttachmentDTO> attachmentList;

    private RdOrderDTO order;

    private List<RdCustomerDTO> customerList;

    private List<RdReportConclusionDTO> reportConclusionList;

    private List<RdTestSampleDTO> testSampleList;

    private List<RdTestSampleGroupDTO> testSampleGroupList;

    private Object dffMappingList;

    private Integer updateVersion;

}
