<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.SlimConfigExtMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.facade.model.rsp.SlimConfigRsp" >
        <result column="SlimLabCode" property="slimLabCode" jdbcType="VARCHAR" />
        <result column="ProductLineCode" property="productLineCode" jdbcType="VARCHAR" />
        <result column="LabCode" property="labCode" jdbcType="VARCHAR" />
        <result column="LocationCode" property="locationCode" jdbcType="VARCHAR" />
        <result column="ToSlimJobPath" property="toSlimJobPath" jdbcType="VARCHAR" />
        <result column="FromSlimRegisterPath" property="fromSlimRegisterPath" jdbcType="VARCHAR" />
        <result column="FromSlimReportDataPath" property="fromSlimReportDataPath" jdbcType="VARCHAR" />
        <result column="ToNotesRtfPath" property="toNotesRtfPath" jdbcType="VARCHAR" />
        <result column="WebServicesUrl" property="webServicesUrl" jdbcType="VARCHAR" />
        <result column="FtpAddr" property="ftpAddr" jdbcType="VARCHAR" />
        <result column="FtpUserName" property="ftpUserName" jdbcType="VARCHAR" />
        <result column="FtpPassWord" property="ftpPassWord" jdbcType="VARCHAR" />
        <result column="Email" property="email" jdbcType="VARCHAR" />
        <result column="ValidateType" property="validateType" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, SlimLabCode, ProductLineCode, LabCode, LocationCode, ToSlimJobPath, FromSlimRegisterPath, FromSlimReportDataPath,
        ToNotesRtfPath, WebServicesUrl, FtpAddr, FtpUserName, FtpPassWord, Email, ValidateType,
        CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
    </sql>

    <select id="getConfInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        from tb_slim_config
        WHERE ProductLineCode = #{productLineCode}
        AND LocationCode = #{locationCode}
        LIMIT 1
    </select>

    <select id="getConfInfoList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        from tb_slim_config
        <trim prefix="WHERE" prefixOverrides="AND |OR">
            <if test="productLineCode!=null and productLineCode!=''">
                AND ProductLineCode = #{productLineCode}
            </if>
            <if test="labCode!=null and labCode!=''">
                AND LabCode = #{labCode}
            </if>
            <if test="locationCode!=null and locationCode!=''">
                AND LocationCode = #{locationCode}
            </if>
        </trim>
    </select>

    <select id="checkTable" resultType="java.lang.Integer" parameterType="String">
        SELECT 1 FROM INFORMATION_SCHEMA.`TABLES` WHERE TABLE_SCHEMA = 'sgs_testdatadb' AND TABLE_NAME = #{tableName};
    </select>

    <select id="getTableInfoList" resultType="java.lang.String">
        SELECT
            `TABLE_NAME`
        FROM INFORMATION_SCHEMA.`TABLES`
        WHERE TABLE_SCHEMA = 'sgs_testdatadb'
          AND `TABLE_NAME` LIKE "tb_test_data_info_%";
    </select>

    <update id="createTestDataConfigTable" parameterType="String">
        CREATE TABLE `${tableName}` LIKE tb_test_data;
    </update>

</mapper>