package com.sgs.testdatabiz.core.enums;

import com.sgs.framework.tool.utils.Func;

import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum TrfSourceType {
    TRF2Order(1, "TRF转Order"),
    Order2TRF(2, "订单转TRF");

    private Integer sourceType;
    private String description;

    TrfSourceType(Integer sourceType, String description) {
        this.sourceType = sourceType;
        this.description = description;
    }

    public static boolean check(Integer sourceType) {
        TrfSourceType enu = findSourceType(sourceType);
        return enu != null;
    }


    public static boolean check(String sourceType) {
        if (Func.isBlank(sourceType)) {
            return false;
        }
        TrfSourceType enu = findSourceType(Func.toInt(sourceType));
        return enu != null;
    }

    public static boolean is(Integer sourceType, TrfSourceType sourceTypeEnum) {
        TrfSourceType enu = findSourceType(sourceType);
        return sourceTypeEnum == enu;
    }

    public static TrfSourceType findSourceType(Integer sourceType) {
        if (sourceType != null && sourceType > 0) {
            TrfSourceType[] var1 = values();
            int var2 = var1.length;

            for (int var3 = 0; var3 < var2; ++var3) {
                TrfSourceType enu = var1[var3];
                if (Objects.equals(sourceType, enu.getSourceType())) {
                    return enu;
                }
            }

            return null;
        } else {
            return null;
        }
    }


    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
