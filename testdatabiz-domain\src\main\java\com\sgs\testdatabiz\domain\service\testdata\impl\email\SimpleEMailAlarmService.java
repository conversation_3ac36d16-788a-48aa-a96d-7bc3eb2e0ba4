package com.sgs.testdatabiz.domain.service.testdata.impl.email;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.preorder.facade.model.rsp.order.OrderSimplifyInfoRsp;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.domain.service.FtpCfgService;
import com.sgs.testdatabiz.domain.service.utils.SourceTypeUtils;
import com.sgs.testdatabiz.facade.model.req.SlimConfigReq;
import com.sgs.testdatabiz.facade.model.rsp.SlimConfigRsp;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.integration.EmailClient;
import com.sgs.testdatabiz.integration.PreOrderClient;
import com.sgs.testdatabiz.integration.UserClient;
import com.sgs.testdatabiz.integration.model.email.EMailRequest;
import com.sgs.testdatabiz.integration.model.employ.EmpInfoReq;
import com.sgs.testdatabiz.integration.model.employ.EmpInfoRsp;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮件告警
 * 针对在TestData的build/import过程中出现的错误发送邮件告警
 *
 * @author: shawn.yang
 * @create: 2023-03-23 13:30
 */
@Component
public class SimpleEMailAlarmService {
    private static final Logger logger = LoggerFactory.getLogger(SimpleEMailAlarmService.class);

    private final EmailClient emailClient;
    private final PreOrderClient preOrderClient;
    private final UserClient userClient;
    @Autowired
    private FtpCfgService ftpCfgService;

    //todo 指定线程池
    @Async
    public void asyncSendEMailIfy(ReportTestDataInfo reportTestData, String errMsg) {
        if (reportTestData == null || StringUtils.isBlank(errMsg)) {
            return;
        }

        // 1.需要发送告警邮件的渠道check
        //todo 后续改成配置化，目前只有slim和starlims渠道的才发邮件
        int sourceType = reportTestData.getSourceType();
        boolean noNeedToSendEmail = !(sourceType == SourceTypeEnum.SLIM.getCode() || sourceType == SourceTypeEnum.STARLIMS.getCode());
        if (noNeedToSendEmail) {
            logger.error("can not send alarm-Email , unsupported sourceType:{}", sourceType);
            return;
        }

        // 2.build邮件request
        EMailRequest mailRequest = this.buildMailRequest4SlimSeries(reportTestData, errMsg);

        // 3.发送邮件
        this.doSend(mailRequest);
    }

    private EMailRequest buildMailRequest4SlimSeries(ReportTestDataInfo reportTestData, String errMsg) {
        // 1.获取收件人地址
        Set<String> mailTo = getMailTo(reportTestData);
        if (mailTo.isEmpty()) {
            return null;
        }

        // 2.build邮件标题
        String mailSubject = buildMailSubject4SlimSeries(reportTestData);

        // 3.build邮件内容
        // 邮箱前缀提取,当做内容的一部分
        String mailToNames = mailTo.stream().map(mailAddress -> {
                    String[] split = mailAddress.split("@");// 1.地址分割
                    if (split.length == 0) {
                        return null;
                    }
                    return split[0];// 2.返回邮箱前缀
                }).filter(Objects::nonNull)// 3.过滤空值
                .collect(Collectors.joining("&"));// 4.使用&拼接
        String mailContent = this.buildMailContent4SlimSeries(reportTestData, errMsg, mailToNames);

        return EMailRequest.builder()
                .mailSubject(mailSubject)
                .mailText(mailContent)
                .mailTo(mailTo)
                .build();
    }

    /**
     *
     * @param reqObject
     * @param mailErrors
     */
    public void doSlimEmail(ReportTestDataInfo reqObject, LinkedList<String> mailErrors){

        Set<String> mailTos = Sets.newHashSet("<EMAIL>","<EMAIL>","<EMAIL>");
        // 邮箱前缀提取,当做内容的一部分
        String mailToNames = mailTos.stream().map(mailAddress -> {
            String[] split = mailAddress.split("@");// 1.地址分割
            if (split.length == 0) {
                return null;
            }
            return split[0];// 2.返回邮箱前缀
        }).filter(Objects::nonNull)// 3.过滤空值
                .collect(Collectors.joining("&"));// 4.使用&拼接

        // build完整的mail content
        LinkedList<String> mails = Lists.newLinkedList();
        mails.add(String.format("Dear %s，", mailToNames));
        mails.add(String.format("Order No: %s", reqObject.getOrderNo()));
        mails.add(String.format("Subcontract No: %s", reqObject.getSubContractNo()));
        mails.add(String.format("Job No: %s", reqObject.getExternalNo()));
        mails.add("Error reason: <br/>");
        mails.add(StringUtils.join(mailErrors, "<br/>"));

        String mailSubject = String.format("SLIM 数据比对异常（%s_%s）", reqObject.getSubContractNo(), reqObject.getExternalNo());
        String mailContent = StringUtils.join(mails, "<br/>");
        // 1.发送邮件
        this.doSend(EMailRequest.builder()
                .mailSubject(mailSubject)
                .mailText(mailContent)
                .mailTo(mailTos)
                .build());
    }

    /**
     * 获取收件人
     * 与产品确认，目前仅发送给订单上的CR，可能存在订单没有CR的情况 shawn.yang 2023/3/23 16:30
     *
     * @param reportTestData
     * @return
     */
    private Set<String> getMailTo(ReportTestDataInfo reportTestData) {
        HashSet<String> mailTo = new HashSet<>();

        // 从订单中获取cr
        String productLineCode = StringUtils.defaultString(reportTestData.getProductLineCode(), ProductLineType.SL.getProductLineAbbr());
        OrderSimplifyInfoRsp order = preOrderClient.getOrderSimplifyInfo(reportTestData.getOrderNo(), productLineCode);
        if (order == null) {
            logger.error("orderClient.getOrderSimplifyInfo(), 没有找到order,orderNo:{}  productLineCode:{},", reportTestData.getOrderNo(), productLineCode);
        } else if (StringUtils.isNotEmpty(order.getCr())) {
            // 获取cr邮箱
            EmpInfoReq reqEmp = new EmpInfoReq();
            reqEmp.setLabCode(reportTestData.getLabCode());
            reqEmp.setRegionAccount(order.getCr());
            reqEmp.setPage(1);
            reqEmp.setRows(100);
            CustomResult<EmpInfoRsp> empResult = userClient.getEmpInfo(reqEmp);
            EmpInfoRsp emp = empResult.getData();
            if (emp == null) {
                logger.error("userClient.getEmpInfo, 获取不到CR Email {}.", order.getCr());
            } else if (StringUtils.isNotEmpty(emp.getEmail())) {
                mailTo.add(emp.getEmail().toLowerCase());
            }
        }

        // 如果是slim或者starlims需要查找   查找slimConfig中配置的报警邮件人员
        int sourceType = reportTestData.getSourceType();
        boolean isSlimSeries = (sourceType == SourceTypeEnum.SLIM.getCode() || sourceType == SourceTypeEnum.STARLIMS.getCode());
        logger.info("get slim config if source is slim series,source:{} ,isSlimSeries:{}", sourceType, isSlimSeries);
        if (isSlimSeries) {
            Set<String> slimConfigMail = getSlimConfigMail(productLineCode, StringUtil.getLocationCode(reportTestData.getLabCode()));
            if (!slimConfigMail.isEmpty()) {
                mailTo.addAll(slimConfigMail);
            }
        }

        return mailTo;
    }

    public Set<String> getSlimConfigMail(String productLineCode, String locationCode) {
        SlimConfigReq slimConfigReq = new SlimConfigReq();
        slimConfigReq.setProductLineCode(productLineCode);
//        slimConfigReq.setLabCode(labelCode);
        slimConfigReq.setLocationCode(locationCode);
        CustomResult<List<SlimConfigRsp>> result = ftpCfgService.queryConfig(slimConfigReq);
        if (!result.isSuccess()) {
            logger.error("getSlimConfigMail error. req:[{}] ,errMsg:{}", slimConfigReq, result.getMsg());
            return Collections.emptySet();
        }
        logger.info("query mailTo by ProductLineCode:{} and  locationCode:{} ,result:{} ,size:{}", productLineCode, locationCode, result.isSuccess(), result.getData());
        // 获取配置的email
        List<SlimConfigRsp> data = result.getData();
        if (data != null && !data.isEmpty()) {
            String emails = data.get(0).getEmail();
            if (StringUtils.isNotEmpty(emails)) {
                return Arrays.stream(emails.split(";")).collect(Collectors.toSet());
            }
            return Collections.emptySet();
        }
        return Collections.emptySet();
    }

    /**
     *
     * @param reqObject
     * @param repeatMatrixErrors
     */
    public void doSendMail(ReportTestDataInfo reqObject, Set<String> repeatMatrixErrors){
        if (repeatMatrixErrors == null || repeatMatrixErrors.isEmpty()){
            return;
        }
        Set<String> mailTo = Sets.newHashSet("<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>");

        final String mailSubject = String.format("非SHEIN分包(%s)Matrix出现重复(%s)数据！", reqObject.getSubContractNo(), reqObject.getExternalNo());
        String mailContent = this.buildMailContent4SlimSeries(reqObject, StringUtils.join(repeatMatrixErrors, "<br/>"), "海豚");

        this.doSend(EMailRequest.builder()
                .mailSubject(mailSubject)
                .mailText(mailContent)
                .mailTo(mailTo)
                .build());
    }

    private void doSend(EMailRequest mailRequest) {
        if (mailRequest == null) {
            return;
        }
        boolean success = emailClient.sendEmail(mailRequest);
        if (success) {
            logger.info("send alarm email success. mailRequest:[{}]", mailRequest);
            return;
        }
    }

    /**
     * build 邮件内容 -------slim/starslim专用模板
     *
     * @param reportTestData
     * @param errMsg
     * @param mailToNames
     * @return
     */
    private String buildMailContent4SlimSeries(ReportTestDataInfo reportTestData, String errMsg, String mailToNames) {
        // build错误信息
        SourceTypeEnum sourceType = SourceTypeUtils.toSourceTypeEnum(reportTestData.getSourceType());

        // build完整的mail content
        LinkedList<String> mails = Lists.newLinkedList();
        mails.add(String.format("Dear %s，", mailToNames));
        // todo 临时加的需求，待优化
        String execSysName = Objects.equals(ProductLineType.SL.getProductLineAbbr(), reportTestData.getProductLineCode()) ? "SODA" : "GPO";
        mails.add("There is an error message from " + execSysName + ":");
        // todo 临时加的需求，待优化
        String sourceName = sourceType == null ? "UNKNOWN" : sourceType.name();
        mails.add("Action: Get " + sourceName + " Data & RTF");
        mails.add(String.format("Order No: %s", reportTestData.getOrderNo()));
        mails.add(String.format("Subcontract No: %s", reportTestData.getSubContractNo()));
        mails.add(String.format(sourceName+" Job No: %s", reportTestData.getExternalNo()));
        if (StringUtils.isNotBlank(reportTestData.getExternalObjectNo())){
            mails.add(String.format("Sub Report No: %s", reportTestData.getExternalObjectNo()));
        }
        mails.add("Error reason:");
        mails.add(errMsg);
        return StringUtils.join(mails, "<br/>");
    }


    /**
     * build 邮件标题  -------slim/starslim专用模板
     *
     * @param reportTestData
     * @return
     */
    private String buildMailSubject4SlimSeries(ReportTestDataInfo reportTestData) {
        SourceTypeEnum sourceType = SourceTypeUtils.toSourceTypeEnum(reportTestData.getSourceType());
        String execSysName = Objects.equals(ProductLineType.SL.getProductLineAbbr(), reportTestData.getProductLineCode()) ? "SODA" : "GPO";

        return String.format("[" + execSysName + "] %s - %s - %s  Get " + (sourceType == null ? "UNKNOWN" : sourceType.name()) + " Data & RTF Failed", reportTestData.getOrderNo(),
                reportTestData.getSubContractNo(), reportTestData.getExternalNo());
    }


    public SimpleEMailAlarmService(EmailClient emailClient, PreOrderClient preOrderClient, UserClient userClient) {
        this.emailClient = emailClient;
        this.preOrderClient = preOrderClient;
        this.userClient = userClient;
    }

}
