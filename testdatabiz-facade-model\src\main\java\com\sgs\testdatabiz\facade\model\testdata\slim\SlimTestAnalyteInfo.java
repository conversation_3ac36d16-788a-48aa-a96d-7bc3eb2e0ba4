package com.sgs.testdatabiz.facade.model.testdata.slim;

public class SlimTestAnalyteInfo {
    /**
     *
     */
    private String analyteCode;

    /**
     *
     */
    private Integer analyteType;

    /**
     *
     */
    private String testAnalyteName;

    /**
     *
     */
    private String testAnalyteNameCN;

    /**
     *
     */
    private String reportUnit;

    /**
     *
     */
    private String reportUnitCN;

    /**
     *
     */
    private String testValue;

    /**
     *
     */
    private String casNo;

    /**
     *
     */
    private String showNd;

    /**
     *
     */
    private String finalValue;

    /**
     *
     */
    private String limitUnit;

    /**
     *
     */
    private Integer analyteSeq;

    /**
     *
     */
    private String reportLimit;

    public String getAnalyteCode() {
        return analyteCode;
    }

    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode;
    }

    public Integer getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getTestAnalyteNameCN() {
        return testAnalyteNameCN;
    }

    public void setTestAnalyteNameCN(String testAnalyteNameCN) {
        this.testAnalyteNameCN = testAnalyteNameCN;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportUnitCN() {
        return reportUnitCN;
    }

    public void setReportUnitCN(String reportUnitCN) {
        this.reportUnitCN = reportUnitCN;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getShowNd() {
        return showNd;
    }

    public void setShowNd(String showNd) {
        this.showNd = showNd;
    }

    public String getFinalValue() {
        return finalValue;
    }

    public void setFinalValue(String finalValue) {
        this.finalValue = finalValue;
    }

    public String getLimitUnit() {
        return limitUnit;
    }

    public void setLimitUnit(String limitUnit) {
        this.limitUnit = limitUnit;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }
}
