package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RdQuotationExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RdQuotationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNull() {
            addCriterion("lab_id is null");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNotNull() {
            addCriterion("lab_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabIdEqualTo(Long value) {
            addCriterion("lab_id =", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotEqualTo(Long value) {
            addCriterion("lab_id <>", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThan(Long value) {
            addCriterion("lab_id >", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThanOrEqualTo(Long value) {
            addCriterion("lab_id >=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThan(Long value) {
            addCriterion("lab_id <", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThanOrEqualTo(Long value) {
            addCriterion("lab_id <=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdIn(List<Long> values) {
            addCriterion("lab_id in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotIn(List<Long> values) {
            addCriterion("lab_id not in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdBetween(Long value1, Long value2) {
            addCriterion("lab_id between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotBetween(Long value1, Long value2) {
            addCriterion("lab_id not between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("system_id is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("system_id is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("system_id =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("system_id <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("system_id >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("system_id >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("system_id <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("system_id <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("system_id in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("system_id not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("system_id between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("system_id not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("report_no is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("report_no is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("report_no =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("report_no <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("report_no >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("report_no >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("report_no <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("report_no <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("report_no like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("report_no not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("report_no in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("report_no not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("report_no between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("report_no not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoIsNull() {
            addCriterion("quotation_no is null");
            return (Criteria) this;
        }

        public Criteria andQuotationNoIsNotNull() {
            addCriterion("quotation_no is not null");
            return (Criteria) this;
        }

        public Criteria andQuotationNoEqualTo(String value) {
            addCriterion("quotation_no =", value, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoNotEqualTo(String value) {
            addCriterion("quotation_no <>", value, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoGreaterThan(String value) {
            addCriterion("quotation_no >", value, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoGreaterThanOrEqualTo(String value) {
            addCriterion("quotation_no >=", value, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoLessThan(String value) {
            addCriterion("quotation_no <", value, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoLessThanOrEqualTo(String value) {
            addCriterion("quotation_no <=", value, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoLike(String value) {
            addCriterion("quotation_no like", value, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoNotLike(String value) {
            addCriterion("quotation_no not like", value, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoIn(List<String> values) {
            addCriterion("quotation_no in", values, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoNotIn(List<String> values) {
            addCriterion("quotation_no not in", values, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoBetween(String value1, String value2) {
            addCriterion("quotation_no between", value1, value2, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andQuotationNoNotBetween(String value1, String value2) {
            addCriterion("quotation_no not between", value1, value2, "quotationNo");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameIsNull() {
            addCriterion("payer_customer_name is null");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameIsNotNull() {
            addCriterion("payer_customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameEqualTo(String value) {
            addCriterion("payer_customer_name =", value, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameNotEqualTo(String value) {
            addCriterion("payer_customer_name <>", value, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameGreaterThan(String value) {
            addCriterion("payer_customer_name >", value, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("payer_customer_name >=", value, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameLessThan(String value) {
            addCriterion("payer_customer_name <", value, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("payer_customer_name <=", value, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameLike(String value) {
            addCriterion("payer_customer_name like", value, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameNotLike(String value) {
            addCriterion("payer_customer_name not like", value, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameIn(List<String> values) {
            addCriterion("payer_customer_name in", values, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameNotIn(List<String> values) {
            addCriterion("payer_customer_name not in", values, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameBetween(String value1, String value2) {
            addCriterion("payer_customer_name between", value1, value2, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerCustomerNameNotBetween(String value1, String value2) {
            addCriterion("payer_customer_name not between", value1, value2, "payerCustomerName");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberIsNull() {
            addCriterion("payer_boss_number is null");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberIsNotNull() {
            addCriterion("payer_boss_number is not null");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberEqualTo(Long value) {
            addCriterion("payer_boss_number =", value, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberNotEqualTo(Long value) {
            addCriterion("payer_boss_number <>", value, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberGreaterThan(Long value) {
            addCriterion("payer_boss_number >", value, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberGreaterThanOrEqualTo(Long value) {
            addCriterion("payer_boss_number >=", value, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberLessThan(Long value) {
            addCriterion("payer_boss_number <", value, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberLessThanOrEqualTo(Long value) {
            addCriterion("payer_boss_number <=", value, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberIn(List<Long> values) {
            addCriterion("payer_boss_number in", values, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberNotIn(List<Long> values) {
            addCriterion("payer_boss_number not in", values, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberBetween(Long value1, Long value2) {
            addCriterion("payer_boss_number between", value1, value2, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBossNumberNotBetween(Long value1, Long value2) {
            addCriterion("payer_boss_number not between", value1, value2, "payerBossNumber");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIsNull() {
            addCriterion("service_item_type is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIsNotNull() {
            addCriterion("service_item_type is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeEqualTo(String value) {
            addCriterion("service_item_type =", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotEqualTo(String value) {
            addCriterion("service_item_type <>", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeGreaterThan(String value) {
            addCriterion("service_item_type >", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeGreaterThanOrEqualTo(String value) {
            addCriterion("service_item_type >=", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLessThan(String value) {
            addCriterion("service_item_type <", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLessThanOrEqualTo(String value) {
            addCriterion("service_item_type <=", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLike(String value) {
            addCriterion("service_item_type like", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotLike(String value) {
            addCriterion("service_item_type not like", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIn(List<String> values) {
            addCriterion("service_item_type in", values, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotIn(List<String> values) {
            addCriterion("service_item_type not in", values, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeBetween(String value1, String value2) {
            addCriterion("service_item_type between", value1, value2, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotBetween(String value1, String value2) {
            addCriterion("service_item_type not between", value1, value2, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameIsNull() {
            addCriterion("service_item_name is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameIsNotNull() {
            addCriterion("service_item_name is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameEqualTo(String value) {
            addCriterion("service_item_name =", value, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameNotEqualTo(String value) {
            addCriterion("service_item_name <>", value, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameGreaterThan(String value) {
            addCriterion("service_item_name >", value, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("service_item_name >=", value, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameLessThan(String value) {
            addCriterion("service_item_name <", value, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameLessThanOrEqualTo(String value) {
            addCriterion("service_item_name <=", value, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameLike(String value) {
            addCriterion("service_item_name like", value, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameNotLike(String value) {
            addCriterion("service_item_name not like", value, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameIn(List<String> values) {
            addCriterion("service_item_name in", values, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameNotIn(List<String> values) {
            addCriterion("service_item_name not in", values, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameBetween(String value1, String value2) {
            addCriterion("service_item_name between", value1, value2, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemNameNotBetween(String value1, String value2) {
            addCriterion("service_item_name not between", value1, value2, "serviceItemName");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqIsNull() {
            addCriterion("service_item_seq is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqIsNotNull() {
            addCriterion("service_item_seq is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqEqualTo(Integer value) {
            addCriterion("service_item_seq =", value, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqNotEqualTo(Integer value) {
            addCriterion("service_item_seq <>", value, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqGreaterThan(Integer value) {
            addCriterion("service_item_seq >", value, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_item_seq >=", value, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqLessThan(Integer value) {
            addCriterion("service_item_seq <", value, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqLessThanOrEqualTo(Integer value) {
            addCriterion("service_item_seq <=", value, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqIn(List<Integer> values) {
            addCriterion("service_item_seq in", values, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqNotIn(List<Integer> values) {
            addCriterion("service_item_seq not in", values, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqBetween(Integer value1, Integer value2) {
            addCriterion("service_item_seq between", value1, value2, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andServiceItemSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("service_item_seq not between", value1, value2, "serviceItemSeq");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagIsNull() {
            addCriterion("special_offer_flag is null");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagIsNotNull() {
            addCriterion("special_offer_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagEqualTo(Integer value) {
            addCriterion("special_offer_flag =", value, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagNotEqualTo(Integer value) {
            addCriterion("special_offer_flag <>", value, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagGreaterThan(Integer value) {
            addCriterion("special_offer_flag >", value, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("special_offer_flag >=", value, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagLessThan(Integer value) {
            addCriterion("special_offer_flag <", value, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagLessThanOrEqualTo(Integer value) {
            addCriterion("special_offer_flag <=", value, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagIn(List<Integer> values) {
            addCriterion("special_offer_flag in", values, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagNotIn(List<Integer> values) {
            addCriterion("special_offer_flag not in", values, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagBetween(Integer value1, Integer value2) {
            addCriterion("special_offer_flag between", value1, value2, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("special_offer_flag not between", value1, value2, "specialOfferFlag");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceIsNull() {
            addCriterion("service_item_list_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceIsNotNull() {
            addCriterion("service_item_list_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceEqualTo(BigDecimal value) {
            addCriterion("service_item_list_unit_price =", value, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("service_item_list_unit_price <>", value, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("service_item_list_unit_price >", value, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_list_unit_price >=", value, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceLessThan(BigDecimal value) {
            addCriterion("service_item_list_unit_price <", value, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_list_unit_price <=", value, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceIn(List<BigDecimal> values) {
            addCriterion("service_item_list_unit_price in", values, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("service_item_list_unit_price not in", values, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_list_unit_price between", value1, value2, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemListUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_list_unit_price not between", value1, value2, "serviceItemListUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceIsNull() {
            addCriterion("service_item_sales_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceIsNotNull() {
            addCriterion("service_item_sales_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceEqualTo(BigDecimal value) {
            addCriterion("service_item_sales_unit_price =", value, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("service_item_sales_unit_price <>", value, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("service_item_sales_unit_price >", value, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_sales_unit_price >=", value, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceLessThan(BigDecimal value) {
            addCriterion("service_item_sales_unit_price <", value, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_sales_unit_price <=", value, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceIn(List<BigDecimal> values) {
            addCriterion("service_item_sales_unit_price in", values, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("service_item_sales_unit_price not in", values, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_sales_unit_price between", value1, value2, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSalesUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_sales_unit_price not between", value1, value2, "serviceItemSalesUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountIsNull() {
            addCriterion("service_item_discount is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountIsNotNull() {
            addCriterion("service_item_discount is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountEqualTo(BigDecimal value) {
            addCriterion("service_item_discount =", value, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountNotEqualTo(BigDecimal value) {
            addCriterion("service_item_discount <>", value, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountGreaterThan(BigDecimal value) {
            addCriterion("service_item_discount >", value, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_discount >=", value, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountLessThan(BigDecimal value) {
            addCriterion("service_item_discount <", value, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_discount <=", value, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountIn(List<BigDecimal> values) {
            addCriterion("service_item_discount in", values, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountNotIn(List<BigDecimal> values) {
            addCriterion("service_item_discount not in", values, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_discount between", value1, value2, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemDiscountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_discount not between", value1, value2, "serviceItemDiscount");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceIsNull() {
            addCriterion("service_item_exchange_rate_price is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceIsNotNull() {
            addCriterion("service_item_exchange_rate_price is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceEqualTo(BigDecimal value) {
            addCriterion("service_item_exchange_rate_price =", value, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceNotEqualTo(BigDecimal value) {
            addCriterion("service_item_exchange_rate_price <>", value, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceGreaterThan(BigDecimal value) {
            addCriterion("service_item_exchange_rate_price >", value, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_exchange_rate_price >=", value, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceLessThan(BigDecimal value) {
            addCriterion("service_item_exchange_rate_price <", value, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_exchange_rate_price <=", value, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceIn(List<BigDecimal> values) {
            addCriterion("service_item_exchange_rate_price in", values, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceNotIn(List<BigDecimal> values) {
            addCriterion("service_item_exchange_rate_price not in", values, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_exchange_rate_price between", value1, value2, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemExchangeRatePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_exchange_rate_price not between", value1, value2, "serviceItemExchangeRatePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceIsNull() {
            addCriterion("service_item_sur_charge_price is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceIsNotNull() {
            addCriterion("service_item_sur_charge_price is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceEqualTo(BigDecimal value) {
            addCriterion("service_item_sur_charge_price =", value, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceNotEqualTo(BigDecimal value) {
            addCriterion("service_item_sur_charge_price <>", value, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceGreaterThan(BigDecimal value) {
            addCriterion("service_item_sur_charge_price >", value, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_sur_charge_price >=", value, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceLessThan(BigDecimal value) {
            addCriterion("service_item_sur_charge_price <", value, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_sur_charge_price <=", value, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceIn(List<BigDecimal> values) {
            addCriterion("service_item_sur_charge_price in", values, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceNotIn(List<BigDecimal> values) {
            addCriterion("service_item_sur_charge_price not in", values, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_sur_charge_price between", value1, value2, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemSurChargePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_sur_charge_price not between", value1, value2, "serviceItemSurChargePrice");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountIsNull() {
            addCriterion("service_item_net_amount is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountIsNotNull() {
            addCriterion("service_item_net_amount is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountEqualTo(BigDecimal value) {
            addCriterion("service_item_net_amount =", value, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountNotEqualTo(BigDecimal value) {
            addCriterion("service_item_net_amount <>", value, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountGreaterThan(BigDecimal value) {
            addCriterion("service_item_net_amount >", value, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_net_amount >=", value, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountLessThan(BigDecimal value) {
            addCriterion("service_item_net_amount <", value, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_net_amount <=", value, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountIn(List<BigDecimal> values) {
            addCriterion("service_item_net_amount in", values, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountNotIn(List<BigDecimal> values) {
            addCriterion("service_item_net_amount not in", values, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_net_amount between", value1, value2, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemNetAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_net_amount not between", value1, value2, "serviceItemNetAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountIsNull() {
            addCriterion("service_item_vat_amount is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountIsNotNull() {
            addCriterion("service_item_vat_amount is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountEqualTo(BigDecimal value) {
            addCriterion("service_item_vat_amount =", value, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountNotEqualTo(BigDecimal value) {
            addCriterion("service_item_vat_amount <>", value, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountGreaterThan(BigDecimal value) {
            addCriterion("service_item_vat_amount >", value, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_vat_amount >=", value, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountLessThan(BigDecimal value) {
            addCriterion("service_item_vat_amount <", value, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_vat_amount <=", value, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountIn(List<BigDecimal> values) {
            addCriterion("service_item_vat_amount in", values, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountNotIn(List<BigDecimal> values) {
            addCriterion("service_item_vat_amount not in", values, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_vat_amount between", value1, value2, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemVatAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_vat_amount not between", value1, value2, "serviceItemVatAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountIsNull() {
            addCriterion("service_item_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountIsNotNull() {
            addCriterion("service_item_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountEqualTo(BigDecimal value) {
            addCriterion("service_item_total_amount =", value, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("service_item_total_amount <>", value, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("service_item_total_amount >", value, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_total_amount >=", value, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountLessThan(BigDecimal value) {
            addCriterion("service_item_total_amount <", value, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_item_total_amount <=", value, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountIn(List<BigDecimal> values) {
            addCriterion("service_item_total_amount in", values, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("service_item_total_amount not in", values, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_total_amount between", value1, value2, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andServiceItemTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_item_total_amount not between", value1, value2, "serviceItemTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountIsNull() {
            addCriterion("sum_net_amount is null");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountIsNotNull() {
            addCriterion("sum_net_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountEqualTo(BigDecimal value) {
            addCriterion("sum_net_amount =", value, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountNotEqualTo(BigDecimal value) {
            addCriterion("sum_net_amount <>", value, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountGreaterThan(BigDecimal value) {
            addCriterion("sum_net_amount >", value, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sum_net_amount >=", value, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountLessThan(BigDecimal value) {
            addCriterion("sum_net_amount <", value, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sum_net_amount <=", value, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountIn(List<BigDecimal> values) {
            addCriterion("sum_net_amount in", values, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountNotIn(List<BigDecimal> values) {
            addCriterion("sum_net_amount not in", values, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sum_net_amount between", value1, value2, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumNetAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sum_net_amount not between", value1, value2, "sumNetAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountIsNull() {
            addCriterion("sum_vat_amount is null");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountIsNotNull() {
            addCriterion("sum_vat_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountEqualTo(BigDecimal value) {
            addCriterion("sum_vat_amount =", value, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountNotEqualTo(BigDecimal value) {
            addCriterion("sum_vat_amount <>", value, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountGreaterThan(BigDecimal value) {
            addCriterion("sum_vat_amount >", value, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sum_vat_amount >=", value, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountLessThan(BigDecimal value) {
            addCriterion("sum_vat_amount <", value, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sum_vat_amount <=", value, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountIn(List<BigDecimal> values) {
            addCriterion("sum_vat_amount in", values, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountNotIn(List<BigDecimal> values) {
            addCriterion("sum_vat_amount not in", values, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sum_vat_amount between", value1, value2, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andSumVatAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sum_vat_amount not between", value1, value2, "sumVatAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNull() {
            addCriterion("total_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNotNull() {
            addCriterion("total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountEqualTo(BigDecimal value) {
            addCriterion("total_amount =", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("total_amount <>", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("total_amount >", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_amount >=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThan(BigDecimal value) {
            addCriterion("total_amount <", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_amount <=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIn(List<BigDecimal> values) {
            addCriterion("total_amount in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("total_amount not in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_amount between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_amount not between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountIsNull() {
            addCriterion("adjustment_amount is null");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountIsNotNull() {
            addCriterion("adjustment_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountEqualTo(BigDecimal value) {
            addCriterion("adjustment_amount =", value, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountNotEqualTo(BigDecimal value) {
            addCriterion("adjustment_amount <>", value, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountGreaterThan(BigDecimal value) {
            addCriterion("adjustment_amount >", value, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("adjustment_amount >=", value, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountLessThan(BigDecimal value) {
            addCriterion("adjustment_amount <", value, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("adjustment_amount <=", value, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountIn(List<BigDecimal> values) {
            addCriterion("adjustment_amount in", values, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountNotIn(List<BigDecimal> values) {
            addCriterion("adjustment_amount not in", values, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("adjustment_amount between", value1, value2, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("adjustment_amount not between", value1, value2, "adjustmentAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountIsNull() {
            addCriterion("adjustment_discount is null");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountIsNotNull() {
            addCriterion("adjustment_discount is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountEqualTo(BigDecimal value) {
            addCriterion("adjustment_discount =", value, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountNotEqualTo(BigDecimal value) {
            addCriterion("adjustment_discount <>", value, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountGreaterThan(BigDecimal value) {
            addCriterion("adjustment_discount >", value, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("adjustment_discount >=", value, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountLessThan(BigDecimal value) {
            addCriterion("adjustment_discount <", value, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("adjustment_discount <=", value, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountIn(List<BigDecimal> values) {
            addCriterion("adjustment_discount in", values, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountNotIn(List<BigDecimal> values) {
            addCriterion("adjustment_discount not in", values, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("adjustment_discount between", value1, value2, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAdjustmentDiscountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("adjustment_discount not between", value1, value2, "adjustmentDiscount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountIsNull() {
            addCriterion("final_amount is null");
            return (Criteria) this;
        }

        public Criteria andFinalAmountIsNotNull() {
            addCriterion("final_amount is not null");
            return (Criteria) this;
        }

        public Criteria andFinalAmountEqualTo(BigDecimal value) {
            addCriterion("final_amount =", value, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountNotEqualTo(BigDecimal value) {
            addCriterion("final_amount <>", value, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountGreaterThan(BigDecimal value) {
            addCriterion("final_amount >", value, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("final_amount >=", value, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountLessThan(BigDecimal value) {
            addCriterion("final_amount <", value, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("final_amount <=", value, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountIn(List<BigDecimal> values) {
            addCriterion("final_amount in", values, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountNotIn(List<BigDecimal> values) {
            addCriterion("final_amount not in", values, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("final_amount between", value1, value2, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andFinalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("final_amount not between", value1, value2, "finalAmount");
            return (Criteria) this;
        }

        public Criteria andPpNoIsNull() {
            addCriterion("pp_no is null");
            return (Criteria) this;
        }

        public Criteria andPpNoIsNotNull() {
            addCriterion("pp_no is not null");
            return (Criteria) this;
        }

        public Criteria andPpNoEqualTo(Integer value) {
            addCriterion("pp_no =", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoNotEqualTo(Integer value) {
            addCriterion("pp_no <>", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoGreaterThan(Integer value) {
            addCriterion("pp_no >", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("pp_no >=", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoLessThan(Integer value) {
            addCriterion("pp_no <", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoLessThanOrEqualTo(Integer value) {
            addCriterion("pp_no <=", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoIn(List<Integer> values) {
            addCriterion("pp_no in", values, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoNotIn(List<Integer> values) {
            addCriterion("pp_no not in", values, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoBetween(Integer value1, Integer value2) {
            addCriterion("pp_no between", value1, value2, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoNotBetween(Integer value1, Integer value2) {
            addCriterion("pp_no not between", value1, value2, "ppNo");
            return (Criteria) this;
        }

        public Criteria andTestLineIdIsNull() {
            addCriterion("test_line_id is null");
            return (Criteria) this;
        }

        public Criteria andTestLineIdIsNotNull() {
            addCriterion("test_line_id is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineIdEqualTo(Long value) {
            addCriterion("test_line_id =", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdNotEqualTo(Long value) {
            addCriterion("test_line_id <>", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdGreaterThan(Long value) {
            addCriterion("test_line_id >", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdGreaterThanOrEqualTo(Long value) {
            addCriterion("test_line_id >=", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdLessThan(Long value) {
            addCriterion("test_line_id <", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdLessThanOrEqualTo(Long value) {
            addCriterion("test_line_id <=", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdIn(List<Long> values) {
            addCriterion("test_line_id in", values, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdNotIn(List<Long> values) {
            addCriterion("test_line_id not in", values, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdBetween(Long value1, Long value2) {
            addCriterion("test_line_id between", value1, value2, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdNotBetween(Long value1, Long value2) {
            addCriterion("test_line_id not between", value1, value2, "testLineId");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIsNull() {
            addCriterion("citation_type is null");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIsNotNull() {
            addCriterion("citation_type is not null");
            return (Criteria) this;
        }

        public Criteria andCitationTypeEqualTo(Integer value) {
            addCriterion("citation_type =", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeNotEqualTo(Integer value) {
            addCriterion("citation_type <>", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeGreaterThan(Integer value) {
            addCriterion("citation_type >", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("citation_type >=", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeLessThan(Integer value) {
            addCriterion("citation_type <", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeLessThanOrEqualTo(Integer value) {
            addCriterion("citation_type <=", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIn(List<Integer> values) {
            addCriterion("citation_type in", values, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeNotIn(List<Integer> values) {
            addCriterion("citation_type not in", values, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeBetween(Integer value1, Integer value2) {
            addCriterion("citation_type between", value1, value2, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("citation_type not between", value1, value2, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationIdIsNull() {
            addCriterion("citation_id is null");
            return (Criteria) this;
        }

        public Criteria andCitationIdIsNotNull() {
            addCriterion("citation_id is not null");
            return (Criteria) this;
        }

        public Criteria andCitationIdEqualTo(Integer value) {
            addCriterion("citation_id =", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotEqualTo(Integer value) {
            addCriterion("citation_id <>", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdGreaterThan(Integer value) {
            addCriterion("citation_id >", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("citation_id >=", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdLessThan(Integer value) {
            addCriterion("citation_id <", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdLessThanOrEqualTo(Integer value) {
            addCriterion("citation_id <=", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdIn(List<Integer> values) {
            addCriterion("citation_id in", values, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotIn(List<Integer> values) {
            addCriterion("citation_id not in", values, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdBetween(Integer value1, Integer value2) {
            addCriterion("citation_id between", value1, value2, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotBetween(Integer value1, Integer value2) {
            addCriterion("citation_id not between", value1, value2, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationNameIsNull() {
            addCriterion("citation_name is null");
            return (Criteria) this;
        }

        public Criteria andCitationNameIsNotNull() {
            addCriterion("citation_name is not null");
            return (Criteria) this;
        }

        public Criteria andCitationNameEqualTo(String value) {
            addCriterion("citation_name =", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotEqualTo(String value) {
            addCriterion("citation_name <>", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameGreaterThan(String value) {
            addCriterion("citation_name >", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameGreaterThanOrEqualTo(String value) {
            addCriterion("citation_name >=", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLessThan(String value) {
            addCriterion("citation_name <", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLessThanOrEqualTo(String value) {
            addCriterion("citation_name <=", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLike(String value) {
            addCriterion("citation_name like", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotLike(String value) {
            addCriterion("citation_name not like", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameIn(List<String> values) {
            addCriterion("citation_name in", values, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotIn(List<String> values) {
            addCriterion("citation_name not in", values, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameBetween(String value1, String value2) {
            addCriterion("citation_name between", value1, value2, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotBetween(String value1, String value2) {
            addCriterion("citation_name not between", value1, value2, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIsNull() {
            addCriterion("citation_full_name is null");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIsNotNull() {
            addCriterion("citation_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameEqualTo(String value) {
            addCriterion("citation_full_name =", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotEqualTo(String value) {
            addCriterion("citation_full_name <>", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameGreaterThan(String value) {
            addCriterion("citation_full_name >", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("citation_full_name >=", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLessThan(String value) {
            addCriterion("citation_full_name <", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLessThanOrEqualTo(String value) {
            addCriterion("citation_full_name <=", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLike(String value) {
            addCriterion("citation_full_name like", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotLike(String value) {
            addCriterion("citation_full_name not like", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIn(List<String> values) {
            addCriterion("citation_full_name in", values, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotIn(List<String> values) {
            addCriterion("citation_full_name not in", values, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameBetween(String value1, String value2) {
            addCriterion("citation_full_name between", value1, value2, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotBetween(String value1, String value2) {
            addCriterion("citation_full_name not between", value1, value2, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdIsNull() {
            addCriterion("quotation_version_id is null");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdIsNotNull() {
            addCriterion("quotation_version_id is not null");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdEqualTo(String value) {
            addCriterion("quotation_version_id =", value, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdNotEqualTo(String value) {
            addCriterion("quotation_version_id <>", value, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdGreaterThan(String value) {
            addCriterion("quotation_version_id >", value, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdGreaterThanOrEqualTo(String value) {
            addCriterion("quotation_version_id >=", value, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdLessThan(String value) {
            addCriterion("quotation_version_id <", value, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdLessThanOrEqualTo(String value) {
            addCriterion("quotation_version_id <=", value, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdLike(String value) {
            addCriterion("quotation_version_id like", value, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdNotLike(String value) {
            addCriterion("quotation_version_id not like", value, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdIn(List<String> values) {
            addCriterion("quotation_version_id in", values, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdNotIn(List<String> values) {
            addCriterion("quotation_version_id not in", values, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdBetween(String value1, String value2) {
            addCriterion("quotation_version_id between", value1, value2, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationVersionIdNotBetween(String value1, String value2) {
            addCriterion("quotation_version_id not between", value1, value2, "quotationVersionId");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusIsNull() {
            addCriterion("quotation_status is null");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusIsNotNull() {
            addCriterion("quotation_status is not null");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusEqualTo(Integer value) {
            addCriterion("quotation_status =", value, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusNotEqualTo(Integer value) {
            addCriterion("quotation_status <>", value, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusGreaterThan(Integer value) {
            addCriterion("quotation_status >", value, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("quotation_status >=", value, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusLessThan(Integer value) {
            addCriterion("quotation_status <", value, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusLessThanOrEqualTo(Integer value) {
            addCriterion("quotation_status <=", value, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusIn(List<Integer> values) {
            addCriterion("quotation_status in", values, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusNotIn(List<Integer> values) {
            addCriterion("quotation_status not in", values, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusBetween(Integer value1, Integer value2) {
            addCriterion("quotation_status between", value1, value2, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andQuotationStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("quotation_status not between", value1, value2, "quotationStatus");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("last_modified_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("last_modified_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("last_modified_timestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("last_modified_timestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("last_modified_timestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("last_modified_timestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("last_modified_timestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("last_modified_timestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}