package com.sgs.testdatabiz.core.enums;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public enum TestLineType {
    None(0, "普通【默认值】"),
    Pretreatment(1, "Pretreatment"),
    SubContractOrder(2, "SubContract(Order)"),
    CSPP(4, "CSPP"),
    <PERSON><PERSON><PERSON>(8, "Claim"),
    IngredientTL(16, "SubTL"),
    CloneTestLine(32, "CloneTestLine"),
    Job(64, "Job"),
    FCM(256, "Fcm"),
    SUB_PP(512, "SubPP"),
    VIRTAUL_TL(1024, "Virtaul TL"),
    CSPP_OPEN(4096, "CSPP Open"),
    CSPP_OPEN_TL(8192, "CSPP展开得到的TL"),
    CSPP_TESTLINE_TRIMS_DATA(16384, "标记此CSPP需要使用CSPP下TestLine的基础数据");

    private int type;
    private String message;
    public static final Map<Integer, TestLineType> maps = new HashMap<>();

    static {
        for (TestLineType type : TestLineType.values()) {
            maps.put(type.getType(), type);
        }
    }

    TestLineType(int type, String message) {
        this.type = type;
        this.message = message;
    }


    public int getType() {
        return this.type;
    }


    public String getMessage() {
        return this.message;
    }

    public static TestLineType findType(Integer type) {
        return type != null && maps.containsKey(type) ? maps.get(type) : null;
    }

    public static boolean check(Integer type, TestLineType testLineType) {
        if (type != null && testLineType != null) {
            return (type & testLineType.getType()) > 0;
        }
        return false;
    }

    public static boolean isPretreatment(String pretreatment) {
        return pretreatment != null && pretreatment.toLowerCase().contains("pretreatment");
    }

    public static Integer calculateType(Integer type, TestLineType testLineType) {
        return type != null && testLineType != null ? type ^ testLineType.getType() : null;
    }
} 