package com.sgs.testdatabiz.facade.model.info.starlims;

import com.sgs.framework.core.common.PrintFriendliness;

public final class TestDataMatrixLangInfo extends PrintFriendliness {
    /**
     *
     */
    private String citationName;

    /**
     *
     */
    private String evaluationAlias;

    /**
     *
     */
    private String methodDesc;

    /**
     *
     */
    private String conclusionDisplay;

    /**
     *
     */
    private Integer languageId;

    public String getCitationName() {
        return citationName;
    }

    public void setCitationName(String citationName) {
        this.citationName = citationName;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getMethodDesc() {
        return methodDesc;
    }

    public void setMethodDesc(String methodDesc) {
        this.methodDesc = methodDesc;
    }

    public String getConclusionDisplay() {
        return conclusionDisplay;
    }

    public void setConclusionDisplay(String conclusionDisplay) {
        this.conclusionDisplay = conclusionDisplay;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }
}
