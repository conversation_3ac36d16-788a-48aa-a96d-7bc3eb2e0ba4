package com.sgs.testdatabiz.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class InterfaceConfig {

    @Value("${localiLayer.url}")
    private String localiLayerUrl;

    @Value("${frameWorkApi.url}")
    private String frameWorkApiUrl;

    @Value("${notification.url}")
    private String notificationUrl;

    @Value("${base.url}")
    private String baseUrl;

    public String getBaseUrl() {
        return baseUrl;
    }

    public String getNotificationUrl() {
        return notificationUrl;
    }

    public String getLocaliLayerUrl() {
        return localiLayerUrl;
    }

    public String getFrameWorkApiUrl() {
        return frameWorkApiUrl;
    }

    public String getNotificationApi() {
        return notificationUrl;
    }
}
