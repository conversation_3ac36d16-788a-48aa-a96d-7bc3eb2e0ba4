package com.sgs.testdatabiz.web.base;

import java.util.List;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.framework.tool.utils.Func;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.HeaderHelper;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.integration.FrameWorkClient;
import com.sgs.testdatabiz.facade.model.req.ConclusionInfoReq;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/28 10:39
 */
@Slf4j
@Aspect
@Component
public class HttpRequestAspect {
    private static Logger logger = LoggerFactory.getLogger(HttpRequestAspect.class);
    @Autowired
    private FrameWorkClient frameWorkClient;
//    @Autowired
//    private RedissonClient redissonClient;

    private static final String REQUEST_ID = "requestId";
    private static final String LAB_CODE = "labCode";
    private static final String SYSTEM_ID = "systemId";
    private static final String LAB_ID = "labId";
    private static final String BU_ID = "buId";
    private static final String BU_CODE = "buCode";

    @Pointcut("execution(* com.sgs.testdatabiz.web.controllers.ReportDataController.*(..)))")
    public void aspect() {

    }

    /**
     * @param point
     * @return
     */
    @Around("aspect()")
    public Object around(ProceedingJoinPoint point) {
        Object resp = null;
        RLock rLock = null;
        try {
            Object[] args = point.getArgs();
            if (args ==null || args.length == 0){
                return point.proceed();
            }

            if (!(args[0] instanceof List) && !(args[0] instanceof BaseModel)){
                return point.proceed(args);
            }

            if ((args[0] instanceof List)){
                List list = (List) args[0];
                if (CollectionUtils.isEmpty(list) || !(list.get(0) instanceof BaseModel)){
                    return point.proceed(new Object[]{args});
                }
            }

            //从header中取出基础数据
            String requestId = HeaderHelper.getParamValue(REQUEST_ID);
            String labCode = HeaderHelper.getParamValue(LAB_CODE);
            String systemId = HeaderHelper.getParamValue(SYSTEM_ID);
            log.info("requestId:{} ,labCode:{} ,systemId:{}", requestId, labCode, systemId);

            if (StringUtils.isEmpty(labCode) || StringUtils.isEmpty(systemId)) {
                return BaseResponse.newFailInstance (ResponseCode.PARAM_MISS);
            }
            //把基础数据设置到attribute
//            if (requestId != null) {
//                request.setAttribute(REQUEST_ID, requestId);
//            }

            LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCodeFromCache(labCode,systemId);
            if (labInfo == null) {
                log.error("lab 信息不存在,labCode:{}", labCode);
                BaseResponse errResp = BaseResponse.newFailInstance(ResponseCode.PARAM_VALID_ERROR);
                errResp.setMessage("lab 不存在");
                return errResp;
            }

            // requestId保存到 redis, 五分钟过期，五分钟内再次请求失败
//            String requestKey = String.format(String.format("SCI:TRF:SYSTEM:%s:%s", systemId, requestId).toUpperCase());
//            rLock = redissonClient.getLock(requestKey);
//            Assert.isTrue(rLock.tryLock(1, 300, TimeUnit.SECONDS), ResponseCode.ILLEGAL_ARGUMENT, "请检查Header中requestId(五分钟内不能重复)");

            if (args[0] instanceof List){
                List<BaseModel> list = (List<BaseModel>) args[0];
                list.forEach(baseModel -> {
                    setBaseModelValue(baseModel, requestId, labCode, labInfo, systemId);
                });
                resp = point.proceed(new Object[]{args[0]});
            }else {
                BaseModel baseRequest = (BaseModel) args[0];
                setBaseModelValue(baseRequest, requestId, labCode, labInfo, systemId);
                resp = point.proceed(new Object[]{baseRequest});
            }
        } catch (BizException ex) {
            String errorMsg = ex.getMessage();
            if (StringUtils.isBlank(errorMsg)) {
                errorMsg = "服务器出了点小差错，请稍后再试.";
            }
            logger.error("testdata RequestAspect.around Error.", ex);
            resp = new BaseResponse(ex.getCode(), errorMsg);
        } catch (Throwable ex) {
            logger.error("testdata HttpRequestAspect.around Error.", ex);
            resp = new BaseResponse(ResponseCode.UNKNOWN.getCode(), ex.getMessage());
        } finally {
//            // TODO 处理结束后删除redis ???
//            if (rLock != null && rLock.isLocked() && rLock.isHeldByCurrentThread()){
//                rLock.unlock();
//            }
        }
        return resp;
    }

    private static void setBaseModelValue(BaseModel baseRequest, String requestId, String labCode, LabInfo labInfo, String systemId) {
        baseRequest.setRequestId(requestId);
        baseRequest.setLabCode(labCode);
        if (StringUtils.isNotEmpty(labInfo.getLaboratoryID())){
            baseRequest.setLabId(Long.valueOf(labInfo.getLaboratoryID()));
        }
        baseRequest.setBuCode(labInfo.getProductLineAbbr());
        if (StringUtils.isNotEmpty(systemId)){
            baseRequest.setSystemId(Long.valueOf(systemId));
        }
        if  (StringUtils.isNotEmpty(labInfo.getProductLineID())){
            baseRequest.setBuId(Long.valueOf(labInfo.getProductLineID()));
        }
    }
}
