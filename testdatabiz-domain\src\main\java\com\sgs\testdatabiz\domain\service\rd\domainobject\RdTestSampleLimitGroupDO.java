/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestSampleLimitGroupDO implements Serializable {

    private String limitGroupInstanceId;

    private Integer limitGroupId;
    private Integer pPBaseId;
    private Integer limitGroupTypeId;
    private String limitGroupName;
    private String limitGroupTypeName;
    private String conclusionMode;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private List<RdTestSampleLimitGroupLangDO> languageList;

    private List<RdProductAttributeDO> productAttributeList;

    @Data
    public static class RdTestSampleLimitGroupLangDO implements Serializable{
        private Integer languageId;
        private String limitGroupName;
        private String limitGroupTypeName;
    }
}
