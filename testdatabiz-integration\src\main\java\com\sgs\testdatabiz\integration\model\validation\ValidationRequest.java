package com.sgs.testdatabiz.integration.model.validation;

import lombok.Data;
import lombok.ToString;

@ToString(exclude="data")
@Data
public class ValidationRequest {
    private String buCode;
    private Long buId;
    private String customerGroupCode;
    private String customerNo;
    private Object data;
    private String labCode;
    private Long labId;
    private String mode;
    private String operationTime;
    private String operator;
    private String operatorEmail;
    private String trfNo;
    /**
     * 产品线编码
     */
    private String productLineCode;
    private Integer refSystemId;
    private String requestId;
    private String serviceUnit;
    /**
     * 源产品线编码
     */
    private String sourceProductLineCode;
    private Long systemId;
    private String validateLevel;
    private String validateName;

}
