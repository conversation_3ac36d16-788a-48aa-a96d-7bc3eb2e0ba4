package com.sgs.testdatabiz.domain.service.testdata.impl.check.strategy;

import com.sgs.testdatabiz.domain.service.testdata.impl.check.RTDContext;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

/**
 * @author: shawn.yang
 * @create: 2023-03-27 17:09
 */
public class MatrixCheckStrategy extends AbstractDataCheckStrategy{

    @Override
    public boolean doCheck(final ReportTestDataInfo reportTestData,final RTDContext rtdContext) {

        return false;
    }

    @Override
    public boolean isMute() {
        return false;
    }
}
