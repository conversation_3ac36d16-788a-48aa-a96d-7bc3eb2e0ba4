package com.sgs.testdatabiz.facade.model.req.fast;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 从FAST过来的测试数据应与其他数据一样进入新的test data 表
 */
@ApiModel
public class TestDataInfoReq extends BaseModel {

    private String orderNo;//tb_test_data_object_rel.orderNo
    private String objectNo;//tb_test_data_object_rel.objectNo=JobNo
    private String reportNo;
    /**
     *
     */
    private Date completedDate;
    private String jobNo;//tb_test_data_object_rel.objectNo=JobNo
    private List<TestLineTestDataReq> datas;

    @Override
    public String getExtId() {
        return StringUtils.isNotBlank(reportNo) ? reportNo : orderNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public List<TestLineTestDataReq> getDatas() {
        return datas;
    }

    public void setDatas(List<TestLineTestDataReq> datas) {
        this.datas = datas;
    }

}
