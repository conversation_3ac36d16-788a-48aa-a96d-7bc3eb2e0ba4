<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportSampleGroupMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="SampleGroupInstanceId" property="sampleGroupInstanceId" jdbcType="VARCHAR" />
    <result column="SampleId" property="sampleId" jdbcType="VARCHAR" />
    <result column="SampleGroupId" property="sampleGroupId" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
    <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
    <result column="CeatedTime" property="ceatedTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, SampleGroupInstanceId, SampleId, SampleGroupId, ActiveIndicator, ReportNo, OrderNo, 
    LastModifiedTimestamp, CeatedTime
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_sample_group
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_sample_group
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupExample" >
    delete from tb_report_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupPO" >
    insert into tb_report_sample_group (Id, SampleGroupInstanceId, SampleId, 
      SampleGroupId, ActiveIndicator, ReportNo, 
      OrderNo, LastModifiedTimestamp, CeatedTime
      )
    values (#{id,jdbcType=BIGINT}, #{sampleGroupInstanceId,jdbcType=VARCHAR}, #{sampleId,jdbcType=VARCHAR}, 
      #{sampleGroupId,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=TINYINT}, #{reportNo,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{lastModifiedTimestamp,jdbcType=TIMESTAMP}, #{ceatedTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupPO" >
    insert into tb_report_sample_group
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="sampleGroupInstanceId != null" >
        SampleGroupInstanceId,
      </if>
      <if test="sampleId != null" >
        SampleId,
      </if>
      <if test="sampleGroupId != null" >
        SampleGroupId,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="reportNo != null" >
        ReportNo,
      </if>
      <if test="orderNo != null" >
        OrderNo,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
      <if test="ceatedTime != null" >
        CeatedTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sampleGroupInstanceId != null" >
        #{sampleGroupInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null" >
        #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="sampleGroupId != null" >
        #{sampleGroupId,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="ceatedTime != null" >
        #{ceatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_sample_group
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sampleGroupInstanceId != null" >
        SampleGroupInstanceId = #{record.sampleGroupInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleId != null" >
        SampleId = #{record.sampleId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleGroupId != null" >
        SampleGroupId = #{record.sampleGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.reportNo != null" >
        ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ceatedTime != null" >
        CeatedTime = #{record.ceatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_sample_group
    set Id = #{record.id,jdbcType=BIGINT},
      SampleGroupInstanceId = #{record.sampleGroupInstanceId,jdbcType=VARCHAR},
      SampleId = #{record.sampleId,jdbcType=VARCHAR},
      SampleGroupId = #{record.sampleGroupId,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      CeatedTime = #{record.ceatedTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupPO" >
    update tb_report_sample_group
    <set >
      <if test="sampleGroupInstanceId != null" >
        SampleGroupInstanceId = #{sampleGroupInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null" >
        SampleId = #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="sampleGroupId != null" >
        SampleGroupId = #{sampleGroupId,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="reportNo != null" >
        ReportNo = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="ceatedTime != null" >
        CeatedTime = #{ceatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupPO" >
    update tb_report_sample_group
    set SampleGroupInstanceId = #{sampleGroupInstanceId,jdbcType=VARCHAR},
      SampleId = #{sampleId,jdbcType=VARCHAR},
      SampleGroupId = #{sampleGroupId,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      ReportNo = #{reportNo,jdbcType=VARCHAR},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      CeatedTime = #{ceatedTime,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>