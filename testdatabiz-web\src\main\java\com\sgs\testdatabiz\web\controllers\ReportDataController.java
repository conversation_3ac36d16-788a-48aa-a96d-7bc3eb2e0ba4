package com.sgs.testdatabiz.web.controllers;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataBatchDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.facade.model.req.rd.*;
import com.sgs.testdatabiz.facade.v2.ReportDataBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 13:48
 */
@Slf4j
@RestController
@RequestMapping("/report-data")
@Api(value = "/report-data", tags = "report-data")
public class ReportDataController {

    public static final String ADMIN_TOKEN = "a1b2c3d4e5f6g7h8i9j0";
    private final ReportDataBizService reportDataBizService;


    @PostMapping("/import")
    @ApiOperation(value = "ReportData信息导入")
    public BaseResponse<Void> importReportData(@RequestBody ImportReportDataReq request) throws InterruptedException {
        // ImportReportDataReq --> ReportDataDTO
        ReportDataBatchDTO reportDataDTO = new ReportDataBatchDTO();
        BeanUtils.copyProperties(request, reportDataDTO);

        return reportDataBizService.importReportData(reportDataDTO, request.getLabId(), request.getLabCode(), "v1", true);
    }


    @PostMapping("/updateNonReportInfo")
    @ApiOperation(value = "updateNonReportInfo")
    public BaseResponse<Void> updateNonReportInfo(@RequestBody ImportReportDataReq request) throws InterruptedException {
        // ImportReportDataReq --> ReportDataDTO
        ReportDataBatchDTO reportDataDTO = new ReportDataBatchDTO();
        BeanUtils.copyProperties(request, reportDataDTO);

        return reportDataBizService.importReportData(reportDataDTO, request.getLabId(), request.getLabCode(), "v1", false);
    }

    @PostMapping("/querySubReportData")
    public BaseResponse<?> querySubReportData(@RequestBody QuerySubReportDataReq subReportDataReq) {
        return reportDataBizService.querySubReportData(subReportDataReq);
    }

//    @PostMapping("/batch-import")
//    @ApiOperation(value = "ReportData信息批量导入")
//    public BaseResponse<Void> batchImportReportData(@RequestBody List<ImportReportDataReq> reqList) {
//        List<ReportDataDTO> reportDataDTOList = reqList.stream().map(req -> {
//            ReportDataDTO reportDataDTO = new ReportDataDTO();
//            BeanUtils.copyProperties(req, reportDataDTO);
//            return reportDataDTO;
//        }).collect(Collectors.toList());
//
//        return reportDataBizService.batchImportReportData(reportDataDTOList);
//    }

    @PostMapping("/batch-exist")
    @ApiOperation(value = "ReportData是否存在")
    public BaseResponse<?> batchExistReportData(@RequestBody BatchExistReportDataReq req) {
        return reportDataBizService.batchExistReportData(req.getLabId(), req.getReportNos());
    }

    @PostMapping("/cancel")
    @ApiOperation(value = "ReportData Cancel")
    public BaseResponse<Void> cancel(@RequestBody CancelReportDataReq req) {
        return reportDataBizService.cancel(req.getLabId(), req.getReportNo(), req.getReportStatus());
    }

    @PostMapping("/modifyReportStatusById")
    @ApiOperation(value = "Modify Report Status By Id")
    public BaseResponse<Void> modifyReportStatusById(@RequestBody @Validated ModifyReportStatusByIdReq req) {
        if(!Objects.equals(req.getToken(), ADMIN_TOKEN)) {
            throw new IllegalStateException("The execution of the token is denied.");
        }
        return reportDataBizService.modifyReportStatusById(req.getReportId(), req.getReportStatus());
    }

    @PostMapping("/updateReportExpire")
    public BaseResponse<?> updateReportExpire(@RequestBody BatchExportReportDataReq request) {
        return reportDataBizService.updateReportExpire(request.getLabId(), request.getReportNos(), request.getLabCode());
    }

    @PostMapping("/updateReportExpireById")
    @ApiOperation(value = "Update Report Expire By Id")
    public BaseResponse<?> updateReportExpireById(@RequestBody @Validated UpdateReportExpireByIdReq request) {
        if(!Objects.equals(request.getToken(), ADMIN_TOKEN)) {
            throw new IllegalStateException("The execution of the token is denied.");
        }
        return reportDataBizService.updateReportExpireById(request.getReportId(), request.getActiveIndicator());
    }

    @PostMapping("/export")
    @ApiOperation(value = "ReportData信息导出")
    public BaseResponse<ReportDataDTO> exportReportData(@RequestBody ExportReportDataReq request) {
        return reportDataBizService.exportReportData(request.getLabId(), request.getReportNo(), request.getLabCode());
    }

    @PostMapping("/batch-export-header")
    @ApiOperation(value = "Report信息批量导出")
    public BaseResponse<?> exportReportHeaderList(@RequestBody BatchExportReportDataReq request) {
        return reportDataBizService.exportReportHeaderList(request.getLabId(), request.getReportNos(), request.getLabCode());
    }
    @PostMapping("/export-with-filter")
    @ApiOperation(value = "ReportData信息带过滤导出")
    public BaseResponse<ReportDataDTO> exportReportDataWithFilter(@RequestBody ExportReportDataReq request) {
        return reportDataBizService.exportReportDataWithFilter(request.getLabId(), request.getReportNo(), request.getLabCode());
    }
    @PostMapping("/export-list-with-filter")
    @ApiOperation(value = "ReportData信息带过滤导出列表")
    public BaseResponse<List<ReportDataDTO>> exportReportDataListWithFilter(@RequestBody BatchExportReportDataReq request) {
        return reportDataBizService.exportReportDataListWithFilter(request.getLabId(), request.getReportNos(), request.getLabCode(), request.getTestSampleLevel());
    }

    @PostMapping("/batch-export")
    @ApiOperation(value = "ReportData信息批量导出")
    public BaseResponse<?> exportReportDataList(@RequestBody BatchExportReportDataReq request) {
        return reportDataBizService.exportReportDataList(request.getLabId(), request.getReportNos(), request.getLabCode());
    }

    @PostMapping("/batch-export-all-status")
    public BaseResponse<?> exportReportDataListAllStatus(@RequestBody BatchExportReportDataReq request) {
        return reportDataBizService.exportReportDataListAllStatus(request.getLabId(), request.getReportNos(), request.getLabCode());
    }

    @PostMapping("/exportByVersion")
    public BaseResponse<?> exportByVersion(@RequestBody ExportReportDataReq request) {
        return reportDataBizService.exportByVersion(request.getLabId(), request.getReportNo(), request.getLabCode());
    }

//    @PostMapping("/batch-export-by-trf-no")
//    @ApiOperation(value = "根据trfNo ReportData信息导出")
//    public BaseResponse<?> exportReportDataListByTrfNo(@RequestBody ExportReportDataTrfNoReq request) {
//        return reportDataBizService.exportReportDataByTrfNo(request.getLabId(), request.getTrfNo(), request.getLabCode());
//    }

    @PostMapping("/import-invoice")
    @ApiOperation(value = "导入invoice")
    public BaseResponse<?> importInvoice(@RequestBody ImportReportInvoiceReq request) {
        return reportDataBizService.importInvoice(request);
    }

    @PostMapping("/export-invoice")
    @ApiOperation(value = "导出invoice")
    public BaseResponse<?> exportInvoice(@RequestBody ExportReportInvoiceReq request) {
        return reportDataBizService.exportInvoice(request);
    }

    @PostMapping("/exist-invoice")
    @ApiOperation(value = "invoice是否存在")
    public BaseResponse<?> existInvoice(@RequestBody ExistReportInvoiceReq request) {
        return reportDataBizService.existInvoice(request);
    }

    @PostMapping("/import-quotation")
    @ApiOperation(value = "导入quotation")
    public BaseResponse<?> importQuotation(@RequestBody ImportQuotationReq request) {
        return reportDataBizService.importQuotation(request);
    }

    @PostMapping("/export-quotation")
    @ApiOperation(value = "导出quotation")
    public BaseResponse<?> exportQuotation(@RequestBody ExportQuotationReq request) {
        return reportDataBizService.exportQuotation(request);
    }

    @PostMapping("/exist-quotation")
    @ApiOperation(value = "quotation是否存在")
    public BaseResponse<?> existQuotation(@RequestBody ExistQuotationReq request) {
        return reportDataBizService.existQuotation(request);
    }

    public ReportDataController(ReportDataBizService reportDataBizService) {
        this.reportDataBizService = reportDataBizService;
    }
}
