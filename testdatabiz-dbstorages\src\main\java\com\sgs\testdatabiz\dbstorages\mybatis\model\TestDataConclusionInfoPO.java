package com.sgs.testdatabiz.dbstorages.mybatis.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class TestDataConclusionInfoPO {
    /**
     *
     */
    @ApiModelProperty("SubContractNo")
    private String subContractNo;

    /**
     *
     */
    @ApiModelProperty("testMatrixId")
    private String testMatrixId;

    /**
     *
     */
    @ApiModelProperty("ppVersionId")
    private Integer ppVersionId;

    /**
     *
     */
    @ApiModelProperty("subReportNo")
    private String subReportNo;

    /**
     *
     */
    @ApiModelProperty("reportNo")
    private String reportNo;

    /**
     *
     */
    @ApiModelProperty("aid")
    private Long aid;

    /**
     *
     */
    @ApiModelProperty("测试项")
    private Integer testLineId;

    /**
     *
     */
    @ApiModelProperty("测试标准")
    private Integer citationId;

    /**
     *
     */
    @ApiModelProperty("测试版本")
    private Integer citationVersionId;

    /**
     *
     */
    @ApiModelProperty("测试类型")
    private Integer citationType;

    /**
     *
     */
    @ApiModelProperty("测试名称")
    private String citationName;

    /**
     *
     */
    @ApiModelProperty("样品Id")
    private String sampleId;

    /**
     *
     */
    @ApiModelProperty("样品编号")
    private String sampleNo;

    @ApiModelProperty("多语言")
    private String languages;

    @ApiModelProperty("多语言类型")
    private Integer languageType;
    /**
     *
     */
    @ApiModelProperty("结论Id")
    private Integer conclusionId;

    /**
     *
     */
    @ApiModelProperty("结论")
    private String conclusionDisplay;

    private String testLineInstanceId;
    @ApiModelProperty("评价别名")
    private String evaluationAlias;
    /**
     * testDataMatrixId
     */
    @ApiModelProperty("testDataMatrixId")
    private String testDataMatrixId;

    @ApiModelProperty("extFields")
    private String extFields;
}
