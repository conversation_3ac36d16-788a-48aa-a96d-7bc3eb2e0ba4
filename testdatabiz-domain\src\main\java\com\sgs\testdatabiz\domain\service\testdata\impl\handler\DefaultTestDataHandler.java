package com.sgs.testdatabiz.domain.service.testdata.impl.handler;

import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.otsnotes.facade.model.rsp.matrix.SubContractTestMatrixRsp;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMatrixExtPO;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.base.AbstractTestDataHandler;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 默认数据处理类
 */
@Service("defaultTestDataHandler")
public class DefaultTestDataHandler extends AbstractTestDataHandler<Object> {
    @Override
    protected boolean inputValidate(Object inputData) {
        throw new UnsupportedOperationException();
    }

    @Override
    protected ReportTestDataInfo inputBuild(Object inputData) {
        throw new UnsupportedOperationException();
    }

    @Override
    protected boolean checkChannelData(ReportTestDataInfo testMatrixs, List<SubContractTestMatrixRsp> subContractTestMatrixs, List<TestDataMatrixExtPO> testDataMatrixInfoList) {
        return true;
    }

    @Override
    public SourceTypeEnum getChannel() {
        throw new UnsupportedOperationException();
    }

}
