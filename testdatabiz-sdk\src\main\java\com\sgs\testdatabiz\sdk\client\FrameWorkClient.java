package com.sgs.testdatabiz.sdk.client;

import cn.hutool.json.ObjectMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.sgs.testdatabiz.sdk.model.DictEnumInfo;
import com.sgs.testdatabiz.sdk.util.HttpClientUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class FrameWorkClient {

    private static final Logger logger = LoggerFactory.getLogger(FrameWorkClient.class);

    private static Map<String, String> requestUrlMap = new HashMap<>();
    private static final String UAT = "UAT";
    private static final String PROD = "PROD";
    private static final String TEST = "TEST";
    static {
        requestUrlMap.put(TEST, "https://cnapp-test.sgs.net/FrameWorkApi");
        requestUrlMap.put(UAT, "https://cnapp-uat.sgs.net/FrameWorkApi");
        requestUrlMap.put(PROD, "https://cnapp.sgs.net/FrameWorkApi");
    }


    /**
     * @param keyGroup
     * @return
     */
    public static ConcurrentHashMap<String, String> getDataDictionaryMap(String keyGroup,String systemID,String env) {
        ConcurrentHashMap<String, String> hashMaps = new ConcurrentHashMap<>();
        try {
            HashMap<String, String> reqParams = new HashMap<>();
//            reqParams.put("bUID", "1");
            reqParams.put("systemID", systemID);
            reqParams.put("SysKeyGroup", keyGroup);

            String frameWorkApi = String.format("%s/dataDictionary/api/v1/get/dataDictionary", requestUrlMap.get(env));
            String jsonStr = HttpClientUtil.sendGet(frameWorkApi, reqParams);
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            JSONArray jsonArray = JSONArray.parseArray(jsonStr);
            if (jsonArray == null || jsonArray.isEmpty()) {
                return null;
            }
            String sysKey = "";
            for (int jsonIndex = 0; jsonIndex < jsonArray.size(); jsonIndex++) {
                JSONObject json = jsonArray.getJSONObject(jsonIndex);
                sysKey = json.getString("sysKey");
                if (hashMaps.containsKey(sysKey)) {
                    continue;
                }
                hashMaps.put(sysKey, json.getString("sysValue"));
            }
        } catch (Exception ex) {
            logger.error("FrameWorkClient.getDataDictionaryMap 信息异常,keyGroup=" + keyGroup, ex);
        }
        return hashMaps;
    }

    public static List<DictEnumInfo> getDataDictionaryMap(FrameWorkReq frameWorkReq, String env) {
        List<DictEnumInfo> dictEnumInfoList = Lists.newArrayList();
        try {
            HashMap<String, String> reqParams = new HashMap<>();
//            reqParams.put("bUID", "1");
            reqParams.put("systemID", frameWorkReq.getSystemID());
            reqParams.put("SysKeyGroup", frameWorkReq.getSysKeyGroup());

            String frameWorkApi = String.format("%s/dataDictionary/api/v1/get/dataDictionary", requestUrlMap.get(env));
            String jsonStr = HttpClientUtil.sendGet(frameWorkApi, reqParams);
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            JSONArray jsonArray = JSONArray.parseArray(jsonStr);
            if (jsonArray == null || jsonArray.isEmpty()) {
                return null;
            }
            for (int jsonIndex = 0; jsonIndex < jsonArray.size(); jsonIndex++) {
                JSONObject json = jsonArray.getJSONObject(jsonIndex);
                DictEnumInfo dictEnumInfo = JSONObject.toJavaObject(json, DictEnumInfo.class);
                dictEnumInfoList.add(dictEnumInfo);
            }
        } catch (Exception ex) {
            logger.error("FrameWorkClient.getDataDictionaryMap 信息异常,frameWorkReq=" + frameWorkReq, ex);
        }
        return dictEnumInfoList;
    }

    public static FrameWorkResp getDataDictionaryMap(List<FrameWorkReq> frameWorkReqList,String env) {
        FrameWorkResp responseMap = new FrameWorkResp();
        try {
            String jsonRequest = JSONArray.toJSONString(frameWorkReqList);

            String frameWorkApi = String.format("%s/dataDictionary/api/v1/get/dataDictionaryBatch", requestUrlMap.get(env));
            String jsonStr = HttpClientUtil.postJson(frameWorkApi, jsonRequest);
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            ConcurrentHashMap<String, List<DictEnumInfo>> dataDictionaryMap= JSON.parseObject(jsonStr, new TypeReference<ConcurrentHashMap<String, List<DictEnumInfo>>>() {});

            responseMap.setDataDictionaryMap(dataDictionaryMap);
        } catch (Exception ex) {
            logger.error("FrameWorkClient.getDataDictionaryMap 信息异常,jsonRequest=" + frameWorkReqList, ex);
        }
        return responseMap;
    }

    /**
     * @param keyGroup
     * @param sysKey
     * @return
     */
    public static String getDataDictionaryValue(String keyGroup, String systemId,String sysKey,String env) {
        if (sysKey == null||env ==null) {
            return "";
        }
        ConcurrentHashMap<String, String> hashMaps = getDataDictionaryMap(keyGroup,systemId,env);
        if (hashMaps == null || hashMaps.isEmpty() || !hashMaps.containsKey(sysKey)) {
            return "";
        }
        return hashMaps.get(sysKey);
    }
}
