package com.sgs.testdatabiz.web.config;

import com.sgs.testdatabiz.core.config.TomcatConfig;
import org.apache.catalina.connector.Connector;
import org.apache.coyote.AbstractProtocol;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;

/**
 * Created by <PERSON> on 2019/05/09.
 * Description:
 */
public class MyTomcatConnectorCustomizer implements TomcatConnectorCustomizer {
    private TomcatConfig tomcatConfig;

    public MyTomcatConnectorCustomizer(TomcatConfig tomcatConfig) {
        this.tomcatConfig = tomcatConfig;
    }

    @Override
    public void customize(Connector connector) {
        AbstractProtocol<?> protocol = (AbstractProtocol<?>) connector.getProtocolHandler();
        // 最大排队数
        connector.setProperty("acceptCount", String.valueOf(tomcatConfig.getAcceptCount()));
        // 最大连接数
        protocol.setMaxConnections(tomcatConfig.getMaxConnections());
        // 最大线程数
        protocol.setMaxThreads(tomcatConfig.getMaxThreads());
        // 连接超时
        protocol.setConnectionTimeout(tomcatConfig.getConnectionTimeout());
    }
}
