package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportSampleGroupPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportSampleGroupMapper {
    int countByExample(RdReportSampleGroupExample example);

    int deleteByExample(RdReportSampleGroupExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportSampleGroupPO record);

    int insertSelective(RdReportSampleGroupPO record);

    List<RdReportSampleGroupPO> selectByExample(RdReportSampleGroupExample example);

    RdReportSampleGroupPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportSampleGroupPO record, @Param("example") RdReportSampleGroupExample example);

    int updateByExample(@Param("record") RdReportSampleGroupPO record, @Param("example") RdReportSampleGroupExample example);

    int updateByPrimaryKeySelective(RdReportSampleGroupPO record);

    int updateByPrimaryKey(RdReportSampleGroupPO record);
}