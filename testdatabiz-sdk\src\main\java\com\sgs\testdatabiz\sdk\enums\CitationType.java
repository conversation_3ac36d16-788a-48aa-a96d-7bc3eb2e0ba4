package com.sgs.testdatabiz.sdk.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/1/5 11:38
 */
public enum CitationType {
    None(0, "None","默认"),
    Method(1,  "Method","测试方法"),
    Regulation(2,  "Regulation","测试章程"),
    Standard(3,  "Standard","测试标准");

    private int type;
    private String message;
    private String desc;
    static Map<Integer, CitationType> maps = new HashMap();

    private CitationType(int type, String message , String desc) {
        this.type = type;
        this.message = message;
        this.desc = desc;
    }


    public int getType() {
        return this.type;
    }

    public String getMessage() {
        return this.message;
    }

    public String getDesc() {
        return desc;
    }
    public static CitationType findType(Integer type) {
        return type != null && maps.containsKey(type) ? (CitationType)maps.get(type) : null;
    }

    public static boolean check(Integer type, CitationType citationType) {
        if (type != null && maps.containsKey(type)) {
            return maps.get(type) == citationType;
        } else {
            return false;
        }
    }

    static {
        CitationType[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            CitationType type = var0[var2];
            if (type.getType() > 0) {
                maps.put(type.getType(), type);
            }
        }

    }
}

