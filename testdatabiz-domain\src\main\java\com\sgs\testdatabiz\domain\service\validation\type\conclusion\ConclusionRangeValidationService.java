package com.sgs.testdatabiz.domain.service.validation.type.conclusion;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.ConclusionType;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdConclusionDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Component("conclusionRangeValidationService")
public class ConclusionRangeValidationService extends AbstractConclusionValidationService {

    @Override
    protected void validateConclusion(RdConclusionDTO conclusion, AtomicBoolean isValid, List<String> errorMessages) {
        String conclusionCode = conclusion.getConclusionCode();
        
        if (Func.isEmpty(conclusionCode)) {
            return;
        }

        boolean isValidCode = false;
        for (ConclusionType type : ConclusionType.values()) {
            if (conclusionCode.equals(type.getConclusion()) || conclusionCode.equals(type.getKey())) {
                isValidCode = true;
                break;
            }
        }

        if (!isValidCode) {
            isValid.set(false);
            errorMessages.add("ConclusionCode " + conclusionCode + " is not in valid range");
        }
    }

    @Override
    public Integer getOrder() {
        return 2;
    }
} 