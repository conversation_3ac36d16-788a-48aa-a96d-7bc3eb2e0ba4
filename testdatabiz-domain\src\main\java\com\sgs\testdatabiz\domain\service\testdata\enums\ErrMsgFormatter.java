package com.sgs.testdatabiz.domain.service.testdata.enums;

import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.utils.TestDataImportContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @author: shawn.yang
 * @create: 2023-03-23 11:31
 */
public enum ErrMsgFormatter {
    SAMPLE_NO("SampleNo"),
    ANALYTE_NAME("AnalyteName"),
    TEST_VALUE("TestValue"),
    SCHEME("Scheme"),
    TEST_MATRIX("TestMatrix"),
    ORDER_TEST_MATRIX("OrderTestMatrix"),
    CONCLUSION("Conclusion"),
    CONCLUSION_ID("ConclusionId"),
    SCHEME_LACK_SAMPLE("SchemeLackSample"),
    SCHEME_ASSIGN_SAMPLE("SchemeAssignSample"),
    ANALYTE_CODE("AnalyteCode");
    ;


    private static final Map<String, Pair> BY_CHANNEL_MAP = new HashMap<>();

    private final String code;

    ErrMsgFormatter(String code) {
        this.code = code;
    }

    private static class Pair {
        private final String fmt;
        private final String title;

        public Pair(String fmt, String title) {
            this.fmt = fmt;
            this.title = title;
        }
    }

    static {
        //========================================初始化STARLIMS format和 title =====================================
        String STARLIMS = SourceTypeEnum.STARLIMS.name();
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, SAMPLE_NO.name()), new Pair("%s%s", "EXTERNALIDENT在Slim中缺少DESCRIPTION_1："));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, ANALYTE_NAME.name()), new Pair("%s（%s）", "Test Line缺少中文Analyte："));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, TEST_VALUE.name()), new Pair("%s（%s）", "Anlayte下缺测试结果："));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, SCHEME.name()), new Pair("%s%s", "在Order中没有找Test Line，请确认是否有添加该TestLine Id："));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, TEST_MATRIX.name()), new Pair("%s：%s", "Matrix下有多组重复结果"));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, ORDER_TEST_MATRIX.name()), new Pair("%s和%s", "当前Matrix已存在Test Data："));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, CONCLUSION.name()), new Pair("%s：%s", "Matrix下有多个Conclusion"));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, CONCLUSION_ID.name()), new Pair("%s：%s", "Conclusion 必须有效"));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, SCHEME_LACK_SAMPLE.name()), new Pair("%s：%s", "Test Line下缺少Sample："));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, SCHEME_ASSIGN_SAMPLE.name()), new Pair("%s和%s", "在Order中没有找到Matrix的Assign关系："));
        BY_CHANNEL_MAP.put(buildKey(STARLIMS, ANALYTE_CODE.name()), new Pair("%s：%s", "Test Line缺少Analyte (Sample-AnalyteCode)："));


        //========================================初始化SLIM format和 title =====================================
        String SLIM = SourceTypeEnum.SLIM.name();
        BY_CHANNEL_MAP.put(buildKey(SLIM, SAMPLE_NO.name()), new Pair("%s%s", "EXTERNALIDENT在Slim中缺少DESCRIPTION_1："));
        BY_CHANNEL_MAP.put(buildKey(SLIM, ANALYTE_NAME.name()), new Pair("%s（%s）", "SCH_CODE(ANALYTECODE)缺少中文Analyte Name(LSA_DESC)："));
        BY_CHANNEL_MAP.put(buildKey(SLIM, TEST_VALUE.name()), new Pair("%s（%s）", "SCH_CODE(ANALYTECODE)缺少ROUNDEDVALUE："));
        BY_CHANNEL_MAP.put(buildKey(SLIM, SCHEME.name()), new Pair("%s", "在Order中没有找到SCH_CODE对应的 TestLine，请确认是否有添加该TestLine，或者缺少该TestLine和Scheme的Mapping关系："));
        BY_CHANNEL_MAP.put(buildKey(SLIM, TEST_MATRIX.name()), new Pair("%s：%s", "Matrix下有多组重复结果"));
        BY_CHANNEL_MAP.put(buildKey(SLIM, ORDER_TEST_MATRIX.name()), new Pair("%s和%s", "当前Matrix已存在Test Data："));
        BY_CHANNEL_MAP.put(buildKey(SLIM, CONCLUSION.name()), new Pair("%s：%s", "Matrix下有多个Conclusion"));
        BY_CHANNEL_MAP.put(buildKey(SLIM, CONCLUSION_ID.name()), new Pair("%s：%s", "Conclusion 必须有效"));
        BY_CHANNEL_MAP.put(buildKey(SLIM, SCHEME_LACK_SAMPLE.name()), new Pair("%s：%s", "SCH_CODE下缺少Sample："));
        BY_CHANNEL_MAP.put(buildKey(SLIM, SCHEME_ASSIGN_SAMPLE.name()), new Pair("%s和%s", "在Order中没有找到SCH_CODE和DESCRIPTION_1的Assign关系："));
        BY_CHANNEL_MAP.put(buildKey(SLIM, ANALYTE_CODE.name()), new Pair("%s：%s", "SCH_CODE缺少Analyte (Sample-AnalyteCode)："));

    }

    private static String buildKey(String channel, String errType) {
        return channel + "_" + errType;
    }

    public String format(String key, String value) {
        return String.format(getFormat(), StringUtils.defaultString(key), StringUtils.defaultString(value));
    }

    public static ErrMsgFormatter findByCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        for (ErrMsgFormatter value : values()) {
            if (Objects.equals(value.code, code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getFormat() {
        SourceTypeEnum channel = TestDataImportContext.getChannel();
        return getFormat(channel);
    }

    public String getFormat(SourceTypeEnum channel) {
        if (channel != null) {
            Pair pair = BY_CHANNEL_MAP.get(buildKey(channel.name(), name()));
            return pair == null ? null : pair.fmt;
        }
        return null;
    }

    public String getTitle() {
        SourceTypeEnum channel = TestDataImportContext.getChannel();
        return getTitle(channel);
    }

    public String getTitle(SourceTypeEnum channel) {
        if (channel != null) {
            Pair pair = BY_CHANNEL_MAP.get(buildKey(channel.name(), name()));
            return pair == null ? null : pair.title;
        }
        return null;
    }

}
