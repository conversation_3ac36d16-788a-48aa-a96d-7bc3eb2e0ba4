package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportProductDffMapper {
    int countByExample(RdReportProductDffExample example);

    int deleteByExample(RdReportProductDffExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportProductDffPO record);

    int insertSelective(RdReportProductDffPO record);

    List<RdReportProductDffPO> selectByExample(RdReportProductDffExample example);

    RdReportProductDffPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportProductDffPO record, @Param("example") RdReportProductDffExample example);

    int updateByExample(@Param("record") RdReportProductDffPO record, @Param("example") RdReportProductDffExample example);

    int updateByPrimaryKeySelective(RdReportProductDffPO record);

    int updateByPrimaryKey(RdReportProductDffPO record);

    int batchInsert(List<RdReportProductDffPO> list);

    int batchUpdate(List<RdReportProductDffPO> list);
}