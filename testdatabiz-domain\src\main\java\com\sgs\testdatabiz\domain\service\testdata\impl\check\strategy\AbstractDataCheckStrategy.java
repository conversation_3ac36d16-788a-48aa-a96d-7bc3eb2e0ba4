package com.sgs.testdatabiz.domain.service.testdata.impl.check.strategy;

import com.sgs.testdatabiz.domain.service.testdata.impl.check.RTDContext;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

/**
 * @author: shawn.yang
 * @create: 2023-03-27 19:32
 */
public abstract class AbstractDataCheckStrategy implements DataCheckStrategy{
    private DataCheckStrategy next;


    @Override
    public final boolean check(ReportTestDataInfo reportTestData, RTDContext rtdContext) {
        //当前策略执行结果
        boolean checkResult = this.doCheck(reportTestData, rtdContext);

        // 当前策略是mute,且有下一个策略，执行下一个策略，否则直接返回true
        if (this.isMute()){
            if (next !=null){
                return next.check(reportTestData, rtdContext);
            }else {
                return true;
            }
        }else { // 如果当前不是mute，且有下一个策略，需要当前结果为true，才执行下一个策略，否则直接返回当前结果
            if (next !=null){
                return checkResult && next.check(reportTestData, rtdContext);
            }else {
                return checkResult;
            }
        }
    }

    protected abstract boolean doCheck(final ReportTestDataInfo reportTestData,final RTDContext rtdContext);

    @Override
    public final DataCheckStrategy getNext() {
        return next;
    }

    @Override
    public final DataCheckStrategy setNext(final DataCheckStrategy next) {
        this.next = next;
        return next;
    }
}
