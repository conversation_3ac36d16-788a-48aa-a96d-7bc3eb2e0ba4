spring.profiles.active=uat
spring.application.name=testDataBiz
sgs.trace.application.system=testDataBiz
sgs.trace.dynamic.aop.config=execution(public * com.sgs.testdatabiz.web.controllers..*Controller.*(..)))

# Filter Configuration
report.filter.enabled=true
# systemId=0 means the configuration applies to all systems
report.filter.systems.0.enabled=true
# Define all possible filter types that should be enabled globally
report.filter.systems.0.filterTypes=ATTACHMENT_VISIBILITY,PRETREATMENT_TEST_LINE,TEST_LINE_REMARK,CONCLUSION_CONVERTER,TEST_LINE_STATUS

logging.config=classpath:logback.xml
#spring.mvc.favicon.enabled=false
server.servlet.context-path=/testdatabiz/api

# Buffer output such that it is only flushed periodically.
server.tomcat.accesslog.buffered=true
# Directory in which log files are created. Can be relative to the tomcat base dir or absolute.
server.tomcat.accesslog.directory=/usr/local/applogs/testdatabiz.iapi.sgs.net
# Enable access log.
server.tomcat.accesslog.enabled=true
# Date format to place in log file name.
server.tomcat.accesslog.file-date-format=.yyyy-MM-dd
# Format pattern for access logs.
server.tomcat.accesslog.pattern=%h %l %u %t "%r" %s %b %D %F %{X-FORWARDED-FOR}i
# Log file name prefix.
server.tomcat.accesslog.prefix=access_log
# Defer inclusion of the date stamp in the file name until rotate time.
server.tomcat.accesslog.rename-on-rotate=false
# Set request attributes for IP address, Hostname, protocol and port used for the request.
server.tomcat.accesslog.request-attributes-enabled=false
# Enable access log rotation.
server.tomcat.accesslog.rotate=true
# Log file name suffix.
server.tomcat.accesslog.suffix=.log
# Log file name max-days
server.tomcat.accesslog.max-days=15

# 数据库资源配置
datasource.validationQuery=SELECT 'x'
# 初始化连接池大小
datasource.initialSize=20
datasource.maxActive=100
datasource.maxIdle=10
datasource.minIdle=5
# 数据库最大等待时间，单位毫秒
datasource.maxWait=60000
# 数据库最大空闲时间，单位毫秒
datasource.timeBetweenEvictionRunsMillis=60000
datasource.minEvictableIdleTimeMillis=300000
datasource.maxPoolPreparedStatementPerConnectionSize=20
datasource.testWhileIdle=true
datasource.testOnBorrow=false
datasource.testOnReturn=false
datasource.poolPreparedStatements=true
datasource.queryTimeout=30
datasource.transactionQueryTimeout=30

management.endpoint.shutdown.enabled=false
management.endpoints.web.exposure.include=shutdown
swagger.is.enable=false
sgs.trace.kafka.servers=10.168.136.31:9092,10.168.136.28:9092

api.request.recorder.exclusion-methods[0].method-name=getSubTestDataInfo