/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestSampleDO {

    // add 20230529
    private String orderNo;
    private String realOrderNo;
    // SCI-1490
    private String reportNo;

    // add 20230529
    private Integer systemId;
    private String testSampleInstanceId;
    private String parentTestSampleId;
    private List<RdTestSampleGroupDO> testSampleGroupList;
    private String testSampleNo;
    private String testSampleName;
    // TODO 230922 未来需要移除
    private String externalSampleNo;
    private String externalSampleName;
    private Integer testSampleType;
    private String category;
    private Integer testSampleSeq;
    private RdMaterialAttrDO materialAttr;
    private RdTestSampleExternalDO external;
    private RdConclusionDO conclusion;
    private List<RdAttachmentDO> testSamplePhoto;
    private List<RdTestSampleLimitGroupDO> limitGroupList;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
