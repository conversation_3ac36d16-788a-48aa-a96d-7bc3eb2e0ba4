/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdOrderRelDO implements Serializable {

    // add 20230529
    private Integer systemId;
    private String orderNo;
    private String realOrderNo;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String orderInstanceId;

}
