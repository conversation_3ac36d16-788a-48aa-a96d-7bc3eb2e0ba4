package com.sgs.testdatabiz.facade.impl.exportReportData;

import com.alibaba.cola.extension.ExtensionPointI;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.facade.model.req.rd.ExportReportDataReq;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExportReportDataActionExtPt extends ExtensionPointI {

    ReportDataDTO exportReportData(@NotNull Long labId, @NotEmpty String reportNo,@NotEmpty String labCode);

    List<ReportDataDTO> exportReportDataByTrfNo(@NotNull Long labId, @NotEmpty String trfNo,@NotEmpty String labCode);

    List<ReportDataDTO> exportReportDataList(@NotNull Long labId, @NotEmpty List<String> reportNos, @NotEmpty String labCode);
}
