package com.sgs.testdatabiz.domain.service.testdata.impl.check;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.domain.service.validation.ValidatinServiceFactory;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.config.ValidationExcludeManager;
import com.sgs.testdatabiz.domain.service.validation.model.ReportTestDataValidationDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataBatchDTO;
import com.sgs.testdatabiz.integration.model.validation.ValidationResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ReportDataChecker {

    @Autowired
    private ValidatinServiceFactory validationServiceFactory;
    
    @Autowired
    private ValidationExcludeManager excludeManager;

    /**
     * 校验远程返回的校验结果
     * @param response 远程校验结果
     * @return 校验结果
     */
    public ValidationResultDTO validateResponse(ValidationResponse response) {
        if (response == null) {
            return ValidationResultDTO.fail("Validation response is null");
        }
        //或缺远程校验的服务列表
        List<ValidationService> validationServices = validationServiceFactory.getValidationServices(ValidationTypeEnum.REMOTE.getName());
        if (CollectionUtils.isEmpty(validationServices)) {
            return ValidationResultDTO.fail("No validation services configured");
        }
        // 构建校验请求
        ReportTestDataValidationDTO validationRequest = new ReportTestDataValidationDTO();
        validationRequest.setValidationResponse(response);
        // 执行校验
        for (ValidationService validationService : validationServices) {
            ValidationResultDTO result = validationService.validate(validationRequest);
            if (!result.isValidFlag()) {
                return ValidationResultDTO.fail(result.getResultMessage());
            }
        }
        return ValidationResultDTO.success("All validations passed");
    }

    /**
     * 校验报告数据
     * @param reportData 报告数据
     * @return 校验结果
     */
    public ValidationResultDTO check(ReportDataBatchDTO reportData) {
        if (reportData == null) {
            return ValidationResultDTO.fail("Report data is null");
        }

        // 获取系统ID
        Integer systemId = getSystemId(reportData);

        // 获取所有可用的校验服务
        List<ValidationService> validationServices = validationServiceFactory.getAllValidationServices();
        if (CollectionUtils.isEmpty(validationServices)) {
            log.warn("[ReportDataChecker] 未找到任何校验服务");
            return ValidationResultDTO.success("No validation services configured");
        }
        log.info("[ReportDataChecker] 获取到{}个校验服务", validationServices.size());

        // 构建校验请求
        ReportTestDataValidationDTO validationRequest = new ReportTestDataValidationDTO();
        validationRequest.setReportDataBatchDTO(reportData);
        validationRequest.setSystemId(reportData.getSystemId());
        validationRequest.setLabCode(reportData.getLabCode());
        List<String> errorMessages = new ArrayList<>();
        boolean isValid = true;

        // 执行校验
        for (ValidationService validationService : validationServices) {
            // 获取校验类型
            String validationType = validationService.getType();
            
            // 检查是否需要执行该类型的校验
            if (!excludeManager.shouldValidate(validationType, systemId)) {
                log.info("Skip validation for type {} on system {}", validationType, systemId);
                continue;
            }

            // 执行校验
            ValidationResultDTO result = validationService.validate(validationRequest);
            if (!result.isValidFlag()) {
                isValid = false;
                errorMessages.add(String.format("[%s] %s", validationType, result.getResultMessage()));
            }
        }

        // 返回校验结果
        if (isValid) {
            return ValidationResultDTO.success("All validations passed");
        } else {
            return ValidationResultDTO.fail(String.join("; ", errorMessages));
        }
    }

    /**
     * 获取系统ID
     * 优先从TRF列表获取，如果没有则从订单列表获取
     */
    private Integer getSystemId(ReportDataBatchDTO reportData) {
        // 优先从TRF列表获
        if (Func.isNotEmpty(reportData.getTrfList())) {
            return reportData.getTrfList().stream()
                .filter(trf -> Func.isNotEmpty(trf.getOrderList()))
                .flatMap(trf -> trf.getOrderList().stream())
                .filter(order -> order != null && order.getSystemId() != null)
                .map(order -> order.getSystemId())
                .findFirst()
                .orElse(null);
        }
        
        // 其次从订单列表获取
        if (Func.isNotEmpty(reportData.getOrderList())) {
            return reportData.getOrderList().stream()
                .filter(order -> order != null && order.getSystemId() != null)
                .map(order -> order.getSystemId())
                .findFirst()
                .orElse(null);
        }
        
        log.warn("No valid systemId found in report data");
        return null;
    }
}
