package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.testline;

import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestLineDO;
import com.sgs.testdatabiz.core.enums.TestLineStatus;
import com.sgs.testdatabiz.core.enums.FilterType;
import org.springframework.stereotype.Component;
import java.util.Objects;

@DefaultFilter(order = 5)
@Component
public class TestLineStatusFilter extends AbstractTestLineFilter {
    
    @Override
    protected String getFilterType() {
        return FilterType.TEST_LINE_STATUS.name();
    }

    @Override
    protected String getFilterName() {
        return "TestLineStatusFilter";
    }

    @Override
    protected String getFilterMessage() {
        return "Filtered test lines with invalid status";
    }

    @Override
    protected boolean filterTestLine(RdTestLineDO testLine) {
        if (testLine == null) {
            return false;
        }
        if(testLine.getTestLineStatus() == null){
            return true;
        }
        // 过滤掉已取消和NC状态的测试线
        return !Objects.equals(testLine.getTestLineStatus(), TestLineStatus.Cancelled.getStatus()) &&
               !Objects.equals(testLine.getTestLineStatus(), TestLineStatus.NC.getStatus());
    }
} 