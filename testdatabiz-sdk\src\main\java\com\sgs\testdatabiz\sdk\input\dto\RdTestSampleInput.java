/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestSampleInput implements Serializable {

    // add 20230529
    private String orderNo;
    // add 20230529
    private Integer systemId;
    private String testSampleInstanceId;
    private String parentTestSampleId;
    private List<RdTestSampleGroupInput> testSampleGroupList;
    private String testSampleNo;
    private String testSampleName;
    // TODO 230922本次暂不移除，后续需移除
    private String externalSampleNo;
    private String externalSampleName;
    private Integer testSampleType;
    private String category;
    private Integer testSampleSeq;
    private RdMaterialAttrInput materialAttr;
    private RdConclusionInput conclusion;

    private RdTestSampleExternalInput external;
    private List<RdAttachmentInput> testSamplePhoto;


    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
