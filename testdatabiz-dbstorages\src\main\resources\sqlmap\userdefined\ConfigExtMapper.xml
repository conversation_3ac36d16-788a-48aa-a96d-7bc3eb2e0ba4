<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.ConfigExtMapper" >


    <select id="checkTable" resultType="java.lang.Integer" >
        SELECT 1 FROM INFORMATION_SCHEMA.`TABLES` WHERE TABLE_SCHEMA = 'sgs_testdatadb' AND TABLE_NAME = #{tableName};
    </select>


    <update id="createTestDataConfigTable" >
        CREATE TABLE `${newTableName}` LIKE `${sourceName}`;
    </update>

    <select id="getTableInfoList" resultType="java.lang.String">
        SELECT `TABLE_NAME`
        FROM INFORMATION_SCHEMA.`TABLES`
        WHERE `TABLE_NAME` LIKE #{tableName};
    </select>

    <select id="getColumnInfoList" resultType="com.sgs.testdatabiz.facade.model.info.starlims.ColumnInfo">
        SELECT `COLUMN_NAME` AS columnName
             ,COLUMN_DEFAULT AS columnDef   -- 是否默认值
             ,IS_NULLABLE AS isNull 		-- 是否允许为空
             ,COLUMN_TYPE AS columnType		-- 字段类型
             ,COLUMN_COMMENT AS comment	    -- 字段备注
        FROM INFORMATION_SCHEMA.`COLUMNS`
        WHERE TABLE_SCHEMA = 'sgs_testdatadb'
          AND `TABLE_NAME` = #{tableName}
        ORDER BY ORDINAL_POSITION ASC;
    </select>

    <update id="alterTableColumn" parameterType="com.sgs.testdatabiz.facade.model.info.starlims.ColumnInfo">
        ALTER TABLE `${tableName}`
        <choose>
            <when test="addColumn">
                ADD COLUMN
            </when >
            <otherwise>
                MODIFY COLUMN
            </otherwise>
        </choose>
        ${columnName}
        ${columnType}
        <choose>
            <when test="isNull != null and isNull == 'NO'">
                NOT NULL
            </when >
            <otherwise>
                NULL
            </otherwise>
        </choose>
        <if test="columnDef != null and columnDef != ''">
            DEFAULT #{columnDef}
        </if>
        <if test="comment != null and comment != ''">
            COMMENT #{comment}
        </if>
        <if test="alterName != null and alterName != ''">
            AFTER ${alterName}
        </if>
    </update>

    <select id="getAllIndexNameFromTableName" resultType="java.lang.String">
        SELECT DISTINCT index_name FROM information_schema.statistics WHERE table_name = #{tableName} AND index_name != 'PRIMARY'
    </select>

    <select id="getAllIndexFromTableName" resultType="java.lang.String">
        SELECT COLUMN_NAME FROM information_schema.statistics WHERE table_name = #{tableName} AND index_name = #{idxName} AND index_name != 'PRIMARY'
    </select>

    <select id="findIndexFromTableName" resultType="java.lang.String">
        SELECT index_name FROM information_schema.statistics WHERE table_name = #{tableName} AND index_name = #{idxName}
    </select>

    <update id="commonCreatIndex">
        CREATE INDEX ${idxName} ON `${tableName}`
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            `${item}`
        </foreach>;
    </update>
    
    
    <select id="queryTestDataGroup" resultType="com.sgs.testdatabiz.dbstorages.mybatis.extmodel.OrderIdRelDTO">
        SELECT DISTINCT
            OrderNo,
            MAX(id) maxId
        FROM `tb_test_data_${suffix}`
        WHERE ActiveIndicator = 1 and id > #{id}
        <if test="createDate != null and createDate != ''">
            and CreatedDate &gt;= #{createDate}
            and CreatedDate &lt;= #{endDate}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and orderNo = #{orderNo}
        </if>
        GROUP BY orderNo
        ORDER BY Id
            limit 100
    </select>

    <select id="queryTestData" resultType="com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataOldDTO">
        SELECT
            OrderNo
        ,ObjectNo
        ,ExternalNo
        ,ExternalCode
        ,SampleNo
        ,ExternalSampleNo
        ,ReportLimit
        ,AnalyteCode
        ,AnalyteType
        ,TestAnalyteName
        ,TestAnalyteNameCN
        ,ReportUnit
        ,ReportUnitCN
        ,TestValue
        ,AnalyteSeq
        ,AnalyteSeq
        ,MaterialName
        ,MaterialTexture
        ,UsedPosition
        ,MaterialColor
        ,SystemId
        ,ActiveIndicator
        ,CreatedDate
        ,ModifiedDate
        FROM `tb_test_data_${suffix}`
        WHERE ActiveIndicator = 1
        and orderNo in
        <foreach collection="orderNos" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>;
    </select>


    <select id="queryTestLineMappingByLabCode"
            resultType="com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMappingDTO">
        SELECT
        TestLineId, SlimCode, LabCode, ProductLineCode, TestLineEvaluation,
        StandardId, StandardName, SystemId, ActiveIndicator
        FROM tb_test_line_mapping
        <where>
            ActiveIndicator = 1
            AND LabCode = #{labCode}
        </where>

    </select>
    <select id="queryTestDataConfig" resultType="com.sgs.testdatabiz.facade.model.rsp.TestDataConfigRsp">

        select
            CustomerGroupCode,
            TestLineId,
            CitationId,
            ConditionId,
            LanguageId,
            rule
        from
            tb_data_append_rule_config
        <where>
            <if test="req == null">
                CustomerGroupCode is null
                and TestLineId is null
                and CitationId is null
                and LanguageId is null
                and ConditionId is null
                and rule is not null
            </if>
            <if test="req !=null">
                <if test="req.customerGroupCode != null and req.customerGroupCode!=''">
                    CustomerGroupCode = #{req.customerGroupCode}
                </if>
                <if test="req.languageId != null and req.languageId!=''">
                    and LanguageId = #{req.languageId}
                </if>
                <if test="req.testLineIds != null and req.testLineIds.size()>0">
                    and TestLineId in
                    <foreach collection="req.testLineIds" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                <if test="req.citationIds != null and req.citationIds.size()>0">
                    and CitationId in
                    <foreach collection="req.citationIds" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                <if test="req.conditionIds != null and req.conditionIds.size()>0">
                    and ConditionId in
                    <foreach collection="req.conditionIds" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
            </if>
        </where>
    </select>


</mapper>