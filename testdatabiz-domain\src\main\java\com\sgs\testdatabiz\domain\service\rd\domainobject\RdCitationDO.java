/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCitationDO {

    private Integer citationId;
    private Integer citationType;
    private Integer citationVersionId;
    private Integer citationSectionId;
    private String citationSectionName;
    private String citationName;
    private String citationFullName;
    private List<RdCitationLanguageDO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}