/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCertificateDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProductDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdSampleDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportCertificateDTO;
import com.sgs.testdatabiz.facade.model.enums.ReportType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdReportInput implements Serializable{

    private Integer systemId;
    // add 20230529
    private String orderNo;
    private String rootOrderNo;
    private String reportId;
    private String reportNo;
    private Date reportDueDate;
    private String approveBy;
    private Date approveDate;
    private Date softCopyDeliveryDate;
    private String originalReportNo;
    private String rootReportNo;
    private String oldReportNo;
    private String reportVersion;
    private String rslstatus;
    private String failCode;
    private Integer reportStatus;
    private String certificateName;
    private String createBy;
    private Date createDate;
    private RdLabInput lab;
    private RdConclusionInput conclusion;
    private List<ReportCertificateDTO> reportCertificateList;
    private List<RdReportMatrixInput> reportMatrixList;
    private List<RdSubReportInput> subReportList;
    private List<RdAttachmentInput> reportFileList;
    private List<RdProductDTO> productList;
    private List<RdSampleDTO> sampleList;
    private String reportRemark;

    private Long actualTat;

    private RdRelationshipInput relationship;

    // add 20231103
    private String excludeCustomerInterface;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    //SCI-1378 添加证书信息
    private RdCertificateDTO certificate;
    //SCI-1378 json字符串
    private String signList;
    private ReportType reportType;
    //SubReport 的情况下追加，例如Starlims、内部分包等
    private String reportSource;
    private String testingType;
    private String countryOfDestination;
    private String metaData;
}
