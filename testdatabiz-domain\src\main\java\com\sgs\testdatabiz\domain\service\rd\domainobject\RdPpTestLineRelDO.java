/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPpTestLineRelDO {

    private String ppTlRelId;
    private Long ppArtifactRelId;
    private Long ppBaseId;
    private Long rootPPBaseId;
    private Integer ppNo;
    private Integer ppVersionId;
    private String ppName;
    private String ppNotes;
    private Integer sectionId;
    private Integer sectionLevel;
    private String sectionName;
    private Long aid;
    private RdCitationDO citation;
    //    private RdConclusionDO conclusion;
    private List<RdPpTestLineRelLangDO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;


    /**
     * @since SCI-1276 Target Inspectorio
     */
    private String testCategoryCode;
}
