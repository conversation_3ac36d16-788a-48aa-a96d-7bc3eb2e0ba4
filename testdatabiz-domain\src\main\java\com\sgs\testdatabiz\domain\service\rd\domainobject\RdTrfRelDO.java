/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTrfRelDO implements Serializable {

    // add 20230529
    private Integer refSystemId;
    private String trfNo;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;


}
