package com.sgs.testdatabiz.facade.model.req;

import java.util.Set;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.testdatabiz.facade.model.base.BaseModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ConclusionInfoReq",description = "获取 Conclusion")
public class ConclusionInfoReq extends BaseRequest {
    /**
     *
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     *
     */
    @ApiModelProperty("lab Code")
    private String labCode;

    private Long systemId;
    /**
     *
     */
    private Set<Integer> sourceTypes;

}
