package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdAttachmentPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 附件标识
     */
    private Long id;

    /**
     * lab_id BIGINT(19) 必填<br>
     * Trims系统实验室标识
     */
    private Long labId;

    /**
     * object_type INTEGER(10)<br>
     * RD 对象类型：TestSample等
1:TRF;
2:Order;
3:Report;
4:SubReport;
5:Quotation;
6:Invoice;
     */
    private Integer objectType;

    /**
     * object_no VARCHAR(50)<br>
     * 例如：Order No
     */
    private String objectNo;

    /**
     * object_id VARCHAR(36)<br>
     * 例如：Order ID
     */
    private String objectId;

    /**
     * order_no VARCHAR(36)<br>
     * 订单号
     */
    private String orderNo;

    /**
     * system_id BIGINT(19)<br>
     * 
     */
    private Long systemId;

    /**
     * report_no VARCHAR(36) 必填<br>
     * 报告号
     */
    private String reportNo;

    /**
     * language_id INTEGER(10)<br>
     * 语言编码
     */
    private Integer languageId;

    /**
     * biz_type VARCHAR(50)<br>
     * 业务对象类型
     */
    private String bizType;

    /**
     * file_name VARCHAR(300)<br>
     * 文件名称
     */
    private String fileName;

    /**
     * cloud_id VARCHAR(500)<br>
     * 附件地址
     */
    private String cloudId;

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 附件标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 附件标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * lab_id BIGINT(19) 必填<br>
     * 获得 Trims系统实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19) 必填<br>
     * 设置 Trims系统实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * object_type INTEGER(10)<br>
     * 获得 RD 对象类型：TestSample等
1:TRF;
2:Order;
3:Report;
4:SubReport;
5:Quotation;
6:Invoice;
     */
    public Integer getObjectType() {
        return objectType;
    }

    /**
     * object_type INTEGER(10)<br>
     * 设置 RD 对象类型：TestSample等
1:TRF;
2:Order;
3:Report;
4:SubReport;
5:Quotation;
6:Invoice;
     */
    public void setObjectType(Integer objectType) {
        this.objectType = objectType;
    }

    /**
     * object_no VARCHAR(50)<br>
     * 获得 例如：Order No
     */
    public String getObjectNo() {
        return objectNo;
    }

    /**
     * object_no VARCHAR(50)<br>
     * 设置 例如：Order No
     */
    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo == null ? null : objectNo.trim();
    }

    /**
     * object_id VARCHAR(36)<br>
     * 获得 例如：Order ID
     */
    public String getObjectId() {
        return objectId;
    }

    /**
     * object_id VARCHAR(36)<br>
     * 设置 例如：Order ID
     */
    public void setObjectId(String objectId) {
        this.objectId = objectId == null ? null : objectId.trim();
    }

    /**
     * order_no VARCHAR(36)<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(36)<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * system_id BIGINT(19)<br>
     * 获得 
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * system_id BIGINT(19)<br>
     * 设置 
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * report_no VARCHAR(36) 必填<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(36) 必填<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * language_id INTEGER(10)<br>
     * 获得 语言编码
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 设置 语言编码
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * biz_type VARCHAR(50)<br>
     * 获得 业务对象类型
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * biz_type VARCHAR(50)<br>
     * 设置 业务对象类型
     */
    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    /**
     * file_name VARCHAR(300)<br>
     * 获得 文件名称
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * file_name VARCHAR(300)<br>
     * 设置 文件名称
     */
    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    /**
     * cloud_id VARCHAR(500)<br>
     * 获得 附件地址
     */
    public String getCloudId() {
        return cloudId;
    }

    /**
     * cloud_id VARCHAR(500)<br>
     * 设置 附件地址
     */
    public void setCloudId(String cloudId) {
        this.cloudId = cloudId == null ? null : cloudId.trim();
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", labId=").append(labId);
        sb.append(", objectType=").append(objectType);
        sb.append(", objectNo=").append(objectNo);
        sb.append(", objectId=").append(objectId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", systemId=").append(systemId);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", languageId=").append(languageId);
        sb.append(", bizType=").append(bizType);
        sb.append(", fileName=").append(fileName);
        sb.append(", cloudId=").append(cloudId);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}