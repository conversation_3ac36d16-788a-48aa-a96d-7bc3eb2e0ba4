package com.sgs.testdatabiz.domain.service.rd.manager;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportProductDffMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffPO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.facade.model.enums.ProductDffObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ReportProductDffManager {

    public final RdReportProductDffMapper reportProductDffMapper;

    @Autowired
    private IdService ID_GENERATOR;

    public List<RdReportProductDffPO> getProductDffByReportNo(String reportNo) {
        RdReportProductDffExample reportProductDffExample = new RdReportProductDffExample();
        reportProductDffExample.createCriteria().andReportNoEqualTo(reportNo);
        return reportProductDffMapper.selectByExample(reportProductDffExample);
    }


    public ReportProductDffManager(RdReportProductDffMapper reportProductDffMapper) {
        this.reportProductDffMapper = reportProductDffMapper;
    }

    public void saveProductDff(List<RdProductDO> productList, Integer objectType, Long labId, String orderNo, Long rdReportId, String reportNo) {
        List<RdReportProductDffPO> list = new ArrayList<>();
        productList.forEach(
                l -> {
                    List<RdProductSampleAttrDO> productAttrList = l.getProductAttrList();
                    if (Func.isNotEmpty(productAttrList)) {
                        productAttrList.forEach(
                                productAttr -> {
                                    RdReportProductDffPO productDffPO = new RdReportProductDffPO();
                                    productDffPO.setId(ID_GENERATOR.nextId());
                                    productDffPO.setLabId(labId);
                                    productDffPO.setOrderNo(orderNo);
                                    productDffPO.setReportNo(reportNo);
                                    productDffPO.setObjectType(objectType);
                                    productDffPO.setProductInstanceId(l.getProductInstanceId());
                                    productDffPO.setRdReportId(rdReportId);

                                    productDffPO.setFormId(l.getTemplateId());
                                    // TODO 此处待处理
                                    List<RdAttrLanguageDO> languageList = productAttr.getLanguageList();
                                    if (Func.isNotEmpty(languageList)) {
//                                    productDffPO.setLanguageId();

                                    }
                                    productDffPO.setLabelCode(productAttr.getLabelCode());
                                    productDffPO.setLabelName(productAttr.getLabelName());
                                    // 此处和Mike沟通fieldCode先和labelCode保持一致
                                    productDffPO.setFieldCode(productAttr.getLabelCode());
                                    productDffPO.setCustomerLabel(productAttr.getCustomerLabel());
                                    productDffPO.setDataType(productAttr.getDataType());
                                    productDffPO.setValue(productAttr.getValue());
                                    productDffPO.setSeq(productAttr.getSeq());
                                    productDffPO.setDisplayInReport(productAttr.getDisplayInReport());
                                    list.add(productDffPO);
                                }
                        );
                    }
                }
        );

        if (Func.isNotEmpty(list)) {
            reportProductDffMapper.batchInsert(list);
        }
    }

    public void saveProductDff(ReportDataBatchDO reportDataDO, Map<String, RdOrderDO> orderMap) {
        List<RdReportProductDffPO> list = new ArrayList<>();

        List<RdReportDO> reportList = reportDataDO.getReportList();
        emptyData(reportList.stream().map(RdReportDO::getId).filter(Func::isNotEmpty).distinct().collect(Collectors.toList()));
        for (RdReportDO report : reportList) {
            String reportNo = report.getReportNo();
            Long reportId = report.getId();
            String orderNo = report.getOrderNo();
            Integer systemId = report.getSystemId();
            RdOrderDO orderDO = orderMap.get(systemId + orderNo);
            if (Func.isEmpty(orderDO)) {
                break;
            }
            List<RdProductDO> productList = orderDO.getProductList();
            if (Func.isNotEmpty(productList)) {
                productList.forEach(
                        product -> {
                            List<RdProductSampleAttrDO> productAttrList = product.getProductAttrList();
                            List<RdReportProductDffPO> productDffPOS = buildProductSampleAttr(
                                    productAttrList,
                                    reportDataDO.getLabId(),
                                    reportId,
                                    orderNo,
                                    reportNo,
                                    product.getProductInstanceId(),
                                    product.getTemplateId()
                            );
                            if (Func.isNotEmpty(productDffPOS)) {
                                list.addAll(productDffPOS);
                            }
                        }
                );
            }

            List<RdSampleDO> sampleList = orderDO.getSampleList();
            if (Func.isNotEmpty(sampleList)) {
                sampleList.forEach(
                        sample -> {
                            List<RdProductSampleAttrDO> sampleAttrList = sample.getSampleAttrList();
                            List<RdReportProductDffPO> productDffPOS = buildProductSampleAttr(
                                    sampleAttrList,
                                    reportDataDO.getLabId(),
                                    reportId,
                                    orderNo,
                                    reportNo,
                                    sample.getTestSampleInstanceId(),
                                    sample.getTemplateId()
                            );
                            if (Func.isNotEmpty(productDffPOS)) {
                                list.addAll(productDffPOS);
                            }
                        }
                );
            }
        }
        if (Func.isNotEmpty(list)) {
            reportProductDffMapper.batchInsert(list);
        }
    }

    public void emptyData(List<Long> ids) {
        if (Func.isEmpty(ids)) {
            return;
        }
        RdReportProductDffExample example = new RdReportProductDffExample();
        example.createCriteria().andRdReportIdIn(ids);
        reportProductDffMapper.deleteByExample(example);
    }

    public List<RdReportProductDffPO> buildProductSampleAttr(
            List<RdProductSampleAttrDO> productAttrList,
            Long labId,
            Long reportId,
            String orderNo,
            String reportNo,
            String instanceId,
            String templateId
    ) {
        if (Func.isNotEmpty(productAttrList)) {
            List<RdReportProductDffPO> list = new ArrayList<>();
            productAttrList.forEach(
                    productAttr -> {
                        RdReportProductDffPO productDffPO = new RdReportProductDffPO();
                        productDffPO.setId(ID_GENERATOR.nextId());
                        productDffPO.setLabId(labId);
                        productDffPO.setRdReportId(reportId);
                        productDffPO.setOrderNo(orderNo);
                        productDffPO.setReportNo(reportNo);
                        productDffPO.setObjectType(ProductDffObjectTypeEnum.PRODUCT.getCode());
                        productDffPO.setProductInstanceId(instanceId);
                        productDffPO.setFormId(templateId);
                        productDffPO.setLabelCode(productAttr.getLabelCode());
                        productDffPO.setFieldCode(productAttr.getLabelCode());
                        productDffPO.setDataType(productAttr.getDataType());
                        productDffPO.setSeq(productAttr.getSeq());
                        productDffPO.setDisplayInReport(productAttr.getDisplayInReport());
                        productDffPO.setLabelName(productAttr.getLabelName());
                        productDffPO.setCustomerLabel(productAttr.getCustomerLabel());
                        productDffPO.setValue(productAttr.getValue());
                        List<RdAttrLanguageDO> languageList = productAttr.getLanguageList();
                        if (Func.isNotEmpty(languageList)) {
                            languageList.forEach(
                                    lang -> {
                                        productDffPO.setLanguageId(lang.getLanguageId());
                                        productDffPO.setLabelName(lang.getLabelName());
                                        productDffPO.setCustomerLabel(lang.getCustomerLabel());
                                        productDffPO.setValue(lang.getValue());
                                    }
                            );
                        }
                        list.add(productDffPO);
                    }
            );
            return list;
        }
        return null;
    }

}
