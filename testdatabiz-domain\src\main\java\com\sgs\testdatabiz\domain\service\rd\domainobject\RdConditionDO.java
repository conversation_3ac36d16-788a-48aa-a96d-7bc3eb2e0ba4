/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdConditionDO {
    private String conditionInstanceId;
    private Integer conditionId;
    private Integer conditionType;
    private Integer conditionTypeId;
    private String conditionTypeName;
    private String conditionName;
    private String conditionDesc;
    private Integer conditionSeq;
    private List<RdConditionLanguageDO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}