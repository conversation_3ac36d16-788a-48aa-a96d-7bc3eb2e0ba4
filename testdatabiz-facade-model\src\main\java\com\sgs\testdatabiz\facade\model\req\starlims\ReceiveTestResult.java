package com.sgs.testdatabiz.facade.model.req.starlims;

import com.sgs.framework.core.base.BaseRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2022/10/18 0018 15:33
 */

public class ReceiveTestResult extends BaseRequest {
    /**
     * analyteId
     */
    private Integer id;

    /**
     * analyteConclusion
     */
    private String conclusion;

    private String conclusionId;

    /**
     * analyteCode
     */
    private String analyte;

    /**
     * analyteName
     */
    private String analyteAlias;

    /**
     * analyteSeq
     */
    private Integer sorter;

    /**
     * casNo
     */
    private String casNo;

    /**
     * limit
     */
    private String limit;
    /**
     *  reportLimit
     */
    private String reportLimit;

    /**
     * reportUnit
     */
    private String unit;

    /**
     * testValue
     */
    private String result;

    /**
     * testValue
     */
    private List<ReceiveTestLanguages> languages;


    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getConclusionId() {
        return conclusionId;
    }

    public void setConclusionId(String conclusionId) {
        this.conclusionId = conclusionId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public String getAnalyte() {
        return analyte;
    }

    public void setAnalyte(String analyte) {
        this.analyte = analyte;
    }

    public String getAnalyteAlias() {
        return analyteAlias;
    }

    public void setAnalyteAlias(String analyteAlias) {
        this.analyteAlias = analyteAlias;
    }

    public Integer getSorter() {
        return sorter;
    }

    public void setSorter(Integer sorter) {
        this.sorter = sorter;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getLimit() {
        return limit;
    }

    public void setLimit(String limit) {
        this.limit = limit;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public List<ReceiveTestLanguages> getLanguages() {
        return languages;
    }

    public void setLanguages(List<ReceiveTestLanguages> languages) {
        this.languages = languages;
    }
}
