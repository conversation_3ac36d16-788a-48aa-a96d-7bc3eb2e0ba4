/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCustomerContactDO {

    private String customerContactId;
    private String customerContactAddressId;
    private Long bossContactId;
    private Long bossSiteUseId;
    private String contactName;
    private String contactTelephone;
    private String contactMobile;
    private String contactFAX;
    private String contactEmail;
    private String sgsUserId;
    private String sgsAccountCode;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
