package com.sgs.testdatabiz.facade.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.SourceDataStrategyFactory;
import com.sgs.testdatabiz.facade.FastFacade;
import com.sgs.testdatabiz.facade.model.req.fast.TestDataInfoReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component("fastFacade")
public class FastFacadeImpl implements FastFacade {

    @Autowired
    private SourceDataStrategyFactory sourceDataStrategyFactory;

    @Override
    public BaseResponse fastSaveTestData(TestDataInfoReq reqObject) {
        return BaseResponse.newInstance(sourceDataStrategyFactory.doInvoke(reqObject, SourceTypeEnum.FAST));
    }
}
