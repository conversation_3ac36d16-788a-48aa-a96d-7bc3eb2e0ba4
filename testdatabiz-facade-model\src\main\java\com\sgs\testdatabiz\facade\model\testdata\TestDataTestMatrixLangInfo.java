package com.sgs.testdatabiz.facade.model.testdata;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

public class TestDataTestMatrixLangInfo extends PrintFriendliness {
    /**
     * 多语言（1：英文、2：中文）
     */
    @ApiModelProperty("多语言（1：英文、2：中文）")
    private Integer languageId;

    /**
     *
     */
    @ApiModelProperty("evaluationAlias 取值规则： \n \n " +
            "   StarLims： testReportName \n "+
            "   SLIM： \n "+
            "   FAST：  \n "+
            "")
    private String evaluationAlias;

    /**
     *
     */
    @ApiModelProperty("测试方法描述 取值规则： \n \n " +
            "   StarLims：取testMethod  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String methodDesc;

    /**
     *
     */
    @ApiModelProperty("测试结论别名 取值规则： \n \n " +
            "   StarLims：取conclusionAlias  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String conclusionDisplay;

    /**
     *
     */
    private String materialName;

    /**
     *
     */
    private String materialTexture;

    /**
     *
     */
    private String usedPosition;

    /**
     *
     */
    private String materialColor;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getMethodDesc() {
        return methodDesc;
    }

    public void setMethodDesc(String methodDesc) {
        this.methodDesc = methodDesc;
    }

    public String getConclusionDisplay() {
        return conclusionDisplay;
    }

    public void setConclusionDisplay(String conclusionDisplay) {
        this.conclusionDisplay = conclusionDisplay;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }
}
