package com.sgs.testdatabiz.core.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ReflectionUtils;

import java.util.*;

/**
 * 继承自Spring util的工具类，减少jar依赖
 *
 * <AUTHOR>
 */
public class StringUtil extends StringUtils {

	public static final int INDEX_NOT_FOUND = -1;


	/**
	 *
	 * @param strVal
	 * @return
	 */
	public static String isNullOrEmpty(String strVal){
		if (StringUtils.isBlank(strVal)){
			return null;
		}
		return strVal;
	}


	/**
	 *
	 * @param strValue
	 * @return
	 */
	public static int hashCode(final String strValue) {
		if (strValue == null || strValue.length() <= 0){
			return 0;
		}
		return strValue.hashCode();
	}

	/**
	 *
	 * @param intValue
	 * @return
	 */
	public static int hashCode(final Integer intValue) {
		if (intValue == null){
			return 0;
		}
		return intValue.intValue();
	}

	/**
	 *
	 * @param dateValue
	 * @return
	 */
	public static int hashCode(final Date dateValue) {
		if (dateValue == null){
			return 0;
		}
		return dateValue.hashCode();
	}

	/**
	 *
	 * @param strVals
	 * @return
	 */
	public static String getFirstVal(String... strVals){
		if (strVals == null || strVals.length <= 0){
			return null;
		}
		for (String strVal: strVals) {
			if (StringUtils.isNoneBlank(strVal)){
				return strVal;
			}
		}
		return null;
	}

	/**
	 *
	 * @param value
	 * @param maxLen
	 * @return
	 */
	public static boolean checkLen(String value, int maxLen){
		return charLen(value) > maxLen;
	}

	/**
	 *
	 * @param value
	 * @param minLen
	 * @param maxLen
	 * @return
	 */
	public static boolean checkLen(String value, int minLen, int maxLen){
		int currLen = length(value);
		return currLen >= minLen && currLen <= maxLen;
	}

	/**
	 *
	 * @param value
	 * @return
	 */
	public static int charLen(String value) {
		if (value == null || value.length() <= 0){
			return 0;
		}
		char[] charArr = value.toCharArray();
		int len = 0;
		for (int index = 0; index < charArr.length; index++) {
			len++;
			if (!isLetter(charArr[index])) {
				len++;
			}
		}
		return len;
	}

	public static boolean isLetter(char c) {
		int k = 0x80;
		return c / k == 0 ? true : false;
	}

	public static Map<String, Object> parseObj2Map(Object object) {
		if (object == null) {
			return null;
		}
		return Arrays.stream(BeanUtils.getPropertyDescriptors(object.getClass()))
				.filter(pd -> !"class".equals(pd.getName()))
				.collect(HashMap::new,
						(map, pd) -> map.put(pd.getName(), ReflectionUtils.invokeMethod(pd.getReadMethod(), object)),
						HashMap::putAll);
	}

	/**
	 * 处理LabCode
	 * @param labCode
	 * @return
	 */
	public static String getTestDataSuffix(String labCode) {
		if (StringUtils.isBlank(labCode)) {
			return StringUtils.EMPTY;
		}
		if (!labCode.contains(" "))  {
			return StringUtils.EMPTY;
		}
		String[] s = labCode.split(" ");
		if (s.length == 2) {
			return String.format("%s_%s", s[1].toLowerCase(), s[0].toLowerCase());
		}

		return "";
	}
	public static void main(String[] args) {
		String labCode = "GZ SL";
		System.out.println(getTestDataSuffix(labCode));
	}

	public static String getLocationCode(String labCode){
			if (StringUtils.isBlank(labCode)) {
				return StringUtils.EMPTY;
			}
			if (!labCode.contains(" "))  {
				return StringUtils.EMPTY;
			}
			String[] s = labCode.split(" ");
			if (s.length == 2) {
				return s[0];
			}

			return StringUtils.EMPTY;
	}


}

