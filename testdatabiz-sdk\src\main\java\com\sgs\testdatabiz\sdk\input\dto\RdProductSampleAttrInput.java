/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdProductSampleAttrInput implements Serializable {

    private Integer seq;
    private String labelName;
    private String customerLabel;
    private String dataType;
    private String labelCode;
    private String value;
    private String displayInReport;
    private List<RdAttrLanguageInput> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
