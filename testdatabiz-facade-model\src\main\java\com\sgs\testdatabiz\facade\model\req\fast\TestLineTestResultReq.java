package com.sgs.testdatabiz.facade.model.req.fast;

import com.sgs.framework.core.common.PrintFriendliness;
import lombok.Data;

import java.util.List;

/**
 * https://cnwiki.sgs.net/pages/viewpage.action?pageId=31162408
 * <AUTHOR>
 * @date 2022/8/30 13:39
 */
@Data
public class TestLineTestResultReq extends PrintFriendliness {
    private String analyte;
    private String analyteAlias;
    private Integer sorter;
    private String unit;
    private String result;

//    private String casNo;
    private List<TestResultLangReq> languages;

}
