package com.sgs.testdatabiz.domain.service.rd.filter.config;

import com.sgs.testdatabiz.core.enums.FilterType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class FilterConfigManager {
    
    private static final String GLOBAL_SYSTEM_ID = "0";
    
    @Autowired
    private FilterNacosConfig nacosConfig;
    
    @Autowired
    private Environment environment;
    
    @PostConstruct
    public void init() {
        // 打印Nacos配置
        log.info("[FilterConfigManager] Nacos配置: enabled={}, systems={}", 
            nacosConfig.isEnabled(), 
            nacosConfig.getSystems());
            
        // 打印本地配置
        log.info("[FilterConfigManager] 本地配置: enabled={}, systems={}", 
            environment.getProperty("report.filter.enabled", Boolean.class),
            environment.getProperty("report.filter.systems", Map.class));
    }
    
    public boolean isFilterEnabled(String filterType, Integer systemId) {
        // // 1. 优先使用Nacos配置
        // if (hasNacosConfig()) {
        //     return isEnabledByNacos(filterType, systemId);
        // }
        
        // // 2. 回退到本地配置
        // return isEnabledByLocal(filterType, systemId);
        return true;
    }
    
    private boolean hasNacosConfig() {
        return nacosConfig != null && nacosConfig.getSystems() != null;
    }
    
    private boolean isEnabledByNacos(String filterType, Integer systemId) {
        // 1. 检查总开关
        if (!nacosConfig.isEnabled()) {
            log.debug("全局滤开关关闭");
            return false;
        }
        
        // 2. 检查全局系统配置(systemId=0)
        FilterNacosConfig.SystemConfig globalConfig = 
            nacosConfig.getSystems().get(GLOBAL_SYSTEM_ID);
        if (isSystemConfigEnabled(globalConfig, filterType)) {
            log.debug("全局系统配置允许过滤器{}", filterType);
            return true;
        }
        
        // 3. 检查特定系统配置
        FilterNacosConfig.SystemConfig sysConfig = 
            nacosConfig.getSystems().get(String.valueOf(systemId));
        if (sysConfig == null || !sysConfig.isEnabled()) {
            log.debug("系统{}未启用过滤", systemId);
            return false;
        }
        
        // 4. 检查过滤器类型
        Collection<FilterType> allowedTypes = sysConfig.getFilterTypes();
        boolean enabled = allowedTypes == null || allowedTypes.isEmpty() || allowedTypes.stream().anyMatch(type -> type.name().equals(filterType));
        log.debug("系统{}的过滤器{}是否启用: {}", systemId, filterType, enabled);
        return enabled;
    }
    
    private boolean isEnabledByLocal(String filterType, Integer systemId) {
        // 1. 检查总开关
        Boolean enabled = environment.getProperty("report.filter.enabled", Boolean.class);
        if (enabled == null || !enabled) {
            log.debug("本地全局过滤开关关闭");
            return false;
        }
        
        // 2. 检查全局系统配置(systemId=0)
        if (isLocalSystemConfigEnabled(GLOBAL_SYSTEM_ID, filterType)) {
            log.debug("全局系统配置允许过滤器{}", filterType);
            return true;
        }
        
        // 3. 检查特定系统配置
        return isLocalSystemConfigEnabled(String.valueOf(systemId), filterType);
    }
    
    private boolean isSystemConfigEnabled(FilterNacosConfig.SystemConfig config, String filterType) {
        if (config == null || !config.isEnabled()) {
            return false;
        }
        Collection<FilterType> allowedTypes = config.getFilterTypes();
        return allowedTypes == null || allowedTypes.isEmpty() || 
               allowedTypes.stream()
                          .map(FilterType::name)
                          .anyMatch(name -> name.equals(filterType));
    }
    
    private boolean isLocalSystemConfigEnabled(String systemId, String filterType) {
        String systemPath = String.format("report.filter.systems.%s", systemId);
        Boolean sysEnabled = environment.getProperty(systemPath + ".enabled", Boolean.class);
        if (sysEnabled == null || !sysEnabled) {
            log.debug("本地系统{}未启用过滤", systemId);
            return false;
        }
        
        List<String> allowedTypes = environment.getProperty(
            systemPath + ".filterTypes", List.class);
        boolean typeEnabled = allowedTypes == null || allowedTypes.isEmpty() || 
                            allowedTypes.contains(filterType);
        log.debug("本地系统{}的过滤器{}是否启用: {}", systemId, filterType, typeEnabled);
        return typeEnabled;
    }
} 