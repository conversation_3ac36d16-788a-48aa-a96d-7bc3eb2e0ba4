package com.sgs.testdatabiz.facade.model.req.backsubcontract;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.List;

public class TestDataMatrixReq extends PrintFriendliness {

    private String testMatrixId;
    private String externalCode;
    private Integer ppVersionId;
    private Long ppBaseId;
    private Long aid;
    private Integer testLineId;
    private Integer citationId;
    private Integer citationVersionId;
    private String citationName;
    private String sampleId;
    private String sampleNo;
    private String externalSampleNo;
    private String sampleSeq;
    private String extFields;
    private String condition;
    private String evaluationAlias;
    private String conclusionId;
    private String conclusionDisplay;

    private List<TestDataResultReq> resultDTOS;

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    public Long getPpBaseId() {
        return ppBaseId;
    }

    public void setPpBaseId(Long ppBaseId) {
        this.ppBaseId = ppBaseId;
    }

    public Long getAid() {
        return aid;
    }

    public void setAid(Long aid) {
        this.aid = aid;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public Integer getCitationVersionId() {
        return citationVersionId;
    }

    public void setCitationVersionId(Integer citationVersionId) {
        this.citationVersionId = citationVersionId;
    }

    public String getCitationName() {
        return citationName;
    }

    public void setCitationName(String citationName) {
        this.citationName = citationName;
    }

    public String getSampleId() {
        return sampleId;
    }

    public void setSampleId(String sampleId) {
        this.sampleId = sampleId;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public String getSampleSeq() {
        return sampleSeq;
    }

    public void setSampleSeq(String sampleSeq) {
        this.sampleSeq = sampleSeq;
    }

    public String getExtFields() {
        return extFields;
    }

    public void setExtFields(String extFields) {
        this.extFields = extFields;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getConclusionId() {
        return conclusionId;
    }

    public void setConclusionId(String conclusionId) {
        this.conclusionId = conclusionId;
    }

    public String getConclusionDisplay() {
        return conclusionDisplay;
    }

    public void setConclusionDisplay(String conclusionDisplay) {
        this.conclusionDisplay = conclusionDisplay;
    }

    public List<TestDataResultReq> getResultDTOS() {
        return resultDTOS;
    }

    public void setResultDTOS(List<TestDataResultReq> resultDTOS) {
        this.resultDTOS = resultDTOS;
    }
}
