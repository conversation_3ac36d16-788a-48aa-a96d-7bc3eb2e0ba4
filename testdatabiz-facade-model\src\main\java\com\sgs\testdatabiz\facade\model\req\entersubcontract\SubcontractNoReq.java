package com.sgs.testdatabiz.facade.model.req.entersubcontract;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "SubcontractNoReq", description = "SubcontractNoReq")
public class SubcontractNoReq extends BaseRequest {

    @ApiModelProperty("分包号")
    private String subcontractNo;

    @ApiModelProperty("扩展号码")
    private String externalObjectNo;

    private String labCode;

    public String getExternalObjectNo() {
        return externalObjectNo;
    }

    public void setExternalObjectNo(String externalObjectNo) {
        this.externalObjectNo = externalObjectNo;
    }

    public String getSubcontractNo() {
        return subcontractNo;
    }

    public void setSubcontractNo(String subcontractNo) {
        this.subcontractNo = subcontractNo;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }
}
