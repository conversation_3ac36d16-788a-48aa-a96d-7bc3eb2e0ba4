package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TestDataMatrixInfoMapper {
    int countByExample(TestDataMatrixInfoExample example);

    int deleteByExample(TestDataMatrixInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TestDataMatrixInfoPO record);

    int insertSelective(TestDataMatrixInfoPO record);

    List<TestDataMatrixInfoPO> selectByExampleWithBLOBs(TestDataMatrixInfoExample example);

    List<TestDataMatrixInfoPO> selectByExample(TestDataMatrixInfoExample example);

    TestDataMatrixInfoPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TestDataMatrixInfoPO record, @Param("example") TestDataMatrixInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") TestDataMatrixInfoPO record, @Param("example") TestDataMatrixInfoExample example);

    int updateByExample(@Param("record") TestDataMatrixInfoPO record, @Param("example") TestDataMatrixInfoExample example);

    int updateByPrimaryKeySelective(TestDataMatrixInfoPO record);

    int updateByPrimaryKeyWithBLOBs(TestDataMatrixInfoPO record);

    int updateByPrimaryKey(TestDataMatrixInfoPO record);

    int batchInsert(List<TestDataMatrixInfoPO> list);

    int batchUpdate(List<TestDataMatrixInfoPO> list);
}