package com.sgs.testdatabiz.facade.v2;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

/**
 * 报告检测数据facade类
 * @author: shawn.yang
 * @create: 2023-03-14 10:33
 */
public interface ReportTestDataFacade {

    /**
     * slim  build testData ---> ReportTestDataInfo
     * @param rawDataJson rawData-json
     * @return
     */
    BaseResponse<ReportTestDataInfo> build4Slim(String rawDataJson);

    /**
     * starLims  build testData ---> ReportTestDataInfo
     * @param rawDataJson rawData-json
     * @return
     */
    BaseResponse<ReportTestDataInfo> build4StarLims(String rawDataJson);


}
