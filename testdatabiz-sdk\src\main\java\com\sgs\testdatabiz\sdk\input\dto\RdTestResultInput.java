/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestResultInput implements Serializable {

    // add 20230529
    private String orderNo;
    // add 20230529
    private Integer systemId;
    private String testMatrixId;
    private String subReportNo;
    private RdTestResultResultInput testResult;
    private Integer testResultSeq;
    private RdReportLimitInput reportLimit;
    private RdShareDataReferInput shareDataRefer;
    // add 230922
    private RdMethodLimitInput methodLimit;
    private List<RdTestResultLanguageInput> languageList;
    private String testResultInstanceId;
    private Date lastModifiedTimestamp;
    private String metaData;
    private String casNo;
    private Integer activeIndicator;
}
