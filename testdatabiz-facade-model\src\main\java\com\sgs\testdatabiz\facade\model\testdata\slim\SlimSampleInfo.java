package com.sgs.testdatabiz.facade.model.testdata.slim;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 *
 */
@XmlRootElement(name = "SAMPLE")
@XmlAccessorType(XmlAccessType.FIELD)
public class SlimSampleInfo {
    /**
     *
     */
    @XmlAttribute(name = "DESCRIPTION_1")
    private String sampleNo;

    /**
     *
     */
    @XmlAttribute(name = "SAMPLEIDENT")
    private String externalSampleNo;

    /**
     *
     */
    @XmlAttribute(name = "REPORTACTIVE")
    private Integer reportAction;

    /**
     *
     */
    @XmlAttribute(name = "EXTERNALIDENT")
    private String externalident;

    /**
     *
     */
    @XmlElement(name = "SCHEME")
    private List<SlimSchemeInfo> slimCodes;

    @XmlAttribute(name = "SAM_REMARKS")
    private String materialName;

    @XmlAttribute(name = "DESCRIPTION_5")
    private String materialTexture;

    @XmlAttribute(name = "DESCRIPTION_2")
    private String usedPosition;

    @XmlAttribute(name = "DESCRIPTION_6")
    private String materialColor;

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public Integer getReportAction() {
        return reportAction;
    }

    public void setReportAction(Integer reportAction) {
        this.reportAction = reportAction;
    }

    public List<SlimSchemeInfo> getSlimCodes() {
        return slimCodes;
    }

    public void setSlimCodes(List<SlimSchemeInfo> slimCodes) {
        this.slimCodes = slimCodes;
    }

    public String getExternalident() {
        return externalident;
    }

    public void setExternalident(String externalident) {
        this.externalident = externalident;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }
}
