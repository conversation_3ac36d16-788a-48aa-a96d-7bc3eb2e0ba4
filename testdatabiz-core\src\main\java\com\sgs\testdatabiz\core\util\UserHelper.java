package com.sgs.testdatabiz.core.util;

import com.sgs.core.domain.UserInfo;
import org.springframework.util.Assert;

public final class UserHelper {
    private static final ThreadLocal<UserInfo> contextHolder = new ThreadLocal<>();

    public static void setLocalUser(UserInfo user){
        Assert.notNull(user, "UserInfo对象不能为空.");
        contextHolder.set(user);
    }

    public static UserInfo getLocalUser(){
        return contextHolder.get();
    }

    public static void clear() {
        contextHolder.remove();
    }
}