package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdReportSampleGroupPO {
    /**
     * Id BIGINT(19) 必填<br>
     * 
     */
    private Long id;

    /**
     * SampleGroupInstanceId VARCHAR(50)<br>
     * 
     */
    private String sampleGroupInstanceId;

    /**
     * SampleId VARCHAR(50)<br>
     * 
     */
    private String sampleId;

    /**
     * SampleGroupId VARCHAR(50)<br>
     * 
     */
    private String sampleGroupId;

    /**
     * ActiveIndicator TINYINT(3)<br>
     * 
     */
    private Byte activeIndicator;

    /**
     * ReportNo VARCHAR(50)<br>
     * 
     */
    private String reportNo;

    /**
     * OrderNo VARCHAR(50)<br>
     * 
     */
    private String orderNo;

    /**
     * LastModifiedTimestamp TIMESTAMP(19)<br>
     * 
     */
    private Date lastModifiedTimestamp;

    /**
     * CeatedTime TIMESTAMP(19)<br>
     * 
     */
    private Date ceatedTime;

    /**
     * Id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getId() {
        return id;
    }

    /**
     * Id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * SampleGroupInstanceId VARCHAR(50)<br>
     * 获得 
     */
    public String getSampleGroupInstanceId() {
        return sampleGroupInstanceId;
    }

    /**
     * SampleGroupInstanceId VARCHAR(50)<br>
     * 设置 
     */
    public void setSampleGroupInstanceId(String sampleGroupInstanceId) {
        this.sampleGroupInstanceId = sampleGroupInstanceId == null ? null : sampleGroupInstanceId.trim();
    }

    /**
     * SampleId VARCHAR(50)<br>
     * 获得 
     */
    public String getSampleId() {
        return sampleId;
    }

    /**
     * SampleId VARCHAR(50)<br>
     * 设置 
     */
    public void setSampleId(String sampleId) {
        this.sampleId = sampleId == null ? null : sampleId.trim();
    }

    /**
     * SampleGroupId VARCHAR(50)<br>
     * 获得 
     */
    public String getSampleGroupId() {
        return sampleGroupId;
    }

    /**
     * SampleGroupId VARCHAR(50)<br>
     * 设置 
     */
    public void setSampleGroupId(String sampleGroupId) {
        this.sampleGroupId = sampleGroupId == null ? null : sampleGroupId.trim();
    }

    /**
     * ActiveIndicator TINYINT(3)<br>
     * 获得 
     */
    public Byte getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * ActiveIndicator TINYINT(3)<br>
     * 设置 
     */
    public void setActiveIndicator(Byte activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * ReportNo VARCHAR(50)<br>
     * 获得 
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * ReportNo VARCHAR(50)<br>
     * 设置 
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * OrderNo VARCHAR(50)<br>
     * 获得 
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * OrderNo VARCHAR(50)<br>
     * 设置 
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(19)<br>
     * 设置 
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    /**
     * CeatedTime TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getCeatedTime() {
        return ceatedTime;
    }

    /**
     * CeatedTime TIMESTAMP(19)<br>
     * 设置 
     */
    public void setCeatedTime(Date ceatedTime) {
        this.ceatedTime = ceatedTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sampleGroupInstanceId=").append(sampleGroupInstanceId);
        sb.append(", sampleId=").append(sampleId);
        sb.append(", sampleGroupId=").append(sampleGroupId);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append(", ceatedTime=").append(ceatedTime);
        sb.append("]");
        return sb.toString();
    }
}