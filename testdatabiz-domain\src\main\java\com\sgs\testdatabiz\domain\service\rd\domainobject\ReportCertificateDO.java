package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/15 11:06
 */
@Data
public class ReportCertificateDO {
    private String certificateId;
    private String certificateNo;
    private String certificateType;
    private String certificateTypeDisplay;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date expireDate;
    private Integer toCustomerFlag;
    private Integer deliverToCustomerFlag;
    private String remark;
    private Integer status;
    private String statusDisplay;
    private Date issueDate;
    private List<String> testReportNumbersInCertificate;

    private List<TestReportInfo> testReportInfoInCertificate;

    @Data
    public static class TestReportInfo {
        private String reportNo;
        private Date publishedDate;
        private RdEfilingDO efiling;
    }
}
