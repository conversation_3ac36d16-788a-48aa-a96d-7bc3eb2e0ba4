package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.attachment;

import com.sgs.testdatabiz.domain.service.rd.filter.AbstractReportDataFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.FilterContext;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdAttachmentDO;
import java.util.List;

public abstract class AbstractAttachmentFilter extends AbstractReportDataFilter {

    @Override
    protected ReportDataDO doFilterInternal(ReportDataDO reportData, FilterContext context) {
        if (reportData == null) {
            return reportData;
        }

        // 获取需要过滤的附件列表
        List<RdAttachmentDO> attachments = getAttachmentList(reportData);
        if (attachments == null) {
            return reportData;
        }

        int beforeCount = attachments.size();
        
        // 执行过滤
        attachments.removeIf(attachment -> !filterAttachment(attachment));
        
        int afterCount = attachments.size();

        // 记录过滤结果
        if (beforeCount != afterCount) {
            context.addFilterRecord(
                getFilterName(),
                getAttachmentField(),
                beforeCount,
                afterCount,
                getFilterMessage()
            );
        }

        return reportData;
    }

    /**
     * 获取需要过滤的附件列表
     */
    protected abstract List<RdAttachmentDO> getAttachmentList(ReportDataDO reportData);

    /**
     * 获取附件字段名
     */
    protected abstract String getAttachmentField();

    /**
     * 获取过滤器名称
     */
    protected abstract String getFilterName();

    /**
     * 获取过滤消息
     */
    protected abstract String getFilterMessage();

    /**
     * 过滤附件的具体逻辑
     * @param attachment 待过滤的附件
     * @return true-保留 false-过滤掉
     */
    protected abstract boolean filterAttachment(RdAttachmentDO attachment);
} 