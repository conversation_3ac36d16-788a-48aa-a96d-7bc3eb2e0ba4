/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdConditionInput implements Serializable{
    private String conditionInstanceId;
    private Integer conditionId;
    private Integer conditionType;
    private Integer conditionTypeId;
    private String conditionTypeName;
    private String conditionName;
    private String conditionDesc;
    private Integer conditionSeq;
    private List<RdConditionLanguageInput> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
