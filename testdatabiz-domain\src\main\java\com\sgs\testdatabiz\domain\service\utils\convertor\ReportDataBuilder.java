package com.sgs.testdatabiz.domain.service.utils.convertor;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.ReportStatus;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.enums.CustomerType;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.*;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.domain.service.rd.ReportDataService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.facade.model.dto.TrfReportDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdDelayDTO;
import com.sgs.testdatabiz.facade.model.enums.AttachmentObjectTypeEnum;
import com.sgs.testdatabiz.integration.CustomerBizClient;
import com.sgs.testdatabiz.integration.FrameWorkClient;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.testdatabiz.domain.service.constant.SystemConstants.DEFAULT_USER;

/**
 * <AUTHOR>
 */
@Component
public final class ReportDataBuilder {

    @Autowired
    private ReportDataService reportDataService;

    @Autowired
    private RdReportExtMapper rdReportExtMapper;
    @Autowired
    private FrameWorkClient frameWorkClient;

    @Autowired
    private CustomerBizClient customerBizClient;

    @Autowired
    private IdService idService;

    public Long getId() {
        return idService.nextId();
    }

    public List<RdReportPO> buildRdReportPO(ReportDataBatchDO reportDataDO, Map<String, RdOrderDO> orderMap, Map<String, RdReportPO> map) {
        if (Func.isEmpty(reportDataDO)) {
            return null;
        }

        List<RdReportDO> reportList = reportDataDO.getReportList();
        if (Func.isEmpty(reportList)) {
            return null;
        }

        List<String> reportNos = reportList.stream().filter(Objects::nonNull).map(RdReportDO::getReportNo).filter(Objects::nonNull).collect(Collectors.toList());
        if (Func.isEmpty(reportNos)) {
            return null;
        }

        CustomResult<List<TrfReportDTO>> trfRelationshop = customerBizClient.reportRelationship(reportNos);
        if(!trfRelationshop.isSuccess()) {
            throw new IllegalStateException("query trf report relationship failed.pls retry later");
        }

        List<TrfReportDTO> trfReportDTOList = trfRelationshop.getData();
        boolean hasRelationship = Func.isNotEmpty(trfReportDTOList);
        

        List<RdReportPO> reportPOList = new ArrayList<>();
        reportList.forEach(
                report -> {
                    RdReportPO rdReportPO = new RdReportPO();
                    RdReportPO rdReportPOExists = map.get(report.getReportNo());
                    //SCI-1475
                    if(hasRelationship && Objects.nonNull(rdReportPOExists) && ReportStatus.check(rdReportPOExists.getReportStatus(), ReportStatus.Cancelled) && !ReportStatus.check(report.getReportStatus(), ReportStatus.Cancelled)) {
                        throw new IllegalStateException("report status is cancelled, cannot modify to other status");
                    }
                    long reportId = Func.isEmpty(rdReportPOExists) ? idService.nextId() : rdReportPOExists.getId();
                    report.setId(reportId);

                    rdReportPO.setId(reportId);
                    rdReportPO.setLabId(reportDataDO.getLabId());
                    rdReportPO.setBuId(reportDataDO.getBuId());
                    rdReportPO.setReportId(report.getReportId());
                    rdReportPO.setReportNo(report.getReportNo());
                    rdReportPO.setOriginalReportNo(report.getOriginalReportNo());
                    rdReportPO.setReportDueDate(report.getReportDueDate());
                    rdReportPO.setReportSourceType(report.getReportSourceType());
                    rdReportPO.setExcludeCustomerInterface(Func.isEmpty(report.getExcludeCustomerInterface()) ? "0" : report.getExcludeCustomerInterface());
                    RdOrderDO rdOrderDO = orderMap.get(report.getSystemId() + report.getOrderNo());
                    if (Func.isNotEmpty(rdOrderDO) && Func.isNotEmpty(rdOrderDO.getServiceRequirement())) {
                        RdServiceRequirementDO serviceRequirement = rdOrderDO.getServiceRequirement();
                        if (serviceRequirement != null && serviceRequirement.getReport() != null) {
                            RdServiceRequirementReportDO reportServiceRequirement = serviceRequirement.getReport();
                            rdReportPO.setReportHeader(reportServiceRequirement.getReportHeader());
                            rdReportPO.setReportAddress(reportServiceRequirement.getReportAddress());
                        }
                    }
                    rdReportPO.setReportStatus(report.getReportStatus());
                    rdReportPO.setReportCertificateName(report.getCertificateName());
                    rdReportPO.setReportApproverBy(report.getApproveBy());
                    rdReportPO.setReportApproverDate(report.getApproveDate());
                    rdReportPO.setSoftcopyDeliveryDate(report.getSoftCopyDeliveryDate());
                    RdConclusionDO reportConclusion = report.getConclusion();
                    if (Func.isNotEmpty(reportConclusion)) {
                        rdReportPO.setConclusionCode(reportConclusion.getConclusionCode());
                        rdReportPO.setCustomerConclusion(reportConclusion.getCustomerConclusion());
                        rdReportPO.setReviewConclusion(reportConclusion.getReviewConclusion());
                        rdReportPO.setConclusionRemark(reportConclusion.getConclusionRemark());
                    }
                    rdReportPO.setActiveIndicator(ActiveType.Enable.getStatus());
                    rdReportPO.setReportCreatedBy(report.getCreateBy());
                    rdReportPO.setReportCreatedDate(report.getCreateDate());
                    rdReportPO.setCreatedBy(DEFAULT_USER);
                    rdReportPO.setCreatedDate(new Date());
                    rdReportPO.setSystemId(Func.isNotEmpty(report.getSystemId()) ? Long.parseLong(String.valueOf(report.getSystemId())) : null);
                    rdReportPO.setModifiedBy(DEFAULT_USER);
                    rdReportPO.setModifiedDate(new Date());
                    reportPOList.add(rdReportPO);
                }
        );

        return reportPOList;
    }

    public List<RdReportLangPO> buildRdReportLangPO(ReportDataBatchDO reportDataDO, Map<String, RdOrderDO> orderMap) {
        if (Func.isEmpty(reportDataDO)) {
            return Collections.emptyList();
        }
        List<RdReportDO> reportList = reportDataDO.getReportList();
        List<RdReportLangPO> langPOList = new ArrayList<>();
        for (RdReportDO report : reportList) {
            String orderNo = report.getOrderNo();
            Integer systemId = report.getSystemId();
            RdOrderDO order = orderMap.get(systemId + orderNo);
            if (Func.isEmpty(order)) {
                break;
            }
            RdServiceRequirementDO serviceRequirement = order.getServiceRequirement();
            if (serviceRequirement == null) {
                return Collections.emptyList();
            }
            RdServiceRequirementReportDO serviceRequirementReport = serviceRequirement.getReport();
            if (serviceRequirementReport == null) {
                return Collections.emptyList();
            }
            List<RdReportLanguageDO> languageList = serviceRequirementReport.getLanguageList();
            if (CollectionUtils.isEmpty(languageList)) {
                return Collections.emptyList();
            }
            List<RdReportLangPO> langList = languageList.stream().map(lang -> {
                RdReportLangPO rdReportLangPO = new RdReportLangPO();
                rdReportLangPO.setId(idService.nextId());
                rdReportLangPO.setRdReportId(report.getId());
                rdReportLangPO.setLanguageId(lang.getLanguageId());
                rdReportLangPO.setReportHeader(lang.getReportHeader());
                rdReportLangPO.setReportAddress(lang.getReportAddress());
                return rdReportLangPO;
            }).collect(Collectors.toList());
            langPOList.addAll(langList);
        }
        return langPOList;
    }

    public List<RdAttachmentPO> buildAttachments(final ReportDataBatchDO reportDataDO) {
        final ArrayList<RdAttachmentPO> attachmentList = new ArrayList<>();

        List<RdReportDO> reportList = reportDataDO.getReportList();

        for (RdReportDO report : reportList) {
            List<RdAttachmentDO> reportFileList = report.getReportFileList();
            if (Func.isNotEmpty(reportFileList)) {

                List<RdAttachmentPO> attachmentPOList = buildAttachmentPO(
                        reportFileList,
                        reportDataDO.getLabId(),
                        report.getReportId(),
                        report.getReportNo(),
                        AttachmentObjectTypeEnum.REPORT.getCode(),
                        report.getOrderNo(),
                        report.getReportNo(),
                        Long.parseLong(String.valueOf(report.getSystemId()))
                );
                if (Func.isNotEmpty(attachmentPOList)) {
                    attachmentList.addAll(attachmentPOList);
                }

                List<RdSubReportDO> subReportList = report.getSubReportList();
                if (Func.isNotEmpty(subReportList)) {
                    for (RdSubReportDO subReportDO : subReportList) {
                        List<RdAttachmentDO> subReportFileList = subReportDO.getSubReportFileList();
                        List<RdAttachmentPO> subReportFilePOs = buildAttachmentPO(
                                subReportFileList,
                                reportDataDO.getLabId(),
                                subReportDO.getSubReportId(),
                                subReportDO.getSubReportNo(),
                                AttachmentObjectTypeEnum.SUB_REPORT.getCode(),
                                report.getOrderNo(),
                                report.getReportNo(),
                                Long.parseLong(String.valueOf(report.getSystemId()))
                        );
                        if (Func.isNotEmpty(subReportFilePOs)) {
                            attachmentList.addAll(subReportFilePOs);
                        }
                    }
                }

                if (Func.isNotEmpty(report.getReportFileList())) {
                    report.getReportFileList().forEach(l -> l.setObjectType(AttachmentObjectTypeEnum.REPORT.getCode()));
                }
            }
        }

        // 3.testSampleList to RdTestSampleDO
//        List<RdTestSampleDO> testSampleList = reportDataDO.getTestSampleList();
//        if (!CollectionUtils.isEmpty(testSampleList)) {
//            for (RdTestSampleDO testSampleDO : testSampleList) {
//                List<RdAttachmentDO> testSamplePhotoList = testSampleDO.getTestSamplePhoto();
//                if (CollectionUtils.isEmpty(testSamplePhotoList)) {
//                    continue;
//                }
//
//                testSamplePhotoList.forEach(testSamplePhotoDO -> {
//                    RdAttachmentPO attachmentPO = newBasicAttachmentPO(reportDataDO);
//                    attachmentPO.setObjectType(AttachmentObjectTypeEnum.TEST_SAMPLE.getCode());
//                    attachmentPO.setObjectNo(testSampleDO.getTestSampleNo());
//                    attachmentPO.setObjectId(testSampleDO.getTestSampleInstanceId());
//                    attachmentPO.setBizType(String.valueOf(testSamplePhotoDO.getFileType()));
//                    attachmentPO.setFileName(testSamplePhotoDO.getFileName());
//                    attachmentPO.setCloudId(testSamplePhotoDO.getCloudId());
//                    attachmentList.add(attachmentPO);
//                });
//            }
//        }
        return attachmentList;
    }

    public List<RdAttachmentPO> buildInvoiceFile(List<RdInvoiceDO> invoiceList) {
        List<RdAttachmentPO> list = new ArrayList<>();
        invoiceList.forEach(invoice -> {
            List<RdAttachmentDO> invoiceFileList = invoice.getInvoiceFileList();
            if (Func.isNotEmpty(invoiceFileList)) {
                invoiceFileList.forEach(
                        invoiceFile -> {
                            RdAttachmentPO attachmentPO = new RdAttachmentPO();
                            attachmentPO.setObjectType(AttachmentObjectTypeEnum.INVOICE.getCode());
                            attachmentPO.setObjectNo(invoice.getInvoiceNo());
                            attachmentPO.setObjectId(String.valueOf(invoice.getRdInvoiceId()));
                            attachmentPO.setActiveIndicator(ActiveType.Enable.getStatus());
                            attachmentPO.setSystemId(invoice.getSystemId());
                            attachmentPO.setReportNo(invoice.getReportNo());
                            // TODO 待确认
                            attachmentPO.setBizType(Func.isNotEmpty(invoiceFile.getFileType()) ? String.valueOf(invoiceFile.getFileType()) : null);
                            attachmentPO.setLabId(invoice.getLabId());
                            attachmentPO.setFileName(invoiceFile.getFileName());
                            attachmentPO.setCloudId(invoiceFile.getCloudId());
                            attachmentPO.setId(idService.nextId());
                            attachmentPO.setCreatedBy(DEFAULT_USER);
                            attachmentPO.setModifiedBy(DEFAULT_USER);
                            attachmentPO.setCreatedDate(DateUtils.getNow());
                            attachmentPO.setModifiedDate(DateUtils.getNow());
//                            attachmentPO.setFilePath(invoiceFile.getFilePath())
                            list.add(attachmentPO);
                        }
                );
            }
        });
        return list;
    }

    public List<RdAttachmentPO> buildAttachmentPO(
            List<RdAttachmentDO> attachmentDOList,
            Long labId,
            String objectId,
            String objectNo,
            Integer objectType,
            String orderNo,
            String reportNo,
            Long systemId
    ) {
        List<RdAttachmentPO> list = new ArrayList<>();
        attachmentDOList.forEach(
                attachment -> {
                    RdAttachmentPO attachmentPO = new RdAttachmentPO();
                    attachmentPO.setId(idService.nextId());
                    attachmentPO.setLabId(labId);
                    attachmentPO.setObjectType(objectType);
                    attachmentPO.setObjectNo(objectNo);
                    attachmentPO.setObjectId(objectId);
                    attachmentPO.setOrderNo(orderNo);
                    attachmentPO.setSystemId(systemId);
                    attachmentPO.setReportNo(reportNo);
                    attachmentPO.setLanguageId(attachment.getLanguageId());
                    attachmentPO.setBizType(Func.isNotEmpty(attachment.getFileType()) ? String.valueOf(attachment.getFileType()) : null);
                    attachmentPO.setFileName(attachment.getFileName());
                    attachmentPO.setCloudId(attachment.getCloudId());
                    attachmentPO.setActiveIndicator(ActiveType.Enable.getStatus());
                    attachmentPO.setCreatedBy(DEFAULT_USER);
                    attachmentPO.setCreatedDate(DateUtils.getNow());
                    attachmentPO.setModifiedBy(DEFAULT_USER);
                    attachmentPO.setModifiedDate(DateUtils.getNow());
                    list.add(attachmentPO);
                }
        );
        return list;
    }


//    public static RdReportExtPO buildReportExtPO(Long rdReportId, ReportDataDO reportDataDO) {
//        RdReportExtPO reportExtPO = new RdReportExtPO();
//        reportExtPO.setId(ID_GENERATOR.nextId());
//        reportExtPO.setRdReportId(rdReportId);
//        reportExtPO.setRequestJson(JSONObject.toJSONString(reportDataDO));
//        return reportExtPO;
//    }

    public List<RdReportExtPO> buildReportExtPO(ReportDataBatchDO reportDataDO, String version) {

        List<RdReportExtPO> list = new ArrayList<>();

        List<RdTrfDO> trfList = reportDataDO.getTrfList();
//        Map<String, List<RdTrfDO>> trfMap = Maps.newConcurrentMap();
        Multimap<String, RdTrfDO> trfMap = ArrayListMultimap.create();
        if (Func.isNotEmpty(trfList)) {
//            trfMap = trfList.stream().collect(Collectors.groupingBy(l -> l.getRefSystemId() + l.getTrfNo()));
            trfList.forEach(
                    trf -> {
                        List<RdOrderRelDO> orderList = trf.getOrderList();
                        if (Func.isNotEmpty(orderList)) {
                            for (RdOrderRelDO order : orderList) {
                                Integer systemId = order.getSystemId();
                                String orderNo = order.getOrderNo();
                                String key = systemId + orderNo;
                                trfMap.put(key, trf);
                            }
                        }
                    }
            );
        }

        String traceabilityId = reportDataDO.getTraceabilityId();

        List<RdOrderDO> orderList = reportDataDO.getOrderList();
        Map<String, List<RdOrderDO>> orderMap = Maps.newConcurrentMap();
        if (Func.isNotEmpty(orderList)) {
            orderMap = orderList.stream().collect(Collectors.groupingBy(l -> l.getSystemId() + l.getOrderNo()));
        }

        List<RdQuotationDO> quotationList = reportDataDO.getQuotationList();
        Map<String, List<RdQuotationDO>> quotationMap = Maps.newConcurrentMap();
        if (Func.isNotEmpty(quotationList)) {
            quotationMap = quotationList.stream().collect(Collectors.groupingBy(l -> l.getSystemId() + l.getOrderNo()));
        }

        List<RdTestSampleDO> testSampleList = reportDataDO.getTestSampleList();
        Map<String, List<RdTestSampleDO>> testSampleMap = Maps.newConcurrentMap();
        if (Func.isNotEmpty(testSampleList)) {
            testSampleMap = testSampleList.stream().collect(Collectors.groupingBy(l -> l.getSystemId() + l.getOrderNo()));
        }

        List<RdInvoiceDO> invoiceList = reportDataDO.getInvoiceList();
        Map<String, List<RdInvoiceDO>> invoiceMap = Maps.newConcurrentMap();
        if (Func.isNotEmpty(invoiceList)) {
            invoiceMap = invoiceList.stream().collect(Collectors.groupingBy(l -> l.getSystemId() + l.getOrderNo()));
        }

        List<RdConditionGroupDO> conditionGroupList = reportDataDO.getConditionGroupList();
        Map<String, List<RdConditionGroupDO>> conditionGroupMap = Maps.newConcurrentMap();
        if (Func.isNotEmpty(conditionGroupList)) {
            conditionGroupMap = conditionGroupList.stream().collect(Collectors.groupingBy(l -> l.getSystemId() + l.getOrderNo()));
        }

        List<RdReportConclusionDO> reportConclusionList = reportDataDO.getReportConclusionList();
        Map<String, List<RdReportConclusionDO>> reportConclusionMap = Maps.newConcurrentMap();
        if (Func.isNotEmpty(reportConclusionList)) {
            reportConclusionMap = reportConclusionList.stream().collect(Collectors.groupingBy(l -> l.getSystemId() + l.getOrderNo()));
        }

        List<RdTestLineDO> testLineList = reportDataDO.getTestLineList();
        Map<String, List<RdTestLineDO>> testLineMap = Maps.newConcurrentMap();
        if (Func.isNotEmpty(testLineList)) {
            testLineMap = testLineList.stream().collect(Collectors.groupingBy(l -> l.getSystemId() + l.getOrderNo()));
        }

        List<RdTestResultDO> testResultList = reportDataDO.getTestResultList();
        Map<String, List<RdTestResultDO>> testResultMap = Maps.newConcurrentMap();
        if (Func.isNotEmpty(testResultList)) {
            testResultMap = testResultList.stream().collect(Collectors.groupingBy(l -> l.getSystemId() + l.getOrderNo()));
        }


        List<RdReportDO> reportList = reportDataDO.getReportList();
        for (RdReportDO report : reportList) {
            Integer systemId = report.getSystemId();
            String orderNo = report.getOrderNo();
            String key = systemId + orderNo;
            List<RdTrfDO> trfDOList = new ArrayList<>(trfMap.get(key));
            List<RdOrderDO> orderDOList = orderMap.get(key);
            List<RdQuotationDO> quotationDOList = quotationMap.get(key);
            List<RdTestSampleDO> testSampleDOList = testSampleMap.get(key);
            List<RdInvoiceDO> invoiceDOList = invoiceMap.get(key);
            List<RdConditionGroupDO> conditionGroupDOList = conditionGroupMap.get(key);
            List<RdReportConclusionDO> conclusionDOList = reportConclusionMap.get(key);
            List<RdTestLineDO> testLineDOList = testLineMap.get(key);
            List<RdTestResultDO> testResultDOList = testResultMap.get(key);
            ReportDataDO dataDO = buildReportDataDO(
                    trfDOList,
                    orderDOList,
                    quotationDOList,
                    testSampleDOList,
                    invoiceDOList,
                    conditionGroupDOList,
                    conclusionDOList,
                    testLineDOList,
                    testResultDOList,
                    report,
                    traceabilityId
            );
            RdReportExtPO reportExtPO = new RdReportExtPO();
            reportExtPO.setId(idService.nextId());
            reportExtPO.setTraceabilityId(traceabilityId);
            reportExtPO.setRdReportId(report.getId());
            reportExtPO.setRequestJson(JSONObject.toJSONString(dataDO));
            reportExtPO.setVersion(version);
            reportExtPO.setCreatedDate(new Date());
            reportExtPO.setUpdateVersion(getUpdateCount(report.getId()));
            list.add(reportExtPO);
        }
        return list;
    }

    public int getUpdateCount(Long rdReportId) {
        RdReportExtExample example = new RdReportExtExample();
        example.createCriteria().andRdReportIdEqualTo(rdReportId);
        return rdReportExtMapper.countByExample(example);
    }

    public ReportDataDO buildReportDataDO(
            List<RdTrfDO> trfDOList,
            List<RdOrderDO> orderDOList,
            List<RdQuotationDO> quotationDOList,
            List<RdTestSampleDO> testSampleDOList,
            List<RdInvoiceDO> invoiceDOList,
            List<RdConditionGroupDO> conditionGroupDOList,
            List<RdReportConclusionDO> reportConclusionDOList,
            List<RdTestLineDO> testLineDOList,
            List<RdTestResultDO> testResultDOList,
            RdReportDO reportDO,
            String traceabilityId
    ) {
        ReportDataDO dataDO = new ReportDataDO();
        dataDO.setTrfList(trfDOList);
        dataDO.setTraceabilityId(traceabilityId);
        if (Func.isNotEmpty(orderDOList)) {
            RdOrderDO rdOrderDO = new RdOrderDO();
            BeanUtils.copyProperties(orderDOList.get(0), rdOrderDO);
            RdLabDO lab = reportDO.getLab();
            Date approveDate = reportDO.getApproveDate();
            Date orderExpectDueDate = rdOrderDO.getOrderExpectDueDate();
            String labCode = "";
            if (Func.isNotEmpty(lab)) {
                labCode = lab.getLabCode();
            }
            String actualTatStr = frameWorkClient.queryActualTat(labCode, approveDate, reportDO.getReportDueDate());
            if (Func.isNotBlank(actualTatStr)) {
                reportDO.setActualTat(Func.toLong(actualTatStr));
            }

            String delayDayStr = frameWorkClient.queryDelayDay(labCode, approveDate, rdOrderDO.getServiceStartDate());
            if (Func.isNotBlank(delayDayStr)) {
                RdOrderOthersDO others = rdOrderDO.getOthers();
                if (Func.isEmpty(others)) {
                    others = new RdOrderOthersDO();
                    rdOrderDO.setOthers(others);
                }
                RdDelayDTO delay = others.getDelay();
                if (Func.isEmpty(delay)) {
                    delay = new RdDelayDTO();
                    others.setDelay(delay);
                }
                delay.setDelayDays(Func.toLong(delayDayStr));
            }

            //SCI-1490
            Optional.ofNullable(rdOrderDO.getProductList())
                    .map(productList ->
                            productList.stream()
                                    .filter(Objects::nonNull)
                                    .filter(p -> (StringUtils.isBlank(p.getReportNo()) || Objects.equals(p.getReportNo(), reportDO.getReportNo())))
                                    .collect(Collectors.toList())
                    )
                    .ifPresent(rdOrderDO::setProductList);

            //SCI-1490
            Optional.ofNullable(rdOrderDO.getSampleList())
                    .map(sampleList ->
                            sampleList.stream()
                                    .filter(Objects::nonNull)
                                    .filter(s -> (StringUtils.isBlank(s.getReportNo()) || Objects.equals(s.getReportNo(), reportDO.getReportNo())))
                                    .collect(Collectors.toList())
                    )
                    .ifPresent(rdOrderDO::setSampleList);

            dataDO.setOrder(rdOrderDO);
        }
        dataDO.setHeader(reportDO);
        dataDO.setTestSampleList(
                Optional.ofNullable(testSampleDOList)
                        .map(testSampleList ->
                                testSampleList.stream()
                                        .filter(Objects::nonNull)
                                        .filter(testSample -> StringUtils.isBlank(testSample.getReportNo()) || Objects.equals(testSample.getReportNo(), reportDO.getReportNo()))
                                        .collect(Collectors.toList())
                        )
                        .orElse(null)
        );
        dataDO.setTestLineList(
                Optional.ofNullable(testLineDOList)
                        .map(testLineList ->
                                testLineList.stream()
                                        .filter(Objects::nonNull)
                                        .filter(testLine -> StringUtils.isBlank(testLine.getReportNo()) || Objects.equals(testLine.getReportNo(), reportDO.getReportNo()))
                                        .collect(Collectors.toList())
                        )
                        .orElse(null)
        );
        dataDO.setTestResultList(
                Optional.ofNullable(testResultDOList)
                        .map(testResultList ->
                                testResultList.stream()
                                        .filter(Objects::nonNull)
                                        .filter(testResult -> StringUtils.isBlank(testResult.getReportNo()) || Objects.equals(testResult.getReportNo(), reportDO.getReportNo()))
                                        .collect(Collectors.toList())
                        )
                        .orElse(null)
        );
        dataDO.setReportConclusionList(reportConclusionDOList);
        dataDO.setConditionGroupList(conditionGroupDOList);
        dataDO.setQuotationList(quotationDOList);
        dataDO.setInvoiceList(invoiceDOList);
//        dataDO.setBuId(buId);
//        dataDO.setBuCode(buCode);
//        dataDO.setLabId(labId);
//        dataDO.setLabCode(labCode);
//        dataDO.setSystemId(systemId);
//        dataDO.setToken(token);
//        dataDO.setSign(sign);
//        dataDO.setRequestId(requestId);
//        dataDO.setSourceProductLineCode(sourceProductLineCode);
//        dataDO.setProductLineCode(productLineCode);
        return dataDO;
    }


    public Map<String, List<RdReportDO>> getReportOrderMap(ReportDataBatchDO reportDataDO) {
        List<RdReportDO> reportList = reportDataDO.getReportList();
        return reportList.stream().collect(Collectors.groupingBy(RdReportDO::getOrderNo));
    }

    public List<RdReportTrfRelPO> buildReportTrfRelPO(ReportDataBatchDO reportDataDO) {
        List<RdTrfDO> trfList = reportDataDO.getTrfList();
        if (CollectionUtils.isEmpty(trfList)) {
            return Collections.emptyList();
        }

        Map<String, List<RdReportDO>> reportOrderMap = getReportOrderMap(reportDataDO);

        List<RdReportTrfRelPO> list = new ArrayList<>();
        for (RdTrfDO trfDO : trfList) {
            List<RdOrderRelDO> orderList = trfDO.getOrderList();
            if (Func.isNotEmpty(orderList)) {
                orderList.forEach(
                        l -> {
                            String orderNo = l.getOrderNo();
                            List<RdReportDO> rdReportDOS = reportOrderMap.get(orderNo);
                            if (Func.isNotEmpty(rdReportDOS)) {
                                rdReportDOS.forEach(
                                        v -> {
                                            RdReportTrfRelPO reportTrfRelPO = new RdReportTrfRelPO();
                                            reportTrfRelPO.setId(idService.nextId());
                                            reportTrfRelPO.setLabId(reportDataDO.getLabId());
                                            reportTrfRelPO.setBuId(reportDataDO.getBuId());
                                            reportTrfRelPO.setTrfRefSystemId(ObjectUtils.defaultIfNull(trfDO.getRefSystemId(), 0));
                                            reportTrfRelPO.setTrfNo(StringUtils.defaultString(trfDO.getTrfNo()));
                                            reportTrfRelPO.setOrderSystemId(l.getSystemId());
                                            reportTrfRelPO.setOrderNo(l.getOrderNo());
                                            reportTrfRelPO.setReportNo(v.getReportNo());
                                            reportTrfRelPO.setActiveIndicator(ActiveType.Enable.getStatus());
                                            reportTrfRelPO.setCreatedBy(DEFAULT_USER);
                                            reportTrfRelPO.setCreatedDate(new Date());
                                            reportTrfRelPO.setModifiedBy(DEFAULT_USER);
                                            reportTrfRelPO.setModifiedDate(new Date());
                                            list.add(reportTrfRelPO);
                                        }
                                );
                            }

                        }
                );
            }
        }
        return list;
    }

    public List<RdReportInvoicePO> buildRdReportInvoicePOList(Long reportId, String orderNo, Long labId, String reportNo, List<RdInvoiceDO> invoiceList, Long systemId) {
        List<RdReportInvoicePO> invoicePOList = new ArrayList<>();
        invoiceList.forEach(
                invoice -> {
                    RdReportInvoicePO invoicePO = new RdReportInvoicePO();
                    long invoiceId = idService.nextId();
                    invoicePO.setSystemId(systemId);
                    invoicePO.setId(invoiceId);
                    invoicePO.setLabId(labId);
                    invoicePO.setRdReportId(reportId);
                    invoicePO.setOrderNo(orderNo);
                    invoicePO.setReportNo(reportNo);
                    invoicePO.setBossOrderNo(invoice.getBossOrderNo());
                    invoicePO.setProductCode(invoice.getProductCode());
                    invoicePO.setCostCenter(invoice.getCostCenter());
                    invoicePO.setProjectTemplate(invoice.getProjectTemplate());
//                    invoicePO.setBossInvoiceDate();
                    invoicePO.setInvoiceNo(invoice.getInvoiceNo());
//                    invoicePO.setInvoiceDate();
                    invoicePO.setCurrency(invoice.getCurrency());
                    invoicePO.setNetAmount(invoice.getNetAmount());
                    invoicePO.setVatAmount(invoice.getVatAmount());
                    invoicePO.setTotalAmount(invoice.getTotalAmount());
                    invoicePO.setPrePaidAmount(invoice.getPrePaidAmount());
                    invoicePO.setInvoiceStatus(invoice.getInvoiceStatus());
                    invoicePO.setActiveIndicator(ActiveType.Enable.getStatus());
                    invoicePO.setCreatedBy(DEFAULT_USER);
                    invoicePO.setCreatedDate(DateUtils.getNow());
                    invoicePO.setModifiedBy(DEFAULT_USER);
                    invoicePO.setModifiedDate(DateUtils.getNow());
                    invoicePOList.add(invoicePO);

                    invoice.setRdInvoiceId(invoiceId);
                }
        );
        return invoicePOList;
    }

    public List<RdAttachmentDO> buildInvoiceQuotationFileDO(List<RdAttachmentPO> invoiceQuotationFilePOList) {
        if (Func.isNotEmpty(invoiceQuotationFilePOList)) {
            List<RdAttachmentDO> list = new ArrayList<>();
            invoiceQuotationFilePOList.forEach(
                    l -> {
                        RdAttachmentDO invoiceFileDO = new RdAttachmentDO();
                        invoiceFileDO.setObjectType(l.getObjectType());
                        invoiceFileDO.setObjectId(l.getObjectId());
                        invoiceFileDO.setLanguageId(l.getLanguageId());
                        invoiceFileDO.setFileName(l.getFileName());
                        invoiceFileDO.setCloudId(l.getCloudId());
                        list.add(invoiceFileDO);
                    }
            );
            return list;
        }
        return null;
    }

    public QuotationBean buildQuotationPOList(Long labId, Long systemId, String reportNo, String orderNo, List<RdQuotationDO> quotationList) {
        List<RdQuotationPO> quotationPOList = new ArrayList<>();
        List<RdQuotationLangPO> quotationLangPOList = new ArrayList<>();
        quotationList.forEach(
                l -> {
                    List<RdServiceItemDO> serviceItemList = l.getServiceItemList();
                    if (Func.isNotEmpty(serviceItemList)) {
                        serviceItemList.forEach(
                                v -> {
                                    Map<Integer, RdQuotationLangPO> quotationLangPOMap = new HashMap<>();
                                    RdQuotationPO quotationPO = new RdQuotationPO();
                                    long quotationId = idService.nextId();
                                    quotationPO.setId(quotationId);
                                    quotationPO.setLabId(labId);
                                    quotationPO.setSystemId(systemId);
                                    quotationPO.setOrderNo(orderNo);
                                    quotationPO.setReportNo(reportNo);
                                    quotationPO.setQuotationNo(l.getQuotationNo());
                                    quotationPO.setCurrencyCode(l.getCurrency());
                                    RdCustomerDO payer = l.getPayer();
                                    if (Func.isNotEmpty(payer)) {
                                        quotationPO.setPayerCustomerName(payer.getCustomerName());
                                        quotationPO.setPayerBossNumber(payer.getBossNo());
                                        List<RdCustomerLanguageDO> languageList = payer.getLanguageList();
                                        if (Func.isNotEmpty(languageList)) {
                                            languageList.forEach(
                                                    lang -> {
                                                        RdQuotationLangPO quotationLangPO = quotationLangPOMap.get(lang.getLanguageId());
                                                        if (Func.isEmpty(quotationLangPO)) {
                                                            quotationLangPO = new RdQuotationLangPO();
                                                            quotationLangPO.setId(idService.nextId());
                                                            quotationLangPO.setRtQuotationId(quotationId);
                                                            quotationLangPO.setLanguageId(lang.getLanguageId());
                                                            quotationLangPO.setCreatedBy(DEFAULT_USER);
                                                            quotationLangPO.setCreatedDate(DateUtils.getNow());
                                                            quotationLangPO.setModifiedBy(DEFAULT_USER);
                                                            quotationLangPO.setModifiedDate(DateUtils.getNow());
                                                        }
                                                        quotationLangPO.setPayerCustomerName(lang.getCustomerName());
//                                                        quotationLangPOList.add(quotationLangPO);
                                                        quotationLangPOMap.put(lang.getLanguageId(), quotationLangPO);
                                                    }
                                            );
                                        }
                                    }
//                    quotationPO.setSpecialOfferFlag();
                                    quotationPO.setSumNetAmount(l.getNetAmount());
                                    quotationPO.setSumVatAmount(l.getVatAmount());
                                    quotationPO.setTotalAmount(l.getTotalAmount());
                                    quotationPO.setAdjustmentAmount(l.getAdjustmentAmount());
                                    // TODO 缺少
//                    quotationPO.setAdjustmentDiscount();
                                    quotationPO.setFinalAmount(l.getFinalAmount());
//                    quotationPO.setCitationFullName();
                                    // TODO 缺少版本ID  暂时先不管 用不到
//                    quotationPO.setVersionId();
                                    quotationPO.setQuotationStatus(l.getQuotationStatus());
                                    quotationPO.setActiveIndicator(ActiveType.Enable.getStatus());
                                    quotationPO.setCreatedBy(DEFAULT_USER);
                                    quotationPO.setCreatedDate(DateUtils.getNow());
                                    quotationPO.setModifiedBy(DEFAULT_USER);
                                    quotationPO.setModifiedDate(DateUtils.getNow());
                                    quotationPOList.add(quotationPO);
//                                    quotationPO.setServiceItemType();
                                    quotationPO.setServiceItemName(v.getServiceItemName());
//                                    quotationPO.setServiceItemSeq();
                                    quotationPO.setQuantity(v.getQuantity());
                                    quotationPO.setServiceItemListUnitPrice(v.getServiceItemListUnitPrice());
                                    quotationPO.setServiceItemSalesUnitPrice(v.getServiceItemSalesUnitPrice());
                                    quotationPO.setServiceItemDiscount(v.getServiceItemDiscount());
//                                    quotationPO.setServiceItemExchangeRatePrice();
//                                    quotationPO.setServiceItemSurChargePrice();
                                    quotationPO.setServiceItemNetAmount(v.getServiceItemNetAmount());
                                    quotationPO.setServiceItemVatAmount(v.getServiceItemVATAmount());
                                    quotationPO.setServiceItemTotalAmount(v.getServiceItemTotalAmount());
                                    quotationPO.setPpNo(v.getPpNo());
                                    quotationPO.setTestLineId(NumberUtil.toLong(v.getTestLineId()));
                                    quotationPO.setCitationType(v.getCitationType());
                                    quotationPO.setCitationId(v.getCitationId());
                                    quotationPO.setCitationName(v.getCitationName());
//                                    l.setRdQuotationId(quotationId);
                                    quotationPO.setQuotationVersionId(l.getQuotationVersionId());
                                    // 封装quotation_lang
                                    List<RdServiceItemLanguageDO> languageList = v.getLanguageList();
                                    if (Func.isNotEmpty(languageList)) {
                                        languageList.forEach(
                                                k -> {
                                                    RdQuotationLangPO rdQuotationLangPO = quotationLangPOMap.get(k.getLanguageId());
                                                    if (Func.isEmpty(rdQuotationLangPO)) {
                                                        rdQuotationLangPO = new RdQuotationLangPO();
                                                        rdQuotationLangPO.setId(idService.nextId());
                                                        rdQuotationLangPO.setRtQuotationId(quotationId);
                                                        rdQuotationLangPO.setLanguageId(k.getLanguageId());
                                                        rdQuotationLangPO.setCreatedBy(DEFAULT_USER);
                                                        rdQuotationLangPO.setCreatedDate(DateUtils.getNow());
                                                        rdQuotationLangPO.setModifiedBy(DEFAULT_USER);
                                                        rdQuotationLangPO.setModifiedDate(DateUtils.getNow());
                                                    }
                                                    rdQuotationLangPO.setServiceItemName(k.getServiceItemName());
                                                    quotationLangPOMap.put(k.getLanguageId(), rdQuotationLangPO);
//                                                    quotationLangPOList.add(quotationLangPO);
                                                }
                                        );
                                    }

                                    RdServiceItemExternalDO externalInfo = v.getExternalInfo();
                                    if (Func.isNotEmpty(externalInfo)) {
                                        List<RdLanguageDO> externalInfoLanguageList = externalInfo.getLanguageList();
                                        if (Func.isNotEmpty(externalInfoLanguageList)) {
                                            externalInfoLanguageList.forEach(
                                                    externalInfoLang -> {
                                                        Integer languageId = externalInfoLang.getLanguageId();
                                                        RdQuotationLangPO rdQuotationLangPO = quotationLangPOMap.get(languageId);
                                                        if (Func.isEmpty(rdQuotationLangPO)) {
                                                            rdQuotationLangPO = new RdQuotationLangPO();
                                                            rdQuotationLangPO.setId(idService.nextId());
                                                            rdQuotationLangPO.setRtQuotationId(quotationId);
                                                            rdQuotationLangPO.setLanguageId(languageId);
                                                            rdQuotationLangPO.setCreatedBy(DEFAULT_USER);
                                                            rdQuotationLangPO.setCreatedDate(DateUtils.getNow());
                                                            rdQuotationLangPO.setModifiedBy(DEFAULT_USER);
                                                            rdQuotationLangPO.setModifiedDate(DateUtils.getNow());
                                                        }
                                                        rdQuotationLangPO.setCitationName(externalInfoLang.getTestCitationName());
                                                        quotationLangPOMap.put(languageId, rdQuotationLangPO);
                                                    }
                                            );
                                        }

                                    }
                                    quotationLangPOMap.forEach(
                                            (k, value) -> quotationLangPOList.add(value)
                                    );
                                }
                        );
                    }
                }
        );
        QuotationBean quotationBean = new QuotationBean();
        quotationBean.setQuotationPOList(quotationPOList);
        quotationBean.setQuotationLangPOList(quotationLangPOList);
        return quotationBean;
    }

    public List<RdQuotationDO> buildRdQuotationDOList(List<RdQuotationPO> quotationPOList) {
        if (Func.isEmpty(quotationPOList)) {
            return null;
        }
        Map<String, List<RdQuotationPO>> quotationMap = quotationPOList.stream().collect(Collectors.groupingBy(RdQuotationPO::getQuotationNo));
        List<String> quotationNos = quotationPOList.stream()
                .map(RdQuotationPO::getQuotationNo)
                .distinct()
                .collect(Collectors.toList());

        List<RdQuotationDO> list = new ArrayList<>();
        quotationNos.forEach(
                quotationNo -> {
                    RdQuotationDO quotationDO = new RdQuotationDO();
                    List<RdQuotationPO> quotationPOS = quotationMap.get(quotationNo);
                    RdQuotationPO rdQuotationPO = quotationPOS.get(0);
                    quotationDO.setQuotationNo(rdQuotationPO.getQuotationNo());
                    RdCustomerDO customerDO = new RdCustomerDO();
                    customerDO.setBossNo(rdQuotationPO.getPayerBossNumber());
//                    customerDO.setCustomerAddress();
                    customerDO.setCustomerName(rdQuotationPO.getPayerCustomerName());
                    customerDO.setCustomerUsage(CustomerType.Payer.getStatus());

                    quotationDO.setPayer(customerDO);
                    quotationDO.setCurrency(rdQuotationPO.getCurrencyCode());
                    quotationDO.setNetAmount(rdQuotationPO.getSumNetAmount());
                    quotationDO.setVatAmount(rdQuotationPO.getSumVatAmount());
                    quotationDO.setDiscount(rdQuotationPO.getAdjustmentDiscount());
                    quotationDO.setTotalAmount(rdQuotationPO.getTotalAmount());
                    quotationDO.setAdjustmentAmount(rdQuotationPO.getAdjustmentAmount());
                    quotationDO.setFinalAmount(rdQuotationPO.getFinalAmount());
                    quotationDO.setQuotationStatus(rdQuotationPO.getQuotationStatus());

                    quotationDO.setLabId(rdQuotationPO.getLabId());
                    quotationDO.setSystemId(rdQuotationPO.getSystemId());
                    quotationDO.setReportNo(rdQuotationPO.getReportNo());
                    quotationDO.setOrderNo(rdQuotationPO.getOrderNo());
                    quotationDO.setQuotationVersionId(rdQuotationPO.getQuotationVersionId());
                    List<RdServiceItemDO> serviceItemDOS = new ArrayList<>();
                    quotationPOS.forEach(
                            l -> {
                                RdServiceItemDO serviceItemDO = new RdServiceItemDO();
                                serviceItemDO.setServiceItemName(l.getServiceItemName());
                                serviceItemDO.setPpNo(l.getPpNo());
                                serviceItemDO.setTestLineId(NumberUtil.toInt(l.getTestLineId()));
                                serviceItemDO.setCitationId(l.getCitationId());
                                serviceItemDO.setCitationType(l.getCitationType());
                                serviceItemDO.setCitationName(l.getCitationName());
//                                        serviceItemDO.setEvaluationAlias();
                                serviceItemDO.setServiceItemListUnitPrice(l.getServiceItemListUnitPrice());
                                serviceItemDO.setServiceItemSalesUnitPrice(l.getServiceItemSalesUnitPrice());
                                serviceItemDO.setServiceItemDiscount(l.getServiceItemDiscount());
                                serviceItemDO.setQuantity(l.getQuantity());
                                serviceItemDO.setServiceItemNetAmount(l.getServiceItemNetAmount());
                                serviceItemDO.setServiceItemVATAmount(l.getServiceItemVatAmount());
                                serviceItemDO.setServiceItemTotalAmount(l.getServiceItemTotalAmount());
                                serviceItemDOS.add(serviceItemDO);
//                                        serviceItemDO.setLanguageList();
//                                        serviceItemDO.setExternalInfo();
                            }
                    );
                    quotationDO.setServiceItemList(serviceItemDOS);
                    list.add(quotationDO);
                }
        );
        return list;
    }

    public List<RdAttachmentPO> buildQuotationFile(List<RdQuotationDO> quotationList) {
        List<RdAttachmentPO> list = new ArrayList<>();
        quotationList.forEach(quotation -> {
            List<RdAttachmentDO> quotationFileList = quotation.getQuotationFileList();
            if (Func.isNotEmpty(quotationFileList)) {
                quotationFileList.forEach(
                        quotationFile -> {
                            RdAttachmentPO attachmentPO = new RdAttachmentPO();
                            attachmentPO.setId(idService.nextId());
                            attachmentPO.setObjectType(AttachmentObjectTypeEnum.QUOTATION.getCode());
                            attachmentPO.setObjectNo(quotation.getQuotationNo());
                            attachmentPO.setLabId(quotation.getLabId());
                            attachmentPO.setReportNo(quotation.getReportNo());
//                            attachmentPO.setObjectId(String.valueOf(quotation.getRdQuotationId()));
                            attachmentPO.setActiveIndicator(ActiveType.Enable.getStatus());
                            // TODO 待确认
                            attachmentPO.setBizType(Func.isNotEmpty(quotationFile.getFileType()) ? String.valueOf(quotationFile.getFileType()) : null);
                            attachmentPO.setSystemId(quotation.getSystemId());
                            attachmentPO.setFileName(quotationFile.getFileName());
                            attachmentPO.setCloudId(quotationFile.getCloudId());
                            attachmentPO.setCreatedBy(DEFAULT_USER);
                            attachmentPO.setModifiedBy(DEFAULT_USER);
                            attachmentPO.setCreatedDate(DateUtils.getNow());
                            attachmentPO.setModifiedDate(DateUtils.getNow());
//                            attachmentPO.setFilePath(invoiceFile.getFilePath())
                            list.add(attachmentPO);
                        }
                );
            }
        });
        return list;
    }

    public List<RdQuotationInvoiceRelPO> buildQuotationInvoiceRelPOList(List<RdInvoiceDO> invoiceList) {
        List<RdQuotationInvoiceRelPO> list = new ArrayList<>();
        invoiceList.forEach(
                l -> {
                    List<String> quotationNos = l.getQuotationNos();
                    if (Func.isNotEmpty(quotationNos)) {
                        quotationNos.forEach(
                                v -> {
                                    RdQuotationInvoiceRelPO quotationInvoiceRelPO = new RdQuotationInvoiceRelPO();
                                    quotationInvoiceRelPO.setId(idService.nextId());
                                    quotationInvoiceRelPO.setQuotationNo(v);
                                    quotationInvoiceRelPO.setInvoiceNo(l.getInvoiceNo());
                                    quotationInvoiceRelPO.setSystemId(l.getSystemId());
                                    quotationInvoiceRelPO.setCreatedBy(DEFAULT_USER);
                                    quotationInvoiceRelPO.setModifiedBy(DEFAULT_USER);
                                    quotationInvoiceRelPO.setCreatedDate(DateUtils.getNow());
                                    quotationInvoiceRelPO.setModifiedDate(DateUtils.getNow());
                                    list.add(quotationInvoiceRelPO);
                                }
                        );
                    }
                }
        );
        return list;
    }
}
