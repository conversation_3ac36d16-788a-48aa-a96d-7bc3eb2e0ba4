/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdConclusionDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdSampleDO {
    private String testSampleInstanceId;
    private String templateId;
    // SCI-1490
    private String reportNo;
    // add 20230720
    private String sampleNo;
    // add 20230720
    private String externalSampleNo;
    private String productItemNo;
    private List<RdProductSampleAttrDO> sampleAttrList;
    private RdConclusionDO conclusion;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
