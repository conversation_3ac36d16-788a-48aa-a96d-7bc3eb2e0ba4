package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdTestSampleDTO extends BaseModel{

    private Long rdOrderId;
    private String orderNo;
    private String testSampleInstanceId;
    private String parentTestSampleId;
    private String sampleNo;
    private String sampleName;
    private Integer sampleType;
    private Integer sampleSeq;
    private String category;
    private String description;
    private String composition;
    private String color;
    private String sampleRemark;
    private String endUse;
    private String material;
    private String otherSampleInfo;
    private Integer applicableFlag;
    private String sampleTypeLabel;
    private String categoryLabel;
    private Integer activeIndicator;
    private Date lastModifiedTimestamp;

}
