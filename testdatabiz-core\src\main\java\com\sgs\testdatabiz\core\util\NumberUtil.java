package com.sgs.testdatabiz.core.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.regex.Pattern;

public class NumberUtil {
    /**
     *
     * @param intVal1
     * @param intVal2
     * @return
     */
    public static boolean equals(final Integer intVal1, final Integer intVal2){
        return equals(intVal1, intVal2, false);
    }

    /**
     *
     * @param byteVal1
     * @param intVal2
     * @return
     */
    public static boolean equals(final Byte byteVal1, final Integer intVal2){
        if (byteVal1 == null || intVal2 == null){
            return false;
        }
        return byteVal1.byteValue() == intVal2.byteValue();
    }

    /**
     *
     * @param intVal1
     * @param intVal2
     * @param includeNull
     * @return
     */
    public static boolean equals(Integer intVal1, Integer intVal2, boolean includeNull){
        if (intVal1 == null || intVal2 == null){
            if (!includeNull){
                return false;
            }
            if (intVal1 == null){
                intVal1 = 0;
            }
            if (intVal2 == null){
                intVal2 = 0;
            }
        }
        return intVal1.intValue() == intVal2.intValue();
    }

    /**
     *
     * @param longVal1
     * @param longVal2
     * @return
     */
    public static boolean equals(final Long longVal1, final Long longVal2){
        return equals(longVal1, longVal2, false);
    }

    /**
     *
     * @param longVal1
     * @param longVal2
     * @param includeNull
     * @return
     */
    public static boolean equals(Long longVal1, Long longVal2, boolean includeNull){
        if (longVal1 == null || longVal2 == null){
            if (!includeNull){
                return false;
            }
            if (longVal1 == null){
                longVal1 = 0L;
            }
            if (longVal2 == null){
                longVal2 = 0L;
            }
        }
        return longVal1.longValue() == longVal2.longValue();
    }

    /**
     * 判断是否为数字格式不限制位数
     * @param str
     *     待校验参数
     * @return
     *     如果全为数字，返回true；否则，返回false
     */
    public static boolean isNumber(Object str){
        return (Pattern.compile("^[1-9]\\d*$")).matcher(String.valueOf(str)).matches();
    }

    /**
     *
     * @param str
     * @return
     */
    public static boolean isLetterDigit(String str) {
        String regex = "^[a-z0-9A-Z]+$";
        return str.matches(regex);
    }

    /**
     *
     * @param intValue
     * @return
     */
    public static int toInt(Integer intValue) {
        return intValue == null ? 0 : intValue.intValue();
    }

    public static int doZeroDefVal(Integer intValue, int defVal) {
        return intValue == null || intValue.intValue() <= 0 ? defVal : intValue.intValue();
    }

    public static int toInt(Object value) {
        if (value == null){
            return 0;
        }
        return toInt(value.toString(), 0);
    }

    public static boolean toBoolean(Boolean boolValue) {
        if (boolValue == null){
            return false;
        }
        return boolValue.booleanValue();
    }

    /**
     *
     * @param intVal
     * @return
     */
    public static byte toByte(final Integer intVal) {
        byte defValue = (byte)0;
        if (intVal == null){
            return defValue;
        }
        return intVal.byteValue();
    }

    /**
     *
     * @param intValue
     * @return
     */
    public static Long toLong(Integer intValue) {
        return intValue == null ? 0L : intValue.longValue();
    }


    /**
     *
     * @param longValue
     * @return
     */
    public static Long toLong(Long longValue) {
        return longValue == null ? 0L : longValue.longValue();
    }

    /**
     *
     * @param longValue
     * @param defValue
     * @return
     */
    public static Long toLong(Long longValue, long defValue) {
        return longValue == null || longValue.longValue() <= 0  ? defValue : longValue.longValue();
    }

    /**
     *
     * @param doubleValue
     * @return
     */
    public static Double toDouble(Double doubleValue) {
        return doubleValue == null ? 0 : doubleValue.doubleValue();
    }

    /**
     *
     * @param decimalValue
     * @return
     */
    public static BigDecimal toDecimal(BigDecimal decimalValue) {
        return decimalValue == null ? BigDecimal.valueOf(0) : decimalValue.abs();
    }

    /**
     *
     * @param doubleValue
     * @param defValue
     * @return
     */
    public static Double toDouble(Double doubleValue, long defValue) {
        return doubleValue == null || doubleValue.doubleValue() <= 0  ? defValue : doubleValue.doubleValue();
    }

    /**
     *
     * @param longValue
     * @param defValue
     * @return
     */
    public static Long toLong(Object longValue, long defValue) {
        return longValue == null ? defValue : toLong(longValue.toString(), 0L);
    }

    /**
     *
     * @param longValue
     * @return
     */
    public static Long toLong(String longValue) {
        return toLong(longValue, 0L);
    }

    /**
     *
     * @param str
     * @return
     */
    public static int toInt(String str) {
        return toInt(str, 0);
    }


    /**
     *
     * @param intValue
     * @param defValue
     * @return
     */
    public static int toInt(Integer intValue, Integer defValue) {
        if (intValue == null || intValue.intValue() <= 0) {
            return toInt(defValue);
        }
        return intValue.intValue();
    }

    public static long toLong(String str, long defaultValue) {
        if (str == null || StringUtils.isBlank(str)) {
            return defaultValue;
        }
        try {
            return Long.parseLong(str);
        } catch (NumberFormatException var3) {
            return defaultValue;
        }
    }

    /**
     *
     * @param str
     * @param defaultValue
     * @return
     */
    public static int toInt(String str, int defaultValue) {
        if (str == null || StringUtils.isBlank(str)) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException var3) {
            return defaultValue;
        }
    }

    /**
     *
     * @param str
     * @param defaultValue
     * @return
     */
    public static Long defaultIfBlank(Long str, Long defaultValue) {
        return NumberUtil.toLong(str) > 0 ? str : defaultValue;
    }

    /**
     *
     * @param str
     * @return
     */
    public static Float toFloat(String str) {
        return toFloat(str, null);
    }

    /**
     *
     * @param str
     * @param defaultValue
     * @return
     */
    public static Float toFloat(String str, Float defaultValue) {
        if (str == null) {
            return defaultValue;
        } else {
            try {
                return Float.parseFloat(str.replace("+",""));
            } catch (NumberFormatException var3) {
                return defaultValue;
            }
        }
    }
    public static Integer getMin(Integer intVal1, Integer intVal2){
        if (intVal1 == null || intVal1.intValue() <= 0){
            return intVal2;
        }
        if (intVal2 == null || intVal2.intValue() <= 0){
            return intVal1;
        }
        return intVal1.intValue() > intVal2.intValue() ? intVal2.intValue() : intVal1.intValue();
    }
}
