package com.sgs.testdatabiz.sdk.output.dto;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCertificateDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportCertificateDTO;
import com.sgs.testdatabiz.facade.model.enums.ReportType;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdReportDTO extends BaseModel{

    private Long labId;

    private String reportId;

    private String reportNo;

    private String originalReportNo;
    private Long actualTat;
    private Long delayDay;
    private String delayReason;
    private String reportHeader;

    private String reportAddress;

    private Integer reportStatus;

    private String reportStatusLabel;

    private String reportCertificateName;

    private String reportCreatedBy;

    private Date reportCreatedDate;

    private String reportApproveBy;

    private Date reportApproveDate;

    private Date softcopyDeliveryDate;

    private String conclusionCode;

    private String customerConclusion;

    private String reviewConclusion;

    private String reportDeliveredTo;

    private String conclusionRemark;

    private Integer activeIndicator;

    private List<RdReportLangDTO> reportLangList;
    private List<ReportCertificateDTO> reportCertificateList;
    private Date lastModifiedTimestamp;

    private String reportInstanceId;

    private String reportRemark;

    //SCI-1378
    private String acceditationRemark;
    private String reportApprovedStatement;
    private String description;
    //SCI-1378 json字符串
    private String signList;
    private ReportType reportType;
    //SubReport 的情况下追加，例如Starlims、内部分包等
    private String reportSource;
    private String testingType;
    private String countryOfDestination;
    private String metaData;

}
