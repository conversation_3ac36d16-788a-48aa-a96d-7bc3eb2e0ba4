package com.sgs.testdatabiz.facade.model.testdata.slim;

import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR> vincent
 * @version V1.0
 * @Project: sgs-otsnotes
 * @Package com.sgs.otsnotes.facade.model.po.xml
 * @Description: 扫描到xml文件解析实体
 * @date Date : 2019年06月15日 10:31
 */
@XmlRootElement(name = "JOB")
@XmlAccessorType(XmlAccessType.FIELD)
public class SlimJobInfo extends BaseModel {
	/**
	 *
	 */
	@XmlAttribute(name = "LABCODE")
	private String labCode;

	/**
	 *
	 */
	@XmlAttribute(name = "PRO_JOB")
	private String proJob;

	/**
	 *
	 */
	@XmlAttribute(name = "ORDERNO")
	private String orderNo;

	/**
	 *
	 */
	private String productLineCode;

	/**
	 *
	 */
	@XmlAttribute(name = "JOBSTATUS")
	private String jobStatus;

	/**
	 *
	 */
	@XmlAttribute(name = "COMPLETED")
	private String completed;

	/**
	 *
	 */
	@XmlAttribute(name = "APPROVED")
	private String approved;

	/**
	 *
	 */
	@XmlElement(name = "JOBBIOFIELD")
	private List<SlimJobBioInfo> jobBioFieldList;

	/**
	 *
	 */
	@XmlElement(name = "SAMPLE")
	private List<SlimSampleInfo> testSamples;

	@Override
	public String getExtId() {
		return orderNo;
	}

	public String getLabCode() {
		return labCode;
	}

	public void setLabCode(String labCode) {
		this.labCode = labCode;
	}

	public String getProJob() {
		return proJob;
	}

	public void setProJob(String proJob) {
		this.proJob = proJob;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getProductLineCode() {
		return productLineCode;
	}

	public void setProductLineCode(String productLineCode) {
		this.productLineCode = productLineCode;
	}

	public String getJobStatus() {
		return jobStatus;
	}

	public void setJobStatus(String jobStatus) {
		this.jobStatus = jobStatus;
	}

	public String getCompleted() {
		return completed;
	}

	public void setCompleted(String completed) {
		this.completed = completed;
	}

	public String getApproved() {
		return approved;
	}

	public void setApproved(String approved) {
		this.approved = approved;
	}

	public List<SlimJobBioInfo> getJobBioFieldList() {
		return jobBioFieldList;
	}

	public void setJobBioFieldList(List<SlimJobBioInfo> jobBioFieldList) {
		this.jobBioFieldList = jobBioFieldList;
	}

	public List<SlimSampleInfo> getTestSamples() {
		return testSamples;
	}

	public void setTestSamples(List<SlimSampleInfo> testSamples) {
		this.testSamples = testSamples;
	}
}
