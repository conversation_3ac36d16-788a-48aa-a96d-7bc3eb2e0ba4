/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdMethodLimitDTO implements Serializable {
    private String limitType;
    private String limitGroup;
    private String limitValueFullName;
    private RdLimitValueFullNameRelDTO limitValueFullNameRel;
    private String limitUnit;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
