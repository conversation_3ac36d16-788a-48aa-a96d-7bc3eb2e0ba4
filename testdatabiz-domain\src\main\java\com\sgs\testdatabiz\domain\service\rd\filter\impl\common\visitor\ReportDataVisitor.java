package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.visitor;

import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCertificateDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdDelayDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProcessDTO;

/**
 * 报告数据访问者接口
 */
public interface ReportDataVisitor {
    void visitReportHeader(RdReportDO header);
    void visitTestLine(RdTestLineDO testLine);
    void visitTestResult(RdTestResultDO testResult);
    void visitReportConclusion(RdReportConclusionDO conclusion);
    void visitReportMatrix(RdReportMatrixDO matrix);
    void visitReportCertificate(ReportCertificateDO certificate);
    void visitCertificate(RdCertificateDTO certificate);
    void visitConclusion(RdConclusionDO conclusion);
    void visitTestSample(RdTestSampleDO testSample);
    void visitTestSampleGroup(RdTestSampleGroupDO sampleGroup);
    void visitMaterialAttr(RdMaterialAttrDO materialAttr);
    void visitAttachment(RdAttachmentDO attachment);
    void visitConditionGroup(RdConditionGroupDO conditionGroup);
    void visitCondition(RdConditionDO condition);
    void visitTrf(RdTrfDO trf);
    void visitOrder(RdOrderDO order);
    void visitQuotation(RdQuotationDO quotation);
    void visitInvoice(RdInvoiceDO invoice);
    void visitTestResultResult(RdTestResultResultDO testResultResult);
    void visitTestLineExternal(RdTestLineExternalDO external);
    void visitWi(RdWiDO wi);
    void visitAnalyte(RdAnalyteDO analyte);
    void visitCitation(RdCitationDO citation);
    void visitPpTestLineRel(RdPpTestLineRelDO ppTestLineRel);
    void visitSpecimen(RdSpecimenDO specimen);
    void visitOrderOthers(RdOrderOthersDO others);
    void visitOrderDelay(RdDelayDTO delay);
    void visitServiceRequirement(RdServiceRequirementDO serviceRequirement);
    void visitProcess(RdProcessDTO process);
} 