/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdLabDO {

    private Integer labId;
    private String labCode;
    private Integer locationId;
    private String locationCode;
    private Integer buId;
    private String buCode;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String labName;
    private String labAddress;
    private String labTelephoneNumber;
    private String labEmail;
    private String labCountry;
    private String labCity;

}