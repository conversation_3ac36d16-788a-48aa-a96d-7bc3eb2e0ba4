package com.sgs.testdatabiz.facade.model.req.starlims;

import com.sgs.framework.core.common.PrintFriendliness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FolderStarlimsReportRsp extends PrintFriendliness {
    /**
     *
     */
    private String approvedDate;

    /**
     *
     */
    private String bu;
    private String conclusion;
    private String conclusionAlias;
    private String conclusionId;
    private List<ReceiveReportData> data;
    private String externalId;
    private String externalParentId;
    private String folderNo;
    private String languageId;
    private String reportId;
    private String reportNo;
    private String reportType;
    private String system;

}
