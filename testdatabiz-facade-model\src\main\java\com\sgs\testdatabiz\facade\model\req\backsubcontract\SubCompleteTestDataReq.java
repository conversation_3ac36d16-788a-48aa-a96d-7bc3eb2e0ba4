package com.sgs.testdatabiz.facade.model.req.backsubcontract;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;

import java.util.Date;
import java.util.List;

@ApiModel
public class SubCompleteTestDataReq extends BaseRequest {

    private String labCode;
    private String orderNo;
    private String parentOrderNo;
    private String reportNo;
    private String objectNo;
    private String externalId;
    /**
     *
     */
    private Date completedDate;
    private String externalNo;
    private List<TestDataMatrixReq> matrixDTOList;

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public List<TestDataMatrixReq> getMatrixDTOList() {
        return matrixDTOList;
    }

    public void setMatrixDTOList(List<TestDataMatrixReq> matrixDTOList) {
        this.matrixDTOList = matrixDTOList;
    }
}
