package com.sgs.testdatabiz.core.errorcode;

import com.sgs.testdatabiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorTypeEnum;

public class ErrorCodeFactory {
    public static ErrorCode createNewErrorCode(ErrorCategoryEnum errorCategory, ErrorBizModelEnum module, ErrorFunctionTypeEnum function, ErrorTypeEnum errorType) {
        // 尝试查找已存在的错误码
        ErrorCode existingError = ErrorCodeRegistry.getInstance().findErrorCode(errorCategory,module, function, errorType);
        if (existingError != null) {
            return existingError;
        } else {
            // 如果没有找到相似的错误码，则创建新的错误码
            ErrorCode errorCode = new ErrorCode(errorCategory,module, function, errorType);
            ErrorCodeRegistry.getInstance().registerErrorCode(errorCode);
            return errorCode;
        }
    }
}
