/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCancelDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdDelayDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdOrderOthersDO {

    private RdPendingDO pending;
    private RdCancelDTO cancel;
    private RdDelayDTO delay;
    private String orderRemark;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
