package com.sgs.testdatabiz.domain.service.rd.filter;

import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.rd.filter.config.FilterConfigManager;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractReportDataFilter implements ReportDataFilter {
    
    @Autowired
    protected FilterConfigManager filterConfigManager;
    
    @Override
    public ReportDataDO doFilter(ReportDataDO reportData, FilterContext context) {
        // 检查是否启用过滤器
        if (reportData != null && context != null && 
            filterConfigManager.isFilterEnabled(getFilterType(), reportData.getHeader().getSystemId())) {
            return doFilterInternal(reportData, context);
        }
        return reportData;
    }
    
    protected abstract String getFilterType();
    protected abstract ReportDataDO doFilterInternal(ReportDataDO reportData, FilterContext context);
} 