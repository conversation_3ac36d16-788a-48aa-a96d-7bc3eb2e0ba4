package com.sgs.testdatabiz.facade.model.req.rd;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ImportReportInvoiceReq extends BaseModel {

    private RdOrderDTO order;

    private String reportNo;

    private List<RdInvoiceDTO> invoiceList;

    private List<RdQuotationDTO> quotationList;


    @Override
    public String getExtId() {
        return reportNo;
    }
}
