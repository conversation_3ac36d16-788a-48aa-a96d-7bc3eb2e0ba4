/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdHeaderInput implements Serializable{

    private Integer systemId;
    private String reportId;
    private String reportNo;
    private String originalReportNo;
    private Integer reportStatus;
    private Date reportDueDate;
    private String approveBy;
    private Date approveDate;
    private Date softCopyDeliveryDate;
    private String createBy;
    private Date createDate;
    private String certificateName;
    private RdLabInput lab;
    private RdConclusionInput conclusion;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
