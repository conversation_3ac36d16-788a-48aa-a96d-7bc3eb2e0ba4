package com.sgs.testdatabiz.facade.model.req.starlims;

import com.sgs.framework.core.base.BaseRequest;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2022/11/01 0018 15:33
 */
public class ReceiveReportLanguages extends BaseRequest {
    /**
     *
     */
    private String languageId;

    /**
     * conclusionDisplay
     */
    private String conclusionAlias;

    /**
     * methodDesc
     */
    private String testMethod;

    /**
     * evaluationAlias
     */
    private String testReportName;

    /**
     * materialName
     */
    private String sampleDescription;

    /**
     * materialColor
     */
    private String sampleColor;

    /**
     * materialTexture
     */
    private String sampleMaterial;

    public String getLanguageId() {
        return languageId;
    }

    public void setLanguageId(String languageId) {
        this.languageId = languageId;
    }

    public String getConclusionAlias() {
        return conclusionAlias;
    }

    public void setConclusionAlias(String conclusionAlias) {
        this.conclusionAlias = conclusionAlias;
    }

    public String getTestMethod() {
        return testMethod;
    }

    public void setTestMethod(String testMethod) {
        this.testMethod = testMethod;
    }

    public String getTestReportName() {
        return testReportName;
    }

    public void setTestReportName(String testReportName) {
        this.testReportName = testReportName;
    }

    public String getSampleDescription() {
        return sampleDescription;
    }

    public void setSampleDescription(String sampleDescription) {
        this.sampleDescription = sampleDescription;
    }

    public String getSampleColor() {
        return sampleColor;
    }

    public void setSampleColor(String sampleColor) {
        this.sampleColor = sampleColor;
    }

    public String getSampleMaterial() {
        return sampleMaterial;
    }

    public void setSampleMaterial(String sampleMaterial) {
        this.sampleMaterial = sampleMaterial;
    }
}
