package com.sgs.testdatabiz.facade.model.enums;

public enum AnalyteTypeEnum {
    General(0,"General"),
    Conclusion(1,"Conclusion");

    private int type;
    private String text;

    public int getType() {
        return type;
    }

    public String getText() {
        return text;
    }

    AnalyteTypeEnum(int type, String text){
        this.type = type;
        this.text = text;
    }

    public static String getText(Integer analyteType) {
        AnalyteTypeEnum enu = findType(analyteType);
        return enu != null ? enu.getText() : null;
    }

    /**
     *
     * @param type
     * @param analyteType
     * @return
     */
    public static boolean check(Integer type, AnalyteTypeEnum analyteType) {
        if (type == null || analyteType == null){
            return false;
        }
        return type.intValue() == analyteType.getType();
    }

    /**
     *
     * @param analyteType
     * @return
     */
    public static AnalyteTypeEnum findType(Integer analyteType) {
        if (analyteType == null){
            return null;
        }
        for (AnalyteTypeEnum enu: values()) {
            if (enu.getType() == analyteType.intValue()){
                return enu;
            }
        }
        return null;
    }

    public static AnalyteTypeEnum findType(Integer analyteType, AnalyteTypeEnum defType) {
        AnalyteTypeEnum type = findType(analyteType);
        return type != null ? type : defType;
    }
}