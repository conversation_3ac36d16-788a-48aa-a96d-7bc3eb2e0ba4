/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCitationInput implements Serializable{

    private Integer citationId;
    private Integer citationType;
    private Integer citationVersionId;
    private Integer citationSectionId;
    private String citationSectionName;
    private String citationName;
    private String citationFullName;
    private List<RdCitationLanguageInput> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
