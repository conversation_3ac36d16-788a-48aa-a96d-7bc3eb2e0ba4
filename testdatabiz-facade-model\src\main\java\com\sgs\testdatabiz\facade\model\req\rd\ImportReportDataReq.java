package com.sgs.testdatabiz.facade.model.req.rd;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import java.util.List;
import java.util.Optional;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ImportReportDataReq extends BaseModel {


    @ApiModelProperty(value = "skipCheck", dataType = "Boolean", required = false)
    private boolean skipCheck = false;

    @ApiModelProperty(value = "trfList",dataType = "List", required = true)
    private List<RdTrfDTO> trfList;
    @ApiModelProperty(value = "orderList",dataType = "List", required = true)
    private List<RdOrderDTO> orderList;
    @ApiModelProperty(value = "reportList",dataType = "List", required = true)
    private List<RdReportDTO> reportList;
    private RdRelationshipDTO relationship;
    @ApiModelProperty(value = "testSampleList",dataType = "List", required = true)
    private List<RdTestSampleDTO> testSampleList;
    @ApiModelProperty(value = "testLineList",dataType = "List", required = true)
    private List<RdTestLineDTO> testLineList;
    @ApiModelProperty(value = "testResultList",dataType = "List", required = true)
    private List<RdTestResultDTO> testResultList;
    @ApiModelProperty(value = "reportConclusionList",dataType = "List", required = true)
    private List<RdReportConclusionDTO> reportConclusionList;
    private List<RdConditionGroupDTO> conditionGroupList;
    private List<RdQuotationDTO> quotationList;
    private List<RdInvoiceDTO> invoiceList;
    @Override
    public String getExtId() {
        if (CollectionUtils.isNotEmpty(orderList)) {
            return orderList.get(0).getOrderNo();
        }
        return super.getExtId();
    }

    public boolean isNeedCheck() {
        return ! skipCheck;
    }

    public void skipCheck() {
        this.skipCheck = true;
    }
}
