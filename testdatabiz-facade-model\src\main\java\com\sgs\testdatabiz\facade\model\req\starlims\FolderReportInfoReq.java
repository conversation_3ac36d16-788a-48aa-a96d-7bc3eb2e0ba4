package com.sgs.testdatabiz.facade.model.req.starlims;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel
public class FolderReportInfoReq extends BaseRequest {
    /**
     * SL922093207198FW
     */
    private String externalParentId;

    /**
     * GZSL2220719801GZCCL
     */
    private String externalId;

    /**
     * CAN22-0000686
     */
    private String folderNo;

    /**
     *
     */
    @JsonIgnore
    @ApiModelProperty(value = "", hidden = true)
    private Date lastModifiedTime;

    public String getExternalParentId() {
        return externalParentId;
    }

    public void setExternalParentId(String externalParentId) {
        this.externalParentId = externalParentId;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getFolderNo() {
        return folderNo;
    }

    public void setFolderNo(String folderNo) {
        this.folderNo = folderNo;
    }

    public Date getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(Date lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }
}
