package com.sgs.testdatabiz.facade.model.req.rd;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.sgs.testdatabiz.facade.model.base.BaseModel;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class BatchExportReportDataReq extends BaseModel {

    public static final String TEST_SAMPLE_LEVEL_FULL_LIST = "fullList";
    public static final String TEST_SAMPLE_LEVEL_NO_MIX_AND_SHARE_LIST = "noMixAndShareList";

    private List<String> reportNos;

    private String testSampleLevel = "fullList";

    @Override
    public String getExtId() {
        return CollectionUtils.isNotEmpty(reportNos) ? reportNos.stream().collect(Collectors.joining(",")) : null;
    }
}
