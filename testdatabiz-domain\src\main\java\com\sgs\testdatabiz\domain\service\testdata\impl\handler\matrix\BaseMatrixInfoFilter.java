package com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix;

import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.facade.model.dto.starlims.SubContractMatrixDTO;
import com.sgs.testdatabiz.facade.model.enums.MatrixSource;

/**
 * <AUTHOR>
 * @date 2024/11/27 19:31
 */
public abstract class BaseMatrixInfoFilter implements MatrixInfoFilter {

    public void setTestMatrixInfo(SubContractMatrixDTO testMatrixInfo, String matrixId, String testLineInstanceId, String testSampleId, Integer citationType, String matrixSource) {
        testMatrixInfo.setTestMatrixId(matrixId);
        testMatrixInfo.setTestSampleId(testSampleId);
        testMatrixInfo.setTestLineInstanceId(testLineInstanceId);
        testMatrixInfo.setCitationType(citationType);
        testMatrixInfo.setMatrixSource(matrixSource);
    }

    public void buildTestMatrixIndo(SubContractMatrixDTO testMatrixInfo, SubContractTestMatrixInfo subContractTestMatrixInfo) {

        String matrixId = subContractTestMatrixInfo.getMatrixId();
        String testLineInstanceId = subContractTestMatrixInfo.getTestLineInstanceId();
        String testSampleId = subContractTestMatrixInfo.getTestSampleId();
        Integer citationType = subContractTestMatrixInfo.getCitationType();
        String matrixSource = MatrixSource.ExecSystem.getSource();

        setTestMatrixInfo(testMatrixInfo, matrixId, testLineInstanceId, testSampleId, citationType, matrixSource);
    }

    public String findPpTestLineCitationKey(SubContractTestMatrixInfo subContractTestMatrixInfo) {
        if (subContractTestMatrixInfo == null) {
            return null;
        }
        return String.format("%s_%s_%s_%s", NumberUtil.toInt(subContractTestMatrixInfo.getAid()),
                NumberUtil.toInt(subContractTestMatrixInfo.getTestLineId()),
                NumberUtil.toInt(subContractTestMatrixInfo.getCitationVersionId()),
                NumberUtil.toInt(subContractTestMatrixInfo.getCitationType()));
    }

    public String findPpTestLineCitationKey(ChemPpArtifactTestLineInfoRsp chemTestLine) {
        if (chemTestLine == null) {
            return null;
        }
        return String.format("%s_%s_%s_%s", NumberUtil.toInt(chemTestLine.getAid()),
                NumberUtil.toInt(chemTestLine.getTestLineId()),
                NumberUtil.toInt(chemTestLine.getCitationVersionId()),
                NumberUtil.toInt(chemTestLine.getCitationType()));
    }


}
