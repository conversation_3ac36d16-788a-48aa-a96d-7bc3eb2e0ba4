package com.sgs.testdatabiz.domain.service.rd.manager;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataAndMatrixDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportTestResultLangMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestDataInfoMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestLineDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestResultDO;
import com.sgs.testdatabiz.domain.service.utils.convertor.TestResultBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 15:13
 */
@Slf4j
@Component
public class ReportResultManager {
    @Autowired
    private TestDataInfoExtMapper testDataInfoExtMapper;

    @Autowired
    private TestDataInfoMapper testDataInfoMapper;

    @Autowired
    private RdReportTestResultLangMapper testResultLangMapper;

    @Autowired
    private TestResultBuilder testResultBuilder;

    /**
     * 保存TestData数据
     *
     * @param testResultList
     * @param testLineList
     * @param labId
     * @param orderNo
     * @param reportNo
     * @param reportId
     * @param suffix
     * @param objectRelMap
     * @param objectRelIds
     * @param testDataMatrixList
     */
    public void saveTestDataList(
            List<RdTestResultDO> testResultList,
            List<RdTestLineDO> testLineList,
            Long labId,
            String orderNo,
            String reportNo,
            Long reportId,
            String suffix,
            Map<String, String> objectRelMap,
            List<String> objectRelIds,
            List<TestDataMatrixInfoPO> testDataMatrixList
    ) {
        // 构建testData
        Map testResultMap = testResultBuilder.buildTestDataInfoPO(
                testResultList,
                testDataMatrixList,
                testLineList,
                labId,
                objectRelMap,
                orderNo,
                reportNo,
                reportId
        );
        if (Func.isNotEmpty(testResultMap)) {
            Object testResult = testResultMap.get("testResult");
            if (Func.isNotEmpty(testResult)) {
                List<TestDataInfoPO> testDataList = (List<TestDataInfoPO>) testResult;
                if (Func.isNotEmpty(objectRelIds)) {

                    List<TestDataInfoPO> testDataInfoList = testDataInfoExtMapper.queryTestDataInfoList(objectRelIds, suffix);
                    if (Func.isNotEmpty(testDataInfoList)) {
                        Object testResultLang = testResultMap.get("testResultLang");
                        Map<Long, List<RdReportTestResultLangPO>> testResultLangMap = new HashMap<>();
                        List<RdReportTestResultLangPO> testResultLangList = new ArrayList<>();
                        if (Func.isNotEmpty(testResultLang)) {
                            testResultLangList = (List<RdReportTestResultLangPO>) testResultLang;
                            testResultLangMap = testResultLangList.stream().collect(Collectors.groupingBy(RdReportTestResultLangPO::getRdReportTestResultId));
                        }
                        Map<Long, List<RdReportTestResultLangPO>> finalTestResultLangMap = testResultLangMap;
                        testDataInfoList.forEach(dbTestData -> {
                            String dbBizVersionId = testResultBuilder.getTestDataReportMd5(dbTestData);
                            TestDataInfoPO testData = testDataList.stream().filter(td ->
                                    StringUtils.equalsIgnoreCase(td.getBizVersionId(), dbBizVersionId) ||
                                            // QA 存在直接改DB的数据，会导致DB的BizVersionId跟实际算出来的MD5不一致
                                            StringUtils.equalsIgnoreCase(td.getBizVersionId(), dbTestData.getBizVersionId())).findFirst().orElse(null);
                            if (testData != null) {
                                Long id = testData.getId();
                                List<RdReportTestResultLangPO> langPOS = finalTestResultLangMap.get(id);
                                if (Func.isNotEmpty(langPOS)) {
                                    langPOS.forEach(l -> l.setRdReportTestResultId(dbTestData.getId()));
                                }
                                testData.setId(dbTestData.getId());
                                return;
                            }
                            dbTestData.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
                            testDataList.add(dbTestData);
                        });
                        // 处理testResultLang数据
                        if (Func.isNotEmpty(testResultLangList)) {
                            List<Long> testResultIds = testResultLangList.stream().map(RdReportTestResultLangPO::getRdReportTestResultId).collect(Collectors.toList());
                            this.deleteTestResultLang(testResultIds);
                            testResultLangMapper.batchInsert(testResultLangList);
                        }
                    }
                }

                testDataInfoExtMapper.batchInsert(testDataList, suffix);
            }
        }
    }

    public List<TestDataInfoPO> getTestDataList(String suffix, Long reportId) {
        return testDataInfoExtMapper.queryTestDataInfoByReportId(reportId, suffix);
    }

    public List<RdReportTestResultLangPO> getTestResultLangList(List<Long> ids) {
        RdReportTestResultLangExample testResultLangExample = new RdReportTestResultLangExample();
        testResultLangExample.createCriteria().andRdReportTestResultIdIn(ids);
        return testResultLangMapper.selectByExample(testResultLangExample);
    }

    public void deleteTestResultLang(List<Long> testResultIds) {
        RdReportTestResultLangExample testResultLangExample = new RdReportTestResultLangExample();
        testResultLangExample.createCriteria().andRdReportTestResultIdIn(testResultIds);
        testResultLangMapper.deleteByExample(testResultLangExample);
    }

    public List<TestDataInfoPO> queryTestDataByOrderNo(@Param("suffix") String suffix, @Param("orderNo") String orderNo) {
        return testDataInfoExtMapper.queryTestDataByOrderNo(suffix,orderNo);
    }
    public List<TestDataAndMatrixDTO> queryTestDataInfoByOrderNo(@Param("suffix") String suffix, @Param("orderNo") String orderNo) {
        return testDataInfoExtMapper.queryTestDataInfoByOrderNo(suffix,orderNo);
    }

    public void emptyTestData(Long id,String suffix) {
        testDataInfoExtMapper.emptyTestResultByReport(id, suffix);
    }
}
