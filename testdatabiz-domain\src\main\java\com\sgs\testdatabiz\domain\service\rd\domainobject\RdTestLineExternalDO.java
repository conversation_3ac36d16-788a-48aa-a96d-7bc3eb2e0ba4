/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestLineExternalDO implements Serializable {
    private Integer testLineMappingId;

    // TODO 230922 未来需要移除
    private String externalId;
    // TODO 230922 未来需要移除
    private String externalCode;

    // add 230922
    private String testItemId;
    // add 230922
    private String testItemName;
    // add 230922
    private String citationId;
    // add 230922
    private String citationName;

    private Integer checkType;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
