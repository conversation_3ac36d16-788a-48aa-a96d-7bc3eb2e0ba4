package com.sgs.testdatabiz.integration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.otsnotes.facade.SubContractFacade;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractTestLineDTO;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.req.subcontract.GetSubContractInfo;
import com.sgs.otsnotes.facade.model.req.subcontract.GetSubContractInfoListReq;
import com.sgs.otsnotes.facade.model.req.subcontract.SubContractChemTestMatrixReq;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.testdatabiz.core.constant.ApiUrlConstants;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SubcontractClient {
    private static final Logger logger = LoggerFactory.getLogger(SubcontractClient.class);

    @Value("${api.base.otsnotes}")
    private String otsnotesBaseUrl;

    @Autowired
    private TokenClient tokenClient;

    public SubContractInfo getSubContractInfo(String subcontractNo) {
        try {
            String url = otsnotesBaseUrl + ApiUrlConstants.OtsNotes.SubContract.GET_SUBCONTRACT;
            
            GetSubContractInfo reqObject = new GetSubContractInfo();
            reqObject.setSubContractNo(subcontractNo);

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            String response = HttpClientUtil.postJsonHeader(url, JSONObject.toJSONString(reqObject), headers);
            
            if (Func.isEmpty(response)) {
                logger.error("SubContractClient.getSubContractInfo error, subcontractNo:{}, response is empty", subcontractNo);
                return null;
            }
            BaseResponse baseResponse = JSON.parseObject(response, new TypeReference<BaseResponse>(){});

            if (baseResponse.getStatus() != 200 || baseResponse.getData() == null) {
                logger.error("SubContractClient.getSubContractInfo error, response:{}", response);
                return null;
            }
            return (SubContractInfo)baseResponse.getData();

        } catch (Exception e) {
            logger.error("SubContractClient.getSubContractInfo exception, subcontractNo:{}", subcontractNo, e);
            return null;
        }
    }

    public List<SubContractTestLineDTO> getSubContractTestLineInfo(List<String> subcontractNos, String productLineCode) {
        try {
            String url = otsnotesBaseUrl + ApiUrlConstants.OtsNotes.SubContract.GET_TEST_LINE_INFO;
            
            GetSubContractInfoListReq reqObject = new GetSubContractInfoListReq();
            reqObject.setSubContractNos(subcontractNos);
            reqObject.setProductLineCode(productLineCode);

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            String response = HttpClientUtil.postJsonHeader(url, reqObject, headers);
            
            if (Func.isEmpty(response)) {
                logger.error("SubContractClient.getSubContractTestLineInfo error, response is empty");
                return Lists.newArrayList();
            }

            BaseResponse baseResponse = JSON.parseObject(response, new TypeReference<BaseResponse>(){});

            if (baseResponse.getStatus() != 200 || baseResponse.getData() == null) {
                logger.error("SubContractClient.getSubContractTestLineInfo error, response:{}", response);
                return Lists.newArrayList();
            }
            return JSON.parseArray(JSON.toJSONString(baseResponse.getData()), SubContractTestLineDTO.class);
            
        } catch (Exception e) {
            logger.error("SubContractClient.getSubContractTestLineInfo exception", e);
            return Lists.newArrayList();
        }
    }

    public List<ChemPpArtifactTestLineInfoRsp> getSubContractChemTestMatrixList(SubContractChemTestMatrixReq reqObject) {
        try {
            String url = otsnotesBaseUrl + ApiUrlConstants.OtsNotes.SubContract.GET_CHEM_TEST_MATRIX;
            
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            String response = HttpClientUtil.postJsonHeader(url, reqObject, headers);
            
            if (Func.isEmpty(response)) {
                logger.error("SubContractClient.getSubContractChemTestMatrixList error, response is empty");
                return Lists.newArrayList();
            }
            
            BaseResponse baseResponse = JSON.parseObject(response, new TypeReference<BaseResponse>(){});
                
            if (baseResponse.getStatus() != 200 || baseResponse.getData() == null) {
                logger.error("SubContractClient.getSubContractChemTestMatrixList error, response:{}", response);
                return Lists.newArrayList();
            }
            return JSON.parseArray(JSON.toJSONString(baseResponse.getData()), ChemPpArtifactTestLineInfoRsp.class);
            
        } catch (Exception e) {
            logger.error("SubContractClient.getSubContractChemTestMatrixList exception", e);
            return Lists.newArrayList();
        }
    }

    public List<SubContractTestMatrixInfo> getSubContractTestMatrixList(SubContractChemTestMatrixReq reqObject) {
        try {
            String url = otsnotesBaseUrl + ApiUrlConstants.OtsNotes.SubContract.GET_TEST_MATRIX;

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            String response = HttpClientUtil.postJsonHeader(url, reqObject, headers);
            
            if (Func.isEmpty(response)) {
                logger.error("SubContractClient.getSubContractTestMatrixList error, response is empty");
                return Lists.newArrayList();
            }

            BaseResponse baseResponse = JSON.parseObject(response, new TypeReference<BaseResponse>(){});

            if (baseResponse.getStatus() != 200 || baseResponse.getData() == null) {
                logger.error("SubContractClient.getSubContractTestMatrixList error, response:{}", response);
                return Lists.newArrayList();
            }
            return JSON.parseArray(JSON.toJSONString(baseResponse.getData()), SubContractTestMatrixInfo.class);
            
        } catch (Exception e) {
            logger.error("SubContractClient.getSubContractTestMatrixList exception", e);
            return Lists.newArrayList();
        }
    }

}
