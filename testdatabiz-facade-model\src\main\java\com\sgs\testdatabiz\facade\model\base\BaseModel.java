package com.sgs.testdatabiz.facade.model.base;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.MDC;

import java.io.Serializable;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 20:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseModel extends BaseRequest {
    private Long buId;
    private String buCode;
    private Long labId;
    private String labCode;
    private Long systemId;
    private String traceabilityId;
    public String getExtId() {

        // 便于日志记录，根据场景标识唯一业务ID
        return null;
    }
}
