package com.sgs.testdatabiz.dbstorages.mybatis.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2022/10/19 0019 15:49
 */
public enum ActiveIndicatorEnum {

    INACTIVE(0),

    ACTIVE(1),

    /**
     * 无奈的标识，回传的数据与Order Mapping不上的Matrix，需要保存但又不能使用，与Lenny确认
     */
    DISABLE(2);


    private Integer value;

    ActiveIndicatorEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
