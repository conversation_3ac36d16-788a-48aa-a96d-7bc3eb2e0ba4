package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdQuotationLangPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 唯一标识
     */
    private Long id;

    /**
     * rt_quotation_id BIGINT(19) 必填<br>
     * RD 报价单标识
     */
    private Long rtQuotationId;

    /**
     * language_id INTEGER(10)<br>
     * 语言标识
     */
    private Integer languageId;

    /**
     * payer_customer_name VARCHAR(300)<br>
     * 付款方公司名称
     */
    private String payerCustomerName;

    /**
     * citation_name VARCHAR(255)<br>
     * 测试标准名称
     */
    private String citationName;

    /**
     * citation_full_name VARCHAR(255)<br>
     * 测试标准名称拼接字段
     */
    private String citationFullName;

    /**
     * service_item_name VARCHAR(2000)<br>
     * 服务项目名称
     */
    private String serviceItemName;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 唯一标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 唯一标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * rt_quotation_id BIGINT(19) 必填<br>
     * 获得 RD 报价单标识
     */
    public Long getRtQuotationId() {
        return rtQuotationId;
    }

    /**
     * rt_quotation_id BIGINT(19) 必填<br>
     * 设置 RD 报价单标识
     */
    public void setRtQuotationId(Long rtQuotationId) {
        this.rtQuotationId = rtQuotationId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 获得 语言标识
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 设置 语言标识
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * payer_customer_name VARCHAR(300)<br>
     * 获得 付款方公司名称
     */
    public String getPayerCustomerName() {
        return payerCustomerName;
    }

    /**
     * payer_customer_name VARCHAR(300)<br>
     * 设置 付款方公司名称
     */
    public void setPayerCustomerName(String payerCustomerName) {
        this.payerCustomerName = payerCustomerName == null ? null : payerCustomerName.trim();
    }

    /**
     * citation_name VARCHAR(255)<br>
     * 获得 测试标准名称
     */
    public String getCitationName() {
        return citationName;
    }

    /**
     * citation_name VARCHAR(255)<br>
     * 设置 测试标准名称
     */
    public void setCitationName(String citationName) {
        this.citationName = citationName == null ? null : citationName.trim();
    }

    /**
     * citation_full_name VARCHAR(255)<br>
     * 获得 测试标准名称拼接字段
     */
    public String getCitationFullName() {
        return citationFullName;
    }

    /**
     * citation_full_name VARCHAR(255)<br>
     * 设置 测试标准名称拼接字段
     */
    public void setCitationFullName(String citationFullName) {
        this.citationFullName = citationFullName == null ? null : citationFullName.trim();
    }

    /**
     * service_item_name VARCHAR(2000)<br>
     * 获得 服务项目名称
     */
    public String getServiceItemName() {
        return serviceItemName;
    }

    /**
     * service_item_name VARCHAR(2000)<br>
     * 设置 服务项目名称
     */
    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName == null ? null : serviceItemName.trim();
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", rtQuotationId=").append(rtQuotationId);
        sb.append(", languageId=").append(languageId);
        sb.append(", payerCustomerName=").append(payerCustomerName);
        sb.append(", citationName=").append(citationName);
        sb.append(", citationFullName=").append(citationFullName);
        sb.append(", serviceItemName=").append(serviceItemName);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}