package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

@Data
public class RdOrderHeaderDTO {

    @ApiModelProperty(value = "orderNo",dataType = "string", required = true)
    protected String orderNo;
    @ApiModelProperty(value = "logicOrderNo",dataType = "string", required = true)
    protected String rootOrderNo;//logicOrderNo
    /**
     * 真实订单号,外部请求时会传rootOrderNo和orderNo来对应逻辑订单号和子订单号
     * DO内部会转换成OrderNo和realOrderNo
     * 内部DO和DTO转换时需要冗余，分包场景会需要使用
     * */
    protected String realOrderNo;
}
