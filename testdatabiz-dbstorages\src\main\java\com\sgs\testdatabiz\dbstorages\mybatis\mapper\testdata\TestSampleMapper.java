package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TestSampleMapper {
    int countByExample(TestSampleExample example);

    int deleteByExample(TestSampleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TestSamplePO record);

    int insertSelective(TestSamplePO record);

    List<TestSamplePO> selectByExampleWithBLOBs(TestSampleExample example);

    List<TestSamplePO> selectByExample(TestSampleExample example);

    TestSamplePO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TestSamplePO record, @Param("example") TestSampleExample example);

    int updateByExampleWithBLOBs(@Param("record") TestSamplePO record, @Param("example") TestSampleExample example);

    int updateByExample(@Param("record") TestSamplePO record, @Param("example") TestSampleExample example);

    int updateByPrimaryKeySelective(TestSamplePO record);

    int updateByPrimaryKeyWithBLOBs(TestSamplePO record);

    int updateByPrimaryKey(TestSamplePO record);

    int batchInsert(List<TestSamplePO> list);

    int batchUpdate(List<TestSamplePO> list);
}