package com.sgs.testdatabiz.facade.model.req.config;

import com.sgs.framework.core.common.PrintFriendliness;

public final class TestLineInfoReq extends PrintFriendliness {
    /**
     *
     */
    private Integer ppNo;
    /**
     *
     */
    private Integer testLineId;
    /**
     *
     */
    private Integer citationId;
    /**
     *
     */
    private Integer citationType;

    public Integer getPpNo() {
        return ppNo;
    }

    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public Integer getCitationType() {
        return citationType;
    }

    public void setCitationType(Integer citationType) {
        this.citationType = citationType;
    }
}
