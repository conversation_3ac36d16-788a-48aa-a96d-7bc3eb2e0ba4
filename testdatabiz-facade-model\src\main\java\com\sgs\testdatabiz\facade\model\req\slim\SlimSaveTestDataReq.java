package com.sgs.testdatabiz.facade.model.req.slim;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@ApiModel
public class SlimSaveTestDataReq extends BaseModel {

    /**
     *
     */
    private String orderNo;
    /**
     * z
     */
    private String labCode;
    /**
     *
     */
    private String reportNo;
    /**
     *
     */
    private String parentOrderNo;
    /**
     *
     */
    private String objectNo;
    /**
     *
     */
    private Date completedDate;
    /**
     *
     */
    private String externalNo;

    /**
     *
     */
    private List<TestDataItemReq> testDatas;

    @Override
    public String getExtId() {
        return StringUtils.isNotBlank(reportNo) ? reportNo : orderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public List<TestDataItemReq> getTestDatas() {
        return testDatas;
    }

    public void setTestDatas(List<TestDataItemReq> testDatas) {
        this.testDatas = testDatas;
    }
}
