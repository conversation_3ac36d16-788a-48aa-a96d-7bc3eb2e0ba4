package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportTrfRelMapper {
    int countByExample(RdReportTrfRelExample example);

    int deleteByExample(RdReportTrfRelExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportTrfRelPO record);

    int insertSelective(RdReportTrfRelPO record);

    List<RdReportTrfRelPO> selectByExample(RdReportTrfRelExample example);

    RdReportTrfRelPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportTrfRelPO record, @Param("example") RdReportTrfRelExample example);

    int updateByExample(@Param("record") RdReportTrfRelPO record, @Param("example") RdReportTrfRelExample example);

    int updateByPrimaryKeySelective(RdReportTrfRelPO record);

    int updateByPrimaryKey(RdReportTrfRelPO record);

    int batchInsert(List<RdReportTrfRelPO> list);

    int batchUpdate(List<RdReportTrfRelPO> list);
}