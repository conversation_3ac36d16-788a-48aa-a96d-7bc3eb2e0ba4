package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class TestLineAnalyteMappingInfoPO {
    /**
     * id INTEGER(10) 必填<br>
     * 
     */
    private Integer id;

    /**
     * testLineMappingId INTEGER(10) 必填<br>
     * 
     */
    private Integer testLineMappingId;

    /**
     * analyteCode VARCHAR(50) 必填<br>
     * 
     */
    private String analyteCode;

    /**
     * status INTEGER(10) 默认值[1] 必填<br>
     * 0无效，1有效
     */
    private Integer status;

    /**
     * createdBy VARCHAR(50) 必填<br>
     * 
     */
    private String createdBy;

    /**
     * createdDate TIMESTAMP(19) 必填<br>
     * 
     */
    private Date createdDate;

    /**
     * modifiedBy VARCHAR(50)<br>
     * 
     */
    private String modifiedBy;

    /**
     * modifiedDate TIMESTAMP(19)<br>
     * 
     */
    private Date modifiedDate;

    /**
     * id INTEGER(10) 必填<br>
     * 获得 
     */
    public Integer getId() {
        return id;
    }

    /**
     * id INTEGER(10) 必填<br>
     * 设置 
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * testLineMappingId INTEGER(10) 必填<br>
     * 获得 
     */
    public Integer getTestLineMappingId() {
        return testLineMappingId;
    }

    /**
     * testLineMappingId INTEGER(10) 必填<br>
     * 设置 
     */
    public void setTestLineMappingId(Integer testLineMappingId) {
        this.testLineMappingId = testLineMappingId;
    }

    /**
     * analyteCode VARCHAR(50) 必填<br>
     * 获得 
     */
    public String getAnalyteCode() {
        return analyteCode;
    }

    /**
     * analyteCode VARCHAR(50) 必填<br>
     * 设置 
     */
    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode == null ? null : analyteCode.trim();
    }

    /**
     * status INTEGER(10) 默认值[1] 必填<br>
     * 获得 0无效，1有效
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * status INTEGER(10) 默认值[1] 必填<br>
     * 设置 0无效，1有效
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * createdBy VARCHAR(50) 必填<br>
     * 获得 
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * createdBy VARCHAR(50) 必填<br>
     * 设置 
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * createdDate TIMESTAMP(19) 必填<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * createdDate TIMESTAMP(19) 必填<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modifiedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modifiedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modifiedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modifiedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", testLineMappingId=").append(testLineMappingId);
        sb.append(", analyteCode=").append(analyteCode);
        sb.append(", status=").append(status);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append("]");
        return sb.toString();
    }
}