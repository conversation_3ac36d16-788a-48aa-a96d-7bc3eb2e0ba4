package com.sgs.testdatabiz.domain.service.testdata.impl.handler.base;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.extsystem.facade.model.customer.rsp.CustomerConfigRsp;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.rsp.matrix.SubContractTestMatrixRsp;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.rsp.customer.CustomerSimplifyInfoRsp;
import com.sgs.testdatabiz.core.enums.ConclusionType;
import com.sgs.testdatabiz.core.exception.ReportDataCheckException;
import com.sgs.testdatabiz.core.util.ListUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestLineAnalyteExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMatrixExtPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.domain.service.ReadXmlLogService;
import com.sgs.testdatabiz.domain.service.ReportTestDataService;
import com.sgs.testdatabiz.domain.service.testdata.TestDataHandler;
import com.sgs.testdatabiz.domain.service.testdata.enums.ErrMsgFormatter;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.TableNameChecker;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.condition.Condition;
import com.sgs.testdatabiz.domain.service.testdata.impl.email.SimpleEMailAlarmService;
import com.sgs.testdatabiz.domain.service.testdata.model.ErrorMsg;
import com.sgs.testdatabiz.domain.service.utils.ErrorMsgUtils;
import com.sgs.testdatabiz.domain.service.utils.SourceTypeUtils;
import com.sgs.testdatabiz.domain.service.utils.TestDataImportContext;
import com.sgs.testdatabiz.facade.model.enums.AnalyteTypeEnum;
import com.sgs.testdatabiz.facade.model.info.ExternalInfo;
import com.sgs.testdatabiz.facade.model.req.config.TestLineInfoReq;
import com.sgs.testdatabiz.facade.model.rsp.TestLineAnalyteMappingRsp;
import com.sgs.testdatabiz.facade.model.rsp.config.CheckTestLineMappingRsp;
import com.sgs.testdatabiz.facade.model.testdata.*;
import com.sgs.testdatabiz.integration.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nullable;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.sgs.testdatabiz.domain.service.testdata.enums.ErrMsgFormatter.*;

/**
 * @param <Input>
 * <AUTHOR>
 */
public abstract class AbstractTestDataHandler<Input> implements TestDataHandler<Input> {
    private static final Logger logger = LoggerFactory.getLogger(AbstractTestDataHandler.class);
    private static final String ERROR_MSG_KEY = "errorMsgList";
    @Autowired
    private ReportTestDataService reportTestDataService;
    @Autowired
    private SimpleEMailAlarmService mailAlarmService;
    @Autowired
    private TestMatrixClient testMatrixClient;
    @Autowired
    private TestDataMatrixInfoExtMapper testDataMatrixInfoExtMapper;
    @Autowired
    private TestLineAnalyteExtMapper testLineAnalyteExtMapper;
    @Autowired
    private CustomerConfClient customerConfClient;
    @Autowired
    private LocaliLayerClient localiLayerClient;
    @Autowired
    private PreOrderClient preOrderClient;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private ReadXmlLogService readXmlLogService;
    @Autowired
    private TestDataObjectRelExtMapper testDataReportObjectRelExtMapper;
    @Autowired
    private TableNameChecker tableNameChecker;
    /**
     * rawData --> ReportTestDataInfo build逻辑
     *
     * @param rawData 原始数据
     * @return ReportTestDataInfo
     */
    @Override
    public final BaseResponse<ReportTestDataInfo> buildData(Input rawData) {
        BaseResponse<ReportTestDataInfo> response = BaseResponse.newFailInstance(ResponseCode.FAIL);
        if (rawData == null) {
            return response;
        }
        ReportTestDataInfo reportTestDataInfo = null;
        try {
            // init context
            setChannel(getChannel());
            logger.info("channel:[{}] start buildData.", getChannel());

            // 1.原始检测数据业务校验
            boolean validated = this.inputValidate(rawData);
            if (!validated) {
                logger.error("buildData inputValidate err. {}", getClass().getName());
                return response;
            }

            // 2.原始数据转换成标准数据对象
            reportTestDataInfo = this.inputBuild(rawData);
            if (reportTestDataInfo == null) {
                logger.error("buildData inputBuild err. {}", getClass().getName());
                return response;
            }
            //2.1 checkTabelName是否存在
            tableNameChecker.checkTableNameExist(reportTestDataInfo.getLabCode());
            // 3.校验业务数据
            boolean checkPass = this.checkData(reportTestDataInfo);
            if (checkPass) {
                response.setStatus(ResponseCode.SUCCESS.getCode());
                response.setMessage(ResponseCode.SUCCESS.getMessage());
                response.setData(reportTestDataInfo);
                return response;
            } else {
                logger.error("channel:[{}] checkData fail.", getChannel());
            }
            logger.info("channel:[{}] buildData finish.", getChannel());

            return response;
        } catch (Exception e) {
            logger.error("buildData err.", e);
            response.setStatus(ResponseCode.UNKNOWN.getCode());
            response.setMessage(e.getMessage());
            return response;
        } finally {
            // attach errMsg
            String errMsg = formatErrMsg(getErrorMsg());
            if (!response.isSuccess() && StringUtils.isNotEmpty(errMsg)) {
                response.setMessage(errMsg);
            }
            //todo 改成本地事件模式

            // 邮件告警错误信息(异步)
            mailAlarmService.asyncSendEMailIfy(reportTestDataInfo, errMsg);
            // 记录错误信息到数据库(异步)
            readXmlLogService.asyncBatchSaveLog(reportTestDataInfo, errMsg);

            // clear context
            this.clearContext();
        }
    }

    /**
     * 具体数据处理方法
     *
     * @param reportTestDataInfo 原始输入数据
     * @return 处理结果
     */
    @Override
    public final BaseResponse<Void> importData(ReportTestDataInfo reportTestDataInfo) {
        logger.info("TestData.importData: [{}]", Func.isEmpty(reportTestDataInfo) ? null : JSONObject.toJSONString(reportTestDataInfo));
        BaseResponse<Void> response = BaseResponse.newFailInstance(ResponseCode.FAIL);

        try {
            String suffix = StringUtil.getTestDataSuffix(reportTestDataInfo.getLabCode());
            tableNameChecker.checkTableNameExist(reportTestDataInfo.getLabCode());
            // init context
            setChannel(SourceTypeUtils.toSourceTypeEnum(reportTestDataInfo.getSourceType()));

            // 1.check ReportTestDataInfo
            boolean checkFailed = !checkData(reportTestDataInfo);
            if (checkFailed) {
                response.setStatus(ResponseCode.ILLEGAL_ARGUMENT.getCode());
                response.setMessage("参数校验失败");
                return response;
            }

            // 2.save ReportTestDataInfo
            boolean saveSuccess = this.saveReportTestData(reportTestDataInfo);
            if (saveSuccess) {
                return new BaseResponse(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage());
            } else {
                response.setStatus(ResponseCode.ILLEGAL_ARGUMENT.getCode());
                response.setMessage("保存testData失败");
                return response;
            }
        } catch (Exception e) {
            logger.error("importData err.", e);
            if(e instanceof ReportDataCheckException){
                ReportDataCheckException reportDataCheckException = (ReportDataCheckException) e;
                return BaseResponse.fail(ResponseCode.ILLEGAL_ARGUMENT.getCode(), reportDataCheckException.getMessage());
            }
            return BaseResponse.newFailInstance(ResponseCode.UNKNOWN);
        } finally {
            // attach errMsg
            String errMsg = formatErrMsg(getErrorMsg());
            if (!response.isSuccess() && StringUtils.isNotEmpty(errMsg)) {
                response.setMessage(errMsg);
            }
            //todo 改成本地事件模式

            // 邮件告警错误信息(异步)
            mailAlarmService.asyncSendEMailIfy(reportTestDataInfo, errMsg);
            // 记录错误信息到数据库(异步)
            readXmlLogService.asyncBatchSaveLog(reportTestDataInfo, errMsg);

            // clear context
            this.clearContext();
        }
    }

    private void setChannel(SourceTypeEnum sourceType) {
        TestDataImportContext.setChannel(sourceType);
    }

    /**
     * 返回当前渠道
     *
     * @return 当前数据来源渠道
     */
    protected @Nullable
    final SourceTypeEnum currentChannel() {
        return TestDataImportContext.getChannel();
    }

    /**
     * 格式化错误消息
     * 目前只支持返回string
     * 对重写开放，需要自定义格式可以重写
     *
     * @param errorMsgList 错误消息列表
     * @return 返回一个格式化后的String
     */
    protected String formatErrMsg(Collection<ErrorMsg> errorMsgList) {
        return ErrorMsgUtils.defaultFormat(TestDataImportContext.getChannel(), errorMsgList);
    }

    /**
     * @param errType
     * @param externalCode
     * @param testLineId
     * @param errorMsg
     */
    protected final void putErrorMsg(ErrMsgFormatter errType, String externalCode, Integer testLineId, String errorMsg) {
        if (errType == null) {
            return;
        }
        /**
         * Scheme Code、TestLineId
         * 当Scheme Code为空时，则取默认取TestLineId
         */
        String key = externalCode;
        if (StringUtils.isBlank(key) && NumberUtil.toInt(testLineId) > 0) {
            key = testLineId.toString();
        }
        putErrorMsg(new ErrorMsg(errType.getCode(), StringUtils.defaultString(key), StringUtils.defaultString(errorMsg)));
    }

    /**
     * 把构建过程中的错误信息加入到context
     *
     * @param errorMsg
     */
    public final void putErrorMsg(ErrorMsg errorMsg) {
        Object o = TestDataImportContext.getContext().get(ERROR_MSG_KEY);
        if (o == null) {
            LinkedHashSet<ErrorMsg> errorMsgList = new LinkedHashSet<>();
            errorMsgList.add(errorMsg);
            TestDataImportContext.getContext().put(ERROR_MSG_KEY, errorMsgList);
        } else {
            LinkedHashSet<ErrorMsg> errorMsgList = (LinkedHashSet<ErrorMsg>) o;
            errorMsgList.add(errorMsg);
        }
    }

    protected final Collection<ErrorMsg> getErrorMsg() {
        Object o = TestDataImportContext.getContext().get(ERROR_MSG_KEY);
        return (o == null) ? Collections.emptyList() : (LinkedHashSet<ErrorMsg>) o;
    }

    private void clearContext() {
        TestDataImportContext.clear();
    }

    /**
     * 校验输入参数的有效性
     *
     * @param inputData 原始输入数据
     * @return
     */
    protected abstract boolean inputValidate(Input inputData);

    /**
     * 转换对接系统的结构到标准结构
     *
     * @param inputData 原始输入数据
     * @return
     */
    protected abstract ReportTestDataInfo inputBuild(Input inputData);

    /**
     * 校验数据
     *
     * @param reqObject
     * @return
     */
    private boolean checkData(ReportTestDataInfo reqObject) {

        // TODO POSL-5099 2023-05-17 临时处理 暂不校验，业务逻辑校验在执行系统处理，RD校验或许补充
        SourceTypeEnum channel = TestDataImportContext.getChannel();
        if (channel != null && channel.check(SourceTypeEnum.SUBCONTRACT, SourceTypeEnum.ENTERSUBCONTRACT)) {
            return true;
        }

        // ---------------------------------start------------------------------
        //fixme 以下代码临时解决问题，暂时没有完善“根据不同condition匹配不同策略组”的逻辑
        // 部分相关代码在com.sgs.testdatabiz.domain.service.testdata.impl.check.strategy包下

        // 针对slim/startlims HL只需要做部分空值校验
        // slim HL
        Condition slimHLCondition = Condition.builder()
                .and(rtd -> Objects.equals(rtd.getSourceType(), SourceTypeEnum.SLIM.getCode()))
                .and(rtd -> Objects.equals(rtd.getProductLineCode(), ProductLineType.HL.getProductLineAbbr()))
                .build();
        // starlims HL
        Condition starLimsHLCondition = Condition.builder()
                .and(rtd -> Objects.equals(rtd.getSourceType(), SourceTypeEnum.STARLIMS.getCode()))
                .and(rtd -> Objects.equals(rtd.getProductLineCode(), ProductLineType.HL.getProductLineAbbr()))
                .build();

        /*if (slimHLCondition.matched(reportTestData) || starLimsHLCondition.matched(reportTestData)){
            return new BasicParamsCheckStrategy().check(reportTestData, this::putErrorMsg);
        }*/
        // ----------------------------------end--------------------------------

        List<TestDataTestMatrixInfo> testMatrixs = reqObject.getTestMatrixs();
        if (testMatrixs == null || testMatrixs.isEmpty()) {
            logger.error("checkData fail, testMatrixs is empty ,externalNo:{} ,subContractNo:{}", reqObject.getExternalNo(), reqObject.getSubContractNo());
            return false;
        }
        // 获取SubContract上的TestMatrix
        List<SubContractTestMatrixRsp> subContractTestMatrixs = this.getSubContractTestMatrixList(reqObject.getSubContractNo(), reqObject.getProductLineCode(), reqObject.getLabCode());

        Set<Integer> testLineMappingIds = testMatrixs.stream().filter(tm -> NumberUtil.toInt(tm.getTestLineMappingId()) > 0).map(tm -> tm.getTestLineMappingId()).collect(Collectors.toSet());
        // 获取SLIM 与TestLine Mapping关系
        Map<Integer, Set<String>> testLineAnalyteMappingMaps = this.getTestLineAnalyteInfoList(testLineMappingIds);

        // 获取Customer Config TestLine Mapping 规则
        @Nullable CustomerConfigRsp customerConfig = customerConfClient.getCustomerConfigByOrderNo(reqObject.getOrderNo(), reqObject.getProductLineCode());
        Set<String> alwaysSplits = this.getTestLineMappingList(reqObject, testMatrixs, (customerConfig == null ? null : customerConfig.getRefSystemId()));
        // TODO 根据指定客户来Check规则
        boolean isCustomerCheck = this.isCustomerCheck(reqObject.getOrderNo(), customerConfig);
        boolean isVerifyMatrix = !(slimHLCondition.matched(reqObject) || starLimsHLCondition.matched(reqObject));
        // add end

        // 数据库中的Matrix数据
        Map<String, TestDataMatrixExtPO> dbTestMatrixMaps = Maps.newConcurrentMap();

        // 处理LabCode（GZ SL）转SL_GZ
        ExternalInfo external = this.getExternalInfo(reqObject);
        Set<String> externalObjectNos = external.getExternalObjectNos();

        List<TestDataMatrixExtPO> testDataMatrixInfoList = testDataMatrixInfoExtMapper.getTestDataMatrixInfoList(external.getOrderNo(), external.getSuffix());
        testDataMatrixInfoList.forEach(tm -> {
            if (StringUtils.isNotBlank(tm.getExternalObjectNo())) {
                // 如果ExternalObjectNo有值，且是当前回传的，则直接忽略，否则要参与Hash计算
                if (!externalObjectNos.isEmpty() && externalObjectNos.contains(tm.getExternalObjectNo())) {
                    return;
                }
            }
            String bizVersionId = reportTestDataService.getTestDataReportMatrixMd5(tm);
            dbTestMatrixMaps.put(bizVersionId, tm);
        });
        Set<Integer> testMatrixHashs = Sets.newHashSet();
        String externalKey = String.format("%s_%s", reqObject.getExternalNo(), StringUtils.defaultString(reqObject.getExternalObjectNo()));

        //TODO ZHIZHI check matrixId 重复
        if (isCustomerCheck && isVerifyMatrix) {
            boolean checked = this.checkChannelData(reqObject, subContractTestMatrixs, testDataMatrixInfoList);
            if (!checked) {
                return checked;
            }
        }

        for (TestDataTestMatrixInfo testMatrix : testMatrixs) {
            // add this judge by vincent 2023年4月10日 , rely on Michael Liu.
            if (!isCustomerCheck) {
                // DIG-8617
//                testMatrix.setPpVersionId(0);
                /*continue;*/
            }
            // DIG-8562
            int testMatrixHash = testMatrix.hashCode();
            if (isCustomerCheck) {
                if (StringUtils.isBlank(testMatrix.getTestSampleNo())) {
                    putErrorMsg(SAMPLE_NO, null, null, testMatrix.getExternalident());
                    continue;
                }
                // 校验 Analyte Code
                // todo starlims tlmappingid为空，所以没有明确加hl判断
                if (!this.checkAnalyteCode(testLineAnalyteMappingMaps, testMatrix)) {
                    continue;
                }

                // TODO 目前除了HL的不需要验证Matrix，其他需要
                if (isVerifyMatrix) {
                    int ppNo = NumberUtil.toInt(testMatrix.getPpNo());
                    //TODO ZHIZHI 是否需要在这里增加check matrix+condition？getTestMatrixInfo 没有看到matrix condition的判断，无法公共check

                    // Test Matrix
                    SubContractTestMatrixRsp subContractTestMatrix = this.getTestMatrixInfo(subContractTestMatrixs, testMatrixs, testMatrix, alwaysSplits);
                    if (subContractTestMatrix == null) {
                        logger.error("在Order中没有找到对应的 Matrix（{}_{}_{}）", ppNo, testMatrix.getAid(), testMatrix.getTestLineId());
                        // putErrorMsg(ORDER_TEST_MATRIX,testMatrix.getTestMatrixId(), testMatrix.getTestLineId(), testMatrix.getTestSampleNo());
                        continue;
                    }
                    testMatrix.setTestMatrixId(subContractTestMatrix.getTestMatrixId());
                }
            }

            String externalCode = StringUtils.defaultString(testMatrix.getExternalCode());
            if (testMatrixHashs.contains(testMatrixHash)) {
                // TODO 暂时验证SHEIN客户，其他只发邮件（DIG-8621）
                if (isCustomerCheck) {
                    putErrorMsg(TEST_MATRIX, externalCode, testMatrix.getTestLineId(), testMatrix.getTestSampleNo());
                    continue;
                }
            }

            // 检查Matrix是否在订单中已存在
            TestDataMatrixInfoPO testDataMatrix = reportTestDataService.getTestDataMatrixInfo(null, testMatrix);
            TestDataMatrixExtPO dbTestDataMatrix = dbTestMatrixMaps.get(testDataMatrix.getBizVersionId());
            if (dbTestDataMatrix != null) {
                String dbExternalKey = String.format("%s_%s", dbTestDataMatrix.getExternalNo(), StringUtils.defaultString(dbTestDataMatrix.getExternalObjectNo()));
                // 当Matrix相同时，判定是在自己的Report里，还是另一个Report，如果是另一个Report则要记录异常
                if (!StringUtils.equalsIgnoreCase(dbExternalKey, externalKey)) {
                    // TODO 暂时验证SHEIN客户，其他只发邮件（DIG-8621）
                    if (isCustomerCheck) {
                        putErrorMsg(ORDER_TEST_MATRIX, externalCode, testMatrix.getTestLineId(), testMatrix.getTestSampleNo());
                        continue;
                    }
                }
            }

            // 校验Test Data数据
            if (isCustomerCheck && this.checkTestData(testMatrix)) {
                continue;
            }
            testMatrixHashs.add(testMatrixHash);
        }
        if (testMatrixs.isEmpty()) {
            logger.error("Order中没有对应的 Matrix（{}_{}_{}）", reqObject.getOrderNo(), reqObject.getSubContractNo(), reqObject.getExternalNo());
            return false;
        }
        Collection<ErrorMsg> errorMsg = this.getErrorMsg();

        return errorMsg.isEmpty();
    }

    /**
     * 校验各个Channel 各自的数据有效性
     *
     * @param testMatrixs
     * @param subContractTestMatrixs
     * @param testDataMatrixInfoList
     * @return
     */
    protected abstract boolean checkChannelData(ReportTestDataInfo testMatrixs, List<SubContractTestMatrixRsp> subContractTestMatrixs, List<TestDataMatrixExtPO> testDataMatrixInfoList);


    /**
     * @param errMsg
     * @param testMatrix
     * @return
     */
    private String addTestMatrixError(ErrMsgFormatter errMsg, TestDataTestMatrixInfo testMatrix) {
        return String.format("ErrorType：%s, PpVersionId_%s，ExternalCode_%s，TestLineId_%s，TestSampleNo_%s，CitationId_%s，CitationVersionId_%s",
                errMsg.getCode(),
                testMatrix.getPpVersionId(),
                StringUtils.defaultString(testMatrix.getExternalCode()),
                testMatrix.getTestLineId(),
                testMatrix.getTestSampleNo(),
                testMatrix.getCitationId(),
                testMatrix.getCitationVersionId()
        );
    }

    /**
     * @param reqObject
     * @return
     */
    private ExternalInfo getExternalInfo(ReportTestDataInfo reqObject) {
        ExternalInfo external = new ExternalInfo();
        external.setOrderNo(reqObject.getOrderNo());
        external.setSuffix(StringUtil.getTestDataSuffix(reqObject.getLabCode()));

        Set<String> externalObjectNos = Sets.newHashSet();
        Set<String> externalNos = Sets.newHashSet(reqObject.getExternalNo());
        if (StringUtils.isNotBlank(reqObject.getOriginalReportNo())) {
            TestDataObjectRelPO rel = new TestDataObjectRelPO();
            rel.setReportNo(reqObject.getReportNo());
            rel.setObjectNo(reqObject.getSubContractNo());
            rel.setExternalNo(reqObject.getExternalNo());
            rel.setSourceType(reqObject.getSourceType());
            // 原报告编号 目前只限于StarLims：取originalReportNo
            rel.setExternalObjectNo(reqObject.getOriginalReportNo());
            TestDataObjectRelPO objectRel = testDataReportObjectRelExtMapper.getReportObjectRelInfo(rel);
            if (objectRel != null) {
                if (StringUtils.isNotBlank(objectRel.getExternalNo())) {
                    externalNos.add(objectRel.getExternalNo());
                }
                if (StringUtils.isNotBlank(objectRel.getExternalObjectNo())) {
                    externalObjectNos.add(objectRel.getExternalObjectNo());
                }
            }
        }
        external.setExternalNos(externalNos);
        external.setExternalObjectNos(externalObjectNos);

        return external;
    }

    /**
     * @param orderNo
     * @param customerConf
     * @return
     */
    private boolean isCustomerCheck(String orderNo, CustomerConfigRsp customerConf) {
        if (customerConf == null) {
            return false;
        }
        logger.info("orderNo:{},获取customer配置，refCheck:{}", orderNo, customerConf.getRefCheck());
        return RefCheckEnum.check(customerConf.getRefCheck(), RefCheckEnum.CHECK_SLIM);
    }

    /**
     * 校验Test Matrix数据
     *
     * @param subContractTestMatrixs
     * @param testMatrixs
     * @param testMatrix
     * @param alwaysSplits
     * @return
     */
    private SubContractTestMatrixRsp getTestMatrixInfo(List<SubContractTestMatrixRsp> subContractTestMatrixs, List<TestDataTestMatrixInfo> testMatrixs, TestDataTestMatrixInfo testMatrix, Set<String> alwaysSplits) {
        String externalCode = testMatrix.getExternalCode();
        if (subContractTestMatrixs == null || subContractTestMatrixs.isEmpty()) {
            logger.error("在Order中没有找到SCH_CODE:{} 对应的 TestLine，请确认是否有添加该TestLine，或者缺少该TestLine和Scheme的Mapping关系", externalCode);
            putErrorMsg(SCHEME, externalCode, testMatrix.getTestLineId(), null);
            return null;
        }
        // 加externalCode是因为不同的externalCode会Mapping到相同的TL（DIG-8628）
        Predicate<SubContractTestMatrixRsp> predicate = tm -> (NumberUtil.equals(tm.getTestLineId(), testMatrix.getTestLineId()));

        Set<SubContractTestMatrixRsp> orderSamples = subContractTestMatrixs.stream().filter(predicate).collect(Collectors.toSet());
        if (orderSamples == null || orderSamples.isEmpty()) {
            logger.error("在Order中没有找到SCH_CODE:{} 对应的 TestLine，请确认是否有添加该TestLine，或者缺少该TestLine和Scheme的Mapping关系", externalCode);
            putErrorMsg(SCHEME, externalCode, testMatrix.getTestLineId(), null);
            return null;
        }
        String extSampleNo = testMatrix.getTestSampleNo();

        // 允许复测样，Slim 中存在的Sample，且在Order中也存在的Sample
        if (alwaysSplits != null && orderSamples.stream().filter(orderSample -> alwaysSplits.contains(String.format("%s_%s", orderSample.getTestLineId(), orderSample.getCitationId()))).count() > 0) {
            // 允许回传：Order Sample（C1,C1+C2）、SLIM （C1,C1+C2）
            if (subContractTestMatrixs.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getSampleNo(), extSampleNo)).count() > 0) {
                // 如果订单中不存在Matrix，分包中的Matrix如果存在则忽略
                if (orderSamples.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getSampleNo(), extSampleNo)).count() > 0) {
                    return this.getTestMatrixInfo(subContractTestMatrixs, orderSamples, testMatrix, false);
                }
            }
        }
        // TestSampleNo，true(是否在订单中)
        Map<String, Boolean> orderSampleNos = Maps.newConcurrentMap();
        // 回传的SampleNo是否存在订单中
        if (orderSamples.stream().filter(sample -> StringUtils.equalsIgnoreCase(sample.getSampleNo(), extSampleNo)).count() > 0) {
            orderSampleNos.put(extSampleNo, false);
        }
        // 订单中的SampleNo都加到列表中
        orderSamples.forEach(sample -> {
            orderSampleNos.put(sample.getSampleNo(), false);
        });
        // 订单中的Mix样的SampleNo拆分，再校验是否在订单中存在
        final String mixSymbol = "+";
        boolean isMixSample = StringUtils.containsIgnoreCase(extSampleNo, mixSymbol);
        if (isMixSample) {
            for (String mixSampleNo : StringUtils.split(extSampleNo, mixSymbol)) {
                if (!orderSampleNos.containsKey(mixSampleNo) && orderSamples.stream().filter(sample -> StringUtils.equalsIgnoreCase(sample.getSampleNo(), mixSampleNo)).count() > 0) {
                    orderSampleNos.put(mixSampleNo, false);
                }
            }
        }
        Set<String> notTestSampleNos = Sets.newHashSet();
        testMatrixs.forEach(tm -> {
            if (!((StringUtils.isBlank(externalCode) && StringUtils.isBlank(tm.getExternalCode())) || StringUtils.equalsIgnoreCase(tm.getExternalCode(), externalCode))) {
                return;
            }
            if (!NumberUtil.equals(tm.getTestLineId(), testMatrix.getTestLineId())) {
                return;
            }
            if (!orderSampleNos.containsKey(tm.getTestSampleNo())) {
                notTestSampleNos.add(tm.getTestSampleNo());
                return;
            }
            orderSampleNos.replace(tm.getTestSampleNo(), true);
        });
        Set<String> testSampleNos = Sets.newHashSet();
        orderSampleNos.entrySet().forEach(sampleNo -> {
            if (sampleNo.getValue().booleanValue()) {
                return;
            }
            testSampleNos.add(sampleNo.getKey());
        });
        if (testSampleNos.size() <= 0 && testSampleNos.size() == notTestSampleNos.size()) {
            return this.getTestMatrixInfo(subContractTestMatrixs, orderSamples, testMatrix, false);
        }
        /**
         * 不允许回传：Order Sample（C1+C2）、SLIM （C1，C2，C1+C2）
         * 允许回传  ：Order Sample（C1，C2）、SLIM （C1+C2）需要拆分成C1，C2
         * 允许回传  ：Order Sample（C1，C2）、SLIM （C1，C2，C1+C2）
         */
        if (testSampleNos.size() <= 0 && notTestSampleNos.size() > 0) {
            // 允许回传  ：Order Sample（C1，C2）、SLIM （C1，C2，C1+C2）
            if (this.checkMixSampleNo(subContractTestMatrixs, orderSamples, notTestSampleNos)) {
                return this.getTestMatrixInfo(subContractTestMatrixs, orderSamples, testMatrix, isMixSample);
            }
            logger.error("在Order中没有找到SCH_CODE:{}和DESCRIPTION_1:{}的Assign关系", externalCode, StringUtils.join(notTestSampleNos, "、"));
            putErrorMsg(SCHEME_ASSIGN_SAMPLE, externalCode, testMatrix.getTestLineId(), StringUtils.join(notTestSampleNos, "、"));
            return null;
        }

        if (testSampleNos.size() > 0 && notTestSampleNos.size() <= 0) {
            logger.error("SCH_CODE：{} 下缺少 {}", externalCode, extSampleNo); // SLIM：externalCode、Starlims：TestLineId
            // DIG-8700 SCHEME 只展示订单中有的
            // putErrorMsg(SCHEME, externalCode, testMatrix.getTestLineId(), StringUtils.join(testSampleNos, "，"));
            return null;
        }
        /**
         * Order Sample：1+2+3、4+5+6
         * Ext   Sample：A1+A2+A3、A4+A5+A6、4、5、6
         */
        if (testSampleNos.size() > 0 && notTestSampleNos.size() > 0 && !testSampleNos.containsAll(notTestSampleNos)) {
            // 允许回传  ：Order Sample（C1，C2，C3）、SLIM （C1，C2，C3，C1+C2）
            if (this.checkMixSampleNo(subContractTestMatrixs, orderSamples, notTestSampleNos)) {
                return this.getTestMatrixInfo(subContractTestMatrixs, orderSamples, testMatrix, isMixSample);
            }
            putErrorMsg(SCHEME_ASSIGN_SAMPLE, externalCode, testMatrix.getTestLineId(), StringUtils.join(notTestSampleNos, "，"));
            return null;
        }
        return this.getTestMatrixInfo(subContractTestMatrixs, orderSamples, testMatrix, false);
    }

    /**
     * @param subContractTestMatrixs
     * @param orderSamples
     * @param notTestSampleNos
     * @return
     */
    private boolean checkMixSampleNo(List<SubContractTestMatrixRsp> subContractTestMatrixs, Set<SubContractTestMatrixRsp> orderSamples, Set<String> notTestSampleNos) {
        notTestSampleNos.removeIf(notTestSampleNo -> {
            Set<String> mixTestSampleNos = this.getMixSampleNos(notTestSampleNo);
            // 如果小于等于1非Mix样
            if (mixTestSampleNos.size() <= 1) {
                return false;
            }
            mixTestSampleNos.removeIf(mixSampleNo -> {
                return subContractTestMatrixs.stream().filter(sample -> StringUtils.equalsIgnoreCase(sample.getSampleNo(), mixSampleNo)).count() > 0 &&
                        orderSamples.stream().filter(sample -> StringUtils.equalsIgnoreCase(sample.getSampleNo(), mixSampleNo)).count() > 0;
            });
            return mixTestSampleNos.isEmpty();
        });
        return notTestSampleNos.isEmpty();
    }

    /**
     * @param extSampleNo
     * @return
     */
    private Set<String> getMixSampleNos(String extSampleNo) {
        Set<String> mixSampleNos = Sets.newHashSet();
        final String mixSymbol = "+";
        if (StringUtils.isBlank(extSampleNo) || !StringUtils.containsIgnoreCase(extSampleNo, mixSymbol)) {
            return mixSampleNos;
        }
        for (String mixSampleNo : StringUtils.split(extSampleNo, mixSymbol)) {
            mixSampleNos.add(mixSampleNo);
        }
        return mixSampleNos;
    }

    /**
     * @param subContractTestMatrixs
     * @param orderSamples
     * @param testMatrix
     * @param isNewMixMatrix
     * @return
     */
    private SubContractTestMatrixRsp getTestMatrixInfo(List<SubContractTestMatrixRsp> subContractTestMatrixs, Set<SubContractTestMatrixRsp> orderSamples, TestDataTestMatrixInfo testMatrix, boolean isNewMixMatrix) {
        int ppNo = NumberUtil.toInt(testMatrix.getPpNo());
        //TODO ZHIZHI
        SubContractTestMatrixRsp subContractTestMatrix = ListUtils.findFirst(subContractTestMatrixs, tm -> {
            if (!NumberUtil.equals(tm.getPpNo(), ppNo)) {
                return false;
            }
            if (!NumberUtil.equals(tm.getTestLineId(), testMatrix.getTestLineId())) {
                return false;
            }
            if (!NumberUtil.equals(tm.getCitationId(), testMatrix.getCitationId())) {
                return false;
            }
            if (!NumberUtil.equals(tm.getCitationVersionId(), testMatrix.getCitationVersionId())) {
                return false;
            }
            return StringUtils.equalsIgnoreCase(tm.getTestSampleId(), testMatrix.getTestSampleId()) && StringUtils.equalsIgnoreCase(tm.getSampleNo(), testMatrix.getTestSampleNo());
        });
        if (subContractTestMatrix != null) {
            return subContractTestMatrix;
        }
        if (!isNewMixMatrix) {
            logger.error("在Order中没有找到对应的 Matrix（{}_{}_{}）", ppNo, testMatrix.getAid(), testMatrix.getTestLineId());
            // TODO DIG-8617 如果订单上存在需要上报异常
            if (orderSamples.stream().filter(orderSample -> SampleType.check(orderSample.getSampleType(), SampleType.MixSample) && StringUtils.equalsIgnoreCase(orderSample.getSampleNo(), testMatrix.getTestSampleNo())).count() > 0) {
                putErrorMsg(SCHEME_ASSIGN_SAMPLE, StringUtils.defaultString(testMatrix.getExternalCode()), testMatrix.getTestLineId(), testMatrix.getTestSampleNo());
            }
            return subContractTestMatrix;
        }
        /**
         * TODO 目前只针对SHEIN订单加的规则
         * 订单C2，C3 - > C2，C3，C2+C3
         * 这里的C2+C3存到RTD里不会有TestMatrixId
         */
        subContractTestMatrix = new SubContractTestMatrixRsp();
        subContractTestMatrix.setTestMatrixId(testMatrix.getTestMatrixId());
        subContractTestMatrix.setTestLineId(testMatrix.getTestLineId());
        subContractTestMatrix.setCitationId(testMatrix.getCitationId());
        subContractTestMatrix.setCitationVersionId(testMatrix.getCitationVersionId());
        subContractTestMatrix.setTestSampleId(testMatrix.getTestSampleId());
        subContractTestMatrix.setSampleNo(testMatrix.getTestSampleNo());

        return subContractTestMatrix;
    }

    /**
     * @param testLineMappingIds
     * @return
     */
    private Map<Integer, Set<String>> getTestLineAnalyteInfoList(Set<Integer> testLineMappingIds) {
        Map<Integer, Set<String>> testLineAnalyteMappingMaps = Maps.newHashMap();
        if (testLineMappingIds == null || testLineMappingIds.isEmpty()) {
            return testLineAnalyteMappingMaps;
        }
        // 0无效，1有效
        List<TestLineAnalyteMappingRsp> testLineAnalytes = testLineAnalyteExtMapper.getTestLineAnalyteMappingList(testLineMappingIds, 1);
        if (testLineAnalytes == null) {
            testLineAnalytes = Lists.newArrayList();
        }
        testLineAnalytes.forEach(analyte -> {
            Integer testLineMappingId = analyte.getTestLineMappingId();
            if (!testLineAnalyteMappingMaps.containsKey(testLineMappingId)) {
                testLineAnalyteMappingMaps.put(testLineMappingId, Sets.newLinkedHashSet());
            }
            testLineAnalyteMappingMaps.get(testLineMappingId).add(analyte.getAnalyteCode());
        });
        return testLineAnalyteMappingMaps;
    }

    /**
     * @param testMatrix
     * @return
     */
    private boolean checkTestData(TestDataTestMatrixInfo testMatrix) {
        List<TestDataResultInfo> testResults = testMatrix.getTestResults();
        if (testResults == null || testResults.isEmpty()) {
            return true;
        }
        testResults.removeIf(tr -> {
            String externalCode = testMatrix.getExternalCode();
            if (StringUtils.isBlank(externalCode)) {
                externalCode = StringUtils.EMPTY;
            }
            // TestValue 必须有值
            if (StringUtils.isBlank(tr.getTestValue())) {
                putErrorMsg(TEST_VALUE, externalCode, testMatrix.getTestLineId(), tr.getAnalyteCode());
                return true;
            }

            // region 【Analyte Name中文必须有值】
            List<TestDataResultLangInfo> languages = tr.getLanguages();
            if (languages == null || languages.isEmpty()) {
                putErrorMsg(ANALYTE_NAME, externalCode, testMatrix.getTestLineId(), tr.getAnalyteCode());
                return true;
            }
            for (TestDataResultLangInfo language : languages) {
                LanguageType languageType = LanguageType.findLanguageId(language.getLanguageId());
                if (languageType == null || !languageType.check(LanguageType.Chinese.getLanguageId())) {
                    continue;
                }
                if (StringUtils.isBlank(language.getTestAnalyteName())) {
                    putErrorMsg(ANALYTE_NAME, externalCode, testMatrix.getTestLineId(), tr.getAnalyteCode());
                    return true;
                }
            }

            // endregion
            return false;
        });
        return testResults.isEmpty();
    }

    /**
     * @param reqObject
     * @param testMatrixs
     * @return
     */
    private Set<String> getTestLineMappingList(ReportTestDataInfo reqObject, List<TestDataTestMatrixInfo> testMatrixs, Integer refSystemId) {
        Set<String> alwaysSplits = Sets.newHashSet();
        if (testMatrixs == null || testMatrixs.isEmpty()) {
            return alwaysSplits;
        }
        List<TestLineInfoReq> testLines = Lists.newArrayList();
        for (TestDataTestMatrixInfo testMatrix : testMatrixs) {
            TestLineInfoReq testLine = new TestLineInfoReq();
            testLine.setPpNo(testMatrix.getPpNo());
            testLine.setTestLineId(testMatrix.getTestLineId());
            testLine.setCitationId(testMatrix.getCitationId());
            testLine.setCitationType(testMatrix.getCitationType());

            testLines.add(testLine);
        }
        if (testLines.isEmpty()) {
            return alwaysSplits;
        }
        BaseResponse<CustomerSimplifyInfoRsp> customerResult = customerClient.getCustomerInfo(reqObject.getOrderNo(), reqObject.getProductLineCode());
        CustomerSimplifyInfoRsp customer = customerResult.getData();
        if (customer == null) {
            // TODO Error Log
            return alwaysSplits;
        }
        com.sgs.framework.core.base.BaseResponse<List<CheckTestLineMappingRsp>> rspResult = localiLayerClient.getTestLineMappingList(customer.getCustomerGroupCode(), refSystemId, testLines);
        List<CheckTestLineMappingRsp> testLineMappings = rspResult.getData();

        if (testLineMappings == null || testLineMappings.isEmpty()) {
            return alwaysSplits;
        }
        OrderInfoDto order = preOrderClient.getOrderInfoByOrderNo(reqObject.getOrderNo(), reqObject.getProductLineCode());
        if (order == null) {
            return alwaysSplits;
        }
        String postfix = StringUtils.EMPTY;
        if (order != null && StringUtils.isNotBlank(order.getPostfix())) {
            postfix = order.getPostfix().toLowerCase();
        }
        String finalPostfix = postfix;
        testLineMappings.stream()
                .filter(item -> item.getSplitMixSetting() != null
                        && SpliteMixConclusionEnum.check(
                        item.getSplitMixSetting().getOrDefault(finalPostfix, item.getSplitMixSetting().get("default")),
                        SpliteMixConclusionEnum.NOT_SPLIT))
                .collect(Collectors.toList()).forEach(item -> {
                    //alwaysSplits.add(String.format("%s_%s_%s_%s", NumberUtil.toInt(item.getPpNo()), item.getTestLineId(), item.getCitationId(), item.getCitationType()));
                    alwaysSplits.add(String.format("%s_%s", item.getTestLineId(), item.getCitationId()));
                });
        return alwaysSplits;
    }

    // endregion

    // region 【校验 Analyte Code】

    /**
     * 校验 Analyte Code
     *
     * @param testLineAnalyteMappingMaps
     * @param testDataMatrix
     * @return
     */
    private boolean checkAnalyteCode(Map<Integer, Set<String>> testLineAnalyteMappingMaps, TestDataTestMatrixInfo testDataMatrix) {
        int testLineMappingId = NumberUtil.toInt(testDataMatrix.getTestLineMappingId());
        if (testLineMappingId <= 0 || testLineAnalyteMappingMaps == null || testLineAnalyteMappingMaps.isEmpty()) {
            return true;
        }
        List<TestDataResultInfo> testResults = testDataMatrix.getTestResults();
        if (testResults == null || testResults.isEmpty()) {
            return true;
        }
        Set<String> testLineAnalyteCodes = testLineAnalyteMappingMaps.get(testLineMappingId);
        if (testLineAnalyteCodes == null || testLineAnalyteCodes.isEmpty()) {
            return true;
        }
        Set<String> analyteCodes = testResults.stream()
                .filter(td ->
                        StringUtils.isNotBlank(td.getAnalyteCode()) &&
                                AnalyteTypeEnum.check(td.getAnalyteType(), AnalyteTypeEnum.General))
                .map(td -> td.getAnalyteCode())
                .collect(Collectors.toSet());
        if (analyteCodes.isEmpty()) {
            return true;
        }
        Set<String> lackAnalyteCodes = Sets.newHashSet();
        testLineAnalyteCodes.forEach(analyteCode -> {
            if (analyteCodes.contains(analyteCode)) {
                return;
            }
            lackAnalyteCodes.add(analyteCode);
        });
        if (lackAnalyteCodes.isEmpty()) {
            testResults.removeIf(td -> {
                if (!AnalyteTypeEnum.check(td.getAnalyteType(), AnalyteTypeEnum.General)) {
                    return true;
                }
                return !testLineAnalyteCodes.contains(td.getAnalyteCode());
            });
            return true;
        }
        putErrorMsg(ANALYTE_CODE, testDataMatrix.getExternalCode(), null, String.format("%s-%s", testDataMatrix.getTestSampleNo(), StringUtils.join(lackAnalyteCodes, "、")));
        return false;
    }
    // endregion

    /**
     * 保存标准TestData
     *
     * @param reqObject
     * @return
     */
    private boolean saveReportTestData(ReportTestDataInfo reqObject) {
        return reportTestDataService.saveTestData(reqObject).isSuccess();
    }

    // region 【订单 Test Matrix】

    /**
     * 获取SubContract上的TestMatrix
     *
     * @param subContractNo
     * @param productLineCode
     * @param labCode
     * @return
     */
    protected List<SubContractTestMatrixRsp> getSubContractTestMatrixList(String subContractNo, String productLineCode, String labCode) {
        // fixme job是否通用？
        List<SubContractTestMatrixRsp> rspResult = testMatrixClient.getSubContractTestMatrixList(subContractNo, productLineCode, labCode);
        if (Func.isEmpty(rspResult)) {
            logger.error("SubContractTestMatrixList_SubContractNo_{}，labCode_{} 返回为空.", subContractNo, labCode);
        }
        List<SubContractTestMatrixRsp> subContractTestMatrixs = rspResult;
        if (subContractTestMatrixs == null) {
            return Lists.newArrayList();
        }
        return subContractTestMatrixs;
    }

    // endregion

}
