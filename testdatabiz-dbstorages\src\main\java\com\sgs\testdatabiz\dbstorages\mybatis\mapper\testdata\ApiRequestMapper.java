package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ApiRequestMapper {
    int countByExample(ApiRequestExample example);

    int deleteByExample(ApiRequestExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ApiRequestPO record);

    int insertSelective(ApiRequestPO record);

    List<ApiRequestPO> selectByExample(ApiRequestExample example);

    ApiRequestPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ApiRequestPO record, @Param("example") ApiRequestExample example);

    int updateByExample(@Param("record") ApiRequestPO record, @Param("example") ApiRequestExample example);

    int updateByPrimaryKeySelective(ApiRequestPO record);

    int updateByPrimaryKey(ApiRequestPO record);

    int batchInsert(List<ApiRequestPO> list);

    int batchUpdate(List<ApiRequestPO> list);
}