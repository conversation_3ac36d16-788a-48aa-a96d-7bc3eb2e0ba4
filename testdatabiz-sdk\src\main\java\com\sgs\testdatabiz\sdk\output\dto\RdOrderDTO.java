package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdOrderDTO extends BaseModel{
    private Integer systemId;
    private String labCode;
    private Long labId;
    private String kaCustomerDeptCode;
    private Integer toDmFlag;
    private String locationCode;
    private String buCode;
    private String orderId;
    private String rootOrderNo;//对应的logicOrderNo
    private String orderNo;//执行系统的子单OrderNo
    private Date createdDate;
    private String createdBy;
    private String oldOrderNo;
    private String bossOrderNo;
    private String csName;
    private String responsibleTeamCode;
    private String orderType;
    private String idbLab;
    private String quoteCurrencyId;
    private Date sampleConfirmDate;
    private Integer orderStatus;
    private Integer serviceLevel;
    private Integer selfTestFlag;
    private Integer operationType;
    private Integer tat;
    private Date serviceStartDate;
    private BigDecimal totalAmount;
    private String remark;
    private String applicantName;
    private Long applicantBossNo;
    private String payerName;
    private Long payerBossNo;
    private String buyerName;
    private Long buyerBossNo;
    private String buyerGroupName;
    private String buyerGroupCode;
    private String agentName;
    private Long agentBossNumber;
    private String agentGroupName;
    private String agentGroupCode;
    private Date sampleReceivedDate;
    private Long actualTat;
    private Long delayDay;
    private Date caseDueDate;
    private String delayReason;
    private String reasonsForOnHold;
    private String reasonsForCancelOrder;
    private String kaCustomerDeptCodeLabel;
    private String orderTypeLabel;
    private String orderStatusLabel;
    private String serviceLevelLabel;
    private Integer activeIndicator;
    private String productCategory;
    private String productSubCategory;
    private String parcelNo;

    private String orderInstanceId;

    private Date lastModifiedTimestamp;
    //SCI-1378
    private String topsLabId;
    private String execLabCode;
}
