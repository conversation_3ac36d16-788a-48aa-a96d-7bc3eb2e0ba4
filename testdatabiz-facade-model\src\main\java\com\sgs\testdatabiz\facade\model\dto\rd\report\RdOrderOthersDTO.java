/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdOrderOthersDTO implements Serializable {

    private RdPendingDTO pending;
    private RdCancelDTO cancel;
    private RdDelayDTO delay;
    private String orderRemark;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
