<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.ReadXmlLogExtMapper" >

    <sql id="Base_Column_List" >
        id, ProductLineId, LabId, FileName, CloudId, NewFileName, SlimJobNo,
        OrderNo, SubcontractNo, SubReportNo, LogCode, LogRecord, SystemId,CreatedDate, ModifiedDate
    </sql>


    <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ReadXmlLogPO" >
        INSERT INTO tb_read_xml_log (
        <include refid="Base_Column_List" />
        )
        VALUES
        (
        #{id}
        ,#{productLineId}
        ,#{labId}
        ,#{fileName}
        ,#{cloudId}
        ,#{newFileName}
        ,#{slimJobNo}
        ,#{orderNo}
        ,#{subcontractNo}
        ,#{subReportNo}
        ,#{logCode}
        ,#{logRecord}
        ,#{systemId}
        ,#{createdDate}
        ,#{modifiedDate}
        )
    </insert>

    <insert id="batchInsert" >
        INSERT INTO tb_read_xml_log (
        <include refid="Base_Column_List" />
        )
        VALUES
        <foreach collection="xmlLogList" item="xmlLog" separator=",">
            (
            #{xmlLog.id}
            ,#{xmlLog.productLineId}
            ,#{xmlLog.labId}
            ,#{xmlLog.fileName}
            ,#{xmlLog.cloudId}
            ,#{xmlLog.newFileName}
            ,#{xmlLog.slimJobNo}
            ,#{xmlLog.orderNo}
            ,#{xmlLog.subcontractNo}
            ,#{xmlLog.subReportNo}
            ,#{xmlLog.logCode}
            ,#{xmlLog.logRecord}
            ,#{xmlLog.systemId}
            ,#{xmlLog.createdDate}
            ,#{xmlLog.modifiedDate}
            )
        </foreach>
    </insert>

    <select id="queryReadXmlLog" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.ReadXmlLogPO">
        select <include refid="Base_Column_List" />
        from tb_read_xml_log
        <where>
            <if test="orderNo != null and orderNo != '' ">
                and OrderNo = #{orderNo}
            </if>
            <if test="subcontractNo != null and subcontractNo != '' ">
                and SubcontractNo = #{subcontractNo}
            </if>
            <if test="slimJobNo != null and slimJobNo != '' ">
                and  SlimJobNo = #{slimJobNo}
            </if>
        </where>
    </select>


    <update id="updateLogInfo" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.ReadXmlLogPO">
        UPDATE tb_read_xml_log
        SET ProductLineId = #{productLineId}
          ,ModifiedDate = #{modifiedDate}
        WHERE id = #{id}
    </update>

</mapper>