# Report Data (RD) 系统

## 项目概述
Report Data (RD) 是SGS的业务数据存储服务系统，提供报告数据的全生命周期管理服务。系统支持Report的创建、状态变更，并对外提供数据导入与查询能力。RD系统主要包含主数据和分包数据两部分，为业务系统提供完整的数据服务支持。

## 项目结构
本项目采用模块化设计，主要包含以下模块：

- **testdatabiz-bootstrap**：项目启动引导模块
- **testdatabiz-core**：核心公共组件模块
- **testdatabiz-dbstorages**：数据存储与访问层模块
- **testdatabiz-domain**：领域服务与业务逻辑模块
- **testdatabiz-facade**：对外服务接口定义模块
- **testdatabiz-facade-impl**：服务接口实现模块（核心交会模块）
- **testdatabiz-facade-model**：接口模型定义模块
- **testdatabiz-integration**：外部系统集成模块
- **testdatabiz-mybatis-generator**：MyBatis代码生成模块
- **testdatabiz-sdk**：SDK工具包模块（供RDC使用）
- **testdatabiz-test**：测试支持模块
- **testdatabiz-web**：Web应用入口模块

详细的项目结构说明请参考[项目结构说明](doc/项目结构.md)。

## 业务视角
RD系统是报表数据管理的核心系统，提供以下关键业务功能：
- 报告创建与状态管理
- 报告数据的导入与导出
- 报告数据的存储与查询
- 发票和报价单数据的管理
- 检测数据的转换与存储

详细业务功能说明请参考[业务功能说明](doc/业务功能说明.md)。

## 技术视角
项目采用SpringBoot框架构建，基于DDD（领域驱动设计）理念进行领域建模和业务逻辑组织，主要技术栈包括：
- Java 8+
- Spring Boot
- MyBatis
- Redis（Redisson）
- Dubbo（服务暴露）
- FastJSON
- Lombok

详细技术架构说明请参考[技术架构说明](doc/技术架构说明.md)。

## 数据视角
RD系统管理多种业务数据实体，包括：
- 报告数据（Report）
- 发票数据（Invoice）
- 报价单数据（Quotation）
- 订单数据（Order）
- 检测数据（TestData）

详细数据模型说明请参考[数据模型说明](doc/数据模型说明.md)。

## 部署视角
本系统采用标准的SpringBoot应用部署方式，支持Docker容器化部署。

详细部署说明请参考[部署说明](doc/部署说明.md)。

## 核心模块说明

### testdatabiz-sdk
为RDC（Report Data Client）提供的SDK工具包，用于将RD中的标准数据转换成RDC可识别的数据结构。

### testdatabiz-web 和 testdatabiz-facade
系统的两个主要入口：
- **testdatabiz-web**：提供主数据的import和query功能
- **testdatabiz-facade**：作为分包数据（starlims）导入的入口

### testdatabiz-facade-impl
作为controller和dubbo服务的核心交会包，所有请求汇总到此模块进行处理。主要实现类包括：
- **ReportDataBizServiceImpl**：报告数据业务服务实现，处理报告数据的导入、导出和状态管理
- **ReportTestDataFacadeImpl**：报告检测数据门面实现，处理不同来源系统的原始数据转换
- **ReportTestDataServiceImpl**：报告检测数据服务实现，处理标准格式检测数据的导入

## 文档索引
- [业务功能说明](doc/业务功能说明.md)
- [技术架构说明](doc/技术架构说明.md)
- [数据模型说明](doc/数据模型说明.md)
- [部署说明](doc/部署说明.md)
- [API接口文档](doc/API接口文档.md)

### 核心服务API文档
- [ReportDataBizServiceImpl接口文档](doc/facade/ReportDataBizServiceImpl.md)
- [ReportTestDataFacadeImpl接口文档](doc/facade/ReportTestDataFacadeImpl.md)
- [ReportTestDataServiceImpl接口文档](doc/facade/ReportTestDataServiceImpl.md)

更多详细文档，请查看[doc](doc/)目录。