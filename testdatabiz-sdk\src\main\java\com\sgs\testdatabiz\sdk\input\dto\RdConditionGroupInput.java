/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdConditionGroupInput implements Serializable{
    // add 20230529
    private String orderNo;
    // add 20230529
    private Integer systemId;
    private String conditionGroupId;
    private String combinedConditionDescription;
    private String requirement;
    private List<RdPpConditionGroupInput> ppConditionGroupList;
    private List<RdConditionGroupLanguageInput> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
