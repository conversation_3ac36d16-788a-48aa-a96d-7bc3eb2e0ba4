package com.sgs.testdatabiz.core.errorcode;

import com.sgs.testdatabiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorTypeEnum;

import java.util.concurrent.ConcurrentHashMap;

public class ErrorCodeRegistry {
    private static volatile ErrorCodeRegistry INSTANCE; // volatile 关键字确保可见性
    protected static final ConcurrentHashMap<String, ErrorCode> registry = new ConcurrentHashMap<>();
    private static final String PREFIX = "SCI";
    private static final String SEPARATOR = ".";
    private ErrorCodeRegistry() {}
    private boolean initialized = false;

    public static ErrorCodeRegistry getInstance() {
        if (INSTANCE == null) { // 第一次检查
            synchronized (ErrorCodeRegistry.class) {
                if (INSTANCE == null) { // 第二次检查
                    INSTANCE = new ErrorCodeRegistry();
                }
            }
        }
        return INSTANCE;
    }

    public void registerErrorCode(ErrorCode errorCode) {
        registry.putIfAbsent(errorCode.getCode(), errorCode);
    }

    public ErrorCode getErrorCode(String code) {
        return registry.get(code);
    }

    public static void main(String[] args) {
        String code = "SCI.400.040104";
        System.out.println(code.split("\\.")[0]);
//        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
//        System.out.println(errorCode.getCode());
        ErrorCodeRegistry registry = ErrorCodeRegistry.getInstance();
        System.out.println("desc="+registry.getErrorCodeDesc(code));
    }
    public ErrorCode findErrorCode(ErrorCategoryEnum errorCategory, ErrorBizModelEnum module, ErrorFunctionTypeEnum function, ErrorTypeEnum errorType) {
        String code = generateCode(errorCategory,module, function, errorType);
        return registry.get(code);
    }
    public static String generateCode(ErrorCategoryEnum errorCategory, ErrorBizModelEnum module, ErrorFunctionTypeEnum function, ErrorTypeEnum errorType) {
        //按照模块、功能、错误类型生成错误码，code组装成一个6位数字字符串
        String codeDetail = module.getCode() + function.getCode() + errorType.getCode();
        return PREFIX+SEPARATOR+errorCategory.getCode()+SEPARATOR + codeDetail;
    }

    public String getErrorCodeDesc(String code) {
        if (!initialized && registry.isEmpty()) {
            initializeErrorCodes();
            initialized = true;
        }
        ErrorCode errorCode = registry.get(code);
        if (errorCode == null) {
            // 动态加载当前错误码
            ErrorCategoryEnum category = ErrorCategoryEnum.fromCode(extractCategoryCode(code));
            ErrorBizModelEnum model = ErrorBizModelEnum.fromCode(extractModelCode(code));
            ErrorFunctionTypeEnum function = ErrorFunctionTypeEnum.fromCode(extractFunctionCode(code));
            ErrorTypeEnum type = ErrorTypeEnum.fromCode(extractTypeError(code));
            errorCode = new ErrorCode(category, model, function, type);
            registerErrorCode(errorCode);
        }
        return errorCode.getDescription();
    }

    private void initializeErrorCodes() {
        // 假设这里有一个枚举或配置文件定义了所有错误码
        for (ErrorCategoryEnum category : ErrorCategoryEnum.values()) {
            for (ErrorBizModelEnum model : ErrorBizModelEnum.values()) {
                for (ErrorFunctionTypeEnum function : ErrorFunctionTypeEnum.values()) {
                    for (ErrorTypeEnum type : ErrorTypeEnum.values()) {
                        ErrorCode errorCode = new ErrorCode(category, model, function, type);
                        registerErrorCode(errorCode);
                    }
                }
            }
        }
    }
    private String extractCategoryCode(String code) {
        String[] parts = code.split("\\.");
        return parts[1];
    }


    private String extractModelCode(String code) {
        String[] parts = code.split("\\.");
        String detail = parts[2];
        return detail.substring(0, 2);
    }

    private String extractFunctionCode(String code) {
        String[] parts = code.split("\\.");
        String detail = parts[2];
        return detail.substring(2, 4);
    }

    private String extractTypeError(String code) {
        String[] parts = code.split("\\.");
        String detail = parts[2];
        return detail.substring(4, 6);
    }
}
