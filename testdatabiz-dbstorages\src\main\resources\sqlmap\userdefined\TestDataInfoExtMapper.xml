<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" >
        <id column="Id" property="id" jdbcType="BIGINT" />
        <result column="ObjectRelId" property="objectRelId" jdbcType="VARCHAR" />
        <result column="TestDataMatrixId" property="testDataMatrixId" jdbcType="BIGINT" />
        <result column="TestMatrixId" property="testMatrixId" jdbcType="VARCHAR" />
        <result column="AnalyteId" property="analyteId" jdbcType="VARCHAR" />
        <result column="AnalyteName" property="analyteName" jdbcType="VARCHAR" />
        <result column="AnalyteType" property="analyteType" jdbcType="INTEGER" />
        <result column="AnalyteCode" property="analyteCode" jdbcType="VARCHAR" />
        <result column="AnalyteSeq" property="analyteSeq" jdbcType="INTEGER" />
        <result column="ReportUnit" property="reportUnit" jdbcType="VARCHAR" />
        <result column="TestValue" property="testValue" jdbcType="VARCHAR" />
        <result column="CasNo" property="casNo" jdbcType="VARCHAR" />
        <result column="ReportLimit" property="reportLimit" jdbcType="VARCHAR" />
        <result column="LimitUnit" property="limitUnit" jdbcType="VARCHAR" />
        <result column="ConclusionId" property="conclusionId" jdbcType="VARCHAR" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="INTEGER" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
        <result column="rd_report_id" property="rdReportId" jdbcType="BIGINT" />
        <result column="lab_id" property="labId" jdbcType="BIGINT" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
        <result column="test_result_full_name" property="testResultFullName" jdbcType="VARCHAR" />
        <result column="test_result_seq" property="testResultSeq" jdbcType="INTEGER" />
        <result column="result_value" property="resultValue" jdbcType="VARCHAR" />
        <result column="result_value_remark" property="resultValueRemark" jdbcType="VARCHAR" />
        <result column="result_unit" property="resultUnit" jdbcType="VARCHAR" />
        <result column="fail_flag" property="failFlag" jdbcType="TINYINT" />
        <result column="limit_value_full_name" property="limitValueFullName" jdbcType="VARCHAR" />
    </resultMap>

  <resultMap id="ResultMapWithBLOBs" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" extends="BaseResultMap" >
    <result column="Position" property="position" jdbcType="LONGVARCHAR" />
    <result column="Languages" property="languages" jdbcType="LONGVARCHAR" />
  </resultMap>

    <sql id="Base_Column_List" >
        Id, ObjectRelId, TestDataMatrixId, TestMatrixId, AnalyteId, AnalyteName,`Position`, AnalyteType,
    AnalyteCode, AnalyteSeq, ReportUnit, TestValue, CasNo, ReportLimit, LimitUnit, ConclusionId,Languages,
    ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, BizVersionId,
    rd_report_id, lab_id, order_no, report_no, test_result_full_name, test_result_seq,
    result_value, result_value_remark, result_unit, fail_flag, limit_value_full_name
    </sql>

    <sql id="Base_Column_List_Select" >
        Id, ObjectRelId, TestDataMatrixId, TestMatrixId, AnalyteId, AnalyteName,`Position`, AnalyteType,
    AnalyteCode, AnalyteSeq, ReportUnit, TestValue, CasNo, ReportLimit, LimitUnit, ConclusionId,Languages,
    ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, BizVersionId,
    rd_report_id as rdReportId, lab_id as labId, order_no as orderNo, report_no as reportNo, test_result_full_name as testResultFullName, test_result_seq as testResultSeq,
    result_value as resultValue, result_value_remark as resultValueRemark, result_unit as resultUnit, fail_flag as failFlag, limit_value_full_name as limitValueFullName
    </sql>

  <sql id="Blob_Column_List" >
    `Position`, Languages
  </sql>

  <insert id="batchInsert">
    INSERT INTO `tb_test_data_info_${suffix}` (
        <include refid="Base_Column_List" />
    )
    VALUES
    <foreach collection="testDatas" item="testData" separator=",">
    (
        #{testData.id},
        #{testData.objectRelId},
        #{testData.testDataMatrixId},
        #{testData.testMatrixId},
        #{testData.analyteId},
        #{testData.analyteName},
        #{testData.position},
        #{testData.analyteType},
        #{testData.analyteCode},
        #{testData.analyteSeq},
        #{testData.reportUnit},
        #{testData.testValue},
        #{testData.casNo},
        #{testData.reportLimit},
        #{testData.limitUnit},
        #{testData.conclusionId},
        #{testData.languages},
        #{testData.activeIndicator},
        #{testData.createdBy},
        #{testData.createdDate},
        #{testData.modifiedBy},
        #{testData.modifiedDate},
        #{testData.bizVersionId},
        #{testData.rdReportId},
     #{testData.labId},
     #{testData.orderNo},
        #{testData.reportNo},
     #{testData.testResultFullName},
     #{testData.testResultSeq},
        #{testData.resultValue},
     #{testData.resultValueRemark},
     #{testData.resultUnit},
        #{testData.failFlag},
     #{testData.limitValueFullName}
    )
    </foreach>
    ON DUPLICATE KEY UPDATE
        TestMatrixId = VALUES(testMatrixId),
        TestDataMatrixId = VALUES(testDataMatrixId),
        AnalyteId = VALUES(analyteId),
        AnalyteName = VALUES(analyteName),
        `Position` = VALUES(`Position`),
        AnalyteType = VALUES(analyteType),
        AnalyteCode = VALUES(analyteCode),
        AnalyteSeq = VALUES(analyteSeq),
        ReportUnit = VALUES(reportUnit),
        TestValue = VALUES(testValue),
        CasNo = VALUES(casNo),
        ReportLimit = VALUES(reportLimit),
        LimitUnit = VALUES(limitUnit),
        ConclusionId = VALUES(conclusionId),
        Languages = VALUES(languages),
        ActiveIndicator = VALUES(activeIndicator),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy),
        rd_report_id = VALUES(rd_report_id),
        lab_id = VALUES(lab_id),
        order_no = VALUES(order_no),
        report_no = VALUES(report_no),
        test_result_full_name = VALUES(test_result_full_name),
        test_result_seq = VALUES(test_result_seq),
        result_value = VALUES(result_value),
        result_value_remark = VALUES(result_value_remark),
        result_unit = VALUES(result_unit),
        fail_flag = VALUES(fail_flag),
        limit_value_full_name = VALUES(limit_value_full_name)
  </insert>

    <insert id="batchInsertSlim">
        INSERT INTO `tb_test_data_info` (
        <include refid="Base_Column_List" />
        )
        VALUES
        <foreach collection="testDatas" item="testData" separator=",">
            (
            #{testData.id},
            #{testData.objectRelId},
            #{testData.testDataMatrixId},
            #{testData.testMatrixId},
            #{testData.analyteId},
            #{testData.analyteName},
            #{testData.position},
            #{testData.analyteType},
            #{testData.analyteCode},
            #{testData.analyteSeq},
            #{testData.reportUnit},
            #{testData.testValue},
            #{testData.casNo},
            #{testData.reportLimit},
            #{testData.limitUnit},
            #{testData.conclusionId},
            #{testData.languages},
            #{testData.activeIndicator},
            #{testData.createdBy},
            #{testData.createdDate},
            #{testData.modifiedBy},
            #{testData.modifiedDate},
            #{testData.bizVersionId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        TestMatrixId = VALUES(testMatrixId),
        TestDataMatrixId = VALUES(testDataMatrixId),
        AnalyteId = VALUES(analyteId),
        AnalyteName = VALUES(analyteName),
        `Position` = VALUES(`Position`),
        AnalyteType = VALUES(analyteType),
        AnalyteCode = VALUES(analyteCode),
        AnalyteSeq = VALUES(analyteSeq),
        ReportUnit = VALUES(reportUnit),
        TestValue = VALUES(testValue),
        CasNo = VALUES(casNo),
        ReportLimit = VALUES(reportLimit),
        LimitUnit = VALUES(limitUnit),
        ConclusionId = VALUES(conclusionId),
        Languages = VALUES(languages),
        ActiveIndicator = VALUES(activeIndicator),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy)
    </insert>
    <delete id="emptyTestResultByReport">
        delete from `tb_test_data_info_${suffix}` where rd_report_id = #{rdReportId}
    </delete>

    <select id="selectBySubcontractRelId" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO">
    select
        <include refid="Base_Column_List_Select" />
    from `tb_test_data_info_${suffix}`
    where ActiveIndicator = 1
    and  ObjectRelId IN
    <foreach collection="objectRelIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

  <select id="queryTestDataInfo" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO">
    select
    <include refid="Base_Column_List_Select" />
    from `tb_test_data_info_${suffix}`
    where objectRelId = #{objectRelId}
  </select>
  <select id="queryTestDataInfoByReportId" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO">
    select
    <include refid="Base_Column_List_Select" />
    from `tb_test_data_info_${suffix}`
    where rd_report_id = #{reportId} and ActiveIndicator = 1
  </select>

  <select id="queryTestDataInfoList" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO">
    select
    <include refid="Base_Column_List_Select" />
    from `tb_test_data_info_${suffix}`
    where ActiveIndicator = 1 and ObjectRelId IN
      <foreach collection="objectRelIds" item="item" separator="," open="(" close=")">
          #{item}
      </foreach>
  </select>

    <select id="getTestDataInfoList" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO">
        SELECT DISTINCT
            Id
            ,TestDataMatrixId
            ,TestValue
        FROM `tb_test_data_info_${suffix}`
        WHERE ActiveIndicator = 1
          AND CreatedBy = 'SLIM'
          AND AnalyteType = 1
          AND Id > #{testDataId}
        ORDER BY Id ASC
        LIMIT 200;
    </select>
    <select id="queryTestDataByOrderNo"
            resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO">
        select
        <include refid="Base_Column_List_Select" />
        from `tb_test_data_object_rel` ob
        from `tb_test_data_info_${suffix}`
        where ActiveIndicator = 1 and ObjectRelId IN
    </select>

   <select id="queryTestDataInfoByOrderNo"
            resultType="com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataAndMatrixDTO">
       SELECT
           ob.OrderNo as orderNo,
           ob.LabCode as labCode,
           ob.ParentOrderNo as parentOrderNo,
           ob.ObjectNo as objectNo,
           ob.SourceType as sourceType,
           ob.ExternalNo as externalNo,
           ob.ExternalObjectNo as ExternalObjectNo,
           m.TestMatrixId AS testMatrixId,
           td.TestDataMatrixId as testDataMatrixId,
           m.ExternalCode as externalCode ,
           m.PpVersionId as ppVersionId,
           m.Aid as aid,
           m.TestLineId as testLineId,
           m.CitationId as citationId,
           m.CitationVersionId as citationVersionId,
           m.CitationType as citationType,
           m.CitationName as citationName,
           m.EvaluationAlias as evaluationAlias,
           m.TestLineSeq as testLineSeq,
           IFNULL(m.SampleId,m.SampleNo) as sampleId,
           m.SampleNo as sampleNo,
           m.ExternalSampleNo as externalSampleNo,
           m.SampleSeq as sampleSeq,
           m.ConclusionId as matrixConclusionId,
           m.Languages as matrixLanuges,
           m.MethodDesc as methodDesc,
           td.AnalyteId as analyteId,
           td.AnalyteName as analyteName,
           td.AnalyteType as analyteType,
           td.ConclusionId as analyteConclusionId,
           td.Position as position,
           td.AnalyteCode as analyteCode,
           td.AnalyteSeq as analyteSeq,
           td.ReportUnit as reportUnit,
           td.TestValue as testValue,
           td.CasNo as casNo,
           td.ReportLimit as reportLimit,
           td.LimitUnit as limitUnit,
           td.ConclusionId as testDataConclusionId,
           td.Languages as testDataLanguages,
           td.Id as testResultInstanceId,
           td.limit_value_full_name as methodLimit,
           m.ExtFields as matrixExtFields,
           m.test_line_instance_id as testLineInstanceId
       FROM
           `tb_test_data_object_rel` ob
               INNER JOIN tb_test_data_matrix_info_${suffix} m ON ob.Id = m.ObjectRelId  AND m.ActiveIndicator = 1
               INNER JOIN tb_test_data_info_${suffix} td ON m.Id = td.TestDataMatrixId  AND td.ActiveIndicator = 1
       WHERE
           ob.ActiveIndicator = 1 and ob.OrderNo = #{orderNo};
    </select>

    <update id="updateInvalidOrValid">
    update `tb_test_data_info_${suffix}`
    set  ActiveIndicator = 0
    and modifiedBy = #{modifiedBy}
    and modifiedDate = NOW()
    where
     objectRelId IN
      <foreach collection="objectRelIds" item="objectRelId" separator="," open="(" close=")">
        #{objectRelId}
      </foreach>
  </update>

    <update id="batchUpdate">
        <foreach collection="testDatas" item="testData" separator=";">
            UPDATE `tb_test_data_info_${suffix}`
            SET ActiveIndicator = #{testData.activeIndicator}
                ,ModifiedDate = #{testData.modifiedDate}
            WHERE Id = #{testData.id}
              AND ActiveIndicator = 1
        </foreach>
    </update>

    <update id="delTestDataInvalid">
        DELETE FROM tb_test_data_info
        WHERE ObjectRelId = #{objectRelId}
          AND ActiveIndicator = 1
    </update>

</mapper>
