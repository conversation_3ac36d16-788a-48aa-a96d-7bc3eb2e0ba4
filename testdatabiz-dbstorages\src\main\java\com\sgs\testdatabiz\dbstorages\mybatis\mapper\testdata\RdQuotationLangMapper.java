package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdQuotationLangMapper {
    int countByExample(RdQuotationLangExample example);

    int deleteByExample(RdQuotationLangExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdQuotationLangPO record);

    int insertSelective(RdQuotationLangPO record);

    List<RdQuotationLangPO> selectByExample(RdQuotationLangExample example);

    RdQuotationLangPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdQuotationLangPO record, @Param("example") RdQuotationLangExample example);

    int updateByExample(@Param("record") RdQuotationLangPO record, @Param("example") RdQuotationLangExample example);

    int updateByPrimaryKeySelective(RdQuotationLangPO record);

    int updateByPrimaryKey(RdQuotationLangPO record);

    int batchInsert(List<RdQuotationLangPO> list);

    int batchUpdate(List<RdQuotationLangPO> list);
}