package com.sgs.testdatabiz.domain.service.utils.convertor;

import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.facade.model.enums.AttachmentObjectTypeEnum;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sgs.testdatabiz.domain.service.constant.SystemConstants.DEFAULT_USER;

/**
 * <AUTHOR>
 */
@Component
public final class TestMatrixBuilder {

    @Autowired
    private IdService ID_GENERATOR;

    public Map buildTestMatrix(List<RdReportMatrixDO> reportMatrixList,
                                      Map<String, List<RdTestLineDO>> testLineMap,
                                      Map<String, List<RdTestSampleDO>> testSampleMap,
                                      List<RdConditionGroupDO> conditionGroupList,
                                      Map<String, String> objectRelMap,
                                      Long labId,
                                      String orderNo,
                                      String reportNo,
                                      Long reportId) {
        if (Func.isEmpty(reportMatrixList)) {
            return null;
        }

        Map resultMap = new HashMap();

        List<TestDataMatrixInfoPO> list = new ArrayList<>();
        List<RdReportMatrixLangPO> langPOList = new ArrayList<>();
        List<TestSamplePO> testSamplePOList = new ArrayList<>();
        List<TestSampleGroupPO> sampleGroupList = new ArrayList<>();
        List<RdAttachmentDO> testMatrixFileDOS = new ArrayList<>();

        reportMatrixList.forEach(
                l -> {
                    Map<Integer, RdReportMatrixLangPO> matrixLangMap = new HashMap<>();
                    // test_matrix_id
                    String testMatrixId = l.getTestMatrixId();
                    // test_matrix_group_id
                    Integer testMatrixGroupId = l.getTestMatrixGroupId();
                    String testConditionGroupId = l.getTestConditionGroupId();
//                    String analyteInstanceId = l.getAnalyteInstanceId();

                    TestDataMatrixInfoPO testDataMatrixInfoPO = new TestDataMatrixInfoPO();
                    testDataMatrixInfoPO.setId(ID_GENERATOR.nextId());
                    testDataMatrixInfoPO.setObjectRelId(objectRelMap.get(l.getSubReportNo()));
                    testDataMatrixInfoPO.setTestMatrixId(testMatrixId);
                    testDataMatrixInfoPO.setRdReportId(reportId);
//                    testDataMatrixInfoPO.setPpVersionId();
//                    testDataMatrixInfoPO.setAid();
//                    testDataMatrixInfoPO.setSampleId();
//                    if (Func.isNotEmpty(conditionGroupList)) {
//                        Map<String, List<RdConditionGroupDO>> conditionGroupMap = conditionGroupList.stream().collect(Collectors.groupingBy(RdConditionGroupDO::getConditionGroupId));
//                        List<RdConditionGroupDO> groupDOList = conditionGroupMap.get(testConditionGroupId);
//                        if (Func.isNotEmpty(groupDOList)) {
//                            // combinedConditionDescription
//                            testDataMatrixInfoPO.setMethodDesc(groupDOList.get(0).getCombinedConditionDescription());
//
//                            List<RdConditionGroupLanguageDO> languageList = groupDOList.get(0).getLanguageList();
//                            if (Func.isNotEmpty(languageList)) {
//                                languageList.forEach(
//                                        v -> {
//                                            String combinedConditionDescription = v.getCombinedConditionDescription();
//                                            RdReportMatrixLangPO langPO = matrixLangMap.get(v.getLanguageId());
//                                            if (Func.isNotEmpty(langPO)) {
//                                                langPO.setMethodDesc(combinedConditionDescription);
//                                            } else {
//                                                langPO = new RdReportMatrixLangPO();
//                                                langPO.setId(ID_GENERATOR.nextId());
//                                                langPO.setLanguageId(v.getLanguageId());
//                                                langPO.setMethodDesc(combinedConditionDescription);
//                                                langPO.setRdReportMatrixId(testDataMatrixInfoPO.getId());
//                                            }
//                                            matrixLangMap.put(v.getLanguageId(), langPO);
//                                        }
//                                );
//                            }
//                        }
//                    }
//                    testDataMatrixInfoPO.setConclusionId();
//                    testDataMatrixInfoPO.setConclusionDisplay();
                    testDataMatrixInfoPO.setCreatedBy(DEFAULT_USER);
                    testDataMatrixInfoPO.setCreatedDate(DateUtils.getNow());
                    testDataMatrixInfoPO.setModifiedBy(DEFAULT_USER);
                    testDataMatrixInfoPO.setModifiedDate(DateUtils.getNow());
                    testDataMatrixInfoPO.setLabId(labId);
                    testDataMatrixInfoPO.setOrderNo(orderNo);
                    testDataMatrixInfoPO.setReportNo(reportNo);
                    testDataMatrixInfoPO.setTestMatrixGroupId(testMatrixGroupId);
                    testDataMatrixInfoPO.setSampleInstanceId(l.getTestSampleInstanceId());
                    testDataMatrixInfoPO.setActiveIndicator(ActiveType.Enable.getStatus());


                    // test_line_instance_id
                    String testLineInstanceId = l.getTestLineInstanceId();
                    testDataMatrixInfoPO.setTestLineInstanceId(testLineInstanceId);
                    if (Func.isNotBlank(testLineInstanceId)) {
                        List<RdTestLineDO> testLineDOS = testLineMap.get(testLineInstanceId);
                        if (Func.isNotEmpty(testLineDOS)) {
                            RdTestLineDO testLineDO = testLineDOS.get(0);
                            // test_line_id
                            Integer testLineId = testLineDO.getTestLineId();
                            // evaluation_alias
                            String evaluationAlias = testLineDO.getEvaluationAlias();
                            // evaluation_name
                            String evaluationName = testLineDO.getEvaluationName();
                            // test_line_status
                            Integer testLineStatus = testLineDO.getTestLineStatus();
                            // test_line_remark
                            String testLineRemark = testLineDO.getTestLineRemark();
                            // test_line_seq
                            Integer testLineSeq = testLineDO.getTestLineSeq();

                            testDataMatrixInfoPO.setTestLineId(testLineId);
                            testDataMatrixInfoPO.setEvaluationAlias(evaluationAlias);
                            testDataMatrixInfoPO.setEvaluationName(evaluationName);
                            testDataMatrixInfoPO.setTestLineStatus(Func.isEmpty(testLineStatus) ? null : String.valueOf(testLineStatus));
                            testDataMatrixInfoPO.setTestLineRemark(testLineRemark);
                            testDataMatrixInfoPO.setTestLineSeq(NumberUtil.toLong(testLineSeq));

                            RdCitationDO citation = testLineDO.getCitation();
                            if (Func.isNotEmpty(citation)) {
                                // citation_id
                                Integer citationId = citation.getCitationId();
                                // citation_type
                                Integer citationType = citation.getCitationType();
                                // citation_name
                                String citationName = citation.getCitationName();
                                // citation_full_name
                                String citationFullName = citation.getCitationFullName();

                                Integer citationVersionId = citation.getCitationVersionId();


                                testDataMatrixInfoPO.setCitationId(citationId);
                                testDataMatrixInfoPO.setCitationVersionId(citationVersionId);
                                testDataMatrixInfoPO.setCitationType(citationType);
                                testDataMatrixInfoPO.setCitationName(citationName);
                                testDataMatrixInfoPO.setCitationFullName(citationFullName);

                                List<RdCitationLanguageDO> languageList = citation.getLanguageList();
                                if (Func.isNotEmpty(languageList)) {
                                    languageList.forEach(
                                            v -> {
                                                RdReportMatrixLangPO langPO = matrixLangMap.get(v.getLanguageId());
                                                if (Func.isNotEmpty(langPO)) {
                                                    langPO.setCitationFullName(v.getCitationFullName());
                                                    langPO.setCitationName(v.getCitationName());
                                                } else {
                                                    langPO = new RdReportMatrixLangPO();
                                                    langPO.setId(ID_GENERATOR.nextId());
                                                    langPO.setLanguageId(v.getLanguageId());
                                                    langPO.setCitationFullName(v.getCitationFullName());
                                                    langPO.setCitationName(v.getCitationName());
                                                    langPO.setRdReportMatrixId(testDataMatrixInfoPO.getId());
                                                }

                                                matrixLangMap.put(v.getLanguageId(), langPO);
                                            }
                                    );
                                }

                            }
                            RdTestLineExternalDO external = testLineDO.getExternal();
                            if (Func.isNotEmpty(external)) {
                                // test_line_mapping_id
                                Integer testLineMappingId = external.getTestLineMappingId();
                                // external_id
                                String externalId = external.getExternalId();
                                // external_code
                                String externalCode = external.getExternalCode();

                                testDataMatrixInfoPO.setTestLineMappingId(testLineMappingId);
                                testDataMatrixInfoPO.setExternalId(externalId);
                                testDataMatrixInfoPO.setExternalCode(externalCode);
                            }

                            List<RdPpTestLineRelDO> ppTestLineRelList = testLineDO.getPpTestLineRelList();
                            if (Func.isNotEmpty(ppTestLineRelList)) {


                            }

                            List<RdTestLineLanguageDO> languageList = testLineDO.getLanguageList();
                            if (Func.isNotEmpty(languageList)) {
                                languageList.forEach(
                                        v -> {
                                            RdReportMatrixLangPO langPO = matrixLangMap.get(v.getLanguageId());
                                            if (Func.isNotEmpty(langPO)) {
                                                langPO.setEvaluationAlias(v.getEvaluationAlias());
                                                langPO.setEvaluationName(v.getEvaluationName());
                                            } else {
                                                langPO = new RdReportMatrixLangPO();
                                                langPO.setId(ID_GENERATOR.nextId());
                                                langPO.setLanguageId(v.getLanguageId());
                                                langPO.setEvaluationAlias(v.getEvaluationAlias());
                                                langPO.setEvaluationName(v.getEvaluationName());
                                                langPO.setRdReportMatrixId(testDataMatrixInfoPO.getId());
                                            }

                                            matrixLangMap.put(v.getLanguageId(), langPO);
                                        }
                                );
                            }

                        }
                    }

                    // sample_instance_id
                    String testSampleInstanceId = l.getTestSampleInstanceId();
                    if (Func.isNotBlank(testSampleInstanceId)) {
                        List<RdTestSampleDO> rdTestSampleDOS = testSampleMap.get(testSampleInstanceId);
                        if (Func.isNotEmpty(rdTestSampleDOS)) {

                            TestSamplePO testSamplePO = new TestSamplePO();

                            RdTestSampleDO testSampleDO = rdTestSampleDOS.get(0);

                            // sample_parent_id
                            String parentTestSampleId = testSampleDO.getParentTestSampleId();
                            // sample_no
                            String testSampleNo = testSampleDO.getTestSampleNo();
                            // external_sample_no
                            String externalSampleNo = testSampleDO.getExternalSampleNo();
                            // sample_type
                            Integer testSampleType = testSampleDO.getTestSampleType();
                            // sample_seq
                            Integer testSampleSeq = testSampleDO.getTestSampleSeq();
                            // category
                            String category = testSampleDO.getCategory();


                            testSamplePO.setId(ID_GENERATOR.nextId());
                            testSamplePO.setSampleInstanceId(testSampleDO.getTestSampleInstanceId());
                            testSamplePO.setSampleParentId(parentTestSampleId);
                            testSamplePO.setRdReportMatrixId(testDataMatrixInfoPO.getId());
                            testSamplePO.setSampleNo(testSampleNo);
                            testSamplePO.setSampleType(testSampleType);
                            testSamplePO.setSampleSeq(testSampleSeq);
                            testSamplePO.setOrderNo(orderNo);
                            testSamplePO.setCategory(category);
                            testSamplePO.setActiveIndicator(ActiveType.Enable.getStatus());
                            testSamplePO.setCreatedDate(DateUtils.getNow());
                            testSamplePO.setCreatedBy(DEFAULT_USER);
                            testSamplePO.setModifiedDate(DateUtils.getNow());
                            testSamplePO.setModifiedBy(DEFAULT_USER);


//                            testDataMatrixInfoPO.setSampleParentId(parentTestSampleId);
//                            testDataMatrixInfoPO.setSampleNo(testSampleNo);
                            testDataMatrixInfoPO.setExternalSampleNo(externalSampleNo);
//                            testDataMatrixInfoPO.setSampleType(String.valueOf(testSampleType));
//                            testDataMatrixInfoPO.setSampleSeq(String.valueOf(testSampleSeq));
//                            testDataMatrixInfoPO.setCategory(category);

                            List<RdTestSampleGroupDO> testSampleGroupList = testSampleDO.getTestSampleGroupList();
                            if (Func.isNotEmpty(testSampleGroupList)) {
                                // sample_group
                                String sampleGroupJson = JSONObject.toJSONString(testSampleGroupList);
                                testDataMatrixInfoPO.setSampleGroup(sampleGroupJson);

                                testSampleGroupList.forEach(
                                        testSampleGroup -> {
                                            TestSampleGroupPO sampleGroupPO = new TestSampleGroupPO();
                                            sampleGroupPO.setId(ID_GENERATOR.nextId());
                                            sampleGroupPO.setSampleGroupId(testSampleGroup.getTestSampleInstanceId());
                                            sampleGroupPO.setSampleId(testSampleDO.getTestSampleInstanceId());
                                            sampleGroupPO.setMainSampleFlag(testSampleGroup.getMainSampleFlag());
                                            sampleGroupPO.setActiveIndicator(ActiveType.Enable.getStatus());
                                            sampleGroupPO.setCreatedDate(DateUtils.getNow());
                                            sampleGroupPO.setCreatedBy(DEFAULT_USER);
                                            sampleGroupPO.setModifiedDate(DateUtils.getNow());
                                            sampleGroupPO.setModifiedBy(DEFAULT_USER);
                                            sampleGroupList.add(sampleGroupPO);
                                        }
                                );
                            }

                            RdMaterialAttrDO materialAttr = testSampleDO.getMaterialAttr();
                            if (Func.isNotEmpty(materialAttr)) {
                                // material_color
                                String materialColor = materialAttr.getMaterialColor();
                                // composition = materialTexture
                                String materialTexture = materialAttr.getMaterialTexture();
                                // material_description
                                String materialDescription = materialAttr.getMaterialDescription();
                                // material_end_use
                                String materialEndUse = materialAttr.getMaterialEndUse();
                                // applicable_flag
                                Integer applicableFlag = materialAttr.getApplicableFlag();

                                // material_other_sample_info
                                String materialOtherSampleInfo = materialAttr.getMaterialOtherSampleInfo();
                                // material_remark
                                String materialRemark = materialAttr.getMaterialRemark();


                                testSamplePO.setDescription(materialDescription);
                                testSamplePO.setComposition(materialTexture);
                                testSamplePO.setColor(materialColor);
                                testSamplePO.setSampleRemark(materialRemark);
                                testSamplePO.setEndUse(materialEndUse);
                                testSamplePO.setMaterial(materialAttr.getMaterialName());
                                testSamplePO.setApplicableFlag(applicableFlag);
                                testSamplePO.setOtherSampleInfo(materialOtherSampleInfo);

//                                testDataMatrixInfoPO.setMaterialColor(materialColor);
//                                testDataMatrixInfoPO.setComposition(materialTexture);
//                                testDataMatrixInfoPO.setMaterialDescription(materialDescription);
//                                testDataMatrixInfoPO.setMaterialEndUse(materialEndUse);
//                                testDataMatrixInfoPO.setApplicableFlag(String.valueOf(applicableFlag));
//                                testDataMatrixInfoPO.setMaterialOtherSampleInfo(materialOtherSampleInfo);
//                                testDataMatrixInfoPO.setMaterialRemark(materialRemark);

                                // TODO 此处待测试
                                Object extFields = materialAttr.getExtFields();
                                if (Func.isNotEmpty(extFields)) {
                                    // ext_fields
                                    String extFieldsJson = JSONObject.toJSONString(extFields);
                                    testDataMatrixInfoPO.setExtFields(extFieldsJson);
                                }
                            }

                            testSamplePOList.add(testSamplePO);


//                            RdConclusionDO conclusion = testSampleDO.getConclusion();
//
                            List<RdAttachmentDO> testSamplePhoto = testSampleDO.getTestSamplePhoto();
                            if (Func.isNotEmpty(testSamplePhoto)) {

                            }

                        }
                    }

                    List<RdConditionDO> conditionList = l.getConditionList();
                    if (Func.isNotEmpty(conditionList)) {
                        // condition
                        String conditionJson = JSONObject.toJSONString(conditionList);
                        testDataMatrixInfoPO.setCondition(conditionJson);
                    }

                    RdConclusionDO conclusion = l.getConclusion();
                    if (Func.isNotEmpty(conclusion)) {
                        String conclusionCode = conclusion.getConclusionCode();
                        String customerConclusion = conclusion.getCustomerConclusion();
                        String conclusionRemark = conclusion.getConclusionRemark();
                        String reviewConclusion = conclusion.getReviewConclusion();

                        testDataMatrixInfoPO.setConclusionCode(conclusionCode);
                        testDataMatrixInfoPO.setConclusionRemark(conclusionRemark);
                        testDataMatrixInfoPO.setCustomerConclusion(customerConclusion);

                    }

                    // TODO 待处理
                    testDataMatrixInfoPO.setBizVersionId(getTestDataReportMatrixMd5(testDataMatrixInfoPO));
                    list.add(testDataMatrixInfoPO);
                    if (Func.isNotEmpty(matrixLangMap)) {
                        matrixLangMap.forEach(
                                (k, v) -> langPOList.add(v)
                        );
                    }


                    List<RdAttachmentDO> testMatrixFileList = l.getTestMatrixFileList();
                    if (Func.isNotEmpty(testMatrixFileList)) {
                        testMatrixFileList.forEach(
                                testMatrixFile -> {
                                    testMatrixFile.setObjectType(AttachmentObjectTypeEnum.TEST_MATRIX.getCode());
                                    testMatrixFile.setObjectId(testMatrixId);
                                    testMatrixFileDOS.add(testMatrixFile);
                                }
                        );
                    }

                }
        );

        resultMap.put("testDataMatrix", list);
        resultMap.put("testMatrixFile", testMatrixFileDOS);
        resultMap.put("testDataMatrixLang", langPOList);
        resultMap.put("testSample", testSamplePOList);
        resultMap.put("sampleGroup", sampleGroupList);
        return resultMap;
    }

    public String getTestDataReportMatrixMd5(TestDataMatrixInfoPO relPO) {
        relPO.setObjectRelId(StringUtil.isNullOrEmpty(relPO.getObjectRelId()));
        relPO.setTestMatrixId(StringUtil.isNullOrEmpty(relPO.getTestMatrixId()));
        relPO.setExternalCode(StringUtil.isNullOrEmpty(relPO.getExternalCode()));
//        relPO.setPpVersionId(NumberUtil.toInt(relPO.getPpVersionId()));
//        relPO.setAid(NumberUtil.toLong(relPO.getAid()));
        relPO.setTestLineId(NumberUtil.toInt(relPO.getTestLineId()));
        relPO.setCitationId(Func.isEmpty(relPO.getCitationId()) ? null : NumberUtil.toInt(relPO.getCitationId()));
        relPO.setCitationVersionId(Func.isEmpty(relPO.getCitationVersionId()) ? null : NumberUtil.toInt(relPO.getCitationVersionId()));
        relPO.setSampleId(StringUtil.isNullOrEmpty(relPO.getSampleId()));
        relPO.setSampleNo(StringUtil.isNullOrEmpty(relPO.getSampleNo()));
        relPO.setExternalSampleNo(StringUtil.isNullOrEmpty(relPO.getExternalSampleNo()));
        relPO.setSampleSeq(relPO.getSampleSeq());
        relPO.setExtFields(StringUtil.isNullOrEmpty(relPO.getExtFields()));
        relPO.setCondition(StringUtil.isNullOrEmpty(relPO.getCondition()));
        relPO.setConclusionId(StringUtil.isNullOrEmpty(relPO.getConclusionId()));
        relPO.setConclusionDisplay(StringUtil.isNullOrEmpty(relPO.getConclusionDisplay()));
        relPO.setEvaluationAlias(StringUtil.isNullOrEmpty(relPO.getEvaluationAlias()));
        relPO.setLanguages(StringUtil.isNullOrEmpty(relPO.getLanguages()));
        relPO.setExternalId(StringUtil.isNullOrEmpty(relPO.getExternalId()));
        relPO.setMethodDesc(StringUtil.isNullOrEmpty(relPO.getMethodDesc()));
        relPO.setCitationType(Func.isEmpty(relPO.getCitationType()) ? null : NumberUtil.toInt(relPO.getCitationType()));

        StringBuilder append = new StringBuilder();
        append
                .append(relPO.getObjectRelId())
                .append(relPO.getTestMatrixId())
                .append(relPO.getExternalCode())
//                .append(relPO.getPpVersionId())
//                .append(relPO.getAid())
                .append(relPO.getTestLineId())
                .append(relPO.getCitationId())
                .append(relPO.getCitationVersionId())
//                .append(relPO.getSampleId())
                .append(relPO.getSampleNo())
                .append(relPO.getExternalSampleNo())
                //.append(relPO.getSampleSeq())
                .append(relPO.getExtFields())
                .append(relPO.getCondition())
                //.append(relPO.getConclusionId())
                //.append(relPO.getConclusionDisplay())
                //.append(relPO.getEvaluationAlias())
                //.append(relPO.getLanguages())
                //.append(relPO.getExternalId())
                //.append(relPO.getMethodDesc())
                .append(relPO.getCitationType());
        return DigestUtils.md5Hex(append.toString().toLowerCase());
    }
}
