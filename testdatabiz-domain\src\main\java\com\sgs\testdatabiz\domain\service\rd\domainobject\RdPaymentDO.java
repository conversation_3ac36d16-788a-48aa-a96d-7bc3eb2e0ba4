/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPaymentDO {

    private Integer paymentStatus;
    private String currency;
    private BigDecimal totalAmount;
    private BigDecimal mainCurrencyTotalAmount;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}