/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.quotation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdInvoiceQuotationFileDTO implements Serializable{

    private String fileName;
    private String cloudId;
    private String filePath;


    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
    public void setFileName(String fileName) {
         this.fileName = fileName;
     }
     public String getFileName() {
         return fileName;
     }

    public void setCloudId(String cloudId) {
         this.cloudId = cloudId;
     }
     public String getCloudId() {
         return cloudId;
     }

    public void setFilePath(String filePath) {
         this.filePath = filePath;
     }
     public String getFilePath() {
         return filePath;
     }

}