package com.sgs.testdatabiz.facade.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.sgs.core.domain.UserInfo;
import com.sgs.testdatabiz.core.constants.Constants;
import com.sgs.testdatabiz.core.util.HeaderHelper;
import com.sgs.testdatabiz.core.util.TokenUtils;
import com.sgs.testdatabiz.core.util.UserHelper;
import com.sgs.testdatabiz.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Facade实现方法的AOP.
 * <p/>
 * 实现与业务无关的通用操作。
 * <p/>
 * 1，日志
 * <p/>
 * 2，异常处理等
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class FacadeAspect {
    private static final Logger logger = LoggerFactory.getLogger(FacadeAspect.class);
    @Autowired
    private TokenUtils tokenUtils;

    /**
     *
     */
    @Pointcut("execution(* com.sgs.testdatabiz.facade.*Facade.*(*)) && args(reqObject)")
    public void aspect(BaseRequest reqObject) {

    }

    /**
     * @param joinPoint
     * @return
     */
    @Around("aspect(reqObject)")
    public Object around(ProceedingJoinPoint joinPoint, BaseRequest reqObject) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        Object resp = null;
        try {
            Object[] args = joinPoint.getArgs();
            if (args[0] instanceof BaseRequest) {
                // 设置当前BU数据源
                this.setProductLineCode((BaseRequest) args[0]);
            } else if (args[0] instanceof JsonNode) {
                // 设置当前BU数据源
                String productLineCode = StringUtils.EMPTY;
                JsonNode getProductLineCode = ((JsonNode) args[0]).get("productLineCode");
                if (getProductLineCode != null) {
                    productLineCode = getProductLineCode.textValue();
                } else {
                    productLineCode = HeaderHelper.getParamValue(Constants.PRODUCTLINECODE);
                }
                if (StringUtils.isBlank(productLineCode)) {
                    throw new BizException("请设置productLineCode.");
                }
                ProductLineContextHolder.setProductLineCode(productLineCode);
            } else {

            }
            UserInfo user = tokenUtils.getUser(reqObject.getToken());
            if (user != null) {
                UserHelper.setLocalUser(user);
            }
            resp = joinPoint.proceed(new Object[]{args[0]});
        } catch (IllegalArgumentException ex) {
            resp = buildErrorResponse(ResponseCode.ILLEGAL_ARGUMENT, ex.getLocalizedMessage());
            logger.error("IllegalArgumentException req: {}", ex);
        } catch (BizException e) {
            // 前端可能将错误msg直接抛给用户
            resp = buildErrorResponse(e.getErrorCode(), e.getLocalizedMessage());
            logger.info("BizException, Error:{}", e.getMessage());
        } catch (Throwable e) {
            resp = buildErrorResponse(ResponseCode.UNKNOWN, e.getMessage());
            logger.error("Throwable req. error:{}. req:{}", e.getMessage(), e);
        } finally {
            stopWatch.stop();
            MDC.clear();
        }
        if (stopWatch.getTime() > 100) {
            logger.info("Finished ,req  Consumed:{}ms", stopWatch.getTime());
        }
        return resp;
    }


    /**
     * @param reqObject
     */
    private void setProductLineCode(BaseRequest reqObject) {
        String productLineCode = reqObject.getProductLineCode();
        if (StringUtils.isBlank(productLineCode)) {
            productLineCode = HeaderHelper.getParamValue(Constants.PRODUCTLINECODE);
        }
        if (StringUtils.isBlank(productLineCode)) {
            throw new BizException("请设置productLineCode.");
        }
        /*if (!ProductLineType.check(productLineAbbr)){
            throw new BizException(String.format("当前请求的ProductLineAbbr(%s)不存在.", productLineAbbr));
        }*/
        reqObject.setProductLineCode(productLineCode);
        ProductLineContextHolder.setProductLineCode(productLineCode);
    }

    /**
     * @param point
     */
    @After("aspect(reqObject)")
    public void after(JoinPoint point, BaseRequest reqObject) {
        UserHelper.clear();
        ProductLineContextHolder.clear();
    }

    /**
     * @param errorCode
     * @param errorMsg
     * @return
     */
    private BaseResponse buildErrorResponse(ResponseCode errorCode, String errorMsg) {
        if (StringUtils.isBlank(errorMsg)) {
            errorMsg = "服务器出了点小差错，请稍后再试.";
        }
        return new BaseResponse(errorCode.getCode(), errorMsg);
    }

}
