/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdReportConclusionDO {
    // add 20230529
    private String orderNo;
    private String realOrderNo;
    // add 20230529
    private Integer systemId;
    private String conclusionInstanceId;
    private Integer conclusionLevelId;
    private String objectId;
    private String objectName;
    private String reportId;
    private String conclusionId;
    private String conclusionCode;
    private String customerConclusionId;
    private String customerConclusion;
    private String conclusionRemark;
    private String testLineInstanceId;
    private String sampleInstanceId;
    private Integer sectionId;
    private String ppArtifactRelId;
    private String ppSampleRelId;
    private String comments;

    private Date lastModifiedTimestamp;
   private Integer testRequestFlag; //SCI-1378
    private Integer activeIndicator;
}
