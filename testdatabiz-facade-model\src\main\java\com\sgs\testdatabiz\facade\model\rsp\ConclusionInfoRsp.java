package com.sgs.testdatabiz.facade.model.rsp;

import java.util.List;

import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.testdatabiz.facade.model.rsp.conclusion.AnalyteConclusionInfo;
import com.sgs.testdatabiz.facade.model.rsp.conclusion.ConclusionLangInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public final class ConclusionInfoRsp extends PrintFriendliness {
    /**
     *
     */
    @ApiModelProperty("SubContractNo")
    private String subContractNo;

    /**
     *
     */
    @ApiModelProperty("testMatrixId")
    private String testMatrixId;

    /**
     *
     */
    @ApiModelProperty("ppVersionId")
    private Integer ppVersionId;

    /**
     *
     */
    @ApiModelProperty("subReportNo")
    private String subReportNo;

    /**
     *
     */
    @ApiModelProperty("reportNo")
    private String reportNo;

    /**
     *
     */
    @ApiModelProperty("aid")
    private Long aid;

    /**
     *
     */
    @ApiModelProperty("测试项")
    private Integer testLineId;

    /**
     *
     */
    @ApiModelProperty("测试标准")
    private Integer citationId;

    /**
     *
     */
    @ApiModelProperty("测试版本")
    private Integer citationVersionId;

    /**
     *
     */
    @ApiModelProperty("测试类型")
    private Integer citationType;

    @ApiModelProperty("测试名称")
    private String citationName;

    /**
     *
     */
    @ApiModelProperty("样品Id")
    private String sampleId;

    /**
     *
     */
    @ApiModelProperty("样品编号")
    private String sampleNo;

    @ApiModelProperty("样品描述")
    private String sampleDescription;

    @ApiModelProperty("多语言")
    private List<ConclusionLangInfo> languageList;
    /**
     *
     */
    @ApiModelProperty("结论Id")
    private Integer conclusionId;

    /**
     *
     */
    @ApiModelProperty("结论")
    private String conclusionDisplay;

    private String testLineInstanceId;
    @ApiModelProperty("评价别名")
    private String evaluationAlias;
    /**
     * 测试分析结果
     */
    @ApiModelProperty("分析结果")
    private List<AnalyteConclusionInfo> analyteConclusionList;
}
