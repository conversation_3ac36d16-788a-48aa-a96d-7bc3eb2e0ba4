//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.sgs.testdatabiz.core.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum CustomerUsage {
    None(0, "none"),
    Applicant(1, "applicant"),
    Payer(2, "payer"),
    Buyer(3, "buyer"),
    Agent(4, "agent"),
    Supplier(5, "supplier"),
    Manufacture(6, "manufacture"),
    CS(7, "cs"),
    SALES(8, "sales"),
    SUBCONTRACTFROM(9, "subcontractFrom"),
    OEM(10, "oem");

    private final int usage;
    private final String code;
    public static final Map<String, CustomerUsage> maps = new HashMap<String, CustomerUsage>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            CustomerUsage[] var1 = CustomerUsage.values();
            int var2 = var1.length;

            for (int var3 = 0; var3 < var2; ++var3) {
                CustomerUsage customerUsage = var1[var3];
                this.put(customerUsage.getCode(), customerUsage);
            }

        }
    };

    private CustomerUsage(int usage, String code) {
        this.usage = usage;
        this.code = code;
    }

    public int getUsage() {
        return this.usage;
    }

    public String getCode() {
        return this.code;
    }

    public static CustomerUsage fromCode(String code) {
        return code != null && code.length() != 0 && maps.containsKey(code) ? maps.get(code) : None;
    }

    public static CustomerUsage findStatus(Integer status) {
        if (status == null) {
            return null;
        } else {
            CustomerUsage[] var1 = values();
            int var2 = var1.length;

            for (int var3 = 0; var3 < var2; ++var3) {
                CustomerUsage customerUsage = var1[var3];
                if (customerUsage.getUsage() == status) {
                    return customerUsage;
                }
            }

            return null;
        }
    }

    public boolean check(CustomerUsage... customerUsages) {
        if (customerUsages != null && customerUsages.length > 0) {
            CustomerUsage[] var2 = customerUsages;
            int var3 = customerUsages.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                CustomerUsage customerUsage = var2[var4];
                if (this.getUsage() == customerUsage.getUsage()) {
                    return true;
                }
            }

            return false;
        } else {
            return false;
        }
    }
}
