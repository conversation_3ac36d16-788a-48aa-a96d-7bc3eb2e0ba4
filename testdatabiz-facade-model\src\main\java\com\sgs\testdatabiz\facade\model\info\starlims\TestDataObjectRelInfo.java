package com.sgs.testdatabiz.facade.model.info.starlims;

import java.util.Date;

public class TestDataObjectRelInfo {
    /**
     * 主键Id
     */
    private String id;

    /**
     * 订单的BU
     */
    private String productLineCode;

    /**
     * 订单所在的Lab
     */
    private String labCode;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * ReportNo VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * ParentOrderNo VARCHAR(50)<br>
     * 发包方订单号
     */
    private String parentOrderNo;

    /**
     * 1、当Slim数据时，该值为SubContractNo
     2、当Fast数据时，该值为JobNo
     3、当Starlims数据时，该值为SubContractNo
     */
    private String objectNo;

    /**
     * 外部系统主键Id
     */
    private String externalId;

    /**
     * ExternalObjectNo VARCHAR(50)<br>
     *
     */
    private String externalObjectNo;

    /**
     * CompleteDate TIMESTAMP(19)<br>
     * CompleteDate
     */
    private Date completeDate;

    /**
     * 1、当Slim数据时，该值为SlimJobNo
     2、当Fast数据时，该值为JobNo
     3、当Starlims数据时，该值为folderNo
     */
    private String externalNo;

    /**
     *
     */
    private Date lastModifiedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProductLineCode() {
        return productLineCode;
    }

    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getExternalObjectNo() {
        return externalObjectNo;
    }

    public void setExternalObjectNo(String externalObjectNo) {
        this.externalObjectNo = externalObjectNo;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public Date getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(Date lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }
}
