package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RdReportConclusionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RdReportConclusionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNull() {
            addCriterion("lab_id is null");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNotNull() {
            addCriterion("lab_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabIdEqualTo(Long value) {
            addCriterion("lab_id =", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotEqualTo(Long value) {
            addCriterion("lab_id <>", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThan(Long value) {
            addCriterion("lab_id >", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThanOrEqualTo(Long value) {
            addCriterion("lab_id >=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThan(Long value) {
            addCriterion("lab_id <", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThanOrEqualTo(Long value) {
            addCriterion("lab_id <=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdIn(List<Long> values) {
            addCriterion("lab_id in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotIn(List<Long> values) {
            addCriterion("lab_id not in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdBetween(Long value1, Long value2) {
            addCriterion("lab_id between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotBetween(Long value1, Long value2) {
            addCriterion("lab_id not between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("report_no is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("report_no is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("report_no =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("report_no <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("report_no >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("report_no >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("report_no <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("report_no <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("report_no like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("report_no not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("report_no in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("report_no not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("report_no between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("report_no not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdIsNull() {
            addCriterion("conclusion_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdIsNotNull() {
            addCriterion("conclusion_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdEqualTo(String value) {
            addCriterion("conclusion_instance_id =", value, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdNotEqualTo(String value) {
            addCriterion("conclusion_instance_id <>", value, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdGreaterThan(String value) {
            addCriterion("conclusion_instance_id >", value, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("conclusion_instance_id >=", value, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdLessThan(String value) {
            addCriterion("conclusion_instance_id <", value, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("conclusion_instance_id <=", value, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdLike(String value) {
            addCriterion("conclusion_instance_id like", value, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdNotLike(String value) {
            addCriterion("conclusion_instance_id not like", value, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdIn(List<String> values) {
            addCriterion("conclusion_instance_id in", values, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdNotIn(List<String> values) {
            addCriterion("conclusion_instance_id not in", values, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdBetween(String value1, String value2) {
            addCriterion("conclusion_instance_id between", value1, value2, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionInstanceIdNotBetween(String value1, String value2) {
            addCriterion("conclusion_instance_id not between", value1, value2, "conclusionInstanceId");
            return (Criteria) this;
        }

        public Criteria andObjectIdIsNull() {
            addCriterion("object_id is null");
            return (Criteria) this;
        }

        public Criteria andObjectIdIsNotNull() {
            addCriterion("object_id is not null");
            return (Criteria) this;
        }

        public Criteria andObjectIdEqualTo(String value) {
            addCriterion("object_id =", value, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdNotEqualTo(String value) {
            addCriterion("object_id <>", value, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdGreaterThan(String value) {
            addCriterion("object_id >", value, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdGreaterThanOrEqualTo(String value) {
            addCriterion("object_id >=", value, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdLessThan(String value) {
            addCriterion("object_id <", value, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdLessThanOrEqualTo(String value) {
            addCriterion("object_id <=", value, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdLike(String value) {
            addCriterion("object_id like", value, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdNotLike(String value) {
            addCriterion("object_id not like", value, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdIn(List<String> values) {
            addCriterion("object_id in", values, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdNotIn(List<String> values) {
            addCriterion("object_id not in", values, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdBetween(String value1, String value2) {
            addCriterion("object_id between", value1, value2, "objectId");
            return (Criteria) this;
        }

        public Criteria andObjectIdNotBetween(String value1, String value2) {
            addCriterion("object_id not between", value1, value2, "objectId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdIsNull() {
            addCriterion("pp_artifact_rel_id is null");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdIsNotNull() {
            addCriterion("pp_artifact_rel_id is not null");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdEqualTo(Long value) {
            addCriterion("pp_artifact_rel_id =", value, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdNotEqualTo(Long value) {
            addCriterion("pp_artifact_rel_id <>", value, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdGreaterThan(Long value) {
            addCriterion("pp_artifact_rel_id >", value, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pp_artifact_rel_id >=", value, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdLessThan(Long value) {
            addCriterion("pp_artifact_rel_id <", value, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdLessThanOrEqualTo(Long value) {
            addCriterion("pp_artifact_rel_id <=", value, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdIn(List<Long> values) {
            addCriterion("pp_artifact_rel_id in", values, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdNotIn(List<Long> values) {
            addCriterion("pp_artifact_rel_id not in", values, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdBetween(Long value1, Long value2) {
            addCriterion("pp_artifact_rel_id between", value1, value2, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpArtifactRelIdNotBetween(Long value1, Long value2) {
            addCriterion("pp_artifact_rel_id not between", value1, value2, "ppArtifactRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdIsNull() {
            addCriterion("pp_sample_rel_id is null");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdIsNotNull() {
            addCriterion("pp_sample_rel_id is not null");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdEqualTo(String value) {
            addCriterion("pp_sample_rel_id =", value, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdNotEqualTo(String value) {
            addCriterion("pp_sample_rel_id <>", value, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdGreaterThan(String value) {
            addCriterion("pp_sample_rel_id >", value, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdGreaterThanOrEqualTo(String value) {
            addCriterion("pp_sample_rel_id >=", value, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdLessThan(String value) {
            addCriterion("pp_sample_rel_id <", value, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdLessThanOrEqualTo(String value) {
            addCriterion("pp_sample_rel_id <=", value, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdLike(String value) {
            addCriterion("pp_sample_rel_id like", value, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdNotLike(String value) {
            addCriterion("pp_sample_rel_id not like", value, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdIn(List<String> values) {
            addCriterion("pp_sample_rel_id in", values, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdNotIn(List<String> values) {
            addCriterion("pp_sample_rel_id not in", values, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdBetween(String value1, String value2) {
            addCriterion("pp_sample_rel_id between", value1, value2, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andPpSampleRelIdNotBetween(String value1, String value2) {
            addCriterion("pp_sample_rel_id not between", value1, value2, "ppSampleRelId");
            return (Criteria) this;
        }

        public Criteria andSectionIdIsNull() {
            addCriterion("section_id is null");
            return (Criteria) this;
        }

        public Criteria andSectionIdIsNotNull() {
            addCriterion("section_id is not null");
            return (Criteria) this;
        }

        public Criteria andSectionIdEqualTo(Integer value) {
            addCriterion("section_id =", value, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdNotEqualTo(Integer value) {
            addCriterion("section_id <>", value, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdGreaterThan(Integer value) {
            addCriterion("section_id >", value, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("section_id >=", value, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdLessThan(Integer value) {
            addCriterion("section_id <", value, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdLessThanOrEqualTo(Integer value) {
            addCriterion("section_id <=", value, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdIn(List<Integer> values) {
            addCriterion("section_id in", values, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdNotIn(List<Integer> values) {
            addCriterion("section_id not in", values, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdBetween(Integer value1, Integer value2) {
            addCriterion("section_id between", value1, value2, "sectionId");
            return (Criteria) this;
        }

        public Criteria andSectionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("section_id not between", value1, value2, "sectionId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIsNull() {
            addCriterion("test_line_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIsNotNull() {
            addCriterion("test_line_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdEqualTo(String value) {
            addCriterion("test_line_instance_id =", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotEqualTo(String value) {
            addCriterion("test_line_instance_id <>", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdGreaterThan(String value) {
            addCriterion("test_line_instance_id >", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("test_line_instance_id >=", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdLessThan(String value) {
            addCriterion("test_line_instance_id <", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("test_line_instance_id <=", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdLike(String value) {
            addCriterion("test_line_instance_id like", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotLike(String value) {
            addCriterion("test_line_instance_id not like", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIn(List<String> values) {
            addCriterion("test_line_instance_id in", values, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotIn(List<String> values) {
            addCriterion("test_line_instance_id not in", values, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdBetween(String value1, String value2) {
            addCriterion("test_line_instance_id between", value1, value2, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotBetween(String value1, String value2) {
            addCriterion("test_line_instance_id not between", value1, value2, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIsNull() {
            addCriterion("sample_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIsNotNull() {
            addCriterion("sample_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdEqualTo(String value) {
            addCriterion("sample_instance_id =", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotEqualTo(String value) {
            addCriterion("sample_instance_id <>", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdGreaterThan(String value) {
            addCriterion("sample_instance_id >", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("sample_instance_id >=", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLessThan(String value) {
            addCriterion("sample_instance_id <", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("sample_instance_id <=", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdLike(String value) {
            addCriterion("sample_instance_id like", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotLike(String value) {
            addCriterion("sample_instance_id not like", value, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdIn(List<String> values) {
            addCriterion("sample_instance_id in", values, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotIn(List<String> values) {
            addCriterion("sample_instance_id not in", values, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdBetween(String value1, String value2) {
            addCriterion("sample_instance_id between", value1, value2, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andSampleInstanceIdNotBetween(String value1, String value2) {
            addCriterion("sample_instance_id not between", value1, value2, "sampleInstanceId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdIsNull() {
            addCriterion("conclusion_level_id is null");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdIsNotNull() {
            addCriterion("conclusion_level_id is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdEqualTo(Integer value) {
            addCriterion("conclusion_level_id =", value, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdNotEqualTo(Integer value) {
            addCriterion("conclusion_level_id <>", value, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdGreaterThan(Integer value) {
            addCriterion("conclusion_level_id >", value, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("conclusion_level_id >=", value, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdLessThan(Integer value) {
            addCriterion("conclusion_level_id <", value, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdLessThanOrEqualTo(Integer value) {
            addCriterion("conclusion_level_id <=", value, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdIn(List<Integer> values) {
            addCriterion("conclusion_level_id in", values, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdNotIn(List<Integer> values) {
            addCriterion("conclusion_level_id not in", values, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdBetween(Integer value1, Integer value2) {
            addCriterion("conclusion_level_id between", value1, value2, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionLevelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("conclusion_level_id not between", value1, value2, "conclusionLevelId");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIsNull() {
            addCriterion("conclusion_code is null");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIsNotNull() {
            addCriterion("conclusion_code is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeEqualTo(String value) {
            addCriterion("conclusion_code =", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotEqualTo(String value) {
            addCriterion("conclusion_code <>", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeGreaterThan(String value) {
            addCriterion("conclusion_code >", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeGreaterThanOrEqualTo(String value) {
            addCriterion("conclusion_code >=", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLessThan(String value) {
            addCriterion("conclusion_code <", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLessThanOrEqualTo(String value) {
            addCriterion("conclusion_code <=", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLike(String value) {
            addCriterion("conclusion_code like", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotLike(String value) {
            addCriterion("conclusion_code not like", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIn(List<String> values) {
            addCriterion("conclusion_code in", values, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotIn(List<String> values) {
            addCriterion("conclusion_code not in", values, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeBetween(String value1, String value2) {
            addCriterion("conclusion_code between", value1, value2, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotBetween(String value1, String value2) {
            addCriterion("conclusion_code not between", value1, value2, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIsNull() {
            addCriterion("customer_conclusion is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIsNotNull() {
            addCriterion("customer_conclusion is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionEqualTo(String value) {
            addCriterion("customer_conclusion =", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotEqualTo(String value) {
            addCriterion("customer_conclusion <>", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionGreaterThan(String value) {
            addCriterion("customer_conclusion >", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("customer_conclusion >=", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLessThan(String value) {
            addCriterion("customer_conclusion <", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLessThanOrEqualTo(String value) {
            addCriterion("customer_conclusion <=", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLike(String value) {
            addCriterion("customer_conclusion like", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotLike(String value) {
            addCriterion("customer_conclusion not like", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIn(List<String> values) {
            addCriterion("customer_conclusion in", values, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotIn(List<String> values) {
            addCriterion("customer_conclusion not in", values, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionBetween(String value1, String value2) {
            addCriterion("customer_conclusion between", value1, value2, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotBetween(String value1, String value2) {
            addCriterion("customer_conclusion not between", value1, value2, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIsNull() {
            addCriterion("conclusion_remark is null");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIsNotNull() {
            addCriterion("conclusion_remark is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkEqualTo(String value) {
            addCriterion("conclusion_remark =", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotEqualTo(String value) {
            addCriterion("conclusion_remark <>", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkGreaterThan(String value) {
            addCriterion("conclusion_remark >", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("conclusion_remark >=", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLessThan(String value) {
            addCriterion("conclusion_remark <", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLessThanOrEqualTo(String value) {
            addCriterion("conclusion_remark <=", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLike(String value) {
            addCriterion("conclusion_remark like", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotLike(String value) {
            addCriterion("conclusion_remark not like", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIn(List<String> values) {
            addCriterion("conclusion_remark in", values, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotIn(List<String> values) {
            addCriterion("conclusion_remark not in", values, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkBetween(String value1, String value2) {
            addCriterion("conclusion_remark between", value1, value2, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotBetween(String value1, String value2) {
            addCriterion("conclusion_remark not between", value1, value2, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("last_modified_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("last_modified_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("last_modified_timestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("last_modified_timestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("last_modified_timestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("last_modified_timestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("last_modified_timestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("last_modified_timestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}