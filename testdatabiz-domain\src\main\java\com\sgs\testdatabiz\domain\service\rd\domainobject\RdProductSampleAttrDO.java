/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdProductSampleAttrDO {

    private Integer seq;
    private String labelName;
    private String customerLabel;
    private String dataType;
    private String labelCode;
    private String value;
    private String displayInReport;
    private String mandatoryFlag;
    private List<RdAttrLanguageDO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}