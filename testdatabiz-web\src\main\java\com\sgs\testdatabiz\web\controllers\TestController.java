package com.sgs.testdatabiz.web.controllers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.core.util.Transcoding;
import com.sgs.testdatabiz.domain.service.rd.ReportDataService;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.v2.ReportTestDataService;
import com.sgs.testdatabiz.sdk.RdConvertUtil;
import com.sgs.testdatabiz.sdk.model.SDKConvertRequest;
import com.sgs.testdatabiz.sdk.output.dto.RdReportDataDTO;
import com.sgs.testdatabiz.sdk.util.Func;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

import static com.sgs.preorder.facade.model.common.BaseResponse.newSuccessInstance;

@RestController
@RequestMapping("/test")
public class TestController {

    private final ReportDataService reportDataService;

    public TestController(ReportDataService reportDataService) {
        this.reportDataService = reportDataService;
    }
    @Autowired
    private ReportTestDataService reportTestDataService;

    @Data
    public static class ReplaceSystemId {
        @NotNull
        private Long systemId;
        @NotNull
        private List<String> reportNos;
    }

    @PostMapping("/replace/system_id")
    @ApiOperation(value = "/replace/system_id")
    public BaseResponse<?> replaceSystemId(@Validated @RequestBody ReplaceSystemId replaceSystemId) {
        reportDataService.replaceSystemId(replaceSystemId.getSystemId(), replaceSystemId.getReportNos());
        return BaseResponse.newInstance("Ok");
    }


    @GetMapping("/getTranscoding")
    @ApiOperation(value = "slim数据保存")
    public BaseResponse getTranscoding() {

        String strVal = "数据保存";
        int strIndex = this.charLen(strVal);

        strVal = "Trans";
        strIndex = this.charLen(strVal);

        strVal = "slim数据保存";
        int strIndex1 = this.charLen(strVal);


        String evaluationAlias = "{\\rtf1\\ansi\\ansicpg936\\deff0\\deflang1033\\deflangfe2052{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\n" +
                "\\viewkind4\\uc1\\pard\\sl-240\\slmult0\\tx2160\\tx2440\\tx2880\\f0\\fs17 Migration of certain elements (Category III:for scrapped-off toy material)\\lang2052\\par\n" +
                "}";
        evaluationAlias = Transcoding.rtfStrEsc(evaluationAlias);
        evaluationAlias = "With reference to EN 71-3:2019+A1:2021, analysis was performed by ICP-OES, IC-UV or LC-ICP-MS.";

        evaluationAlias = Transcoding.rtfStrEsc(evaluationAlias);

        evaluationAlias = "\u53c2\u8003ISO/TS 16179:2012\uff0c\u91c7\u7528GC-MS\u8fdb\u884c\u5206\u6790\u3002";

        evaluationAlias = Transcoding.rtfStrEsc(evaluationAlias);

        return BaseResponse.newSuccessInstance(evaluationAlias);
    }

     @PostMapping("/subTestData/importData")
    public BaseResponse<?>  testSubTestDataImport(@RequestBody ReportTestDataInfo reportTestDataInfo){
        return reportTestDataService.importData(reportTestDataInfo);
    }


    @PostMapping("/sdkConvert")
    public BaseResponse<?> testSdkContent(@RequestBody SDKConvertRequest sdkConvertRequest){
        String version = sdkConvertRequest.getVersion();
        Integer updateVersion = sdkConvertRequest.getUpdateVersion();
        String env = sdkConvertRequest.getEnv();
        String reportNo = sdkConvertRequest.getReportNo();
        Boolean orderNoMapping = sdkConvertRequest.isOrderNoMapping();
        String requestJson = sdkConvertRequest.getRequestJson();
        if(Func.isEmpty(requestJson)){
            requestJson = reportDataService.getReportRequestJsonByReportNo(reportNo);
        }

        String result = RdConvertUtil.convert(requestJson, version, env, updateVersion,orderNoMapping,false);
        RdReportDataDTO output = JSON.parseObject(result, RdReportDataDTO.class);
        return BaseResponse.newSuccessInstance(output);
    }
    /**
     * @param value
     * @return
     */
    public int charLen(String value) {
        if (value == null || value.length() <= 0) {
            return 0;
        }
        char[] charArr = value.toCharArray();
        int len = 0;
        for (int index = 0; index < charArr.length; index++) {
            len++;
            if (!isLetter(charArr[index])) {
                len++;
            }
        }
        return len;
    }

    public boolean isLetter(char c) {
        int k = 0x80;
        return c / k == 0 ? true : false;
    }
}
