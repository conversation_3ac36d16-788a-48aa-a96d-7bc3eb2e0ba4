package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationInvoiceRelExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationInvoiceRelPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdQuotationInvoiceRelMapper {
    int countByExample(RdQuotationInvoiceRelExample example);

    int deleteByExample(RdQuotationInvoiceRelExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdQuotationInvoiceRelPO record);

    int insertSelective(RdQuotationInvoiceRelPO record);

    List<RdQuotationInvoiceRelPO> selectByExample(RdQuotationInvoiceRelExample example);

    RdQuotationInvoiceRelPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdQuotationInvoiceRelPO record, @Param("example") RdQuotationInvoiceRelExample example);

    int updateByExample(@Param("record") RdQuotationInvoiceRelPO record, @Param("example") RdQuotationInvoiceRelExample example);

    int updateByPrimaryKeySelective(RdQuotationInvoiceRelPO record);

    int updateByPrimaryKey(RdQuotationInvoiceRelPO record);

    int batchInsert(List<RdQuotationInvoiceRelPO> list);

    int batchUpdate(List<RdQuotationInvoiceRelPO> list);
}