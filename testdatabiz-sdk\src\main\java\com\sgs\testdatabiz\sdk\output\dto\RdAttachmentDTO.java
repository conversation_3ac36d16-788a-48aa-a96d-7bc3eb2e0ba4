package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdAttachmentDTO extends BaseModel{

    private Long labId;
    private String objectType;
    private String rdObjectId;
    private String objectNo;
    private Integer languageId;
    private Integer toCustomerFlag;
    private String orderNo;
    private String reportNo;
    private String bizType;
    private String fileName;
    private String cloudId;
    private Integer activeIndicator;
    private String objectTypeLabel;
    private String bizTypeLabel;

    private String attachmentInstanceId;

    private Date lastModifiedTimestamp;
    private Date issuedDate;
}
