package com.sgs.testdatabiz.integration;

import com.google.common.collect.Maps;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.core.util.HttpClientUtils;
import com.sgs.testdatabiz.integration.config.URLConfiguration;
import com.sgs.testdatabiz.integration.model.employ.EmpInfoReq;
import com.sgs.testdatabiz.integration.model.employ.EmpInfoRsp;
import com.sgs.testdatabiz.integration.model.employ.EmpResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class UserClient {
    private static Logger logger = LoggerFactory.getLogger(UserClient.class);
    @Autowired
    private URLConfiguration urlConfiguration;

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult<EmpInfoRsp> getEmpInfo(EmpInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        if (
//                StringUtils.isBlank(reqObject.getLabCode()) ||
                StringUtils.isBlank(reqObject.getRegionAccount())){
            return rspResult;
        }
        Map<String, String> paramMaps = Maps.newHashMap();
        paramMaps.put("labCode", reqObject.getLabCode());
        paramMaps.put("regionAccount", reqObject.getRegionAccount());
        paramMaps.put("page", String.valueOf(reqObject.getPage()));
        paramMaps.put("rows", String.valueOf(reqObject.getRows()));

        String reqUrl = String.format("%s/employee/queryUserInfoList", urlConfiguration.getUserManagementUrl());
        try {
            logger.info("UserClient.getEmpInfo({})", reqObject);
            EmpResponse rspObject = HttpClientUtils.post(reqUrl, paramMaps, EmpResponse.class);
            logger.info("UserClient.getEmpInfo({})，===Emp发送接口返回值：{}", reqObject , rspObject);

            if (rspObject == null){
                return rspResult;
            }
            List<EmpInfoRsp> emps = rspObject.getList();
            if (emps == null || emps.isEmpty()){
                return rspResult;
            }
            EmpInfoRsp emp = emps.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getRegionAccount(), reqObject.getRegionAccount())).findFirst().orElse(null);
            if (emp == null){
                return rspResult;
            }
            rspResult.setData(emp);
            rspResult.setSuccess(true);
        } catch (Exception ex) {
            rspResult.setMsg("UserClient.getEmpInfo interface exception");
            logger.error("UserClient.getEmpInfo({})，===调用工信息获取接口异常，{}", reqObject, ex);
        }
        return rspResult;
    }
}
