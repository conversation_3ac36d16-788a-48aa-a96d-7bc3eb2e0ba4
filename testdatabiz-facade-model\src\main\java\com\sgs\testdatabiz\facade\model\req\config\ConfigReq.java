package com.sgs.testdatabiz.facade.model.req.config;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 10:54
 */
@ApiModel
public class ConfigReq extends BaseRequest {

    private List<String> labCodes;

    private Integer sourceType;
    /**
     *
     */
    private String date;
    /**
     *
     */
    private String orderNo;

    public List<String> getLabCodes() {
        return labCodes;
    }

    public void setLabCodes(List<String> labCodes) {
        this.labCodes = labCodes;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
