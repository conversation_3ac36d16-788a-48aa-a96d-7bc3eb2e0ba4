/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdProductAttributeDO implements Serializable {

    private String productAttributeInstanceId;
    private Integer productAttributeId;
    private String productAttributeName;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private List<RdProductAttributeLangDO> languageList;

    @Data
    public static class RdProductAttributeLangDO implements Serializable {
        private Integer languageId;
        private String productAttributeName;
    }
}
