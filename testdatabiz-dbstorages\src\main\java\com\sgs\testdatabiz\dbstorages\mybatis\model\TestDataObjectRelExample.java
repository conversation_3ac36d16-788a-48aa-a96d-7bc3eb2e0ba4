package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestDataObjectRelExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TestDataObjectRelExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("Id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("Id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIsNull() {
            addCriterion("ProductLineCode is null");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIsNotNull() {
            addCriterion("ProductLineCode is not null");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeEqualTo(String value) {
            addCriterion("ProductLineCode =", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotEqualTo(String value) {
            addCriterion("ProductLineCode <>", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeGreaterThan(String value) {
            addCriterion("ProductLineCode >", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ProductLineCode >=", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLessThan(String value) {
            addCriterion("ProductLineCode <", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLessThanOrEqualTo(String value) {
            addCriterion("ProductLineCode <=", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLike(String value) {
            addCriterion("ProductLineCode like", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotLike(String value) {
            addCriterion("ProductLineCode not like", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIn(List<String> values) {
            addCriterion("ProductLineCode in", values, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotIn(List<String> values) {
            addCriterion("ProductLineCode not in", values, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeBetween(String value1, String value2) {
            addCriterion("ProductLineCode between", value1, value2, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotBetween(String value1, String value2) {
            addCriterion("ProductLineCode not between", value1, value2, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNull() {
            addCriterion("LabCode is null");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNotNull() {
            addCriterion("LabCode is not null");
            return (Criteria) this;
        }

        public Criteria andLabCodeEqualTo(String value) {
            addCriterion("LabCode =", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotEqualTo(String value) {
            addCriterion("LabCode <>", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThan(String value) {
            addCriterion("LabCode >", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThanOrEqualTo(String value) {
            addCriterion("LabCode >=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThan(String value) {
            addCriterion("LabCode <", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThanOrEqualTo(String value) {
            addCriterion("LabCode <=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLike(String value) {
            addCriterion("LabCode like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotLike(String value) {
            addCriterion("LabCode not like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeIn(List<String> values) {
            addCriterion("LabCode in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotIn(List<String> values) {
            addCriterion("LabCode not in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeBetween(String value1, String value2) {
            addCriterion("LabCode between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotBetween(String value1, String value2) {
            addCriterion("LabCode not between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("OrderNo is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("OrderNo is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("OrderNo =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("OrderNo <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("OrderNo >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("OrderNo >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("OrderNo <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("OrderNo <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("OrderNo like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("OrderNo not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("OrderNo in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("OrderNo not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("OrderNo between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("OrderNo not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoIsNull() {
            addCriterion("ParentOrderNo is null");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoIsNotNull() {
            addCriterion("ParentOrderNo is not null");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoEqualTo(String value) {
            addCriterion("ParentOrderNo =", value, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoNotEqualTo(String value) {
            addCriterion("ParentOrderNo <>", value, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoGreaterThan(String value) {
            addCriterion("ParentOrderNo >", value, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("ParentOrderNo >=", value, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoLessThan(String value) {
            addCriterion("ParentOrderNo <", value, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoLessThanOrEqualTo(String value) {
            addCriterion("ParentOrderNo <=", value, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoLike(String value) {
            addCriterion("ParentOrderNo like", value, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoNotLike(String value) {
            addCriterion("ParentOrderNo not like", value, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoIn(List<String> values) {
            addCriterion("ParentOrderNo in", values, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoNotIn(List<String> values) {
            addCriterion("ParentOrderNo not in", values, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoBetween(String value1, String value2) {
            addCriterion("ParentOrderNo between", value1, value2, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andParentOrderNoNotBetween(String value1, String value2) {
            addCriterion("ParentOrderNo not between", value1, value2, "parentOrderNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("ReportNo is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("ReportNo is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("ReportNo =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("ReportNo <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("ReportNo >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("ReportNo >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("ReportNo <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("ReportNo <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("ReportNo like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("ReportNo not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("ReportNo in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("ReportNo not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("ReportNo between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("ReportNo not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoIsNull() {
            addCriterion("ObjectNo is null");
            return (Criteria) this;
        }

        public Criteria andObjectNoIsNotNull() {
            addCriterion("ObjectNo is not null");
            return (Criteria) this;
        }

        public Criteria andObjectNoEqualTo(String value) {
            addCriterion("ObjectNo =", value, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoNotEqualTo(String value) {
            addCriterion("ObjectNo <>", value, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoGreaterThan(String value) {
            addCriterion("ObjectNo >", value, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoGreaterThanOrEqualTo(String value) {
            addCriterion("ObjectNo >=", value, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoLessThan(String value) {
            addCriterion("ObjectNo <", value, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoLessThanOrEqualTo(String value) {
            addCriterion("ObjectNo <=", value, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoLike(String value) {
            addCriterion("ObjectNo like", value, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoNotLike(String value) {
            addCriterion("ObjectNo not like", value, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoIn(List<String> values) {
            addCriterion("ObjectNo in", values, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoNotIn(List<String> values) {
            addCriterion("ObjectNo not in", values, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoBetween(String value1, String value2) {
            addCriterion("ObjectNo between", value1, value2, "objectNo");
            return (Criteria) this;
        }

        public Criteria andObjectNoNotBetween(String value1, String value2) {
            addCriterion("ObjectNo not between", value1, value2, "objectNo");
            return (Criteria) this;
        }

        public Criteria andExternalIdIsNull() {
            addCriterion("ExternalId is null");
            return (Criteria) this;
        }

        public Criteria andExternalIdIsNotNull() {
            addCriterion("ExternalId is not null");
            return (Criteria) this;
        }

        public Criteria andExternalIdEqualTo(String value) {
            addCriterion("ExternalId =", value, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdNotEqualTo(String value) {
            addCriterion("ExternalId <>", value, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdGreaterThan(String value) {
            addCriterion("ExternalId >", value, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdGreaterThanOrEqualTo(String value) {
            addCriterion("ExternalId >=", value, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdLessThan(String value) {
            addCriterion("ExternalId <", value, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdLessThanOrEqualTo(String value) {
            addCriterion("ExternalId <=", value, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdLike(String value) {
            addCriterion("ExternalId like", value, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdNotLike(String value) {
            addCriterion("ExternalId not like", value, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdIn(List<String> values) {
            addCriterion("ExternalId in", values, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdNotIn(List<String> values) {
            addCriterion("ExternalId not in", values, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdBetween(String value1, String value2) {
            addCriterion("ExternalId between", value1, value2, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalIdNotBetween(String value1, String value2) {
            addCriterion("ExternalId not between", value1, value2, "externalId");
            return (Criteria) this;
        }

        public Criteria andExternalNoIsNull() {
            addCriterion("ExternalNo is null");
            return (Criteria) this;
        }

        public Criteria andExternalNoIsNotNull() {
            addCriterion("ExternalNo is not null");
            return (Criteria) this;
        }

        public Criteria andExternalNoEqualTo(String value) {
            addCriterion("ExternalNo =", value, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoNotEqualTo(String value) {
            addCriterion("ExternalNo <>", value, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoGreaterThan(String value) {
            addCriterion("ExternalNo >", value, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoGreaterThanOrEqualTo(String value) {
            addCriterion("ExternalNo >=", value, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoLessThan(String value) {
            addCriterion("ExternalNo <", value, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoLessThanOrEqualTo(String value) {
            addCriterion("ExternalNo <=", value, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoLike(String value) {
            addCriterion("ExternalNo like", value, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoNotLike(String value) {
            addCriterion("ExternalNo not like", value, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoIn(List<String> values) {
            addCriterion("ExternalNo in", values, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoNotIn(List<String> values) {
            addCriterion("ExternalNo not in", values, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoBetween(String value1, String value2) {
            addCriterion("ExternalNo between", value1, value2, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalNoNotBetween(String value1, String value2) {
            addCriterion("ExternalNo not between", value1, value2, "externalNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoIsNull() {
            addCriterion("ExternalObjectNo is null");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoIsNotNull() {
            addCriterion("ExternalObjectNo is not null");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoEqualTo(String value) {
            addCriterion("ExternalObjectNo =", value, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoNotEqualTo(String value) {
            addCriterion("ExternalObjectNo <>", value, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoGreaterThan(String value) {
            addCriterion("ExternalObjectNo >", value, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoGreaterThanOrEqualTo(String value) {
            addCriterion("ExternalObjectNo >=", value, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoLessThan(String value) {
            addCriterion("ExternalObjectNo <", value, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoLessThanOrEqualTo(String value) {
            addCriterion("ExternalObjectNo <=", value, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoLike(String value) {
            addCriterion("ExternalObjectNo like", value, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoNotLike(String value) {
            addCriterion("ExternalObjectNo not like", value, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoIn(List<String> values) {
            addCriterion("ExternalObjectNo in", values, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoNotIn(List<String> values) {
            addCriterion("ExternalObjectNo not in", values, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoBetween(String value1, String value2) {
            addCriterion("ExternalObjectNo between", value1, value2, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExternalObjectNoNotBetween(String value1, String value2) {
            addCriterion("ExternalObjectNo not between", value1, value2, "externalObjectNo");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceIsNull() {
            addCriterion("ExcludeCustomerInterface is null");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceIsNotNull() {
            addCriterion("ExcludeCustomerInterface is not null");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceEqualTo(String value) {
            addCriterion("ExcludeCustomerInterface =", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceNotEqualTo(String value) {
            addCriterion("ExcludeCustomerInterface <>", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceGreaterThan(String value) {
            addCriterion("ExcludeCustomerInterface >", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceGreaterThanOrEqualTo(String value) {
            addCriterion("ExcludeCustomerInterface >=", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceLessThan(String value) {
            addCriterion("ExcludeCustomerInterface <", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceLessThanOrEqualTo(String value) {
            addCriterion("ExcludeCustomerInterface <=", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceLike(String value) {
            addCriterion("ExcludeCustomerInterface like", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceNotLike(String value) {
            addCriterion("ExcludeCustomerInterface not like", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceIn(List<String> values) {
            addCriterion("ExcludeCustomerInterface in", values, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceNotIn(List<String> values) {
            addCriterion("ExcludeCustomerInterface not in", values, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceBetween(String value1, String value2) {
            addCriterion("ExcludeCustomerInterface between", value1, value2, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceNotBetween(String value1, String value2) {
            addCriterion("ExcludeCustomerInterface not between", value1, value2, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andSourceTypeIsNull() {
            addCriterion("SourceType is null");
            return (Criteria) this;
        }

        public Criteria andSourceTypeIsNotNull() {
            addCriterion("SourceType is not null");
            return (Criteria) this;
        }

        public Criteria andSourceTypeEqualTo(Integer value) {
            addCriterion("SourceType =", value, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeNotEqualTo(Integer value) {
            addCriterion("SourceType <>", value, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeGreaterThan(Integer value) {
            addCriterion("SourceType >", value, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("SourceType >=", value, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeLessThan(Integer value) {
            addCriterion("SourceType <", value, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("SourceType <=", value, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeIn(List<Integer> values) {
            addCriterion("SourceType in", values, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeNotIn(List<Integer> values) {
            addCriterion("SourceType not in", values, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeBetween(Integer value1, Integer value2) {
            addCriterion("SourceType between", value1, value2, "sourceType");
            return (Criteria) this;
        }

        public Criteria andSourceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("SourceType not between", value1, value2, "sourceType");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNull() {
            addCriterion("LanguageId is null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNotNull() {
            addCriterion("LanguageId is not null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdEqualTo(Integer value) {
            addCriterion("LanguageId =", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotEqualTo(Integer value) {
            addCriterion("LanguageId <>", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThan(Integer value) {
            addCriterion("LanguageId >", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("LanguageId >=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThan(Integer value) {
            addCriterion("LanguageId <", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThanOrEqualTo(Integer value) {
            addCriterion("LanguageId <=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIn(List<Integer> values) {
            addCriterion("LanguageId in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotIn(List<Integer> values) {
            addCriterion("LanguageId not in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdBetween(Integer value1, Integer value2) {
            addCriterion("LanguageId between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("LanguageId not between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andCompleteDateIsNull() {
            addCriterion("CompleteDate is null");
            return (Criteria) this;
        }

        public Criteria andCompleteDateIsNotNull() {
            addCriterion("CompleteDate is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteDateEqualTo(Date value) {
            addCriterion("CompleteDate =", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateNotEqualTo(Date value) {
            addCriterion("CompleteDate <>", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateGreaterThan(Date value) {
            addCriterion("CompleteDate >", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateGreaterThanOrEqualTo(Date value) {
            addCriterion("CompleteDate >=", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateLessThan(Date value) {
            addCriterion("CompleteDate <", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateLessThanOrEqualTo(Date value) {
            addCriterion("CompleteDate <=", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateIn(List<Date> values) {
            addCriterion("CompleteDate in", values, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateNotIn(List<Date> values) {
            addCriterion("CompleteDate not in", values, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateBetween(Date value1, Date value2) {
            addCriterion("CompleteDate between", value1, value2, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateNotBetween(Date value1, Date value2) {
            addCriterion("CompleteDate not between", value1, value2, "completeDate");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIsNull() {
            addCriterion("BizVersionId is null");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIsNotNull() {
            addCriterion("BizVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdEqualTo(String value) {
            addCriterion("BizVersionId =", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotEqualTo(String value) {
            addCriterion("BizVersionId <>", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdGreaterThan(String value) {
            addCriterion("BizVersionId >", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdGreaterThanOrEqualTo(String value) {
            addCriterion("BizVersionId >=", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLessThan(String value) {
            addCriterion("BizVersionId <", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLessThanOrEqualTo(String value) {
            addCriterion("BizVersionId <=", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLike(String value) {
            addCriterion("BizVersionId like", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotLike(String value) {
            addCriterion("BizVersionId not like", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIn(List<String> values) {
            addCriterion("BizVersionId in", values, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotIn(List<String> values) {
            addCriterion("BizVersionId not in", values, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdBetween(String value1, String value2) {
            addCriterion("BizVersionId between", value1, value2, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotBetween(String value1, String value2) {
            addCriterion("BizVersionId not between", value1, value2, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("ActiveIndicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("ActiveIndicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("ActiveIndicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("ActiveIndicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("ActiveIndicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("ActiveIndicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("ActiveIndicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("ActiveIndicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("ActiveIndicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("ActiveIndicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("ActiveIndicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("ActiveIndicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CreatedBy like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("ModifiedBy is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("ModifiedBy is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("ModifiedBy =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("ModifiedBy <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("ModifiedBy >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("ModifiedBy >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("ModifiedBy <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("ModifiedBy <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("ModifiedBy like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("ModifiedBy not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("ModifiedBy in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("ModifiedBy not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("ModifiedBy between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("ModifiedBy not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("ModifiedDate =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("ModifiedDate <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("ModifiedDate >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("ModifiedDate <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("ModifiedDate in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("ModifiedDate not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("LastModifiedTimestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("LastModifiedTimestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("LastModifiedTimestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("LastModifiedTimestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("LastModifiedTimestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("LastModifiedTimestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("LastModifiedTimestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("LastModifiedTimestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNull() {
            addCriterion("LabId is null");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNotNull() {
            addCriterion("LabId is not null");
            return (Criteria) this;
        }

        public Criteria andLabIdEqualTo(Long value) {
            addCriterion("LabId =", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotEqualTo(Long value) {
            addCriterion("LabId <>", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThan(Long value) {
            addCriterion("LabId >", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThanOrEqualTo(Long value) {
            addCriterion("LabId >=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThan(Long value) {
            addCriterion("LabId <", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThanOrEqualTo(Long value) {
            addCriterion("LabId <=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdIn(List<Long> values) {
            addCriterion("LabId in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotIn(List<Long> values) {
            addCriterion("LabId not in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdBetween(Long value1, Long value2) {
            addCriterion("LabId between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotBetween(Long value1, Long value2) {
            addCriterion("LabId not between", value1, value2, "labId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}