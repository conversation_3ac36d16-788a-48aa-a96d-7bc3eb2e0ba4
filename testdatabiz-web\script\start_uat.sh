#!/bin/bash

source "/etc/profile"
GCLOGPATH="/usr/local/applogs/testdatabiz.iapi.sgs.net/gc.log"
APP_LOG="/usr/local/applogs/testdatabiz.iapi.sgs.net"
MAIN_CLASS="com.sgs.testdatabiz.web.TestDataBizApplication"
APP_NAME="testdatabiz.iapi.sgs.net"
CLASS_PATH="conf:lib/*"
nodenum=$(ifconfig eth0|awk '/inet/{split($2, ip, ".");print ip[4]}')
nodeip=$(ifconfig eth0|awk '/inet /{print $2}')
applicationName=testdatabiz-UAT
agentId=testdatabiz-UAT${nodenum}
JAVA_OPTS=" -server \
            -Xms2048m -Xmx2048m \
            -XX:MaxMetaspaceSize=512m \
            -Djava.rmi.server.hostname=${nodeip} \
            -Dcom.sun.management.jmxremote \
            -Dcom.sun.management.jmxremote.port=8986\
            -Dcom.sun.management.jmxremote.authenticate=false \
            -Dcom.sun.management.jmxremote.ssl=false \
            -javaagent:/opt/pp-agent/pinpoint-bootstrap.jar -Dpinpoint.applicationName=${applicationName} -Dpinpoint.agentId=${agentId} \
            -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled \
            -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=75 \
            -XX:+ScavengeBeforeFullGC -XX:+CMSScavengeBeforeRemark \
            -XX:+PrintGCDateStamps -verbose:gc -XX:+PrintGCDetails -Xloggc:/usr/local/applogs/${APP_NAME}/gc.log \
            -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M \
            -Dsun.net.inetaddr.ttl=60 \
            -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/local/applogs/${APP_NAME}/heapdump.hprof \
            -Dspring.profiles.active=uat"

#############intial work##########################
#cd /usr/local/${APP_NAME}/default
#if [ -e "logs" ]; then
#    rm logs
#fi
#ln -s /usr/local/log/${APP_NAME}/ logs

if [ ! -d $APP_LOG ]; then
    mkdir -p $APP_LOG
fi

##############launch the service##################
java ${JAVA_OPTS} -cp ${CLASS_PATH} ${MAIN_CLASS}

