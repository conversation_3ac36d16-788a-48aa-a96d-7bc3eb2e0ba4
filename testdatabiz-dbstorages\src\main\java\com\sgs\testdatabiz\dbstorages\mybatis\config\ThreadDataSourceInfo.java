package com.sgs.testdatabiz.dbstorages.mybatis.config;

import com.sgs.testdatabiz.dbstorages.mybatis.enums.DatabaseTypeEnum;
import com.sgs.framework.model.enums.ProductLineType;

public class ThreadDataSourceInfo {
    /**
     *
     */
    private String appCode;
    /**
     *
     */
    private String dbName;
    /**
     *
     */
    private ProductLineType productLineType;
    /**
     *
     */
    private DatabaseTypeEnum databaseType;
    /**
     *
     */
    private boolean required;

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public ProductLineType getProductLineType() {
        return productLineType;
    }

    public void setProductLineType(ProductLineType productLineType) {
        this.productLineType = productLineType;
    }

    public DatabaseTypeEnum getDatabaseType() {
        return databaseType;
    }

    public void setDatabaseType(DatabaseTypeEnum databaseType) {
        this.databaseType = databaseType;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public String getDataSourceKey(){
        // {DbName}_{SL/HL}_{Master/Slave}
        return String.format("%s-%s-%s", dbName, productLineType, databaseType);
    }
}
