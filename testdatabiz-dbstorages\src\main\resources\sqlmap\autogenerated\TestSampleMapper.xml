<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestSampleMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sample_instance_id" property="sampleInstanceId" jdbcType="VARCHAR" />
    <result column="sample_parent_id" property="sampleParentId" jdbcType="VARCHAR" />
    <result column="rd_report_matrix_id" property="rdReportMatrixId" jdbcType="BIGINT" />
    <result column="sample_no" property="sampleNo" jdbcType="VARCHAR" />
    <result column="sample_type" property="sampleType" jdbcType="INTEGER" />
    <result column="sample_seq" property="sampleSeq" jdbcType="INTEGER" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="category" property="category" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="composition" property="composition" jdbcType="VARCHAR" />
    <result column="color" property="color" jdbcType="VARCHAR" />
    <result column="sample_remark" property="sampleRemark" jdbcType="VARCHAR" />
    <result column="end_use" property="endUse" jdbcType="VARCHAR" />
    <result column="material" property="material" jdbcType="VARCHAR" />
    <result column="applicable_flag" property="applicableFlag" jdbcType="TINYINT" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO" extends="BaseResultMap" >
    <result column="other_sample_info" property="otherSampleInfo" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sample_instance_id, sample_parent_id, rd_report_matrix_id, sample_no, sample_type, 
    sample_seq, order_no, category, description, composition, color, sample_remark, end_use, 
    material, applicable_flag, active_indicator, created_date, created_by, modified_date, 
    modified_by, last_modified_timestamp
  </sql>
  <sql id="Blob_Column_List" >
    other_sample_info
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_sample
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_test_sample
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleExample" >
    delete from tb_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO" >
    insert into tb_test_sample (id, sample_instance_id, sample_parent_id, 
      rd_report_matrix_id, sample_no, sample_type, 
      sample_seq, order_no, category, 
      description, composition, color, 
      sample_remark, end_use, material, 
      applicable_flag, active_indicator, created_date, 
      created_by, modified_date, modified_by, 
      last_modified_timestamp, other_sample_info
      )
    values (#{id,jdbcType=BIGINT}, #{sampleInstanceId,jdbcType=VARCHAR}, #{sampleParentId,jdbcType=VARCHAR}, 
      #{rdReportMatrixId,jdbcType=BIGINT}, #{sampleNo,jdbcType=VARCHAR}, #{sampleType,jdbcType=INTEGER}, 
      #{sampleSeq,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{composition,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR}, 
      #{sampleRemark,jdbcType=VARCHAR}, #{endUse,jdbcType=VARCHAR}, #{material,jdbcType=VARCHAR}, 
      #{applicableFlag,jdbcType=TINYINT}, #{activeIndicator,jdbcType=TINYINT}, now(), 
      #{createdBy,jdbcType=VARCHAR}, now(), #{modifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTimestamp,jdbcType=TIMESTAMP}, #{otherSampleInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO" >
    insert into tb_test_sample
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sampleInstanceId != null" >
        sample_instance_id,
      </if>
      <if test="sampleParentId != null" >
        sample_parent_id,
      </if>
      <if test="rdReportMatrixId != null" >
        rd_report_matrix_id,
      </if>
      <if test="sampleNo != null" >
        sample_no,
      </if>
      <if test="sampleType != null" >
        sample_type,
      </if>
      <if test="sampleSeq != null" >
        sample_seq,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="category != null" >
        category,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="composition != null" >
        composition,
      </if>
      <if test="color != null" >
        color,
      </if>
      <if test="sampleRemark != null" >
        sample_remark,
      </if>
      <if test="endUse != null" >
        end_use,
      </if>
      <if test="material != null" >
        material,
      </if>
      <if test="applicableFlag != null" >
        applicable_flag,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
      <if test="otherSampleInfo != null" >
        other_sample_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sampleInstanceId != null" >
        #{sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="sampleParentId != null" >
        #{sampleParentId,jdbcType=VARCHAR},
      </if>
      <if test="rdReportMatrixId != null" >
        #{rdReportMatrixId,jdbcType=BIGINT},
      </if>
      <if test="sampleNo != null" >
        #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleType != null" >
        #{sampleType,jdbcType=INTEGER},
      </if>
      <if test="sampleSeq != null" >
        #{sampleSeq,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="composition != null" >
        #{composition,jdbcType=VARCHAR},
      </if>
      <if test="color != null" >
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="sampleRemark != null" >
        #{sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="endUse != null" >
        #{endUse,jdbcType=VARCHAR},
      </if>
      <if test="material != null" >
        #{material,jdbcType=VARCHAR},
      </if>
      <if test="applicableFlag != null" >
        #{applicableFlag,jdbcType=TINYINT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="otherSampleInfo != null" >
        #{otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_sample
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sampleInstanceId != null" >
        sample_instance_id = #{record.sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleParentId != null" >
        sample_parent_id = #{record.sampleParentId,jdbcType=VARCHAR},
      </if>
      <if test="record.rdReportMatrixId != null" >
        rd_report_matrix_id = #{record.rdReportMatrixId,jdbcType=BIGINT},
      </if>
      <if test="record.sampleNo != null" >
        sample_no = #{record.sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleType != null" >
        sample_type = #{record.sampleType,jdbcType=INTEGER},
      </if>
      <if test="record.sampleSeq != null" >
        sample_seq = #{record.sampleSeq,jdbcType=INTEGER},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null" >
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null" >
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.composition != null" >
        composition = #{record.composition,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null" >
        color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleRemark != null" >
        sample_remark = #{record.sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.endUse != null" >
        end_use = #{record.endUse,jdbcType=VARCHAR},
      </if>
      <if test="record.material != null" >
        material = #{record.material,jdbcType=VARCHAR},
      </if>
      <if test="record.applicableFlag != null" >
        applicable_flag = #{record.applicableFlag,jdbcType=TINYINT},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.otherSampleInfo != null" >
        other_sample_info = #{record.otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_test_sample
    set id = #{record.id,jdbcType=BIGINT},
      sample_instance_id = #{record.sampleInstanceId,jdbcType=VARCHAR},
      sample_parent_id = #{record.sampleParentId,jdbcType=VARCHAR},
      rd_report_matrix_id = #{record.rdReportMatrixId,jdbcType=BIGINT},
      sample_no = #{record.sampleNo,jdbcType=VARCHAR},
      sample_type = #{record.sampleType,jdbcType=INTEGER},
      sample_seq = #{record.sampleSeq,jdbcType=INTEGER},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      composition = #{record.composition,jdbcType=VARCHAR},
      color = #{record.color,jdbcType=VARCHAR},
      sample_remark = #{record.sampleRemark,jdbcType=VARCHAR},
      end_use = #{record.endUse,jdbcType=VARCHAR},
      material = #{record.material,jdbcType=VARCHAR},
      applicable_flag = #{record.applicableFlag,jdbcType=TINYINT},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
    
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = now(),
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      other_sample_info = #{record.otherSampleInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_sample
    set id = #{record.id,jdbcType=BIGINT},
      sample_instance_id = #{record.sampleInstanceId,jdbcType=VARCHAR},
      sample_parent_id = #{record.sampleParentId,jdbcType=VARCHAR},
      rd_report_matrix_id = #{record.rdReportMatrixId,jdbcType=BIGINT},
      sample_no = #{record.sampleNo,jdbcType=VARCHAR},
      sample_type = #{record.sampleType,jdbcType=INTEGER},
      sample_seq = #{record.sampleSeq,jdbcType=INTEGER},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      composition = #{record.composition,jdbcType=VARCHAR},
      color = #{record.color,jdbcType=VARCHAR},
      sample_remark = #{record.sampleRemark,jdbcType=VARCHAR},
      end_use = #{record.endUse,jdbcType=VARCHAR},
      material = #{record.material,jdbcType=VARCHAR},
      applicable_flag = #{record.applicableFlag,jdbcType=TINYINT},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
    
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = now(),
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO" >
    update tb_test_sample
    <set >
      <if test="sampleInstanceId != null" >
        sample_instance_id = #{sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="sampleParentId != null" >
        sample_parent_id = #{sampleParentId,jdbcType=VARCHAR},
      </if>
      <if test="rdReportMatrixId != null" >
        rd_report_matrix_id = #{rdReportMatrixId,jdbcType=BIGINT},
      </if>
      <if test="sampleNo != null" >
        sample_no = #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleType != null" >
        sample_type = #{sampleType,jdbcType=INTEGER},
      </if>
      <if test="sampleSeq != null" >
        sample_seq = #{sampleSeq,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="composition != null" >
        composition = #{composition,jdbcType=VARCHAR},
      </if>
      <if test="color != null" >
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="sampleRemark != null" >
        sample_remark = #{sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="endUse != null" >
        end_use = #{endUse,jdbcType=VARCHAR},
      </if>
      <if test="material != null" >
        material = #{material,jdbcType=VARCHAR},
      </if>
      <if test="applicableFlag != null" >
        applicable_flag = #{applicableFlag,jdbcType=TINYINT},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="otherSampleInfo != null" >
        other_sample_info = #{otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO" >
    update tb_test_sample
    set sample_instance_id = #{sampleInstanceId,jdbcType=VARCHAR},
      sample_parent_id = #{sampleParentId,jdbcType=VARCHAR},
      rd_report_matrix_id = #{rdReportMatrixId,jdbcType=BIGINT},
      sample_no = #{sampleNo,jdbcType=VARCHAR},
      sample_type = #{sampleType,jdbcType=INTEGER},
      sample_seq = #{sampleSeq,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      composition = #{composition,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      sample_remark = #{sampleRemark,jdbcType=VARCHAR},
      end_use = #{endUse,jdbcType=VARCHAR},
      material = #{material,jdbcType=VARCHAR},
      applicable_flag = #{applicableFlag,jdbcType=TINYINT},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
    
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = now(),
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      other_sample_info = #{otherSampleInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSamplePO" >
    update tb_test_sample
    set sample_instance_id = #{sampleInstanceId,jdbcType=VARCHAR},
      sample_parent_id = #{sampleParentId,jdbcType=VARCHAR},
      rd_report_matrix_id = #{rdReportMatrixId,jdbcType=BIGINT},
      sample_no = #{sampleNo,jdbcType=VARCHAR},
      sample_type = #{sampleType,jdbcType=INTEGER},
      sample_seq = #{sampleSeq,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      composition = #{composition,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      sample_remark = #{sampleRemark,jdbcType=VARCHAR},
      end_use = #{endUse,jdbcType=VARCHAR},
      material = #{material,jdbcType=VARCHAR},
      applicable_flag = #{applicableFlag,jdbcType=TINYINT},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
    
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = now(),
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_sample
      (`id`,`sample_instance_id`,`sample_parent_id`,
      `rd_report_matrix_id`,`sample_no`,`sample_type`,
      `sample_seq`,`order_no`,`category`,
      `description`,`composition`,`color`,
      `sample_remark`,`end_use`,`material`,
      `applicable_flag`,`active_indicator`,`created_date`,
      `created_by`,`modified_date`,`modified_by`,
      `last_modified_timestamp`,`other_sample_info`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.sampleInstanceId, jdbcType=VARCHAR},#{ item.sampleParentId, jdbcType=VARCHAR},
      #{ item.rdReportMatrixId, jdbcType=BIGINT},#{ item.sampleNo, jdbcType=VARCHAR},#{ item.sampleType, jdbcType=INTEGER},
      #{ item.sampleSeq, jdbcType=INTEGER},#{ item.orderNo, jdbcType=VARCHAR},#{ item.category, jdbcType=VARCHAR},
      #{ item.description, jdbcType=VARCHAR},#{ item.composition, jdbcType=VARCHAR},#{ item.color, jdbcType=VARCHAR},
      #{ item.sampleRemark, jdbcType=VARCHAR},#{ item.endUse, jdbcType=VARCHAR},#{ item.material, jdbcType=VARCHAR},
      #{ item.applicableFlag, jdbcType=TINYINT},#{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.createdBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},
      #{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP},#{ item.otherSampleInfo, jdbcType=LONGVARCHAR}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_sample 
      <set>
        <if test="item.sampleInstanceId != null"> 
          `sample_instance_id` = #{item.sampleInstanceId, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleParentId != null"> 
          `sample_parent_id` = #{item.sampleParentId, jdbcType = VARCHAR},
        </if> 
        <if test="item.rdReportMatrixId != null"> 
          `rd_report_matrix_id` = #{item.rdReportMatrixId, jdbcType = BIGINT},
        </if> 
        <if test="item.sampleNo != null"> 
          `sample_no` = #{item.sampleNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleType != null"> 
          `sample_type` = #{item.sampleType, jdbcType = INTEGER},
        </if> 
        <if test="item.sampleSeq != null"> 
          `sample_seq` = #{item.sampleSeq, jdbcType = INTEGER},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.category != null"> 
          `category` = #{item.category, jdbcType = VARCHAR},
        </if> 
        <if test="item.description != null"> 
          `description` = #{item.description, jdbcType = VARCHAR},
        </if> 
        <if test="item.composition != null"> 
          `composition` = #{item.composition, jdbcType = VARCHAR},
        </if> 
        <if test="item.color != null"> 
          `color` = #{item.color, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleRemark != null"> 
          `sample_remark` = #{item.sampleRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.endUse != null"> 
          `end_use` = #{item.endUse, jdbcType = VARCHAR},
        </if> 
        <if test="item.material != null"> 
          `material` = #{item.material, jdbcType = VARCHAR},
        </if> 
        <if test="item.applicableFlag != null"> 
          `applicable_flag` = #{item.applicableFlag, jdbcType = TINYINT},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.otherSampleInfo != null"> 
          `other_sample_info` = #{item.otherSampleInfo, jdbcType = LONGVARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>