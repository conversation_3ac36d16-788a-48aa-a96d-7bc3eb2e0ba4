/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdConditionGroupDO {

    // add 20230529
    private String orderNo;
    private String realOrderNo;
    // add 20230529
    private Integer systemId;
    private String conditionGroupId;
    private String combinedConditionDescription;
    private String requirement;
    private List<RdPpConditionGroupDO> ppConditionGroupList;
    private List<RdConditionGroupLanguageDO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
