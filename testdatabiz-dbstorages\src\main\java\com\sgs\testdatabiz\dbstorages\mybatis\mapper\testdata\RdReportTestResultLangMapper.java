package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangPO;
import java.util.List;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import org.apache.ibatis.annotations.Param;

public interface RdReportTestResultLangMapper {
    int countByExample(RdReportTestResultLangExample example);

    int deleteByExample(RdReportTestResultLangExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportTestResultLangPO record);

    int insertSelective(RdReportTestResultLangPO record);

    List<RdReportTestResultLangPO> selectByExample(RdReportTestResultLangExample example);

    RdReportTestResultLangPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportTestResultLangPO record, @Param("example") RdReportTestResultLangExample example);

    int updateByExample(@Param("record") RdReportTestResultLangPO record, @Param("example") RdReportTestResultLangExample example);

    int updateByPrimaryKeySelective(RdReportTestResultLangPO record);

    int updateByPrimaryKey(RdReportTestResultLangPO record);

    int batchInsert(List<RdReportTestResultLangPO> list);

    int batchUpdate(List<RdReportTestResultLangPO> list);
}
