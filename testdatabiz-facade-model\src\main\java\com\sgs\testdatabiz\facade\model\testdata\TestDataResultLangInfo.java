package com.sgs.testdatabiz.facade.model.testdata;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

public class TestDataResultLangInfo extends PrintFriendliness {
    /**
     * 多语言（1：英文、2：中文）
     */
    @ApiModelProperty("多语言（1：英文、2：中文）")
    private Integer languageId;

    /**
     *
     */
    @ApiModelProperty("分析项名称 取值规则： \n \n " +
            "   StarLims：取analyteAlias  \n "+
            "   SLIM：取LSA_DESC  \n "+
            "")
    private String testAnalyteName;

    /**
     *
     */
    @ApiModelProperty("测试单位")
    private String reportUnit;

    /**
     *
     */
    @ApiModelProperty("测试单位 取值规则： \n \n " +
            "   StarLims：取limit  \n "+
            "")
    private String limitUnit;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getLimitUnit() {
        return limitUnit;
    }

    public void setLimitUnit(String limitUnit) {
        this.limitUnit = limitUnit;
    }
}
