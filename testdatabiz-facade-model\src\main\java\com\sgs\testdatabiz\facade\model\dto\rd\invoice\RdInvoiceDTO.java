/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.invoice;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdAttachmentDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProcessListDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdInvoiceDTO implements Serializable{
    // add 20230529
    private String orderNo;
    private String rootOrderNo;
    /**
     * 真实订单号,外部请求时会传rootOrderNo和orderNo来对应逻辑订单号和子订单号
     * DO内部会转换成OrderNo和realOrderNo
     * 内部DO和DTO转换时需要冗余，分包场景会需要使用
     * */
    private String realOrderNo;
    // add 20230529
    private Integer systemId;
    private String invoiceNo;
    private List<String> quotationNos;
    private String currency;
    private String bossOrderNo;
    private String productCode;
    private String costCenter;
    private String projectTemplate;
    private BigDecimal netAmount;
    private BigDecimal vatAmount;
    private BigDecimal totalAmount;
    // add 20230720
    private BigDecimal prePaidAmount;
    private Integer invoiceStatus;
    private Date invoiceDate;
    private List<RdAttachmentDTO> invoiceFileList;

    private List<RdProcessListDTO> processList;

    private String reportNo;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String invoiceInstanceId;

    private String sourceSystem;

}
