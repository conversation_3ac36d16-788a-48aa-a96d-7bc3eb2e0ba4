package com.sgs.testdatabiz.domain.service.testdata.model;

import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * @author: shawn.yang
 * @create: 2023-03-23 15:30
 */
@Getter
@ToString
public final class ErrorMsg {
    private final String errCode;
    private final String groupCode;
    private final String msg;

    public ErrorMsg(String msg) {
        this.errCode = msg;
        this.groupCode = null;
        this.msg = msg;
    }

    public ErrorMsg(String errCode, String groupCode, String msg) {
        this.errCode = errCode;
        this.groupCode = groupCode;
        this.msg = msg;
    }

    public static ErrorMsg from(String errCode, String groupCode, String msg) {
        return new ErrorMsg(errCode, groupCode, msg);
    }

    public String getErrCode() {
        return errCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public String getMsg() {
        return msg;
    }

    @Override
    public int hashCode() {
        return Objects.hash(errCode, groupCode, msg);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ErrorMsg errorMsg = (ErrorMsg) o;
        return errCode.equals(errorMsg.errCode) && groupCode.equals(errorMsg.groupCode) && msg.equals(errorMsg.msg);
    }
}

