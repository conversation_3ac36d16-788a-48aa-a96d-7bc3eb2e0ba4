/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdConclusionDO {

    private String conclusionCode;
    private String reviewConclusion;
    private String customerConclusion;
    private String conclusionRemark;
    private String comments;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
