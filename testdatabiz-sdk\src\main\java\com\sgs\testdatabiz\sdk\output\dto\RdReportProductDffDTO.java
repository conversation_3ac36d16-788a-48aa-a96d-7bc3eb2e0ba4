package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdReportProductDffDTO extends BaseModel{


    private Long labId;

    private String orderNo;

    private String reportNo;

    private Integer objectType;

    private String objectInstanceId;

    private String formId;

    private Integer languageId;

    private String labelCode;

    private String labelName;

    private String fieldCode;

    private String customerLabel;

    private String dataType;

    private String value;

    private Integer seq;

    private String displayInReport;

    private String formGroupId;

    private String objectTypeLabel;

    private String dataTypeLabel;

    private Integer activeIndicator;

    private Date lastModifiedTimestamp;

}
