package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdQuotationMapper {
    int countByExample(RdQuotationExample example);

    int deleteByExample(RdQuotationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdQuotationPO record);

    int insertSelective(RdQuotationPO record);

    List<RdQuotationPO> selectByExample(RdQuotationExample example);

    RdQuotationPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdQuotationPO record, @Param("example") RdQuotationExample example);

    int updateByExample(@Param("record") RdQuotationPO record, @Param("example") RdQuotationExample example);

    int updateByPrimaryKeySelective(RdQuotationPO record);

    int updateByPrimaryKey(RdQuotationPO record);

    int batchInsert(List<RdQuotationPO> list);

    int batchUpdate(List<RdQuotationPO> list);
}