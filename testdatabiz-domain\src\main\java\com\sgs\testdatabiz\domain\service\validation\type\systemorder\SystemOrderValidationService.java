package com.sgs.testdatabiz.domain.service.validation.type.systemorder;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.model.ReportTestDataValidationDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component("systemOrderValidationService")
public class SystemOrderValidationService implements ValidationService {

    @Override
    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO) {
        ReportTestDataValidationDTO reportTestDataValidationDTO = (ReportTestDataValidationDTO) validationRequestDTO;
        ReportDataBatchDTO reportData = reportTestDataValidationDTO.getReportDataBatchDTO();
        
        List<String> errorMessages = new ArrayList<>();

        // 校验TRF列表
        if (Func.isNotEmpty(reportData.getTrfList())) {
            for (int i = 0; i < reportData.getTrfList().size(); i++) {
                RdTrfDTO trf = reportData.getTrfList().get(i);
                if (trf.getOrderList() != null) {
                    for (int j = 0; j < trf.getOrderList().size(); j++) {
                        validateSystemIdAndOrderNo(trf.getOrderList().get(j), 
                            String.format("TRF[%d].OrderList[%d]", i, j), errorMessages);
                    }
                }
            }
        }

        // 校验Order列表
        if (Func.isNotEmpty(reportData.getOrderList())) {
            for (int i = 0; i < reportData.getOrderList().size(); i++) {
                validateSystemIdAndOrderNo(reportData.getOrderList().get(i), 
                    String.format("OrderList[%d]", i), errorMessages);
            }
        }

        // 校验Report列表
        if (Func.isNotEmpty(reportData.getReportList())) {
            for (int i = 0; i < reportData.getReportList().size(); i++) {
                RdReportDTO report = reportData.getReportList().get(i);
                validateSystemIdAndOrderNo(report, 
                    String.format("Report[%d] (ReportNo: %s)", i, report.getReportNo()), errorMessages);
            }
        }

        // 校验TestLine列表
        if (Func.isNotEmpty(reportData.getTestLineList())) {
            for (int i = 0; i < reportData.getTestLineList().size(); i++) {
                RdTestLineDTO testLine = reportData.getTestLineList().get(i);
                validateSystemIdAndOrderNo(testLine,
                    String.format("TestLine[%d] (TestLineInstanceId: %s)", 
                        i, testLine.getTestLineInstanceId()), errorMessages);
            }
        }

        // 校验TestSample列表
        if (Func.isNotEmpty(reportData.getTestSampleList())) {
            reportData.getTestSampleList().forEach(sample -> validateSystemIdAndOrderNo(sample, "TestSampleList", errorMessages));
        }

        // 校验TestResult列表
        if (Func.isNotEmpty(reportData.getTestResultList())) {
            reportData.getTestResultList().forEach(result -> validateSystemIdAndOrderNo(result, "TestResultList", errorMessages));
        }

        // 校验ReportConclusion列表
        if (Func.isNotEmpty(reportData.getReportConclusionList())) {
            reportData.getReportConclusionList().forEach(conclusion -> 
                validateSystemIdAndOrderNo(conclusion, "ReportConclusionList", errorMessages));
        }

        // 校验ConditionGroup列表
        if (Func.isNotEmpty(reportData.getConditionGroupList())) {
            reportData.getConditionGroupList().forEach(condition -> 
                validateSystemIdAndOrderNo(condition, "ConditionGroupList", errorMessages));
        }

        // 校验Quotation列表
        if (Func.isNotEmpty(reportData.getQuotationList())) {
            reportData.getQuotationList().forEach(quotation -> 
                validateSystemIdAndOrderNo(quotation, "QuotationList", errorMessages));
        }

        // 校验Invoice列表
        if (Func.isNotEmpty(reportData.getInvoiceList())) {
            reportData.getInvoiceList().forEach(invoice -> 
                validateSystemIdAndOrderNo(invoice, "InvoiceList", errorMessages));
        }

        if (errorMessages.isEmpty()) {
            return ValidationResultDTO.success("All systemId and orderNo validations passed");
        } else {
            return ValidationResultDTO.fail(formatErrorMessages(errorMessages));
        }
    }

    private void validateSystemIdAndOrderNo(Object obj, String location, List<String> errorMessages) {
        try {
            Integer systemId = (Integer) obj.getClass().getMethod("getSystemId").invoke(obj);
            String orderNo = (String) obj.getClass().getMethod("getOrderNo").invoke(obj);
            
            if (systemId == null) {
                errorMessages.add(String.format("[%s] SystemId is missing", location));
            }
            if (Func.isEmpty(orderNo)) {
                errorMessages.add(String.format("[%s] OrderNo is missing", location));
            }
        } catch (Exception e) {
            errorMessages.add(String.format("[%s] Failed to validate: %s", 
                    location, e.getMessage()));
        }
    }

    private String formatErrorMessages(List<String> errorMessages) {
        StringBuilder sb = new StringBuilder("System ID and Order No validation failed:\n");
        for (int i = 0; i < errorMessages.size(); i++) {
            sb.append(String.format("%d. %s\n", i + 1, errorMessages.get(i)));
        }
        return sb.toString();
    }

    @Override
    public String getType() {
        return ValidationTypeEnum.SYSTEM_ORDER_NO.getName();
    }

    @Override
    public Integer getOrder() {
        return 1; // 设置较高优先级，因为这是基础校验
    }
} 