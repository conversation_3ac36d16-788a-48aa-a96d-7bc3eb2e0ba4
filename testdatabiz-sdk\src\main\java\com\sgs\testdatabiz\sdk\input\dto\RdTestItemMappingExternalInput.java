/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestItemMappingExternalInput implements Serializable {
    private Integer testLineMappingId;
    private String testItemId;
    private String testItemName;
    private String citationId;
    private String citationName;


    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
