package com.sgs.testdatabiz.domain.service.rd.manager;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.ApiRequestMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportLangMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-04-23 14:21
 */
@Slf4j
@Component
public class ApiRequestManager {

    private final ApiRequestMapper apiRequestMapper;

    @Autowired
    private IdService idService;

    public void insert(String methodName, String extId, String requestBody, String responseBody,String labCode,String createBy,String status) {
        try {

            ApiRequestPO apiRequestPO = new ApiRequestPO();
            apiRequestPO.setId(idService.nextId());
            apiRequestPO.setSystemId(0);
            apiRequestPO.setLabCode(labCode);
            apiRequestPO.setRequestId(UUID.randomUUID().toString());
            apiRequestPO.setMethodName(methodName);
            apiRequestPO.setExtId(extId);
            apiRequestPO.setRequestHeader(null);
            apiRequestPO.setRequestBody(requestBody);
            apiRequestPO.setResponseBody(responseBody);
            apiRequestPO.setResponseStatus(status);
            apiRequestPO.setCreatedBy(createBy);
            apiRequestPO.setCreatedDate(new Date());
            apiRequestPO.setModifiedBy(createBy);
            apiRequestPO.setModifiedDate(new Date());
            apiRequestPO.setLastModifiedTimestamp(new Date());
            apiRequestMapper.insert(apiRequestPO);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }

    public ApiRequestManager(ApiRequestMapper apiRequestMapper) {
        this.apiRequestMapper = apiRequestMapper;
    }
}
