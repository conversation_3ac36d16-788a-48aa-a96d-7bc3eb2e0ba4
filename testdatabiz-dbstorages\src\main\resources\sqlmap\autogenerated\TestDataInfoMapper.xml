<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestDataInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="ObjectRelId" property="objectRelId" jdbcType="VARCHAR" />
    <result column="TestDataMatrixId" property="testDataMatrixId" jdbcType="BIGINT" />
    <result column="TestMatrixId" property="testMatrixId" jdbcType="VARCHAR" />
    <result column="AnalyteId" property="analyteId" jdbcType="VARCHAR" />
    <result column="AnalyteName" property="analyteName" jdbcType="VARCHAR" />
    <result column="AnalyteType" property="analyteType" jdbcType="INTEGER" />
    <result column="AnalyteCode" property="analyteCode" jdbcType="VARCHAR" />
    <result column="AnalyteSeq" property="analyteSeq" jdbcType="INTEGER" />
    <result column="ReportUnit" property="reportUnit" jdbcType="VARCHAR" />
    <result column="TestValue" property="testValue" jdbcType="VARCHAR" />
    <result column="CasNo" property="casNo" jdbcType="VARCHAR" />
    <result column="ReportLimit" property="reportLimit" jdbcType="VARCHAR" />
    <result column="LimitUnit" property="limitUnit" jdbcType="VARCHAR" />
    <result column="ConclusionId" property="conclusionId" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="rd_report_id" property="rdReportId" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="test_result_full_name" property="testResultFullName" jdbcType="VARCHAR" />
    <result column="test_result_seq" property="testResultSeq" jdbcType="INTEGER" />
    <result column="result_value" property="resultValue" jdbcType="VARCHAR" />
    <result column="result_value_remark" property="resultValueRemark" jdbcType="VARCHAR" />
    <result column="result_unit" property="resultUnit" jdbcType="VARCHAR" />
    <result column="fail_flag" property="failFlag" jdbcType="TINYINT" />
    <result column="limit_value_full_name" property="limitValueFullName" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" extends="BaseResultMap" >
    <result column="Position" property="position" jdbcType="LONGVARCHAR" />
    <result column="Languages" property="languages" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, ObjectRelId, TestDataMatrixId, TestMatrixId, AnalyteId, AnalyteName, AnalyteType, 
    AnalyteCode, AnalyteSeq, ReportUnit, TestValue, CasNo, ReportLimit, LimitUnit, ConclusionId, 
    ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, BizVersionId, 
    rd_report_id, lab_id, order_no, report_no, test_result_full_name, test_result_seq, 
    result_value, result_value_remark, result_unit, fail_flag, limit_value_full_name
  </sql>
  <sql id="Blob_Column_List" >
    `Position`, Languages
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_data_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_data_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_data_info
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_test_data_info
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoExample" >
    delete from tb_test_data_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" >
    insert into tb_test_data_info (Id, ObjectRelId, TestDataMatrixId, 
      TestMatrixId, AnalyteId, AnalyteName, 
      AnalyteType, AnalyteCode, AnalyteSeq, 
      ReportUnit, TestValue, CasNo, 
      ReportLimit, LimitUnit, ConclusionId, 
      ActiveIndicator, CreatedBy, CreatedDate, 
      ModifiedBy, ModifiedDate, BizVersionId, 
      rd_report_id, lab_id, order_no, 
      report_no, test_result_full_name, test_result_seq, 
      result_value, result_value_remark, result_unit, 
      fail_flag, limit_value_full_name, `Position`, 
      Languages)
    values (#{id,jdbcType=BIGINT}, #{objectrelid,jdbcType=VARCHAR}, #{testdatamatrixid,jdbcType=BIGINT}, 
      #{testmatrixid,jdbcType=VARCHAR}, #{analyteid,jdbcType=VARCHAR}, #{analytename,jdbcType=VARCHAR}, 
      #{analytetype,jdbcType=INTEGER}, #{analytecode,jdbcType=VARCHAR}, #{analyteseq,jdbcType=INTEGER}, 
      #{reportunit,jdbcType=VARCHAR}, #{testvalue,jdbcType=VARCHAR}, #{casno,jdbcType=VARCHAR}, 
      #{reportlimit,jdbcType=VARCHAR}, #{limitunit,jdbcType=VARCHAR}, #{conclusionid,jdbcType=VARCHAR}, 
      #{activeindicator,jdbcType=INTEGER}, #{createdby,jdbcType=VARCHAR}, #{createddate,jdbcType=TIMESTAMP}, 
      #{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP}, #{bizversionid,jdbcType=CHAR}, 
      #{rdReportId,jdbcType=BIGINT}, #{labId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{testResultFullName,jdbcType=VARCHAR}, #{testResultSeq,jdbcType=INTEGER}, 
      #{resultValue,jdbcType=VARCHAR}, #{resultValueRemark,jdbcType=VARCHAR}, #{resultUnit,jdbcType=VARCHAR}, 
      #{failFlag,jdbcType=TINYINT}, #{limitValueFullName,jdbcType=VARCHAR}, #{position,jdbcType=LONGVARCHAR}, 
      #{languages,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" >
    insert into tb_test_data_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="objectrelid != null" >
        ObjectRelId,
      </if>
      <if test="testdatamatrixid != null" >
        TestDataMatrixId,
      </if>
      <if test="testmatrixid != null" >
        TestMatrixId,
      </if>
      <if test="analyteid != null" >
        AnalyteId,
      </if>
      <if test="analytename != null" >
        AnalyteName,
      </if>
      <if test="analytetype != null" >
        AnalyteType,
      </if>
      <if test="analytecode != null" >
        AnalyteCode,
      </if>
      <if test="analyteseq != null" >
        AnalyteSeq,
      </if>
      <if test="reportunit != null" >
        ReportUnit,
      </if>
      <if test="testvalue != null" >
        TestValue,
      </if>
      <if test="casno != null" >
        CasNo,
      </if>
      <if test="reportlimit != null" >
        ReportLimit,
      </if>
      <if test="limitunit != null" >
        LimitUnit,
      </if>
      <if test="conclusionid != null" >
        ConclusionId,
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdby != null" >
        CreatedBy,
      </if>
      <if test="createddate != null" >
        CreatedDate,
      </if>
      <if test="modifiedby != null" >
        ModifiedBy,
      </if>
      <if test="modifieddate != null" >
        ModifiedDate,
      </if>
      <if test="bizversionid != null" >
        BizVersionId,
      </if>
      <if test="rdReportId != null" >
        rd_report_id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="testResultFullName != null" >
        test_result_full_name,
      </if>
      <if test="testResultSeq != null" >
        test_result_seq,
      </if>
      <if test="resultValue != null" >
        result_value,
      </if>
      <if test="resultValueRemark != null" >
        result_value_remark,
      </if>
      <if test="resultUnit != null" >
        result_unit,
      </if>
      <if test="failFlag != null" >
        fail_flag,
      </if>
      <if test="limitValueFullName != null" >
        limit_value_full_name,
      </if>
      <if test="position != null" >
        `Position`,
      </if>
      <if test="languages != null" >
        Languages,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="objectrelid != null" >
        #{objectrelid,jdbcType=VARCHAR},
      </if>
      <if test="testdatamatrixid != null" >
        #{testdatamatrixid,jdbcType=BIGINT},
      </if>
      <if test="testmatrixid != null" >
        #{testmatrixid,jdbcType=VARCHAR},
      </if>
      <if test="analyteid != null" >
        #{analyteid,jdbcType=VARCHAR},
      </if>
      <if test="analytename != null" >
        #{analytename,jdbcType=VARCHAR},
      </if>
      <if test="analytetype != null" >
        #{analytetype,jdbcType=INTEGER},
      </if>
      <if test="analytecode != null" >
        #{analytecode,jdbcType=VARCHAR},
      </if>
      <if test="analyteseq != null" >
        #{analyteseq,jdbcType=INTEGER},
      </if>
      <if test="reportunit != null" >
        #{reportunit,jdbcType=VARCHAR},
      </if>
      <if test="testvalue != null" >
        #{testvalue,jdbcType=VARCHAR},
      </if>
      <if test="casno != null" >
        #{casno,jdbcType=VARCHAR},
      </if>
      <if test="reportlimit != null" >
        #{reportlimit,jdbcType=VARCHAR},
      </if>
      <if test="limitunit != null" >
        #{limitunit,jdbcType=VARCHAR},
      </if>
      <if test="conclusionid != null" >
        #{conclusionid,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        #{activeindicator,jdbcType=INTEGER},
      </if>
      <if test="createdby != null" >
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null" >
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="bizversionid != null" >
        #{bizversionid,jdbcType=CHAR},
      </if>
      <if test="rdReportId != null" >
        #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="testResultFullName != null" >
        #{testResultFullName,jdbcType=VARCHAR},
      </if>
      <if test="testResultSeq != null" >
        #{testResultSeq,jdbcType=INTEGER},
      </if>
      <if test="resultValue != null" >
        #{resultValue,jdbcType=VARCHAR},
      </if>
      <if test="resultValueRemark != null" >
        #{resultValueRemark,jdbcType=VARCHAR},
      </if>
      <if test="resultUnit != null" >
        #{resultUnit,jdbcType=VARCHAR},
      </if>
      <if test="failFlag != null" >
        #{failFlag,jdbcType=TINYINT},
      </if>
      <if test="limitValueFullName != null" >
        #{limitValueFullName,jdbcType=VARCHAR},
      </if>
      <if test="position != null" >
        #{position,jdbcType=LONGVARCHAR},
      </if>
      <if test="languages != null" >
        #{languages,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_data_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_data_info
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.objectrelid != null" >
        ObjectRelId = #{record.objectrelid,jdbcType=VARCHAR},
      </if>
      <if test="record.testdatamatrixid != null" >
        TestDataMatrixId = #{record.testdatamatrixid,jdbcType=BIGINT},
      </if>
      <if test="record.testmatrixid != null" >
        TestMatrixId = #{record.testmatrixid,jdbcType=VARCHAR},
      </if>
      <if test="record.analyteid != null" >
        AnalyteId = #{record.analyteid,jdbcType=VARCHAR},
      </if>
      <if test="record.analytename != null" >
        AnalyteName = #{record.analytename,jdbcType=VARCHAR},
      </if>
      <if test="record.analytetype != null" >
        AnalyteType = #{record.analytetype,jdbcType=INTEGER},
      </if>
      <if test="record.analytecode != null" >
        AnalyteCode = #{record.analytecode,jdbcType=VARCHAR},
      </if>
      <if test="record.analyteseq != null" >
        AnalyteSeq = #{record.analyteseq,jdbcType=INTEGER},
      </if>
      <if test="record.reportunit != null" >
        ReportUnit = #{record.reportunit,jdbcType=VARCHAR},
      </if>
      <if test="record.testvalue != null" >
        TestValue = #{record.testvalue,jdbcType=VARCHAR},
      </if>
      <if test="record.casno != null" >
        CasNo = #{record.casno,jdbcType=VARCHAR},
      </if>
      <if test="record.reportlimit != null" >
        ReportLimit = #{record.reportlimit,jdbcType=VARCHAR},
      </if>
      <if test="record.limitunit != null" >
        LimitUnit = #{record.limitunit,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionid != null" >
        ConclusionId = #{record.conclusionid,jdbcType=VARCHAR},
      </if>
      <if test="record.activeindicator != null" >
        ActiveIndicator = #{record.activeindicator,jdbcType=INTEGER},
      </if>
      <if test="record.createdby != null" >
        CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.createddate != null" >
        CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedby != null" >
        ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="record.modifieddate != null" >
        ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bizversionid != null" >
        BizVersionId = #{record.bizversionid,jdbcType=CHAR},
      </if>
      <if test="record.rdReportId != null" >
        rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.testResultFullName != null" >
        test_result_full_name = #{record.testResultFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.testResultSeq != null" >
        test_result_seq = #{record.testResultSeq,jdbcType=INTEGER},
      </if>
      <if test="record.resultValue != null" >
        result_value = #{record.resultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.resultValueRemark != null" >
        result_value_remark = #{record.resultValueRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.resultUnit != null" >
        result_unit = #{record.resultUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.failFlag != null" >
        fail_flag = #{record.failFlag,jdbcType=TINYINT},
      </if>
      <if test="record.limitValueFullName != null" >
        limit_value_full_name = #{record.limitValueFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.position != null" >
        `Position` = #{record.position,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.languages != null" >
        Languages = #{record.languages,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_test_data_info
    set Id = #{record.id,jdbcType=BIGINT},
      ObjectRelId = #{record.objectrelid,jdbcType=VARCHAR},
      TestDataMatrixId = #{record.testdatamatrixid,jdbcType=BIGINT},
      TestMatrixId = #{record.testmatrixid,jdbcType=VARCHAR},
      AnalyteId = #{record.analyteid,jdbcType=VARCHAR},
      AnalyteName = #{record.analytename,jdbcType=VARCHAR},
      AnalyteType = #{record.analytetype,jdbcType=INTEGER},
      AnalyteCode = #{record.analytecode,jdbcType=VARCHAR},
      AnalyteSeq = #{record.analyteseq,jdbcType=INTEGER},
      ReportUnit = #{record.reportunit,jdbcType=VARCHAR},
      TestValue = #{record.testvalue,jdbcType=VARCHAR},
      CasNo = #{record.casno,jdbcType=VARCHAR},
      ReportLimit = #{record.reportlimit,jdbcType=VARCHAR},
      LimitUnit = #{record.limitunit,jdbcType=VARCHAR},
      ConclusionId = #{record.conclusionid,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeindicator,jdbcType=INTEGER},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      BizVersionId = #{record.bizversionid,jdbcType=CHAR},
      rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      test_result_full_name = #{record.testResultFullName,jdbcType=VARCHAR},
      test_result_seq = #{record.testResultSeq,jdbcType=INTEGER},
      result_value = #{record.resultValue,jdbcType=VARCHAR},
      result_value_remark = #{record.resultValueRemark,jdbcType=VARCHAR},
      result_unit = #{record.resultUnit,jdbcType=VARCHAR},
      fail_flag = #{record.failFlag,jdbcType=TINYINT},
      limit_value_full_name = #{record.limitValueFullName,jdbcType=VARCHAR},
      `Position` = #{record.position,jdbcType=LONGVARCHAR},
      Languages = #{record.languages,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_data_info
    set Id = #{record.id,jdbcType=BIGINT},
      ObjectRelId = #{record.objectrelid,jdbcType=VARCHAR},
      TestDataMatrixId = #{record.testdatamatrixid,jdbcType=BIGINT},
      TestMatrixId = #{record.testmatrixid,jdbcType=VARCHAR},
      AnalyteId = #{record.analyteid,jdbcType=VARCHAR},
      AnalyteName = #{record.analytename,jdbcType=VARCHAR},
      AnalyteType = #{record.analytetype,jdbcType=INTEGER},
      AnalyteCode = #{record.analytecode,jdbcType=VARCHAR},
      AnalyteSeq = #{record.analyteseq,jdbcType=INTEGER},
      ReportUnit = #{record.reportunit,jdbcType=VARCHAR},
      TestValue = #{record.testvalue,jdbcType=VARCHAR},
      CasNo = #{record.casno,jdbcType=VARCHAR},
      ReportLimit = #{record.reportlimit,jdbcType=VARCHAR},
      LimitUnit = #{record.limitunit,jdbcType=VARCHAR},
      ConclusionId = #{record.conclusionid,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeindicator,jdbcType=INTEGER},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      BizVersionId = #{record.bizversionid,jdbcType=CHAR},
      rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      test_result_full_name = #{record.testResultFullName,jdbcType=VARCHAR},
      test_result_seq = #{record.testResultSeq,jdbcType=INTEGER},
      result_value = #{record.resultValue,jdbcType=VARCHAR},
      result_value_remark = #{record.resultValueRemark,jdbcType=VARCHAR},
      result_unit = #{record.resultUnit,jdbcType=VARCHAR},
      fail_flag = #{record.failFlag,jdbcType=TINYINT},
      limit_value_full_name = #{record.limitValueFullName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" >
    update tb_test_data_info
    <set >
      <if test="objectrelid != null" >
        ObjectRelId = #{objectrelid,jdbcType=VARCHAR},
      </if>
      <if test="testdatamatrixid != null" >
        TestDataMatrixId = #{testdatamatrixid,jdbcType=BIGINT},
      </if>
      <if test="testmatrixid != null" >
        TestMatrixId = #{testmatrixid,jdbcType=VARCHAR},
      </if>
      <if test="analyteid != null" >
        AnalyteId = #{analyteid,jdbcType=VARCHAR},
      </if>
      <if test="analytename != null" >
        AnalyteName = #{analytename,jdbcType=VARCHAR},
      </if>
      <if test="analytetype != null" >
        AnalyteType = #{analytetype,jdbcType=INTEGER},
      </if>
      <if test="analytecode != null" >
        AnalyteCode = #{analytecode,jdbcType=VARCHAR},
      </if>
      <if test="analyteseq != null" >
        AnalyteSeq = #{analyteseq,jdbcType=INTEGER},
      </if>
      <if test="reportunit != null" >
        ReportUnit = #{reportunit,jdbcType=VARCHAR},
      </if>
      <if test="testvalue != null" >
        TestValue = #{testvalue,jdbcType=VARCHAR},
      </if>
      <if test="casno != null" >
        CasNo = #{casno,jdbcType=VARCHAR},
      </if>
      <if test="reportlimit != null" >
        ReportLimit = #{reportlimit,jdbcType=VARCHAR},
      </if>
      <if test="limitunit != null" >
        LimitUnit = #{limitunit,jdbcType=VARCHAR},
      </if>
      <if test="conclusionid != null" >
        ConclusionId = #{conclusionid,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator = #{activeindicator,jdbcType=INTEGER},
      </if>
      <if test="createdby != null" >
        CreatedBy = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null" >
        CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="bizversionid != null" >
        BizVersionId = #{bizversionid,jdbcType=CHAR},
      </if>
      <if test="rdReportId != null" >
        rd_report_id = #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="testResultFullName != null" >
        test_result_full_name = #{testResultFullName,jdbcType=VARCHAR},
      </if>
      <if test="testResultSeq != null" >
        test_result_seq = #{testResultSeq,jdbcType=INTEGER},
      </if>
      <if test="resultValue != null" >
        result_value = #{resultValue,jdbcType=VARCHAR},
      </if>
      <if test="resultValueRemark != null" >
        result_value_remark = #{resultValueRemark,jdbcType=VARCHAR},
      </if>
      <if test="resultUnit != null" >
        result_unit = #{resultUnit,jdbcType=VARCHAR},
      </if>
      <if test="failFlag != null" >
        fail_flag = #{failFlag,jdbcType=TINYINT},
      </if>
      <if test="limitValueFullName != null" >
        limit_value_full_name = #{limitValueFullName,jdbcType=VARCHAR},
      </if>
      <if test="position != null" >
        `Position` = #{position,jdbcType=LONGVARCHAR},
      </if>
      <if test="languages != null" >
        Languages = #{languages,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" >
    update tb_test_data_info
    set ObjectRelId = #{objectrelid,jdbcType=VARCHAR},
      TestDataMatrixId = #{testdatamatrixid,jdbcType=BIGINT},
      TestMatrixId = #{testmatrixid,jdbcType=VARCHAR},
      AnalyteId = #{analyteid,jdbcType=VARCHAR},
      AnalyteName = #{analytename,jdbcType=VARCHAR},
      AnalyteType = #{analytetype,jdbcType=INTEGER},
      AnalyteCode = #{analytecode,jdbcType=VARCHAR},
      AnalyteSeq = #{analyteseq,jdbcType=INTEGER},
      ReportUnit = #{reportunit,jdbcType=VARCHAR},
      TestValue = #{testvalue,jdbcType=VARCHAR},
      CasNo = #{casno,jdbcType=VARCHAR},
      ReportLimit = #{reportlimit,jdbcType=VARCHAR},
      LimitUnit = #{limitunit,jdbcType=VARCHAR},
      ConclusionId = #{conclusionid,jdbcType=VARCHAR},
      ActiveIndicator = #{activeindicator,jdbcType=INTEGER},
      CreatedBy = #{createdby,jdbcType=VARCHAR},
      CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      BizVersionId = #{bizversionid,jdbcType=CHAR},
      rd_report_id = #{rdReportId,jdbcType=BIGINT},
      lab_id = #{labId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      test_result_full_name = #{testResultFullName,jdbcType=VARCHAR},
      test_result_seq = #{testResultSeq,jdbcType=INTEGER},
      result_value = #{resultValue,jdbcType=VARCHAR},
      result_value_remark = #{resultValueRemark,jdbcType=VARCHAR},
      result_unit = #{resultUnit,jdbcType=VARCHAR},
      fail_flag = #{failFlag,jdbcType=TINYINT},
      limit_value_full_name = #{limitValueFullName,jdbcType=VARCHAR},
      `Position` = #{position,jdbcType=LONGVARCHAR},
      Languages = #{languages,jdbcType=LONGVARCHAR}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO" >
    update tb_test_data_info
    set ObjectRelId = #{objectrelid,jdbcType=VARCHAR},
      TestDataMatrixId = #{testdatamatrixid,jdbcType=BIGINT},
      TestMatrixId = #{testmatrixid,jdbcType=VARCHAR},
      AnalyteId = #{analyteid,jdbcType=VARCHAR},
      AnalyteName = #{analytename,jdbcType=VARCHAR},
      AnalyteType = #{analytetype,jdbcType=INTEGER},
      AnalyteCode = #{analytecode,jdbcType=VARCHAR},
      AnalyteSeq = #{analyteseq,jdbcType=INTEGER},
      ReportUnit = #{reportunit,jdbcType=VARCHAR},
      TestValue = #{testvalue,jdbcType=VARCHAR},
      CasNo = #{casno,jdbcType=VARCHAR},
      ReportLimit = #{reportlimit,jdbcType=VARCHAR},
      LimitUnit = #{limitunit,jdbcType=VARCHAR},
      ConclusionId = #{conclusionid,jdbcType=VARCHAR},
      ActiveIndicator = #{activeindicator,jdbcType=INTEGER},
      CreatedBy = #{createdby,jdbcType=VARCHAR},
      CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      BizVersionId = #{bizversionid,jdbcType=CHAR},
      rd_report_id = #{rdReportId,jdbcType=BIGINT},
      lab_id = #{labId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      test_result_full_name = #{testResultFullName,jdbcType=VARCHAR},
      test_result_seq = #{testResultSeq,jdbcType=INTEGER},
      result_value = #{resultValue,jdbcType=VARCHAR},
      result_value_remark = #{resultValueRemark,jdbcType=VARCHAR},
      result_unit = #{resultUnit,jdbcType=VARCHAR},
      fail_flag = #{failFlag,jdbcType=TINYINT},
      limit_value_full_name = #{limitValueFullName,jdbcType=VARCHAR}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_data_info
      (`Id`,`ObjectRelId`,`TestDataMatrixId`,
      `TestMatrixId`,`AnalyteId`,`AnalyteName`,
      `AnalyteType`,`AnalyteCode`,`AnalyteSeq`,
      `ReportUnit`,`TestValue`,`CasNo`,
      `ReportLimit`,`LimitUnit`,`ConclusionId`,
      `ActiveIndicator`,`CreatedBy`,`CreatedDate`,
      `ModifiedBy`,`ModifiedDate`,`BizVersionId`,
      `rd_report_id`,`lab_id`,`order_no`,
      `report_no`,`test_result_full_name`,`test_result_seq`,
      `result_value`,`result_value_remark`,`result_unit`,
      `fail_flag`,`limit_value_full_name`,`Position`,
      `Languages`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.objectrelid, jdbcType=VARCHAR},#{ item.testdatamatrixid, jdbcType=BIGINT},
      #{ item.testmatrixid, jdbcType=VARCHAR},#{ item.analyteid, jdbcType=VARCHAR},#{ item.analytename, jdbcType=VARCHAR},
      #{ item.analytetype, jdbcType=INTEGER},#{ item.analytecode, jdbcType=VARCHAR},#{ item.analyteseq, jdbcType=INTEGER},
      #{ item.reportunit, jdbcType=VARCHAR},#{ item.testvalue, jdbcType=VARCHAR},#{ item.casno, jdbcType=VARCHAR},
      #{ item.reportlimit, jdbcType=VARCHAR},#{ item.limitunit, jdbcType=VARCHAR},#{ item.conclusionid, jdbcType=VARCHAR},
      #{ item.activeindicator, jdbcType=INTEGER},#{ item.createdby, jdbcType=VARCHAR},#{ item.createddate, jdbcType=TIMESTAMP},
      #{ item.modifiedby, jdbcType=VARCHAR},#{ item.modifieddate, jdbcType=TIMESTAMP},#{ item.bizversionid, jdbcType=CHAR},
      #{ item.rdReportId, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.orderNo, jdbcType=VARCHAR},
      #{ item.reportNo, jdbcType=VARCHAR},#{ item.testResultFullName, jdbcType=VARCHAR},#{ item.testResultSeq, jdbcType=INTEGER},
      #{ item.resultValue, jdbcType=VARCHAR},#{ item.resultValueRemark, jdbcType=VARCHAR},#{ item.resultUnit, jdbcType=VARCHAR},
      #{ item.failFlag, jdbcType=TINYINT},#{ item.limitValueFullName, jdbcType=VARCHAR},#{ item.position, jdbcType=LONGVARCHAR},
      #{ item.languages, jdbcType=LONGVARCHAR}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_data_info 
      <set>
        <if test="item.objectrelid != null"> 
          `ObjectRelId` = #{item.objectrelid, jdbcType = VARCHAR},
        </if> 
        <if test="item.testdatamatrixid != null"> 
          `TestDataMatrixId` = #{item.testdatamatrixid, jdbcType = BIGINT},
        </if> 
        <if test="item.testmatrixid != null"> 
          `TestMatrixId` = #{item.testmatrixid, jdbcType = VARCHAR},
        </if> 
        <if test="item.analyteid != null"> 
          `AnalyteId` = #{item.analyteid, jdbcType = VARCHAR},
        </if> 
        <if test="item.analytename != null"> 
          `AnalyteName` = #{item.analytename, jdbcType = VARCHAR},
        </if> 
        <if test="item.analytetype != null"> 
          `AnalyteType` = #{item.analytetype, jdbcType = INTEGER},
        </if> 
        <if test="item.analytecode != null"> 
          `AnalyteCode` = #{item.analytecode, jdbcType = VARCHAR},
        </if> 
        <if test="item.analyteseq != null"> 
          `AnalyteSeq` = #{item.analyteseq, jdbcType = INTEGER},
        </if> 
        <if test="item.reportunit != null"> 
          `ReportUnit` = #{item.reportunit, jdbcType = VARCHAR},
        </if> 
        <if test="item.testvalue != null"> 
          `TestValue` = #{item.testvalue, jdbcType = VARCHAR},
        </if> 
        <if test="item.casno != null"> 
          `CasNo` = #{item.casno, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportlimit != null"> 
          `ReportLimit` = #{item.reportlimit, jdbcType = VARCHAR},
        </if> 
        <if test="item.limitunit != null"> 
          `LimitUnit` = #{item.limitunit, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionid != null"> 
          `ConclusionId` = #{item.conclusionid, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeindicator != null"> 
          `ActiveIndicator` = #{item.activeindicator, jdbcType = INTEGER},
        </if> 
        <if test="item.createdby != null"> 
          `CreatedBy` = #{item.createdby, jdbcType = VARCHAR},
        </if> 
        <if test="item.createddate != null"> 
          `CreatedDate` = #{item.createddate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedby != null"> 
          `ModifiedBy` = #{item.modifiedby, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifieddate != null"> 
          `ModifiedDate` = #{item.modifieddate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.bizversionid != null"> 
          `BizVersionId` = #{item.bizversionid, jdbcType = CHAR},
        </if> 
        <if test="item.rdReportId != null"> 
          `rd_report_id` = #{item.rdReportId, jdbcType = BIGINT},
        </if> 
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.testResultFullName != null"> 
          `test_result_full_name` = #{item.testResultFullName, jdbcType = VARCHAR},
        </if> 
        <if test="item.testResultSeq != null"> 
          `test_result_seq` = #{item.testResultSeq, jdbcType = INTEGER},
        </if> 
        <if test="item.resultValue != null"> 
          `result_value` = #{item.resultValue, jdbcType = VARCHAR},
        </if> 
        <if test="item.resultValueRemark != null"> 
          `result_value_remark` = #{item.resultValueRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.resultUnit != null"> 
          `result_unit` = #{item.resultUnit, jdbcType = VARCHAR},
        </if> 
        <if test="item.failFlag != null"> 
          `fail_flag` = #{item.failFlag, jdbcType = TINYINT},
        </if> 
        <if test="item.limitValueFullName != null"> 
          `limit_value_full_name` = #{item.limitValueFullName, jdbcType = VARCHAR},
        </if> 
        <if test="item.position != null"> 
          `Position` = #{item.position, jdbcType = LONGVARCHAR},
        </if> 
        <if test="item.languages != null"> 
          `Languages` = #{item.languages, jdbcType = LONGVARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `Id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>