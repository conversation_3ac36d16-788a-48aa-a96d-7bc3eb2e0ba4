/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdProductInput implements Serializable {

    private String productInstanceId;
    private String templateId;
    private List<RdProductSampleAttrInput> productAttrList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
