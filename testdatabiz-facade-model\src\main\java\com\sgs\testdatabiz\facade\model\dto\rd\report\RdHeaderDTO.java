/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdHeaderDTO implements Serializable{

    private Integer systemId;
    private String reportId;
    private String reportNo;
    private String originalReportNo;
    private Integer reportStatus;
    private Date reportDueDate;
    private String approveBy;
    private Date approveDate;
    private Date softCopyDeliveryDate;
    private String createBy;
    private Date createDate;
    private String certificateName;
    private RdLabDTO lab;
    private RdConclusionDTO conclusion;
    private List<ReportCertificateDTO> reportCertificateList;
    private String reportRemark;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String reportInstanceId;

}
