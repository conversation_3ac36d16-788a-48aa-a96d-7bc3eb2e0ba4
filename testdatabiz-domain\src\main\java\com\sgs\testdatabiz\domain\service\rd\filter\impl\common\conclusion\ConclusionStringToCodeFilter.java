package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.conclusion;

import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.constants.ConclusionFilterConstants;
import org.springframework.stereotype.Component;


public class ConclusionStringToCodeFilter extends AbstractConclusionCodeFilter {
    
    private static final String FILTER_TYPE = "CONCLUSION_CONVERTER";
    
    @Override
    protected String getFilterType() {
        return FILTER_TYPE;
    }
    
    @Override
    protected String getFilterName() {
        return ConclusionFilterConstants.FilterName.STRING_TO_CODE;
    }

    @Override
    protected String convertConclusionCode(String str) {
        if (str == null) {
            return null;
        }

        // 如果已经是数值形式，直接返回
        try {
            Integer.parseInt(str);
            return str;
        } catch (NumberFormatException e) {
            // 继续处理字符串形式
        }

        String upperStr = str.toUpperCase();
        switch (upperStr) {
            case "P":
            case ConclusionFilterConstants.Code.STR_PASS:
                return String.valueOf(ConclusionFilterConstants.Code.CODE_PASS);
            case "F":
            case ConclusionFilterConstants.Code.STR_FAIL:
                return String.valueOf(ConclusionFilterConstants.Code.CODE_FAIL);
            case ConclusionFilterConstants.Code.STR_NC:
                return String.valueOf(ConclusionFilterConstants.Code.CODE_NC);
            case ConclusionFilterConstants.Code.STR_CANCELLED:
                return String.valueOf(ConclusionFilterConstants.Code.CODE_CANCELLED);
            default:
                return str;
        }
    }
} 