package com.sgs.testdatabiz.core.util;

import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisConnectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis utils
 * <AUTHOR>
 */
@Component
public class RedisUtils {
	private static final Logger logger = LoggerFactory.getLogger(RedisUtils.class);
	@Autowired
	private RedisTemplate redisTemplate;

	/** redisUtil.setIfAbsent 新加的带有超时的setIfAbsent 脚本*/
	String newSetIfAbsentScriptStr = " if 1 == redis.call('setnx',KEYS[1],ARGV[1]) then" +
			" redis.call('expire',KEYS[1],ARGV[2])" +
			" return 1;" +
			" else" +
			" return 0;" +
			" end;";
	public RedisScript<Boolean> newSetIfAbsentScript = new DefaultRedisScript<Boolean>(newSetIfAbsentScriptStr,Boolean.class );

	/**
	 * set database for stand-alone mode, but this operation is not supported for cluster mode
	 * @param index - database index 0 - 15
	 */
	public void setDatabase(int index) {
		JedisConnectionFactory jcf = (JedisConnectionFactory)redisTemplate.getConnectionFactory();
		jcf.setDatabase(index);
	}

	/**
	 * Get Value - GET
	 * @param key
	 * @return value
	 */
	public <K, V> V get(K key) {
		return (V)redisTemplate.opsForValue().get(key);
	}

	/**
	 *
	 * @param key
	 * @param value
	 * @param <K>
	 * @param <V>
	 */
	public <K, V> void set(K key, V value) {
		redisTemplate.opsForValue().set(key, value);
	}

	/**
	 * 将值放入缓存并设置时间
	 *
	 * @param key   键
	 * @param value 值
	 * @param seconds  过期时间(秒)， -1为无期限
	 * @return true成功 false 失败
	 */
	public <K, V> void set(K key, V value, long seconds) {
		ValueOperations valueOperations = redisTemplate.opsForValue();
		if (seconds > 0) {
			valueOperations.set(key, value, seconds, TimeUnit.SECONDS);
			return;
		}
		valueOperations.set(key, value);
	}

	/**
	 * Expire the given key
	 * @param key
	 * @param timeout
	 * @param unit
	 * @return true if successfully
	 */
	public <K> boolean expire(K key, long timeout, TimeUnit unit) {
		return redisTemplate.expire(key, timeout, unit);
	}

	/**
	 * 批量添加 key (重复的键会覆盖)
	 *
	 * @param keyAndValue
	 */
	public <K, V> void batchSet(Map<K, V> keyAndValue) {
		redisTemplate.opsForValue().multiSet(keyAndValue);
	}

	/**
	 * 添加 key-value 只有在键不存在时,才添加
	 * @param key
	 * @param value
	 * @param <K>
	 * @param <V>
	 */
	public <K, V> void setIfAbsent(K key, V value) {
		redisTemplate.opsForValue().setIfAbsent(key, value);
	}

	/**
	 * setIfAbsent升级版，加了超时时间
	 * @param key
	 * @param value
	 * @param seconds 超时时间，秒为单位
	 * @return
	 */
	public boolean setIfAbsent_bak(String key, String value, long seconds){
		List<Object> keys = Lists.newArrayList();
		keys.add(key);
		Object[] args = { value, String.valueOf(seconds)};
		return (boolean)redisTemplate.execute(newSetIfAbsentScript, keys, args);
	}

	/**
	 * 批量添加 key-value 只有在键不存在时,才添加
	 * map 中只要有一个key存在,则全部不添加
	 *
	 * @param keyAndValue
	 */
	public <K, V> void batchSetIfAbsent(Map<K, V> keyAndValue) {
		redisTemplate.opsForValue().multiSetIfAbsent(keyAndValue);
	}

	/**
	 *
	 * @param keys
	 * @param <K>
	 * @param <V>
	 */
	public <K, V> List<V> multiGet(Set<K> keys) {
		return redisTemplate.opsForValue().multiGet(keys);
	}

	/**
	 * 对一个 key-value 的值进行加减操作,
	 * 如果该 key 不存在 将创建一个key 并赋值该 number
	 * 如果 key 存在,但 value 不是长整型 ,将报错
	 *
	 * @param key
	 * @param number
	 */
	public <K, V> Long increment(K key, long number) {
		return redisTemplate.opsForValue().increment(key, number);
	}

	/**
	 * 对一个 key-value 的值进行加减操作,
	 * 如果该 key 不存在 将创建一个key 并赋值该 number
	 * 如果 key 存在,但 value 不是 纯数字 ,将报错
	 *
	 * @param key
	 * @param number
	 */
	public <K, V> Double increment(K key, double number) {
		return redisTemplate.opsForValue().increment(key, number);
	}

	/**
	 * 根据value从一个set中查询,是否存在
	 *
	 * @param key   键
	 * @param value 值
	 * @return true 存在 false不存在
	 */
	public <K, V> boolean hasKey(K key, V value) {
		return redisTemplate.opsForSet().isMember(key, value);
	}

	/**
	 *
	 * @param key
	 * @param <K>
	 * @return
	 */
	public <K> boolean hasKey(K key) {
		return redisTemplate.hasKey(key);
	}

	/**
	 * 批量移除set缓存中元素
	 *
	 * @param key    键
	 * @param values 值
	 * @return
	 */
	public <K, V> void remove(K key, V... values) {
		redisTemplate.opsForSet().remove(key, values);
	}

	/**
	 * 给指定 hash 的 hashkey 做增减操作
	 *
	 * @param key
	 * @param hashKey
	 * @param number
	 * @return
	 */
	public <K, HK> Long increment(K key, HK hashKey, long number) {
		return redisTemplate.opsForHash().increment(key, hashKey, number);
	}

	/**
	 * 给指定 hash 的 hashkey 做增减操作
	 *
	 * @param key
	 * @param hashKey
	 * @param number
	 * @return
	 */
	public <K, HK> Double increment(K key, HK hashKey, Double number) {
		return redisTemplate.opsForHash().increment(key, hashKey, number);
	}

	/**
	 * 获取 key 下的 所有 hashkey 字段
	 *
	 * @param key
	 * @return
	 */
	public <K, V> Set<V> hashKeys(K key) {
		return redisTemplate.opsForHash().keys(key);
	}

	/**
	 *
	 * @param key
	 * @param <K>
	 * @param <V>
	 * @return
	 */
	public <K, V> Set<V> keys(K key) {
		return redisTemplate.keys(key);
	}

	/**
	 * 获取指定 hash 下面的 键值对 数量
	 *
	 * @param key
	 * @return
	 */
	public <K, V> Long hashSize(K key) {
		return redisTemplate.opsForHash().size(key);
	}

	/**
	 *
	 * @param key
	 * @param <K>
	 * @return
	 */
	public <K> Long keySize(K key) {
		return redisTemplate.opsForValue().size(key);
	}

	/**
	 * Set Cache With Expiration Time - SETEX
	 * @param key
	 * @param value
	 * @param seconds - timeout period
	 */
	public <K, V> void setEx(K key, V value, long seconds) {
		JdkSerializationRedisSerializer jsrs = new JdkSerializationRedisSerializer();
		final byte[] bk = jsrs.serialize(key);
		final byte[] bv = jsrs.serialize(value);

		redisTemplate.execute(new RedisCallback<Void>() {
			@Override
			public Void doInRedis(RedisConnection connection) throws DataAccessException {
				connection.setEx(bk, seconds, bv);
				return null;
			}
		});
	}

	/**
	 * delete one cache
	 * @param key
	 */
	public <K> void delete(K key) {
		try{
			redisTemplate.delete(key);
		}catch (Exception ex){
			logger.error("RedisHelper.delete: ", ex);
		}
	}

	/**
	 * batch delete cache
	 * @param keys
	 */
	public <K> void delete(Collection<K> keys) {
		redisTemplate.delete(keys);
	}

	/**
	 * Ping the connection  - PING
	 * @return true if the connection is ok
	 */
	public boolean ping() {
		String result = (String)redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection) throws DataAccessException {
				return connection.ping();
			}
		});
		return "PONG".equals(result);
	}

	/**
	 * check database size - DBSIZE
	 * @return database size
	 */
	public Long dbSize() {
		return (Long)redisTemplate.execute(new RedisCallback<Long>() {
			@Override
			public Long doInRedis(RedisConnection connection) throws DataAccessException {
				return connection.dbSize();
			}
		});
	}


	/**
	 * 释放连接
	 * 场景：异常
	 */
	public void unbindConnection() {
		RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
	}
}
