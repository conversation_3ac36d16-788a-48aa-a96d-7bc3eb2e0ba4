package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RdReportMatrixLangExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RdReportMatrixLangExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdIsNull() {
            addCriterion("rd_report_matrix_id is null");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdIsNotNull() {
            addCriterion("rd_report_matrix_id is not null");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdEqualTo(Long value) {
            addCriterion("rd_report_matrix_id =", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdNotEqualTo(Long value) {
            addCriterion("rd_report_matrix_id <>", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdGreaterThan(Long value) {
            addCriterion("rd_report_matrix_id >", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rd_report_matrix_id >=", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdLessThan(Long value) {
            addCriterion("rd_report_matrix_id <", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdLessThanOrEqualTo(Long value) {
            addCriterion("rd_report_matrix_id <=", value, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdIn(List<Long> values) {
            addCriterion("rd_report_matrix_id in", values, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdNotIn(List<Long> values) {
            addCriterion("rd_report_matrix_id not in", values, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdBetween(Long value1, Long value2) {
            addCriterion("rd_report_matrix_id between", value1, value2, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andRdReportMatrixIdNotBetween(Long value1, Long value2) {
            addCriterion("rd_report_matrix_id not between", value1, value2, "rdReportMatrixId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNull() {
            addCriterion("language_id is null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNotNull() {
            addCriterion("language_id is not null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdEqualTo(Integer value) {
            addCriterion("language_id =", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotEqualTo(Integer value) {
            addCriterion("language_id <>", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThan(Integer value) {
            addCriterion("language_id >", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("language_id >=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThan(Integer value) {
            addCriterion("language_id <", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThanOrEqualTo(Integer value) {
            addCriterion("language_id <=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIn(List<Integer> values) {
            addCriterion("language_id in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotIn(List<Integer> values) {
            addCriterion("language_id not in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdBetween(Integer value1, Integer value2) {
            addCriterion("language_id between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("language_id not between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIsNull() {
            addCriterion("evaluation_alias is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIsNotNull() {
            addCriterion("evaluation_alias is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasEqualTo(String value) {
            addCriterion("evaluation_alias =", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotEqualTo(String value) {
            addCriterion("evaluation_alias <>", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasGreaterThan(String value) {
            addCriterion("evaluation_alias >", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasGreaterThanOrEqualTo(String value) {
            addCriterion("evaluation_alias >=", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLessThan(String value) {
            addCriterion("evaluation_alias <", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLessThanOrEqualTo(String value) {
            addCriterion("evaluation_alias <=", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLike(String value) {
            addCriterion("evaluation_alias like", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotLike(String value) {
            addCriterion("evaluation_alias not like", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIn(List<String> values) {
            addCriterion("evaluation_alias in", values, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotIn(List<String> values) {
            addCriterion("evaluation_alias not in", values, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasBetween(String value1, String value2) {
            addCriterion("evaluation_alias between", value1, value2, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotBetween(String value1, String value2) {
            addCriterion("evaluation_alias not between", value1, value2, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIsNull() {
            addCriterion("evaluation_name is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIsNotNull() {
            addCriterion("evaluation_name is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameEqualTo(String value) {
            addCriterion("evaluation_name =", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotEqualTo(String value) {
            addCriterion("evaluation_name <>", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameGreaterThan(String value) {
            addCriterion("evaluation_name >", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameGreaterThanOrEqualTo(String value) {
            addCriterion("evaluation_name >=", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLessThan(String value) {
            addCriterion("evaluation_name <", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLessThanOrEqualTo(String value) {
            addCriterion("evaluation_name <=", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLike(String value) {
            addCriterion("evaluation_name like", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotLike(String value) {
            addCriterion("evaluation_name not like", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIn(List<String> values) {
            addCriterion("evaluation_name in", values, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotIn(List<String> values) {
            addCriterion("evaluation_name not in", values, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameBetween(String value1, String value2) {
            addCriterion("evaluation_name between", value1, value2, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotBetween(String value1, String value2) {
            addCriterion("evaluation_name not between", value1, value2, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameIsNull() {
            addCriterion("citation_name is null");
            return (Criteria) this;
        }

        public Criteria andCitationNameIsNotNull() {
            addCriterion("citation_name is not null");
            return (Criteria) this;
        }

        public Criteria andCitationNameEqualTo(String value) {
            addCriterion("citation_name =", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotEqualTo(String value) {
            addCriterion("citation_name <>", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameGreaterThan(String value) {
            addCriterion("citation_name >", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameGreaterThanOrEqualTo(String value) {
            addCriterion("citation_name >=", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLessThan(String value) {
            addCriterion("citation_name <", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLessThanOrEqualTo(String value) {
            addCriterion("citation_name <=", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLike(String value) {
            addCriterion("citation_name like", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotLike(String value) {
            addCriterion("citation_name not like", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameIn(List<String> values) {
            addCriterion("citation_name in", values, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotIn(List<String> values) {
            addCriterion("citation_name not in", values, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameBetween(String value1, String value2) {
            addCriterion("citation_name between", value1, value2, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotBetween(String value1, String value2) {
            addCriterion("citation_name not between", value1, value2, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIsNull() {
            addCriterion("citation_full_name is null");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIsNotNull() {
            addCriterion("citation_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameEqualTo(String value) {
            addCriterion("citation_full_name =", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotEqualTo(String value) {
            addCriterion("citation_full_name <>", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameGreaterThan(String value) {
            addCriterion("citation_full_name >", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("citation_full_name >=", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLessThan(String value) {
            addCriterion("citation_full_name <", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLessThanOrEqualTo(String value) {
            addCriterion("citation_full_name <=", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLike(String value) {
            addCriterion("citation_full_name like", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotLike(String value) {
            addCriterion("citation_full_name not like", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIn(List<String> values) {
            addCriterion("citation_full_name in", values, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotIn(List<String> values) {
            addCriterion("citation_full_name not in", values, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameBetween(String value1, String value2) {
            addCriterion("citation_full_name between", value1, value2, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotBetween(String value1, String value2) {
            addCriterion("citation_full_name not between", value1, value2, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andMethodDescIsNull() {
            addCriterion("method_desc is null");
            return (Criteria) this;
        }

        public Criteria andMethodDescIsNotNull() {
            addCriterion("method_desc is not null");
            return (Criteria) this;
        }

        public Criteria andMethodDescEqualTo(String value) {
            addCriterion("method_desc =", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescNotEqualTo(String value) {
            addCriterion("method_desc <>", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescGreaterThan(String value) {
            addCriterion("method_desc >", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescGreaterThanOrEqualTo(String value) {
            addCriterion("method_desc >=", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescLessThan(String value) {
            addCriterion("method_desc <", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescLessThanOrEqualTo(String value) {
            addCriterion("method_desc <=", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescLike(String value) {
            addCriterion("method_desc like", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescNotLike(String value) {
            addCriterion("method_desc not like", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescIn(List<String> values) {
            addCriterion("method_desc in", values, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescNotIn(List<String> values) {
            addCriterion("method_desc not in", values, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescBetween(String value1, String value2) {
            addCriterion("method_desc between", value1, value2, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescNotBetween(String value1, String value2) {
            addCriterion("method_desc not between", value1, value2, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIsNull() {
            addCriterion("customer_conclusion is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIsNotNull() {
            addCriterion("customer_conclusion is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionEqualTo(String value) {
            addCriterion("customer_conclusion =", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotEqualTo(String value) {
            addCriterion("customer_conclusion <>", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionGreaterThan(String value) {
            addCriterion("customer_conclusion >", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("customer_conclusion >=", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLessThan(String value) {
            addCriterion("customer_conclusion <", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLessThanOrEqualTo(String value) {
            addCriterion("customer_conclusion <=", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLike(String value) {
            addCriterion("customer_conclusion like", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotLike(String value) {
            addCriterion("customer_conclusion not like", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIn(List<String> values) {
            addCriterion("customer_conclusion in", values, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotIn(List<String> values) {
            addCriterion("customer_conclusion not in", values, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionBetween(String value1, String value2) {
            addCriterion("customer_conclusion between", value1, value2, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotBetween(String value1, String value2) {
            addCriterion("customer_conclusion not between", value1, value2, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}