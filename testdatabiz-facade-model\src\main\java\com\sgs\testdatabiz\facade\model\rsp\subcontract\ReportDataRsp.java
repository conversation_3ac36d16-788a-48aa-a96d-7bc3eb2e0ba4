package com.sgs.testdatabiz.facade.model.rsp.subcontract;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel
public class ReportDataRsp  extends PrintFriendliness {

    @ApiModelProperty(value = "分包单号")
    private String subContractNo;

    @ApiModelProperty(value = "externalObjectNo")
    private String externalObjectNo;

    @ApiModelProperty(value = "TestLine列表")
    private List<SubTestDataInfo> list;

    public String getExternalObjectNo() {
        return externalObjectNo;
    }

    public void setExternalObjectNo(String externalObjectNo) {
        this.externalObjectNo = externalObjectNo;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public List<SubTestDataInfo> getList() {
        return list;
    }

    public void setList(List<SubTestDataInfo> list) {
        this.list = list;
    }
}
