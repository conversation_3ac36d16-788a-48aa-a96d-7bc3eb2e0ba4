package com.sgs.testdatabiz.domain.service.testdata.impl.handler.base;

import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

/**
 * 和 AbstractTestDataHandler的区别是:
 *  这个类里的inputValidate方法不需要返回结果默认对上层模板方法中的调用返回true，
 *  实现者只需实现mutedInputValidate方法，遇到错误调用putErrorMsg()把错误信息放到context中就行了
 *
 * @author: shawn.yang
 * @create: 2023-03-23 17:42
 */
public abstract class AbstractMutedTestDataHandler<Input> extends AbstractTestDataHandler<Input>{
    @Override
    protected boolean inputValidate(Input inputData) {
        mutedInputValidate(inputData);
        return true;
    }

    @Override
    protected ReportTestDataInfo inputBuild(Input inputData) {
        return mutedInputBuild(inputData);
    }

    protected abstract void mutedInputValidate(Input inputData);

    protected abstract ReportTestDataInfo mutedInputBuild(Input inputData);

}
