package com.sgs.testdatabiz.domain.service;

import com.google.common.collect.Lists;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataObjectRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @param <TInput>
 */
public abstract class AbstractTestDataService<TInput> {
    private final Logger logger = LoggerFactory.getLogger(AbstractTestDataService.class);

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private TestDataInfoExtMapper testDataReporExtMapper;

    @Autowired
    private TestDataMatrixInfoExtMapper testDataReportMatrixExtMapper;

    @Autowired
    private TestDataObjectRelExtMapper testDataReportObjectRelExtMapper;

    final int maxSize = 1000;
    final int minSize = 100;
    /**
     * @param reqObject
     * @return
     */
    protected abstract CustomResult<TestDataDTO> doInvoke(TInput reqObject);

    /**
     * @param rspObject
     * @return
     */
    protected CustomResult doDeal(TestDataDTO rspObject) {
        List<TestDataMatrixInfoPO> testDataReportMatrixPOList = rspObject.getTestDataMatrixInfos();
        List<TestDataObjectRelPO> testDataReportObjectRelPOList = rspObject.getTestDataObjectRels();
        List<TestDataInfoPO> testDataReportPOList = rspObject.getTestDataInfos();
        TestDataObjectRelDTO objectRelDTO = rspObject.getObjectRelDTO();
        String testDataSuffix = rspObject.getTestDataSuffix();
        return transactionTemplate.execute((tranStatus) -> {
            CustomResult rspResult = new CustomResult();
            try{
                if (!CollectionUtils.isEmpty(testDataReportObjectRelPOList)) {
                    testDataReportObjectRelExtMapper.updateInvalidOrValid(objectRelDTO);
                    List<List<TestDataObjectRelPO>> part = Lists.partition(testDataReportObjectRelPOList, calculateBatchSize(testDataReportObjectRelPOList.size()));
                    for (List<TestDataObjectRelPO> testDataObjectRelPOS : part) {
                        testDataReportObjectRelExtMapper.batchInsert(testDataObjectRelPOS);
                    }
                }
                if (!CollectionUtils.isEmpty(testDataReportMatrixPOList)) {
                    Set<String> objectRelIds = testDataReportMatrixPOList.stream().map(TestDataMatrixInfoPO::getObjectRelId).collect(Collectors.toSet());
                    testDataReportMatrixExtMapper.updateInvalidOrValid(objectRelIds, testDataSuffix, objectRelDTO.getModifiedBy());
                    List<List<TestDataMatrixInfoPO>> part = Lists.partition(testDataReportMatrixPOList, calculateBatchSize(testDataReportMatrixPOList.size()));
                    for (List<TestDataMatrixInfoPO> dataMatrixInfoPOS : part) {
                        testDataReportMatrixExtMapper.batchInsert(dataMatrixInfoPOS, testDataSuffix);
                    }
                }
                if (!CollectionUtils.isEmpty(testDataReportPOList)) {
                    Set<String> objectRelIds = testDataReportPOList.stream().map(TestDataInfoPO::getObjectRelId).distinct().collect(Collectors.toSet());
                    testDataReporExtMapper.updateInvalidOrValid(objectRelIds, testDataSuffix, objectRelDTO.getModifiedBy());
                    List<List<TestDataInfoPO>> part = Lists.partition(testDataReportPOList, calculateBatchSize(testDataReportPOList.size()));
                    for (List<TestDataInfoPO> testDataInfoPOS : part) {
                        testDataReporExtMapper.batchInsert(testDataInfoPOS, testDataSuffix);
                    }
                }
                rspResult.setSuccess(true);
                return rspResult;
            }catch (Exception ex){
                rspResult.setSuccess(false);
                rspResult.setMsg(ex.getMessage());
                logger.error("AbstractTestDataService.doDeal, ReportNo_{}, ObjectNo_{}, ExternalNo_{}, Error：{}.", objectRelDTO.getReportNo(), objectRelDTO.getObjectNo(), objectRelDTO.getExternalNo(), ex);
                tranStatus.setRollbackOnly(); // 回滚事务
                return rspResult;
            }
        });
    }

    /**
     * 根据数据大小计算批量插入的数量
     *
     * @param size 数据列表的大小
     * @return 批量插入的数量
     */
    private int calculateBatchSize(int size) {
        // 计算一个合理的批量大小
        int batchSize = Math.max(minSize, Math.min(maxSize, size / 2));

        // 确保批量大小至少为最小值
        batchSize = Math.max(batchSize, minSize);

        // 确保批量大小不超过最大值
        batchSize = Math.min(batchSize, maxSize);

        return batchSize;
    }


    protected String getTestDataReportObjectRelMd5(TestDataObjectRelPO relPO) {
        relPO.setProductLineCode(StringUtil.isNullOrEmpty(relPO.getProductLineCode()));
        relPO.setLabCode(StringUtil.isNullOrEmpty(relPO.getLabCode()));
        relPO.setOrderNo(StringUtil.isNullOrEmpty(relPO.getOrderNo()));
        relPO.setParentOrderNo(StringUtil.isNullOrEmpty(relPO.getParentOrderNo()));
        relPO.setReportNo(StringUtil.isNullOrEmpty(relPO.getReportNo()));
        relPO.setObjectNo(StringUtil.isNullOrEmpty(relPO.getObjectNo()));
        relPO.setExternalId(StringUtil.isNullOrEmpty(relPO.getExternalId()));
        relPO.setExternalNo(StringUtil.isNullOrEmpty(relPO.getExternalNo()));
        relPO.setSourceType(NumberUtil.toInt(relPO.getSourceType()));
        relPO.setLanguageId(NumberUtil.toInt(relPO.getLanguageId()));
        relPO.setExternalObjectNo(StringUtil.isNullOrEmpty(relPO.getExternalObjectNo()));
        relPO.setCompleteDate(DateUtils.isNullOrEmpty(relPO.getCompleteDate()));

        StringBuilder append = new StringBuilder();
        append.append(relPO.getProductLineCode());
        append.append(relPO.getLabCode());
        append.append(relPO.getOrderNo());
        append.append(relPO.getParentOrderNo());
        append.append(relPO.getReportNo());
        append.append(relPO.getObjectNo());
        append.append(relPO.getExternalId());
        append.append(relPO.getExternalNo());
        append.append(relPO.getSourceType());
        append.append(relPO.getLanguageId());
        append.append(relPO.getExternalObjectNo());
        append.append(relPO.getCompleteDate());
        return DigestUtils.md5Hex(append.toString());
    }

    protected String getTestDataReportMatrixMd5(TestDataMatrixInfoPO relPO) {
        relPO.setObjectRelId(StringUtil.isNullOrEmpty(relPO.getObjectRelId()));
        relPO.setTestMatrixId(StringUtil.isNullOrEmpty(relPO.getTestMatrixId()));
        relPO.setExternalCode(StringUtil.isNullOrEmpty(relPO.getExternalCode()));
        relPO.setPpVersionId(NumberUtil.toInt(relPO.getPpVersionId()));
        relPO.setAid(NumberUtil.toLong(relPO.getAid()));
        relPO.setTestLineId(NumberUtil.toInt(relPO.getTestLineId()));
        relPO.setCitationId(NumberUtil.toInt(relPO.getCitationId()));
        relPO.setCitationVersionId(NumberUtil.toInt(relPO.getCitationVersionId()));
        relPO.setSampleId(StringUtil.isNullOrEmpty(relPO.getSampleId()));
        relPO.setSampleNo(StringUtil.isNullOrEmpty(relPO.getSampleNo()));
        relPO.setExternalSampleNo(StringUtil.isNullOrEmpty(relPO.getExternalSampleNo()));
        relPO.setSampleSeq(relPO.getSampleSeq());
        relPO.setExtFields(StringUtil.isNullOrEmpty(relPO.getExtFields()));
        relPO.setCondition(StringUtil.isNullOrEmpty(relPO.getCondition()));
        relPO.setConclusionId(StringUtil.isNullOrEmpty(relPO.getConclusionId()));
        relPO.setConclusionDisplay(StringUtil.isNullOrEmpty(relPO.getConclusionDisplay()));
        relPO.setEvaluationAlias(StringUtil.isNullOrEmpty(relPO.getEvaluationAlias()));
        relPO.setLanguages(StringUtil.isNullOrEmpty(relPO.getLanguages()));
        relPO.setExternalId(StringUtil.isNullOrEmpty(relPO.getExternalId()));
        relPO.setMethodDesc(StringUtil.isNullOrEmpty(relPO.getMethodDesc()));
        relPO.setCitationType(NumberUtil.toInt(relPO.getCitationType()));
        StringBuilder append = new StringBuilder();
        append.append(relPO.getObjectRelId())
                .append(relPO.getTestMatrixId())
                .append(relPO.getExternalCode())
                .append(relPO.getPpVersionId())
                .append(relPO.getAid())
                .append(relPO.getTestLineId())
                .append(relPO.getCitationId())
                .append(relPO.getCitationVersionId())
                .append(relPO.getSampleId())
                .append(relPO.getSampleNo())
                .append(relPO.getExternalSampleNo())
                .append(relPO.getSampleSeq())
                .append(relPO.getExtFields())
                .append(relPO.getCondition())
                .append(relPO.getConclusionId())
                .append(relPO.getConclusionDisplay())
                .append(relPO.getEvaluationAlias())
                .append(relPO.getLanguages())
                .append(relPO.getExternalId())
                .append(relPO.getMethodDesc())
                .append(relPO.getCitationType());
        return DigestUtils.md5Hex(append.toString());
    }


    protected String getTestDataReportMd5(TestDataInfoPO relPO) {
        relPO.setObjectRelId(StringUtil.isNullOrEmpty(relPO.getObjectRelId()));
        relPO.setTestMatrixId(StringUtil.isNullOrEmpty(relPO.getTestMatrixId()));
        relPO.setAnalyteId(StringUtil.isNullOrEmpty(relPO.getAnalyteId()));
        relPO.setAnalyteName(StringUtil.isNullOrEmpty(relPO.getAnalyteName()));
        relPO.setPosition(StringUtil.isNullOrEmpty(relPO.getPosition()));
        relPO.setAnalyteType(NumberUtil.toInt(relPO.getAnalyteType()));
        relPO.setAnalyteCode(StringUtil.isNullOrEmpty(relPO.getAnalyteCode()));
        relPO.setAnalyteSeq(NumberUtil.toInt(relPO.getAnalyteSeq()));
        relPO.setReportUnit(StringUtil.isNullOrEmpty(relPO.getReportUnit()));
        relPO.setTestValue(StringUtil.isNullOrEmpty(relPO.getTestValue()));
        relPO.setCasNo(StringUtil.isNullOrEmpty(relPO.getCasNo()));
        relPO.setReportLimit(StringUtil.isNullOrEmpty(relPO.getReportLimit()));
        relPO.setLimitUnit(StringUtil.isNullOrEmpty(relPO.getLimitUnit()));
        relPO.setConclusionId(StringUtil.isNullOrEmpty(relPO.getConclusionId()));
        relPO.setTestDataMatrixId(relPO.getTestDataMatrixId());
        relPO.setLanguages(StringUtil.isNullOrEmpty(relPO.getLanguages()));

        StringBuilder append = new StringBuilder();
        append.append(relPO.getObjectRelId())
                .append(relPO.getTestMatrixId())
                .append(relPO.getAnalyteId())
                .append(relPO.getAnalyteName())
                .append(relPO.getPosition())
                .append(relPO.getAnalyteType())
                .append(relPO.getAnalyteCode())
                .append(relPO.getAnalyteSeq())
                .append(relPO.getReportUnit())
                .append(relPO.getTestValue())
                .append(relPO.getCasNo())
                .append(relPO.getLimitUnit())
                .append(relPO.getReportLimit())
                .append(relPO.getConclusionId())
                .append(relPO.getTestDataMatrixId())
                .append(relPO.getLanguages());
        return DigestUtils.md5Hex(append.toString());
    }


}
