/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdLabInput implements Serializable{

    private Long labId;
    private String labCode;
    private Integer locationId;
    private String locationCode;
    private Integer buId;
    private String buCode;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
