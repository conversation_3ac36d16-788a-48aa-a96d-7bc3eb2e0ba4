package com.sgs.testdatabiz.facade.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class ValidationStatsDTO {

    /**
     * stats_date DATE(10) 必填<br>
     * 日期
     */
    private Date statsDate;

    /**
     * system_id_of_data INTEGER(10) 必填<br>
     * 执行系统
     */
    private Integer systemIdOfData;

    /**
     * system_if_of_header INTEGER(10) 必填<br>
     * SystemId Of Header
     */
    private Integer systemIfOfHeader;

    /**
     * mode VARCHAR(50) 必填<br>
     * 验证模式
     */
    private String mode;

    /**
     * validate_result VARCHAR(255) 必填<br>
     * 验证结果
     */
    private String validateResult;

    /**
     * stats_count INTEGER(10) 必填<br>
     * 计数
     */
    private Integer statsCount;

    public void setValidateResult(String validateResult) {
        if(Objects.nonNull(validateResult)) {
            if(validateResult.length()>200) {
                this.validateResult = validateResult.substring(0,200);
            } else {
                this.validateResult = validateResult;
            }
        }

    }
}
