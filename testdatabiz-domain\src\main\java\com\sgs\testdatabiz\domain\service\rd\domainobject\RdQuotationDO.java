/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationRelationshipDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdQuotationDO {

    private Long labId;
    private Long systemId;
    private String reportNo;
    private RdQuotationRelationshipDO relationship;
    private String orderNo;
    private String realOrderNo;
    private Long versionId;
//    private Long rdQuotationId;
    private String quotationNo;
    private RdCustomerDO payer;
    private String currency;
    private BigDecimal netAmount;
    private BigDecimal vatAmount;
    private BigDecimal totalAmount;
    private BigDecimal discount;
    private BigDecimal adjustmentAmount;
    private BigDecimal finalAmount;
    private String quotationVersionId;
    private Integer quotationStatus;
    private List<RdServiceItemDO> serviceItemList;
    private List<RdAttachmentDO> quotationFileList;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String quotationInstanceId;
}
