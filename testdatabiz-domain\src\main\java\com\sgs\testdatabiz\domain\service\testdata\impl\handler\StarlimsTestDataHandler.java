package com.sgs.testdatabiz.domain.service.testdata.impl.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.req.subcontract.SubContractChemTestMatrixReq;
import com.sgs.otsnotes.facade.model.rsp.matrix.SubContractTestMatrixRsp;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.testdatabiz.core.annotation.TestDataSource;
import com.sgs.testdatabiz.core.constants.Constants;
import com.sgs.testdatabiz.core.enums.ConclusionType;
import com.sgs.testdatabiz.core.util.ListUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.core.util.Transcoding;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMatrixExtPO;
import com.sgs.testdatabiz.domain.service.testdata.enums.ErrMsgFormatter;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.base.AbstractTestDataHandler;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.StarlimsMatrixFilterHandler;
import com.sgs.testdatabiz.domain.service.testdata.model.ErrorMsg;
import com.sgs.testdatabiz.facade.model.dto.starlims.MatrixFilterDTO;
import com.sgs.testdatabiz.facade.model.dto.starlims.SubContractMatrixDTO;
import com.sgs.testdatabiz.facade.model.enums.AnalyteTypeEnum;
import com.sgs.testdatabiz.facade.model.enums.MatrixSource;
import com.sgs.testdatabiz.facade.model.req.starlims.*;
import com.sgs.testdatabiz.facade.model.testdata.*;
import com.sgs.testdatabiz.integration.SubcontractClient;
import com.sgs.testdatabiz.integration.TrimsLocalClient;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRsp;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationVersionRsp;
import com.sgs.trimslocal.facade.model.enums.ProductLineTypeEnum;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.testdatabiz.domain.service.testdata.enums.ErrMsgFormatter.*;

/**
 * starlims 数据导入处理类
 *
 * <AUTHOR>
 * @date 2023/3/22 10:02
 */
@Service
@TestDataSource(channel = SourceTypeEnum.STARLIMS)
public class StarlimsTestDataHandler extends AbstractTestDataHandler<ReceiveStarLimsReportReq> {

    private final Logger logger = LoggerFactory.getLogger(StarlimsTestDataHandler.class);
    @Autowired
    private SubcontractClient subcontractClient;
    @Autowired
    private TrimsLocalClient trimsLocalClient;
    @Autowired
    private StarlimsMatrixFilterHandler starlimsMatrixFilterHandler;

    /**
     * 接口参数验证
     *
     * @param body
     * @return
     */
    @Override
    public boolean inputValidate(ReceiveStarLimsReportReq body) {
        // 校验productLineCode
        if (StringUtils.isBlank(body.getProductLineCode())) {
            putErrorMsg(new ErrorMsg("ProductLineCode不能为空"));
            return true;
        }

        // 校验订单号
        String orderNo = body.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            logger.error("[starlims-inputValidate] 参数orderNo为null 无法继续校验");
            putErrorMsg(new ErrorMsg("parameter 'orderNo' can't be null"));
            return true;
        }
        String subContractNo = body.getSubContractNo();
        String objectNo = body.getObjectNo();
        if (StringUtils.isBlank(subContractNo) || StringUtils.isBlank(objectNo)) {
            logger.error("[starlims-inputValidate] OrderNo:{} subcontract:{},objectNo:{} 存在空值", orderNo, subContractNo, objectNo);
            putErrorMsg(new ErrorMsg("there has some parameter are null,please check [subContractNo,objectType,objectNo] "));
            return true;
        }

        List<ReceiveReportData> reportDatas = body.getReportDatas();
        if (CollectionUtils.isEmpty(reportDatas)) {
            putErrorMsg(new ErrorMsg("PLEASE CHECK PARAMETER!"));
            return true;
        }

        // 校验conclusionId是否都在枚举中
        for (ReceiveReportData reportData : reportDatas) {
            String conclusionId = reportData.getConclusionId();
            // check conclusionId
            if (Func.isNotEmpty(conclusionId) && !ConclusionType.check(conclusionId)) {
                putErrorMsg(new ErrorMsg(CONCLUSION_ID.getCode(),body.getExternalNo(),StrUtil.format("conclusion：{} not in (Pass, Fail, Inconclusive, Data Only, NA, Exempt)!", conclusionId)));
                return true;
            }
//            List<ReceiveTestResult> testResults = reportData.getTestResults();
//            if (Func.isNotEmpty(testResults)) {
//                for (ReceiveTestResult testResult : testResults) {
//                    String resultConclusionId = testResult.getConclusionId();
//                    // check conclusionId
//                    if (!ConclusionType.check(resultConclusionId)) {
//                        putErrorMsg(new ErrorMsg(CONCLUSION_ID.getCode(),body.getExternalNo(),StrUtil.format("conclusion：{} not in enums!", resultConclusionId)));
//                        return true;
//                    }
//                }
//            }
        }

        Date completedDate = body.getCompletedDate();
        if (completedDate == null) {
            return true;
        }

        return true;
    }

    /**
     * 构建标准数据对象
     *
     * @param reqObject
     * @return
     */
    @Override
    public ReportTestDataInfo inputBuild(ReceiveStarLimsReportReq reqObject) {
        ReportTestDataInfo report = new ReportTestDataInfo();
        // 组装Report 数据对象 report
        buildReportDataInfo(reqObject, report);
        // 构建Test Matrix标准数据对象
        report.setTestMatrixs(getTestMatrixInfoList(reqObject));

        return report;
    }

    private void buildReportDataInfo(ReceiveStarLimsReportReq reqObject, ReportTestDataInfo report) {
        report.setOrderNo(reqObject.getOrderNo());
        report.setParentOrderNo(reqObject.getParentOrderNo());
        report.setSubContractNo(reqObject.getSubContractNo());
        report.setReportNo(reqObject.getReportNo());
        report.setProductLineCode(reqObject.getProductLineCode());
        report.setLabCode(reqObject.getLabCode());
        report.setObjectNo(reqObject.getSubContractNo());
        // 外部系统主键Id.对方的外部系统主键id其实是我们自己的id，我们存储的外部系统主键id是对方系统主键id
        report.setExternalId(reqObject.getExternalId());
        // 当Starlims数据时，该值为folderNo。objectNo属性是starlims对应的字段FolderNo
        report.setExternalNo(reqObject.getObjectNo());
        report.setExternalObjectNo(reqObject.getExternalObjectNo());
        report.setOriginalReportNo(reqObject.getOriginalReportNo());
        report.setCompletedDate(reqObject.getCompletedDate());
        SourceTypeEnum sourceType = SourceTypeEnum.STARLIMS;
        report.setSourceType(sourceType.getCode());
        report.setRegionAccount(sourceType.getDesc());
        // add 231114
        report.setExcludeCustomerInterface(Func.isEmpty(reqObject.getExcludeCustomerInterface()) ? "0" : reqObject.getExcludeCustomerInterface());
        // TODO 转ZHS对应的中文
        setLanguage(reqObject, report);
    }

    private void setLanguage(ReceiveStarLimsReportReq reqObject, ReportTestDataInfo report) {
        LanguageType languageType = LanguageType.findStarLimsCode(reqObject.getLanguageId());
        if (languageType != null) {
            report.setLanguageId(languageType.getLanguageId());
        }else{
            // starLims 返回001 需要转换为3
            if(StringUtils.equals(reqObject.getLanguageId(), Constants.STAR_LIMES_EN_AND_CN_LANGUAGEID)){
                report.setLanguageId(NumberUtil.toInt(ReportLanguage.MultilingualReport.getCode()));
            }
        }
    }

    // region 构建标准数据对象

    /**
     * 构建Test Matrix标准数据对象
     *
     * @param reqObject
     * @return
     */
    private List<TestDataTestMatrixInfo> getTestMatrixInfoList(ReceiveStarLimsReportReq reqObject) {
        List<ReceiveReportData> reportDatas = reqObject.getReportDatas();
        if (CollectionUtils.isEmpty(reportDatas)) {
            putErrorMsg(new ErrorMsg("PLEASE CHECK PARAMETER!"));
            return null;
        }

        List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs = this.getChemTestMatrixList(reqObject);
        ProductLineTypeEnum productLineType = ProductLineTypeEnum.findType(reqObject.getProductLineCode());
        // DIG-9830 starlims 回传数据时，获取Chem数据不做终止校验
        /*if (chemTestMatrixs == null || chemTestMatrixs.isEmpty()) {
            putErrorMsg(new ErrorMsg("未找到对应的分包Matrix."));
            return null;
        }*/
        // 从执行系统获取subContract 中 所有matrix信息
        List<SubContractTestMatrixInfo> subContractTestMatrixList = this.getSubContractTestMatrixList(reqObject.getSubContractNo(), productLineType.getProductLineAbbr());

        Map<Long, PpArtifactRsp> ppArtifactMaps = Maps.newHashMap();
        Map<Integer, CitationVersionRsp> citationVersionMaps = Maps.newHashMap();
        buildPpAidInfo(reportDatas, ppArtifactMaps, chemTestMatrixs, citationVersionMaps);


        List<TestDataTestMatrixInfo> testMatrixs = Lists.newArrayList();
        for (ReceiveReportData reportData : reportDatas) {
            TestDataTestMatrixInfo testMatrix = this.getTestDataMatrixInfo(reqObject.getOrderNo(), chemTestMatrixs, subContractTestMatrixList, ppArtifactMaps, citationVersionMaps, reportData);
            if (testMatrix == null) {
                continue;
            }
            testMatrixs.add(testMatrix);
        }
        return testMatrixs;
    }

    private void buildPpAidInfo(List<ReceiveReportData> reportDatas, Map<Long, PpArtifactRsp> ppArtifactMaps, List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs, Map<Integer, CitationVersionRsp> citationVersionMaps) {
        Set<Long> aIds = Sets.newHashSet();
        Set<Integer> citationVersionIds = Sets.newHashSet();
        // TODO Aid不是实际Order上的值，需要Helen确认
        chemTestMatrixs.forEach(tm -> {
            if (NumberUtil.toLong(tm.getChemAid()) > 0) {
                aIds.add(tm.getChemAid());
            }
            if (NumberUtil.toLong(tm.getAid()) > 0) {
                aIds.add(tm.getAid());
            }
            if (NumberUtil.toInt(tm.getCitationVersionId()) > 0) {
                citationVersionIds.add(tm.getCitationVersionId());
            }
        });
        // 订单中完全没有的情况下 需要加上此逻辑
        reportDatas.forEach(tm -> {
            if (NumberUtil.toLong(tm.getaId()) > 0) {
                aIds.add(tm.getaId());
            }
            if (NumberUtil.toInt(tm.getCitationExternalId()) > 0) {
                citationVersionIds.add(tm.getCitationExternalId());
            }
        });
        List<PpArtifactRsp> ppArtifacts = trimsLocalClient.getPpArtifactRelListByArtifactIds(aIds);
        if (ppArtifacts == null) {
            ppArtifacts = Lists.newArrayList();
        }
        ppArtifacts.forEach(ppArtifact -> {
            ppArtifactMaps.put(ppArtifact.getArtifactId(), ppArtifact);
        });

        List<CitationVersionRsp> citationVersions = trimsLocalClient.getCitationInfoList(citationVersionIds);
        if (citationVersions != null){
            citationVersions.forEach(citationVersion->{
                citationVersionMaps.put(citationVersion.getCitationVersionId(), citationVersion);
            });
        }
    }

    /**
     * @param chemTestMatrixs
     * @param subContractTestMatrixList
     * @param ppArtifactMaps
     * @param reportData
     * @return
     */
    private TestDataTestMatrixInfo getTestDataMatrixInfo(String orderNo, List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs, List<SubContractTestMatrixInfo> subContractTestMatrixList,
                                                         Map<Long, PpArtifactRsp> ppArtifactMaps, Map<Integer, CitationVersionRsp> citationVersionMaps, ReceiveReportData reportData) {
        TestDataTestMatrixInfo testDataMatrix = new TestDataTestMatrixInfo();
        // TODO 不是管给的是什么鬼，我们都接并保存，HELEN确认230412 1531
        long aid = NumberUtil.toLong(reportData.getaId());
        int testLineId = NumberUtil.toInt(reportData.getTestLineId());
        if (testLineId <= 0) {
            putErrorMsg(new ErrorMsg(String.format("Pp(%s)下的TestLineId无效.", reportData.getPpNumber())));
            return null;
        }
        if (StringUtils.isBlank(reportData.getMaterialNumber())) {
            putErrorMsg(new ErrorMsg(String.format("Pp(%s)下的TestSampleNo不能为空.", reportData.getPpNumber())));
            return null;
        }
        int chemPpNo = NumberUtil.toInt(reportData.getPpNumber());
        int citationVersionId = NumberUtil.toInt(reportData.getCitationExternalId());
        String citationName = null;
        CitationVersionRsp citation = citationVersionMaps.get(citationVersionId);

        //  根据 订单中的matrix对象 匹配 starlims 返回的matrix 对象
        ChemPpArtifactTestLineInfoRsp chemTestLine = mappingChemPpArtifactTestLineInfo(chemTestMatrixs, reportData, aid, testLineId, chemPpNo, citationVersionId);
        if (chemTestLine == null) {
            chemTestLine = new ChemPpArtifactTestLineInfoRsp();
            // DIG-8617
            chemTestLine.setPpNo(chemPpNo);
            // DIG-8617
            //chemTestLine.setPpVersionId(reportData.getPpVersionId());
            chemTestLine.setAid(aid);
            chemTestLine.setTestLineId(testLineId);
            chemTestLine.setCitationVersionId(citationVersionId);

            if (citation != null) {
                chemTestLine.setCitationId(citation.getCitationId());
            }
            // TODO 回传的数据与订单数据无法Mapping
            testDataMatrix.setDisableMatrix(true);
        }
        if (citation != null) {
            citationName = citation.getCitationName();
        }

        // 优先使用starlims 返回的aid 获取citation信息

        PpArtifactRsp ppArtifact = ppArtifactMaps.get(aid);
        if (ppArtifact != null) {
            citationName = ppArtifact.getCitationName();

            chemTestLine.setCitationId(ppArtifact.getCitationId());
            chemTestLine.setCitationVersionId(ppArtifact.getCitationVersionId());
            chemTestLine.setCitationType(ppArtifact.getCitationType());
        }

        // 根据逻辑匹配订单中的matrix 信息
        mappingOrderMatrixInfo(orderNo, subContractTestMatrixList, reportData, testDataMatrix, chemTestLine);
        // 获取condition 信息
        List<TestDataConditionInfo> conditionInfoList = this.getConditionInfoList(reportData.getCondition());
        // 构建Test Matrix Lang标准对象
        List<TestDataTestMatrixLangInfo> testMatrixLangInfoList = getTestMatrixLangInfoList(reportData.getLanguages());
        // 构建Test Data标准数据对象
        List<TestDataResultInfo> testResultInfoList = getTestResultInfoList(reportData.getTestResults());

        // 设置Matrix 对象的数据
        setTestMatrixInfo(reportData, testDataMatrix, aid, chemTestLine, citationName, conditionInfoList, testMatrixLangInfoList, testResultInfoList);

        return testDataMatrix;
    }

    private void mappingOrderMatrixInfo(String orderNo, List<SubContractTestMatrixInfo> subContractTestMatrixList, ReceiveReportData reportData, TestDataTestMatrixInfo testDataMatrix, ChemPpArtifactTestLineInfoRsp chemTestLine) {
        // TODO 看接口示例是返回SODA的TestMatrixId？？？等Helen 回复
        // TODO ZHIZHI
        // SCI-1378 修改 TestMatrixId 字段 的保存
        MatrixFilterDTO matrixFilterDTO = MatrixFilterDTO.builder()
                .subContractTestMatrixList(subContractTestMatrixList)
                .chemTestLine(chemTestLine)
                .orderNo(orderNo)
                .reportData(reportData).build();
        SubContractMatrixDTO testMatrixId = starlimsMatrixFilterHandler.findTestMatrixId(matrixFilterDTO);
        if (testMatrixId != null) {
            testDataMatrix.setTestMatrixId(testMatrixId.getTestMatrixId());
            testDataMatrix.setTestSampleId(testMatrixId.getTestSampleId());
            testDataMatrix.setTestLineInstanceId(testMatrixId.getTestLineInstanceId());
            testDataMatrix.setCitationType(testMatrixId.getCitationType());
            testDataMatrix.setMatrixSource(testMatrixId.getMatrixSource());
        } else {
            putErrorMsg(new ErrorMsg(String.format("Pp(%s)下的匹配Matrix失败.", reportData.getPpNumber())));
        }
    }

    private void setTestMatrixInfo(ReceiveReportData reportData, TestDataTestMatrixInfo testDataMatrix, long aid,
                                   ChemPpArtifactTestLineInfoRsp chemTestLine, String citationName, List<TestDataConditionInfo> conditionInfoList,
                                   List<TestDataTestMatrixLangInfo> testMatrixLangInfoList, List<TestDataResultInfo> testResultInfoList) {
        //testDataMatrix.setExternalCode(reportData.getaId() != null ? reportData.getaId().toString() : null);
        testDataMatrix.setAid(aid);
        testDataMatrix.setPpNo(chemTestLine.getPpNo());
        testDataMatrix.setPpVersionId(chemTestLine.getPpVersionId());
        testDataMatrix.setTestLineId(chemTestLine.getTestLineId());
        testDataMatrix.setMethodDesc(Transcoding.rtfStrEsc(reportData.getTestMethod()));//对应Starlims字段：testMethod

        testDataMatrix.setTestSampleNo(reportData.getMaterialNumber());
        testDataMatrix.setExternalSampleNo(reportData.getSampleNo());
        //testDataMatrix.setSampleSeq(reportData.get());

        testDataMatrix.setCitationId(chemTestLine.getCitationId());
        testDataMatrix.setCitationVersionId(chemTestLine.getCitationVersionId());
        testDataMatrix.setCitationName(citationName);
        testDataMatrix.setTestLineSeq(reportData.getSorter());//1、对应Starlims字段：sorter
        testDataMatrix.setEvaluationAlias(Transcoding.rtfStrEsc(reportData.getTestReportName()));

        // TODO 这里需要Mapper转换成对应SODA的conclusion。conclusion属性对应的starlims 对应字段是testConclusion
        testDataMatrix.setConclusionId(reportData.getConclusionId());
        testDataMatrix.setConclusionDisplay(reportData.getConclusionAlias());
        testDataMatrix.setExternalId(String.valueOf(reportData.getId()));
        //
        testDataMatrix.setTestConditions(conditionInfoList);

        testDataMatrix.setAppFactorId(reportData.getApplicationFactorId());
        testDataMatrix.setAppFactorName(reportData.getApplicationFactor());
        testDataMatrix.setMaterialName(reportData.getSampleDescription());
        testDataMatrix.setMaterialColor(reportData.getSampleColor());
        testDataMatrix.setMaterialTexture(reportData.getSampleMaterial());


        testDataMatrix.setLanguages(testMatrixLangInfoList);

        testDataMatrix.setTestResults(testResultInfoList);

        //SCI-1378
        testDataMatrix.setReferFromReportNo(reportData.getReferFromReportNo());
        testDataMatrix.setReferFromSampleNo(reportData.getReferFromSampleNo());
    }

    /**
     * 根据 订单中的matrix对象 匹配 starlims 返回的matrix 对象
     * @param chemTestMatrixs
     * @param reportData
     * @param aid
     * @param testLineId
     * @param chemPpNo
     * @param citationVersionId
     * @return
     */
    @Nullable
    private ChemPpArtifactTestLineInfoRsp mappingChemPpArtifactTestLineInfo(List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs, ReceiveReportData reportData, long aid, int testLineId, int chemPpNo, int citationVersionId) {
        ChemPpArtifactTestLineInfoRsp chemTestLine = ListUtils.findFirst(chemTestMatrixs, tm -> {
            if (!NumberUtil.equals(tm.getTestLineId(), testLineId)) {
                return false;
            }
//            if (!(tm.getTestSampleIds() != null && tm.getTestSampleIds().contains(reportData.getExternalId().toUpperCase()))) {
//                return false;
//            }
            if (citationVersionId > 0 && NumberUtil.equals(tm.getChemPpNo(), chemPpNo) && NumberUtil.equals(tm.getCitationVersionId(), citationVersionId)) {
                return true;
            }
            // TODO 临时解决CSPP没有回传citationVersionId的问题，Starlims要在230414发版
            /*if (citationVersionId <= 0 && chemPpNo > 0 && NumberUtil.equals(tm.getCsPPNo(), testLineId) && PpType.check(tm.getPpType(), PpType.CCPP)){
                return true;
            }*/
            if (citationVersionId <= 0) {
                if (aid == 0) {
                    return false;
                }
                return NumberUtil.toInt(tm.getChemAid()) > 0 && NumberUtil.equals(tm.getChemAid(), aid);
            }
            // TL没有PP，用TL ID+materialNumber，匹配Order里的TL Id+ SampleNo
            if (aid == 0) {
                return true;
            }
            return false;
        });
        return chemTestLine;
    }

    /**
     * @param conditions
     * @return
     */
    private List<TestDataConditionInfo> getConditionInfoList(List<ReceiveConditionInfoReq> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return null;
        }
        List<TestDataConditionInfo> testConditions = Lists.newArrayList();
        for (ReceiveConditionInfoReq condition : conditions) {
            TestDataConditionInfo testCondition = new TestDataConditionInfo();
            testCondition.setTestConditionId(condition.getConditionId());
            testCondition.setTestConditionName(Transcoding.rtfStrEsc(condition.getConditionName()));
            testCondition.setTestConditionSeq(condition.getSorter());

            List<ReceiveConditionLangInfoReq> languages = condition.getLanguages();
            if (languages == null) {
                languages = Lists.newArrayList();
            }
            if (testCondition.getLanguages() == null) {
                testCondition.setLanguages(Lists.newLinkedList());
            }
            languages.forEach(language -> {
                LanguageType languageType = LanguageType.findStarLimsCode(language.getLanguageId());
                if (languageType == null) {
                    return;
                }
                TestDataConditionLangInfo conditionLang = new TestDataConditionLangInfo();
                conditionLang.setTestConditionName(Transcoding.rtfStrEsc(language.getConditionName()));
                conditionLang.setLanguageId(languageType.getLanguageId());
                testCondition.getLanguages().add(conditionLang);
            });
            testConditions.add(testCondition);
        }
        return testConditions;
    }

    /**
     * 构建Test Matrix Lang标准对象
     *
     * @param languages
     * @return
     */
    private List<TestDataTestMatrixLangInfo> getTestMatrixLangInfoList(List<ReceiveReportLanguages> languages) {
        if (languages == null || languages.isEmpty()) {
            return null;
        }
        List<TestDataTestMatrixLangInfo> langs = Lists.newArrayList();
        for (ReceiveReportLanguages language : languages) {
            // TODO ZHS 多语言转换
            com.sgs.otsnotes.facade.model.enums.LanguageType languageType = com.sgs.otsnotes.facade.model.enums.LanguageType.findStarLimsCode(language.getLanguageId());
            if (languageType == null || languageType.getLanguageId() <= 0) {
                continue;
            }
            TestDataTestMatrixLangInfo lang = new TestDataTestMatrixLangInfo();
            lang.setLanguageId(languageType.getLanguageId());
            lang.setEvaluationAlias(Transcoding.rtfStrEsc(language.getTestReportName()));
            String conclusionAlias = Transcoding.unicodeToChar(language.getConclusionAlias());
            if (StringUtils.isBlank(conclusionAlias)) {
                conclusionAlias = language.getConclusionAlias();
            }
            lang.setConclusionDisplay(conclusionAlias);
            lang.setMethodDesc(Transcoding.rtfStrEsc(language.getTestMethod()));

            lang.setMaterialName(Transcoding.rtfStrEsc(language.getSampleDescription()));
            lang.setMaterialColor(Transcoding.rtfStrEsc(language.getSampleColor()));
            lang.setMaterialTexture(Transcoding.rtfStrEsc(language.getSampleMaterial()));

            langs.add(lang);
        }
        return langs;
    }

    /**
     * 构建Test Data标准数据对象
     *
     * @param testResults
     * @return
     */
    protected List<TestDataResultInfo> getTestResultInfoList(List<ReceiveTestResult> testResults) {
        if (testResults == null || testResults.isEmpty()) {
            return null;
        }
        List<TestDataResultInfo> testDataResults = Lists.newArrayList();
        for (ReceiveTestResult td : testResults) {
            TestDataResultInfo testData = new TestDataResultInfo();
            testData.setTestAnalyteId(td.getId());
            testData.setAnalyteCode(td.getAnalyte());
            testData.setTestAnalyteName(td.getAnalyteAlias());

            testData.setAnalyteType(AnalyteTypeEnum.General.getType());// 定义枚举 0
            if (StringUtils.startsWithIgnoreCase(td.getAnalyte(), AnalyteTypeEnum.Conclusion.getText())) {
                testData.setAnalyteType(AnalyteTypeEnum.Conclusion.getType());
            }
            testData.setAnalyteSeq(NumberUtil.toInt(td.getSorter()));
            testData.setReportUnit(td.getUnit());
            //testData.setLimitUnit(td.getLimit());
            testData.setTestValue(td.getResult());
            testData.setCasNo(td.getCasNo());
            testData.setReportLimit(td.getLimit());
            testData.setConclusionId(td.getConclusionId());
            testData.setMethodLimit(td.getReportLimit());
            // 构建Test Result Lang 标准对象
            testData.setLanguages(this.getTestResultLangInfoList(td.getLanguages()));

            testDataResults.add(testData);
        }
        return testDataResults;
    }

    /**
     * 构建Test Result Lang 标准对象
     *
     * @param languages
     * @return
     */
    private List<TestDataResultLangInfo> getTestResultLangInfoList(List<ReceiveTestLanguages> languages) {
        if (languages == null || languages.isEmpty()) {
            return null;
        }
        List<TestDataResultLangInfo> langs = Lists.newArrayList();
        for (ReceiveTestLanguages language : languages) {
            // TODO ZHS 多语言转换
            com.sgs.otsnotes.facade.model.enums.LanguageType languageType = LanguageType.findStarLimsCode(language.getLanguageId());
            if (languageType == null || languageType.getLanguageId() <= 0) {
                continue;
            }
            TestDataResultLangInfo lang = new TestDataResultLangInfo();
            lang.setLanguageId(languageType.getLanguageId());

            String analyteName = Transcoding.unicodeToChar(language.getAnalyteAlias());
            if (StringUtils.isBlank(analyteName)) {
                analyteName = language.getAnalyteAlias();
            }
            lang.setTestAnalyteName(analyteName);

            langs.add(lang);
        }
        return langs;
    }

    // endregion

    /**
     * @param reqObject
     * @return
     */
    private List<ChemPpArtifactTestLineInfoRsp> getChemTestMatrixList(ReceiveStarLimsReportReq reqObject) {
        SubContractChemTestMatrixReq subContract = new SubContractChemTestMatrixReq();
        subContract.setOrderNo(reqObject.getOrderNo());
        subContract.setSubContractNo(reqObject.getSubContractNo());
        subContract.setProductLineCode(reqObject.getProductLineCode());

        return subcontractClient.getSubContractChemTestMatrixList(subContract);
    }

    /**
     *
     * @param subContractNo
     * @param productLineCode
     * @return
     */
    private List<SubContractTestMatrixInfo> getSubContractTestMatrixList(String subContractNo, String productLineCode) {
        SubContractChemTestMatrixReq subContract = new SubContractChemTestMatrixReq();
        subContract.setSubContractNo(subContractNo);
        subContract.setProductLineCode(productLineCode);

        return subcontractClient.getSubContractTestMatrixList(subContract);
    }



    /**
     * DIG-8626 编写的新check方法
     *
     * @param reqObj
     * @param subContractTestMatrixs
     * @param dbTestDataMatrixInfoList
     * @return
     */
    @Override
    protected boolean checkChannelData(ReportTestDataInfo reqObj, List<SubContractTestMatrixRsp> subContractTestMatrixs, List<TestDataMatrixExtPO> dbTestDataMatrixInfoList) {
        return true;
//        return checkStarlimsMatrixInfo(reqObj, subContractTestMatrixs, dbTestDataMatrixInfoList);
    }

    private boolean checkStarlimsMatrixInfo(ReportTestDataInfo reqObj, List<SubContractTestMatrixRsp> subContractTestMatrixs, List<TestDataMatrixExtPO> dbTestDataMatrixInfoList) {
        //matrixCondition Key testMatrixs 自身check
        //用来和db中的数据check
        Set<String> matrixConditionIdKeySet = Sets.newHashSet();

        Set<String> repeatMatrixIdConditionIdMsg = Sets.newHashSet();

        //check 回传的matrixId是否都在数据库中，用分包单的matrix校验
        // 如果不存在，拒绝
        List<String> subMatrixs = subContractTestMatrixs.stream().map(st -> st.getTestMatrixId()).distinct().collect(Collectors.toList());
        boolean checked = true;
        List<TestDataTestMatrixInfo> testMatrixs = reqObj.getTestMatrixs();

        String satrLimsFolderNo = reqObj.getExternalObjectNo();

        for (TestDataTestMatrixInfo m : testMatrixs) {

            // SCI-1378 starlims 类型的不需要  与订单中的数据校验
            if (StringUtils.equalsIgnoreCase(m.getMatrixSource(), MatrixSource.StarLims.getSource())) {
                continue;
            }

            String externalCode = m.getExternalCode();
            String testMatrixId = m.getTestMatrixId();
            //不给matrixid 的 可能是单测，复测，这个check之后还有专门的check
            if (StringUtils.isBlank(testMatrixId)) {
                continue;
            }
            Integer testLineId = m.getTestLineId();
            String testSampleNo = m.getTestSampleNo();

            //回传的matrixId 不在DB中
            if (!subMatrixs.contains(testMatrixId)) {
                checked = false;
                putErrorMsg(SCHEME_ASSIGN_SAMPLE, externalCode, testLineId, testSampleNo);
                continue;
            }

            Integer appFactorId = m.getAppFactorId();

            //check 自身condition + matrixID 是否存在重复
            List<TestDataConditionInfo> testConditions = m.getTestConditions();
            if (!CollectionUtils.isEmpty(testConditions)) {

                for (TestDataConditionInfo c : testConditions) {

                    Integer testConditionId = c.getTestConditionId();
                    String testConditionName = c.getTestConditionName();

                    String matrixConditionIdKey = String.format("%s_%s_%d", testMatrixId, testConditionId, appFactorId);

                    if (matrixConditionIdKeySet.contains(matrixConditionIdKey)) {
                        String repeatMsg = String.format("%s_%s", testSampleNo, testConditionName);
                        repeatMatrixIdConditionIdMsg.add(repeatMsg);
                        putErrorMsg(TEST_MATRIX, externalCode, testLineId, testSampleNo);
                        checked = false;
                        continue;
                    }
                    matrixConditionIdKeySet.add(matrixConditionIdKey);
                }
            }
        }

        if (!checked || !CollectionUtils.isEmpty(repeatMatrixIdConditionIdMsg)) {
            return checked;
        }

        /**
         * check matrixId+conditionId 是否存在于testData库中，用testData库的数据check
         */

        for (TestDataMatrixExtPO testDataMatrixExtPO : dbTestDataMatrixInfoList) {
            String externalNo = testDataMatrixExtPO.getExternalObjectNo();
            //如果回传的externalObjectNo 和DB中一样，不需要进行重复数据check，只有不同的objectNo 才需要check是否存在重复数据
            if (StringUtils.equalsIgnoreCase(satrLimsFolderNo, externalNo)) {
                continue;
            }

            String condition = testDataMatrixExtPO.getCondition();
            String testMatrixId = testDataMatrixExtPO.getTestMatrixId();
            Integer testLineId = testDataMatrixExtPO.getTestLineId();
            String sampleNo = testDataMatrixExtPO.getSampleNo();
            if (StringUtils.isBlank(condition)) {
                continue;
            }
            List<TestDataConditionInfo> testDataConditionInfos = Lists.newArrayList();
            try {
                testDataConditionInfos = JSONObject.parseArray(testDataMatrixExtPO.getCondition(), TestDataConditionInfo.class);
            } catch (Exception e) {

            }
            if (CollectionUtils.isEmpty(testDataConditionInfos)) {
                continue;
            }
            Integer testConditionId = testDataConditionInfos.get(0).getTestConditionId();
            String dbMatrixConditionIdKey = String.format("%s_%s", testMatrixId, testConditionId);
            if (matrixConditionIdKeySet.contains(dbMatrixConditionIdKey)) {
                //记录有重复数据
                putErrorMsg(ORDER_TEST_MATRIX, null, testLineId, sampleNo);
                checked = false;
            }
        }
        return checked;
    }

    @Override
    public SourceTypeEnum getChannel() {
        return SourceTypeEnum.STARLIMS;
    }


    /**
     *
     * @param subContractTestMatrixList
     * @param chemTestLine
     * @return concat(ifnull(ptlr.Aid,0),'-',tli.TestLineId,'-',ifnull(tcb.CitationType ,0) ,'-',tli.CitationVersionId) TestLineBizId,
     */
    private String buildTestLineInstanceIds(List<SubContractTestMatrixInfo> subContractTestMatrixList,
                                            ChemPpArtifactTestLineInfoRsp chemTestLine) {

        if (CollectionUtils.isEmpty(subContractTestMatrixList) || chemTestLine == null) {
            return null;
        }
        // 订单中存在此testLine  直接返回
        SubContractTestMatrixInfo subContractTestMatrixInfo = subContractTestMatrixList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(findPpTestLineCitationKey(item), findPpTestLineCitationKey(chemTestLine)))
                .findFirst().orElse(null);
        if (subContractTestMatrixInfo != null) {
            return subContractTestMatrixInfo.getTestLineInstanceId();
        }
        // 不存在 按concat(ifnull(ptlr.Aid,0),'-',tli.TestLineId,'-',ifnull(tcb.CitationType ,0) ,'-',tli.CitationVersionId) 拼接后计算
        return DigestUtils.md5Hex(findPpTestLineCitationKey(chemTestLine).toLowerCase());
    }

    private Integer buildTestLineCitationType(List<SubContractTestMatrixInfo> subContractTestMatrixList,
                                            ChemPpArtifactTestLineInfoRsp chemTestLine,
                                              String testLineInstanceId) {

        if (CollectionUtils.isEmpty(subContractTestMatrixList) || StringUtils.isBlank(testLineInstanceId)) {
            return chemTestLine.getCitationType();
        }
        if (NumberUtil.toInt(chemTestLine.getCitationType()) > 0) {
            return chemTestLine.getCitationType();
        }
        // 订单中存在此testLine  直接返回
        SubContractTestMatrixInfo subContractTestMatrixInfo = subContractTestMatrixList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getTestLineInstanceId(), testLineInstanceId))
                .findFirst().orElse(null);
        if (subContractTestMatrixInfo != null) {
            return subContractTestMatrixInfo.getCitationType();
        }
        return chemTestLine.getCitationType();
    }


    private String findPpTestLineCitationKey(SubContractTestMatrixInfo subContractTestMatrixInfo) {
        if (subContractTestMatrixInfo == null) {
            return null;
        }
        return String.format("%s_%s_%s_%s", NumberUtil.toInt(subContractTestMatrixInfo.getAid()),
                NumberUtil.toInt(subContractTestMatrixInfo.getTestLineId()),
                NumberUtil.toInt(subContractTestMatrixInfo.getCitationVersionId()),
                NumberUtil.toInt(subContractTestMatrixInfo.getCitationType()));
    }

    private String findPpTestLineCitationKey(ChemPpArtifactTestLineInfoRsp chemTestLine) {
        if (chemTestLine == null) {
            return null;
        }
        return String.format("%s_%s_%s_%s", NumberUtil.toInt(chemTestLine.getAid()),
                NumberUtil.toInt(chemTestLine.getTestLineId()),
                NumberUtil.toInt(chemTestLine.getCitationVersionId()),
                NumberUtil.toInt(chemTestLine.getCitationType()));
    }


}
