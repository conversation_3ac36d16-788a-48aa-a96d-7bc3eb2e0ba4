package com.sgs.testdatabiz.facade;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.info.TestDataInfo;
import com.sgs.testdatabiz.facade.model.req.TestDataConfigReq;
import com.sgs.testdatabiz.facade.model.req.TestDataDeleteReq;
import com.sgs.testdatabiz.facade.model.req.TestDataQueryReq;
import com.sgs.testdatabiz.facade.model.req.TestLineAnalyteMappingReq;
import com.sgs.testdatabiz.facade.model.rsp.TestDataConfigRsp;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixRsp;
import com.sgs.testdatabiz.facade.model.rsp.TestLineAnalyteMappingRsp;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.slim.SlimProJobInfo;

import java.util.List;

public interface TestDataFacade {
    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse slimTestData(SlimProJobInfo reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse saveTestData(ReportTestDataInfo reqObject);

    BaseResponse<List<TestDataInfo>> queryTestData(TestDataQueryReq reqObject);

    /**
     * @param reqObject
     * @return
     */
    BaseResponse deleteTestData(TestDataDeleteReq reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse batchInsert(TestLineAnalyteMappingReq reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse<List<TestLineAnalyteMappingRsp>> getTestLineAnalyteInfoList(TestLineAnalyteMappingReq reqObject);

    /**
     * 参数为空 或者按照参数查询不到数据，均返回general数据（如果有配置general数据）
     * @param req
     * @return
     */
    BaseResponse<List<TestDataConfigRsp>> queryTestDataConfig(TestDataConfigReq req);

    /**
     * 查询TestData数据 for other system
     * @param reqObject
     * @return
     */
    BaseResponse<List<TestDataTestMatrixRsp>> getTestDataInfoList(TestDataQueryReq reqObject);
}
