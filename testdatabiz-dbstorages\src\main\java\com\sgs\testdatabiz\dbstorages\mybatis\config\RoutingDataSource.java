package com.sgs.testdatabiz.dbstorages.mybatis.config;

import com.sgs.testdatabiz.dbstorages.mybatis.enums.DatabaseTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义数据源注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface RoutingDataSource {
    DatabaseTypeEnum value() default DatabaseTypeEnum.Master;
}
