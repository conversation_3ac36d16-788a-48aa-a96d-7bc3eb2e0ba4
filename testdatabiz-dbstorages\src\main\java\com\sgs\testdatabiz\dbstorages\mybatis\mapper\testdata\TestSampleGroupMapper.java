package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TestSampleGroupMapper {
    int countByExample(TestSampleGroupExample example);

    int deleteByExample(TestSampleGroupExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TestSampleGroupPO record);

    int insertSelective(TestSampleGroupPO record);

    List<TestSampleGroupPO> selectByExample(TestSampleGroupExample example);

    TestSampleGroupPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TestSampleGroupPO record, @Param("example") TestSampleGroupExample example);

    int updateByExample(@Param("record") TestSampleGroupPO record, @Param("example") TestSampleGroupExample example);

    int updateByPrimaryKeySelective(TestSampleGroupPO record);

    int updateByPrimaryKey(TestSampleGroupPO record);

    int batchInsert(List<TestSampleGroupPO> list);

    int batchUpdate(List<TestSampleGroupPO> list);
}