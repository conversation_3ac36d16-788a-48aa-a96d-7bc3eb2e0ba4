package com.sgs.testdatabiz.core.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.lang.reflect.*;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentMap;

public class BeanHelper {
    private static final Logger logger = LoggerFactory.getLogger(BeanHelper.class);
    /**
     *
     */
    private static final ConcurrentMap<Class<?>, HashMap<String, Field>> classCaches = Maps.newConcurrentMap();

    /**
     * @param clazz
     * @param argumentIndex
     * @param <TInput>
     * @return
     */
    public static <TInput> TInput instantiate(Class clazz, int argumentIndex) {
        List<Type> actualTypeArguments = getActualTypeArguments(clazz);
        if (actualTypeArguments == null || actualTypeArguments.isEmpty() || actualTypeArguments.size() < argumentIndex) {
            return null;
        }
        try {
            Class<?> clazz1 = getRawType(actualTypeArguments.get(argumentIndex));
            return (TInput) clazz1.newInstance();
        } catch (InstantiationException ex) {
            return null;
        } catch (IllegalAccessException ex) {
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * @param clazz
     * @return
     */
    public static List<Type> getActualTypeArguments(Class clazz) {
        Assert.notNull(clazz, "Class must not be null");
        Type superClass = clazz.getGenericSuperclass();
        if (superClass == null || !(superClass instanceof ParameterizedType)) {
            return null;
        }
        ParameterizedType parameterizedType = (ParameterizedType) superClass;
        // DIG-8555  Change this condition so that it does not always evaluate to "false"
        /*if (parameterizedType == null) {
            return null;
        }*/
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        if (actualTypeArguments == null || actualTypeArguments.length <= 0) {
            return null;
        }
        return Lists.newArrayList(actualTypeArguments);
    }

    /**
     * type不能直接实例化对象，通过type获取class的类型，然后实例化对象
     *
     * @param type
     * @return
     */
    private static Class<?> getRawType(Type type) {
        if (type instanceof Class) {
            return (Class) type;
        } else if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            Type rawType = parameterizedType.getRawType();
            return (Class) rawType;
        } else if (type instanceof GenericArrayType) {
            Type componentType = ((GenericArrayType) type).getGenericComponentType();
            return Array.newInstance(getRawType(componentType), 0).getClass();
        } else if (type instanceof TypeVariable) {
            return Object.class;
        } else if (type instanceof WildcardType) {
            return getRawType(((WildcardType) type).getUpperBounds()[0]);
        } else {
            String className = type == null ? "null" : type.getClass().getName();
            throw new IllegalArgumentException("Expected a Class, ParameterizedType, or GenericArrayType, but <" + type + "> is of type " + className);
        }
    }


}
