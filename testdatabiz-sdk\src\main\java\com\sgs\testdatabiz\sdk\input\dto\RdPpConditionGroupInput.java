/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPpConditionGroupInput implements Serializable {
    private String id;
    private String conditionGroupId;
    private String ppTestLineRelId;
    private Integer languageType;
    private String groupFootNotes;
    private Integer ppNo;
    private String ppName;
    private String sampleNos;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
