package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RdReportTestResultLangExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RdReportTestResultLangExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdIsNull() {
            addCriterion("rd_report_test_result_id is null");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdIsNotNull() {
            addCriterion("rd_report_test_result_id is not null");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdEqualTo(Long value) {
            addCriterion("rd_report_test_result_id =", value, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdNotEqualTo(Long value) {
            addCriterion("rd_report_test_result_id <>", value, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdGreaterThan(Long value) {
            addCriterion("rd_report_test_result_id >", value, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rd_report_test_result_id >=", value, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdLessThan(Long value) {
            addCriterion("rd_report_test_result_id <", value, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdLessThanOrEqualTo(Long value) {
            addCriterion("rd_report_test_result_id <=", value, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdIn(List<Long> values) {
            addCriterion("rd_report_test_result_id in", values, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdNotIn(List<Long> values) {
            addCriterion("rd_report_test_result_id not in", values, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdBetween(Long value1, Long value2) {
            addCriterion("rd_report_test_result_id between", value1, value2, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andRdReportTestResultIdNotBetween(Long value1, Long value2) {
            addCriterion("rd_report_test_result_id not between", value1, value2, "rdReportTestResultId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNull() {
            addCriterion("language_id is null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNotNull() {
            addCriterion("language_id is not null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdEqualTo(Integer value) {
            addCriterion("language_id =", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotEqualTo(Integer value) {
            addCriterion("language_id <>", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThan(Integer value) {
            addCriterion("language_id >", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("language_id >=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThan(Integer value) {
            addCriterion("language_id <", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThanOrEqualTo(Integer value) {
            addCriterion("language_id <=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIn(List<Integer> values) {
            addCriterion("language_id in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotIn(List<Integer> values) {
            addCriterion("language_id not in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdBetween(Integer value1, Integer value2) {
            addCriterion("language_id between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("language_id not between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameIsNull() {
            addCriterion("test_result_full_name is null");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameIsNotNull() {
            addCriterion("test_result_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameEqualTo(String value) {
            addCriterion("test_result_full_name =", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameNotEqualTo(String value) {
            addCriterion("test_result_full_name <>", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameGreaterThan(String value) {
            addCriterion("test_result_full_name >", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("test_result_full_name >=", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameLessThan(String value) {
            addCriterion("test_result_full_name <", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameLessThanOrEqualTo(String value) {
            addCriterion("test_result_full_name <=", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameLike(String value) {
            addCriterion("test_result_full_name like", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameNotLike(String value) {
            addCriterion("test_result_full_name not like", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameIn(List<String> values) {
            addCriterion("test_result_full_name in", values, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameNotIn(List<String> values) {
            addCriterion("test_result_full_name not in", values, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameBetween(String value1, String value2) {
            addCriterion("test_result_full_name between", value1, value2, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameNotBetween(String value1, String value2) {
            addCriterion("test_result_full_name not between", value1, value2, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkIsNull() {
            addCriterion("result_value_remark is null");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkIsNotNull() {
            addCriterion("result_value_remark is not null");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkEqualTo(String value) {
            addCriterion("result_value_remark =", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkNotEqualTo(String value) {
            addCriterion("result_value_remark <>", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkGreaterThan(String value) {
            addCriterion("result_value_remark >", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("result_value_remark >=", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkLessThan(String value) {
            addCriterion("result_value_remark <", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkLessThanOrEqualTo(String value) {
            addCriterion("result_value_remark <=", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkLike(String value) {
            addCriterion("result_value_remark like", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkNotLike(String value) {
            addCriterion("result_value_remark not like", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkIn(List<String> values) {
            addCriterion("result_value_remark in", values, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkNotIn(List<String> values) {
            addCriterion("result_value_remark not in", values, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkBetween(String value1, String value2) {
            addCriterion("result_value_remark between", value1, value2, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkNotBetween(String value1, String value2) {
            addCriterion("result_value_remark not between", value1, value2, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultUnitIsNull() {
            addCriterion("result_unit is null");
            return (Criteria) this;
        }

        public Criteria andResultUnitIsNotNull() {
            addCriterion("result_unit is not null");
            return (Criteria) this;
        }

        public Criteria andResultUnitEqualTo(String value) {
            addCriterion("result_unit =", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitNotEqualTo(String value) {
            addCriterion("result_unit <>", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitGreaterThan(String value) {
            addCriterion("result_unit >", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitGreaterThanOrEqualTo(String value) {
            addCriterion("result_unit >=", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitLessThan(String value) {
            addCriterion("result_unit <", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitLessThanOrEqualTo(String value) {
            addCriterion("result_unit <=", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitLike(String value) {
            addCriterion("result_unit like", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitNotLike(String value) {
            addCriterion("result_unit not like", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitIn(List<String> values) {
            addCriterion("result_unit in", values, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitNotIn(List<String> values) {
            addCriterion("result_unit not in", values, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitBetween(String value1, String value2) {
            addCriterion("result_unit between", value1, value2, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitNotBetween(String value1, String value2) {
            addCriterion("result_unit not between", value1, value2, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameIsNull() {
            addCriterion("limit_value_full_name is null");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameIsNotNull() {
            addCriterion("limit_value_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameEqualTo(String value) {
            addCriterion("limit_value_full_name =", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameNotEqualTo(String value) {
            addCriterion("limit_value_full_name <>", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameGreaterThan(String value) {
            addCriterion("limit_value_full_name >", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("limit_value_full_name >=", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameLessThan(String value) {
            addCriterion("limit_value_full_name <", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameLessThanOrEqualTo(String value) {
            addCriterion("limit_value_full_name <=", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameLike(String value) {
            addCriterion("limit_value_full_name like", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameNotLike(String value) {
            addCriterion("limit_value_full_name not like", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameIn(List<String> values) {
            addCriterion("limit_value_full_name in", values, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameNotIn(List<String> values) {
            addCriterion("limit_value_full_name not in", values, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameBetween(String value1, String value2) {
            addCriterion("limit_value_full_name between", value1, value2, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameNotBetween(String value1, String value2) {
            addCriterion("limit_value_full_name not between", value1, value2, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitUnitIsNull() {
            addCriterion("limit_unit is null");
            return (Criteria) this;
        }

        public Criteria andLimitUnitIsNotNull() {
            addCriterion("limit_unit is not null");
            return (Criteria) this;
        }

        public Criteria andLimitUnitEqualTo(String value) {
            addCriterion("limit_unit =", value, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitNotEqualTo(String value) {
            addCriterion("limit_unit <>", value, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitGreaterThan(String value) {
            addCriterion("limit_unit >", value, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitGreaterThanOrEqualTo(String value) {
            addCriterion("limit_unit >=", value, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitLessThan(String value) {
            addCriterion("limit_unit <", value, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitLessThanOrEqualTo(String value) {
            addCriterion("limit_unit <=", value, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitLike(String value) {
            addCriterion("limit_unit like", value, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitNotLike(String value) {
            addCriterion("limit_unit not like", value, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitIn(List<String> values) {
            addCriterion("limit_unit in", values, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitNotIn(List<String> values) {
            addCriterion("limit_unit not in", values, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitBetween(String value1, String value2) {
            addCriterion("limit_unit between", value1, value2, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andLimitUnitNotBetween(String value1, String value2) {
            addCriterion("limit_unit not between", value1, value2, "limitUnit");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}