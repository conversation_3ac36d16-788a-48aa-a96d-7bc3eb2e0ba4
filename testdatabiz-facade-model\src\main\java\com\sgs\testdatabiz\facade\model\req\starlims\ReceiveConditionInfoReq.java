package com.sgs.testdatabiz.facade.model.req.starlims;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.List;

public class ReceiveConditionInfoReq extends PrintFriendliness {
    /**
     * testConditionId
     */
    private Integer conditionId;

    /**
     * testConditionName
     */
    private String conditionName;

    /**
     * testConditionSeq
     */
    private Integer sorter;

    /**
     *
     */
    private List<ReceiveConditionLangInfoReq> languages;

    public Integer getConditionId() {
        return conditionId;
    }

    public void setConditionId(Integer conditionId) {
        this.conditionId = conditionId;
    }

    public String getConditionName() {
        return conditionName;
    }

    public void setConditionName(String conditionName) {
        this.conditionName = conditionName;
    }

    public Integer getSorter() {
        return sorter;
    }

    public void setSorter(Integer sorter) {
        this.sorter = sorter;
    }

    public List<ReceiveConditionLangInfoReq> getLanguages() {
        return languages;
    }

    public void setLanguages(List<ReceiveConditionLangInfoReq> languages) {
        this.languages = languages;
    }
}
