/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPositionDTO implements Serializable {

    private String positionInstanceId;
    private Integer usageTypeId;
    private String usageTypeName;
    private String positionName;
    private String positionDescription;
    private Integer positionSeq;
    private List<RdPositionLanguageDTO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}