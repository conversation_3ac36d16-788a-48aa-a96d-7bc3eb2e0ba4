package com.sgs.testdatabiz.web.base;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HeaderHelper;
import com.sgs.testdatabiz.core.constants.Constants;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.ApiRequestMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.web.config.ApiRequestRecorderConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Stream;

import static com.sgs.testdatabiz.domain.service.constant.SystemConstants.DEFAULT_USER;

@Slf4j
@Aspect
@Component
// @Order(3) 确保在SCIAntiReplayAttackAspect后执行
@Order(3)
public class ApiRequestRecorderAspect {

    private final IdService idService;

    private final ObjectMapper objectMapper;

    private final ApiRequestMapper apiRequestMapper;

    private final BlockingQueue<Optional<ApiRequestPO>> queue;

    private final ExecutorService consumerExecutor;

    private final ApiRequestRecorderConfig apiRequestRecorderConfig;

    @Value("${api.request.queue.max-size:500}")
    private int maxSizeOfQueue = 500;

    @Value("${api.request.queue.seconds-of-shutdown:60}")
    private int secondsOfShutdown = 60;

    private static final Set<Class<?>> wrapperClasses = ImmutableSet.<Class<?>>builder()
            .add(
                    Integer.class,
                    Long.class,
                    Double.class,
                    Float.class,
                    Character.class,
                    Byte.class,
                    Short.class,
                    Boolean.class,
                    String.class
            )
            .build();

    public ApiRequestRecorderAspect(IdService idService, ObjectMapper objectMapper, ApiRequestMapper apiRequestMapper, ApiRequestRecorderConfig apiRequestRecorderConfig) {
        this.idService = idService;
        this.objectMapper = objectMapper;
        this.apiRequestMapper = apiRequestMapper;
        this.apiRequestRecorderConfig = apiRequestRecorderConfig;
        this.consumerExecutor = Executors.newSingleThreadExecutor();
        this.queue = new LinkedBlockingQueue<>(maxSizeOfQueue);
    }

    @PostConstruct
    public void setUp() {
        this.consumerExecutor.execute(this::consume);
        log.info("apilog consumer of ApiRequestRecorderAspect is started");
    }

    @PreDestroy
    public void shutdown() {
        this.consumerExecutor.shutdownNow();
        try {
            if (!consumerExecutor.awaitTermination(secondsOfShutdown, TimeUnit.SECONDS)) {
                log.warn("Consumer executor did not terminate");
            }
            List<Optional<ApiRequestPO>> resetApiLogs = new ArrayList<>();
            queue.drainTo(resetApiLogs);
            for (Optional<ApiRequestPO> apiLog : resetApiLogs) {
                log.info("write apilog after shutdown threadpool");
                safeWriteIfPresent(apiLog);
            }
        } catch (InterruptedException ex) {
            consumerExecutor.shutdownNow();
            log.warn("Consumer executor terminated now");
            Thread.currentThread().interrupt();
        } finally {
            log.info("apilog consumer of ApiRequestRecorderAspect is shutdown");
        }
    }

    @Pointcut(value = "execution(public * com.sgs.testdatabiz.web.controllers..*Controller.*(..)))")
    public void apiLog(){}

    @Pointcut("execution(* com.sgs.testdatabiz.web.controllers.HealthController.*(..))")
    public void excludeHealthController() {}

    @Around("apiLog() && ! excludeHealthController()") // 定义切点表达式
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        Optional<ApiRequestPO> apiLog = prepareLog(joinPoint);
        try {
            Object resp = joinPoint.proceed();
            recordSuccess(apiLog, resp);
            return resp;
        }  catch (BizException ex) {
            recordException(apiLog, ex);
            throw ex;
        } catch (Throwable ex) {
            recordException(apiLog, ex);
            throw ex;
        } finally {
            queue.put(apiLog);
        }
    }

    private void recordSuccess(Optional<ApiRequestPO> apiLog, Object resp) {
        try {
            if(apiLog.isPresent()) {
                ApiRequestPO apiRequestPO = apiLog.get();
                int statusCode = 0;
                if (resp instanceof BaseResponse) {
                    statusCode = ((BaseResponse<?>) resp).getStatus();
                }
                setStatusAndBody(apiRequestPO, resp, statusCode);
            }
        } catch (Exception e) {
            //ignore
            log.warn("error occur when save api request", e);
        }
    }

    private void recordException(Optional<ApiRequestPO> apiLog, Throwable ex) {
        try {
            if(apiLog.isPresent()) {
                ApiRequestPO apiRequestPO = apiLog.get();
                setStatusAndBody(apiRequestPO,
                        new BaseResponse<>(ResponseCode.UNKNOWN.getCode(), ex.getMessage()),
                        ResponseCode.UNKNOWN.getCode()
                );
            }
        } catch (Exception e) {
            //ignore
            log.warn("error occur when save api request", e);
        }
    }

    private void recordException(Optional<ApiRequestPO> apiLog, BizException ex) {
        try {
            if(apiLog.isPresent()) {
                String errorMsg = ex.getMessage();
                ApiRequestPO apiRequestPO = apiLog.get();
                setStatusAndBody(apiRequestPO, new BaseResponse<>(
                        ex.getCode(),
                        StringUtils.isNotBlank(errorMsg) ? errorMsg : "服务器出了点小差错，请稍后再试."
                ), ex.getCode());
            }
        } catch (Exception e) {
            //ignore
            log.warn("error occur when save api request", e);
        }
    }

    private void setStatusAndBody(ApiRequestPO apiRequestPO, Object body, int status) throws JsonProcessingException {
        if(status != 200) {
            apiRequestPO.setResponseBody(objectMapper.writeValueAsString(body));
        }
        apiRequestPO.setResponseStatus(Func.toStr(status));
    }


    private void consume() {
        try {
            while (true) {
                Optional<ApiRequestPO> apiLog = queue.take();
                safeWriteIfPresent(apiLog);
            }
        } catch (InterruptedException e) {
            log.info("logWriter consumer of ApiRequestRecorderAspect is interrupted");
            Thread.currentThread().interrupt();
        }
    }

    private void safeWriteIfPresent(Optional<ApiRequestPO> apiLog) {
        try {
            apiLog.ifPresent(apiRequestMapper::insert);
        } catch (Throwable e) {
            //ignore
            log.warn("error occur when save api request", e);
        }
    }

    private Optional<ApiRequestPO> prepareLog(ProceedingJoinPoint joinPoint) {
        try {
            String labCode = HeaderHelper.getParamValue(Constants.LAB_CODE);
            String systemId = HeaderHelper.getParamValue(Constants.SYSTEM_ID);
            String requestId = HeaderHelper.getParamValue(Constants.REQUEST_ID);

            // Check if this method should be excluded from recording
            String methodName = joinPoint.getSignature().getName();

            if (shouldExcludeFromRecording(methodName, systemId)) {
                return Optional.empty();
            }

            ApiRequestPO apiRequestPO = new ApiRequestPO();
            apiRequestPO.setId(idService.nextId());
            apiRequestPO.setSystemId(Func.toInt(systemId));
            apiRequestPO.setLabCode(labCode);
            apiRequestPO.setRequestId(
                    Optional.ofNullable(requestId)
                            .orElse(UUID.randomUUID().toString())
            );
            apiRequestPO.setRequestHeader(objectMapper.writeValueAsString(requestHeader()));
            Object[] args = joinPoint.getArgs(); // 获取方法参数
            for (Object arg : args) {
                if (arg instanceof BaseModel) {
                    //记录日志，取请求参数中的extId
                    BaseModel sciBaseRequest = (BaseModel) arg;
                    apiRequestPO.setExtId(sciBaseRequest.getExtId());
                }
            }
            apiRequestPO.setRequestBody(fetchBody(args));
            apiRequestPO.setMethodName(getServletPath());
            apiRequestPO.setCreatedDate(DateUtil.now());
            apiRequestPO.setModifiedDate(DateUtil.now());
            apiRequestPO.setCreatedBy(DEFAULT_USER);
            apiRequestPO.setModifiedBy(DEFAULT_USER);
            return Optional.of(apiRequestPO);
        } catch (Throwable t) {
            return Optional.empty();
        }
    }

    private String fetchBody(Object[] args) throws JsonProcessingException {
        String body = "{}";
        Optional<Object> reqObject = Stream.of(args).filter(arg -> arg instanceof BaseProductLine).findAny();
        if (reqObject.isPresent()) {
            body = objectMapper.writeValueAsString(reqObject.get());
        } else {
            Optional<Object> firstNotPrimitiveArg = Stream.of(args)
                    .filter(this::isNotPrimitive)
                    .findFirst();
            if(firstNotPrimitiveArg.isPresent()) {
                body = objectMapper.writeValueAsString(firstNotPrimitiveArg.get());
            }
        }
        return body;
    }



    public boolean isNotPrimitive(Object arg) {
        Class<?> argClass = arg.getClass();
        return ! wrapperClasses.contains(argClass) && ! argClass.isPrimitive();
    }

    private String getServletPath() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes)requestAttributes;
        if(sra == null) {
            return "";
        }
        HttpServletRequest request = sra.getRequest();
        return request.getServletPath();
    }

    private Map<String, String> requestHeader() {
        HashMap<String, String> requestHeaderMap = Maps.newHashMap();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return requestHeaderMap;
        }
        ServletRequestAttributes sra = (ServletRequestAttributes) requestAttributes;
        HttpServletRequest request = sra.getRequest();
        if (Func.isEmpty(request)) {
            return requestHeaderMap;
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        if (Func.isEmpty(headerNames)) {
            return requestHeaderMap;
        }
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            requestHeaderMap.put(headerName, headerValue);
        }
        return requestHeaderMap;

    }

    /**
     * Check if the method should be excluded from recording based on configuration
     * @param methodName the method name from joinPoint
     * @param systemIdFromHeader the system ID from request header
     * @return true if should be excluded, false otherwise
     */
    private boolean shouldExcludeFromRecording(String methodName, String systemIdFromHeader) {
        if (apiRequestRecorderConfig == null ||
                apiRequestRecorderConfig.getExclusionMethods() == null ||
                apiRequestRecorderConfig.getExclusionMethods().isEmpty()) {
            return false;
        }

        return apiRequestRecorderConfig.getExclusionMethods().stream()
                .anyMatch(exclusion -> {
                    boolean methodMatches = StringUtils.equals(methodName, exclusion.getMethodName());

                    // If configured systemId is null or empty, ignore systemId condition and only match on method name
                    if (StringUtils.isBlank(exclusion.getSystemId())) {
                        return methodMatches;
                    }

                    // Both method name and system ID must match
                    boolean systemIdMatches = StringUtils.equals(systemIdFromHeader, exclusion.getSystemId());
                    return methodMatches && systemIdMatches;
                });
    }

}
