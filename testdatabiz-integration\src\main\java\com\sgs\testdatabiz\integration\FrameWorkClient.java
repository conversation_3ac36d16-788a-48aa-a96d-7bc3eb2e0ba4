package com.sgs.testdatabiz.integration;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.preorder.facade.model.dto.order.DataDictionary;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.HttpClientUtils;
import com.sgs.testdatabiz.integration.config.URLConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 *
 */
@Component
public class FrameWorkClient {
    private static final Logger logger = LoggerFactory.getLogger(FrameWorkClient.class);
    @Autowired
    private URLConfiguration urlConfiguration;

    private LoadingCache<String, LabInfo> labInfoCache;
    @Value("${lab-info.cache.max-size:200}")
    private Integer maxSizeOfCache;

    @Value("${lab-info.cache.duration-minutes:1440}")
    private Long durationMinutesOfCache;

    @PostConstruct
    public void init() {
        this.labInfoCache = CacheBuilder.newBuilder()
                .maximumSize(maxSizeOfCache)  // 设置缓存的最大容量
                .expireAfterWrite(durationMinutesOfCache, TimeUnit.MINUTES)  // 设置缓存项在写入后10分钟后过期
                .build(new CacheLoader<String, LabInfo>() {
                    @Override
                    public LabInfo load(String labCode) {
                        return getLabCodeInfoByLabCode0(labCode);
                    }
                });
    }

    /**
     * @param keyGroup
     * @return
     */
    public ConcurrentHashMap<String, String> getDataDictionaryMap(String keyGroup) {
        ConcurrentHashMap<String, String> hashMaps = new ConcurrentHashMap<>();
        try {
            HashMap<String, String> reqParams = new HashMap<>();
            reqParams.put("bUID", "1");
            reqParams.put("systemID", "2");
            reqParams.put("SysKeyGroup", keyGroup);

            String frameWorkApi = String.format("%s/dataDictionary/api/v1/get/dataDictionary", urlConfiguration.getFrameWorkApiUrl());
            String jsonStr = HttpClientUtil.sendGet(frameWorkApi, reqParams);
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            JSONArray jsonArray = JSONArray.parseArray(jsonStr);
            if (jsonArray == null || jsonArray.isEmpty()) {
                return null;
            }
            String sysKey = "";
            for (int jsonIndex = 0; jsonIndex < jsonArray.size(); jsonIndex++) {
                JSONObject json = jsonArray.getJSONObject(jsonIndex);
                sysKey = json.getString("sysKey");
                if (hashMaps.containsKey(sysKey)) {
                    continue;
                }
                hashMaps.put(sysKey, json.getString("sysValue"));
            }
        } catch (Exception ex) {
            logger.error("FrameWorkClient.getDataDictionaryMap 信息异常,keyGroup=" + keyGroup, ex);
        }
        return hashMaps;
    }

    /**
     * @param keyGroup
     * @param sysKey
     * @return
     */
    public String getDataDictionaryValue(String keyGroup, String sysKey) {
        if (sysKey == null) {
            return "";
        }
        ConcurrentHashMap<String, String> hashMaps = this.getDataDictionaryMap(keyGroup);
        if (hashMaps == null || hashMaps.isEmpty() || !hashMaps.containsKey(sysKey)) {
            return "";
        }
        return hashMaps.get(sysKey);
    }

    public List<DataDictionary> getDataDictionaryList(String sysKeyGroup, String buCode) {
        List<DataDictionary> dataDictionaries = Lists.newArrayList();
        try {
            String frameWorkApi = urlConfiguration.getFrameWorkApiUrl() + "/dataDictionary/api/v1/get/dataDictionary";
            HashMap<String, String> reqParams = new HashMap<>();
            reqParams.put("bUID", "1");
            reqParams.put("systemID", "2");
            reqParams.put("SysKeyGroup", sysKeyGroup);
            reqParams.put("buCode", buCode);
            String s = HttpClientUtil.sendGet(frameWorkApi, reqParams);
            if (StringUtils.isBlank(s)) {
                return dataDictionaries;
            }
            dataDictionaries = JSONArray.parseArray(s, DataDictionary.class);
        } catch (Exception ex) {
            logger.error("FrameWorkClient.getDataDictionaryList 信息异常,sysKeyGroup={},buCode={}", sysKeyGroup, buCode, ex);
        }
        return dataDictionaries;
    }

    /**
     * @param labCode
     * @return
     */
    public LabInfo getLabCodeInfoByLabCode(String labCode) {
        HashMap<String, String> trimsParams = new HashMap();
        trimsParams.put("labCode", labCode);
        List<LabInfo> rows = this.queryLabList(trimsParams);
        if (CollectionUtils.isEmpty(rows)) {
            return new LabInfo();
        }
        return rows.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(labCode, item.getLaboratoryCode()))
                .findFirst().orElse(new LabInfo());
    }

    /**
     * @param labCode
     * @return
     */
    public LabInfo getLabCodeInfoByLabCodeFromCache(String labCode,String systemId) {
        try {
            return labInfoCache.get(labCode);
        } catch (Exception e) {
            if (Func.isNotBlank(systemId) && (Objects.equals(systemId, "1") || Objects.equals(systemId, "10002"))) {
                LabInfo labInfo = new LabInfo();
                labInfo.setLaboratoryCode(labCode);
                return labInfo;
            }
            throw new BizException(com.sgs.framework.core.base.ResponseCode.INTERNAL_SERVER_ERROR, "find labInfo by "+labCode+ " got an error");
        }
    }

    /**
     * @param labCode
     * @return
     */
    private LabInfo getLabCodeInfoByLabCode0(String labCode) {
        HashMap<String, String> trimsParams = new HashMap();
        trimsParams.put("labCode", labCode);
        List<LabInfo> rows = this.queryLabList(trimsParams);
        if (CollectionUtils.isEmpty(rows)) {
            throw new BizException(com.sgs.framework.core.base.ResponseCode.ILLEGAL_ARGUMENT,"query labInfo by "+labCode+ " got no result");
        }
        return rows.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(labCode, item.getLaboratoryCode()))
                .findFirst()
                .orElseThrow(() -> new BizException(com.sgs.framework.core.base.ResponseCode.ILLEGAL_ARGUMENT,"query labInfo by "+labCode+ " got no result"));
    }


    public List<LabInfo> queryLabList(HashMap<String, String> reqParams) {
        try {
            String frameWorkApi = String.format("%s/trims/api/v1/queryLabList", urlConfiguration.getFrameWorkApiUrl());
            String jsonStr = HttpClientUtil.sendGet(frameWorkApi, reqParams);
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            JSONObject object = JSONObject.parseObject(jsonStr);
            String rows = object.getString("rows");
            return JSONArray.parseArray(rows, LabInfo.class);
        } catch (Exception ex) {
            logger.error("FrameWorkClient.queryLabList 信息异常：{}.", ex);
        }
        return null;
    }

    public String queryActualTat(String labCode, Date startDate, Date endDate) {
        try {
            Map<String, Object> reqParams = new HashMap<>();
            reqParams.put("labCode", labCode);
            reqParams.put("startDate", DateUtils.format(startDate, "yyyy-MM-dd HH:mm:ss"));
            reqParams.put("endDate", DateUtils.format(endDate, "yyyy-MM-dd HH:mm:ss"));

            String frameWorkApi = String.format("%s/holiday/api/v1/queryHolidayDaysByDateInterval", urlConfiguration.getFrameWorkApiUrl());
            String jsonStr = HttpClientUtils.postJson(frameWorkApi, JSONObject.toJSONString(Arrays.asList(reqParams)));
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            JSONArray tatArray = JSONArray.parseArray(jsonStr);
            JSONObject jsonObj = (JSONObject) tatArray.get(0);
            int tatTemp = Integer.parseInt(jsonObj.get("workingDays").toString());
            int tat = Math.max(tatTemp, 0);
            return String.valueOf(tat);
        } catch (Exception ex) {
            logger.error("FrameWorkClient.queryHolidayDaysByDateInterval 信息异常：{}.", ex);
        }
        return null;
    }

    public String queryDelayDay(String labCode, Date startDate, Date endDate) {
        try {
            Map<String, Object> reqParams = new HashMap<>();
            reqParams.put("labCode", labCode);
            reqParams.put("startDate", DateUtils.format(startDate, "yyyy-MM-dd HH:mm:ss"));
            reqParams.put("endDate", DateUtils.format(endDate, "yyyy-MM-dd HH:mm:ss"));
            String frameWorkApi = String.format("%s/holiday/api/v1/queryHolidayDaysByDateInterval", urlConfiguration.getFrameWorkApiUrl());
            String jsonStr = HttpClientUtils.postJson(frameWorkApi, JSONObject.toJSONString(Arrays.asList(reqParams)));
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            JSONArray tatArray = JSONArray.parseArray(jsonStr);
            JSONObject jsonObj = (JSONObject) tatArray.get(0);
            int delayDayTemp = Integer.parseInt(jsonObj.get("workingDays").toString());
            int delayDay = delayDayTemp > 0 ? (delayDayTemp - 1) : 0;
            return String.valueOf(delayDay);

        } catch (Exception ex) {
            logger.error("FrameWorkClient.queryHolidayDaysByDateInterval 信息异常：{}.", ex);
        }
        return null;
    }


}
