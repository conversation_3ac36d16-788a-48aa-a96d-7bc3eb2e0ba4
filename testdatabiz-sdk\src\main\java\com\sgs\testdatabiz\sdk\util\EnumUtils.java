package com.sgs.testdatabiz.sdk.util;

public class EnumUtils {

    /**
     * 解析枚举类的全限定名，提取枚举名称的驼峰形式，并去掉 "Enum" 关键字。
     *
     * @param fullyQualifiedName 枚举类的全限定名，例如 "fw.OrderStatusEnum"
     * @return 提取的枚举名称，例如 "orderStatus"
     */
    public static String extractEnumName(String fullyQualifiedName) {
        if (fullyQualifiedName == null || fullyQualifiedName.isEmpty()) {
            throw new IllegalArgumentException("全限定名不能为空");
        }

        // 分割全限定名
        String[] parts = fullyQualifiedName.split("\\.");
        if (parts.length == 0) {
            throw new IllegalArgumentException("无效的全限定名");
        }

        // 获取最后一个部分，即枚举名称
        String enumName = parts[parts.length - 1];

        // 去掉 "Enum" 关键字
        if (enumName.endsWith("Enum")) {
            enumName = enumName.substring(0, enumName.length() - 4);
        }

        // 将枚举名称转换为驼峰形式
        String camelCaseName = toCamelCase(enumName);

        return camelCaseName;
    }

    /**
     * 将枚举名称转换为驼峰形式。
     *
     * @param enumName 枚举名称，例如 "OrderStatus"
     * @return 驼峰形式的名称，例如 "orderStatus"
     */
    private static String toCamelCase(String enumName) {
        if (enumName == null || enumName.isEmpty()) {
            return enumName;
        }

        StringBuilder camelCaseName = new StringBuilder();
        boolean capitalizeNext = false;

        for (int i = 0; i < enumName.length(); i++) {
            char c = enumName.charAt(i);

            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    camelCaseName.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    if (i == 0) {
                        camelCaseName.append(Character.toLowerCase(c));
                    } else {
                        camelCaseName.append(c);
                    }
                }
            }
        }

        return camelCaseName.toString();
    }

    public static void main(String[] args) {
        String fullyQualifiedName = "fw.OrderStatusEnum";
        String extractedName = extractEnumName(fullyQualifiedName);
        System.out.println("Extracted name: " + extractedName); // 输出: orderStatus
    }
}


