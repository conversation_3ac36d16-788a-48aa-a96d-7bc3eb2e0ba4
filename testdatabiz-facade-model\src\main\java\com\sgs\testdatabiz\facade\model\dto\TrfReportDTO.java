package com.sgs.testdatabiz.facade.model.dto;

import java.util.Date;

import lombok.Data;

@Data
public class TrfReportDTO {

    /**
     * id BIGINT(19) 必填<br>
     * 
     */
    private Long id;

    /**
     * trf_id BIGINT(19) 必填<br>
     * 
     */
    private Long trfId;

    /**
     * lab_id INTEGER(10)<br>
     * 
     */
    private Integer labId;

    /**
     * system_id INTEGER(10)<br>
     * 调用系统id
     */
    private Integer systemId;

    /**
     * order_id VARCHAR(100)<br>
     * 
     */
    private String orderId;

    /**
     * order_no VARCHAR(100)<br>
     * 
     */
    private String orderNo;

    /**
     * report_id VARCHAR(100)<br>
     * 
     */
    private String reportId;

    /**
     * report_no VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * origin_report_no VARCHAR(50)<br>
     * originReportNo
     */
    private String originReportNo;

    /**
     * report_status INTEGER(10)<br>
     * 报告状态
     */
    private Integer reportStatus;

    /**
     * report_due_date TIMESTAMP(19)<br>
     * 
     */
    private Date reportDueDate;

    /**
     * review_conclusion VARCHAR(5000)<br>
     * reviewConclusion
     */
    private String reviewConclusion;

    /**
     * comments VARCHAR(5000)<br>
     * 评论
     */
    private String comments;

    /**
     * delivery_flag INTEGER(10)<br>
     * 发送标记
     */
    private Integer deliveryFlag;

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 有效无效标记：0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50) 默认值[system]<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50) 默认值[system]<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;
    
}
