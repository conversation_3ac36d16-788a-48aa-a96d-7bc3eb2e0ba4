package com.sgs.testdatabiz.facade.model.req.entersubcontract;

import com.sgs.framework.core.base.BaseRequest;

import java.io.InputStream;

public class InputStreamReq extends BaseRequest {

    private InputStream inputStream;

    private String fileName;

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
