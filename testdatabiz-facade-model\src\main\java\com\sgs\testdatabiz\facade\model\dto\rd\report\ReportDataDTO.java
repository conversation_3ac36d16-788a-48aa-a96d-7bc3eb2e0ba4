package com.sgs.testdatabiz.facade.model.dto.rd.report;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/31 20:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportDataDTO extends BaseModel implements Serializable {
    private RdReportDTO header;
    private List<RdTestSampleDTO> testSampleList;
    private List<RdTestLineDTO> testLineList;
//    private List<RdReportMatrixDTO> reportMatrixList;
    private List<RdTestResultDTO> testResultList;
    private List<RdReportConclusionDTO> reportConclusionList;
    private List<RdAttachmentDTO> reportFileList;
//    private List<RdSubReportDTO> subReportList;
    private List<RdConditionGroupDTO> conditionGroupList;
    private List<RdTrfDTO> trfList;
    private RdOrderDTO order;
    private List<RdQuotationDTO> quotationList;
    private List<RdInvoiceDTO> invoiceList;
}
