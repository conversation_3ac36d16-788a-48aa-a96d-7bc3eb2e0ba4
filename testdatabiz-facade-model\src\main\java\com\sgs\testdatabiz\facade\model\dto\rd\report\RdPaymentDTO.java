/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPaymentDTO implements Serializable{

    private Integer paymentStatus;
    private String currency;
    private BigDecimal totalAmount;
    private BigDecimal mainCurrencyTotalAmount;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}