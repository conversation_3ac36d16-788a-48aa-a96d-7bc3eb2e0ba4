package com.sgs.testdatabiz.domain.service.utils.convertor;

import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sgs.testdatabiz.domain.service.constant.SystemConstants.DEFAULT_USER;

/**
 * <AUTHOR>
 */
@Component
public final class TestResultBuilder {

    @Autowired
    private IdService ID_GENERATOR;

    public Map buildTestDataInfoPO(List<RdTestResultDO> testResultList,
                                          List<TestDataMatrixInfoPO> testMatrixInfoList,
                                          List<RdTestLineDO> testLineList,
                                          Long labId,
                                          Map<String, String> objectRelMap,
                                          String orderNo,
                                          String reportNo,
                                          Long reportId
    ) {
        if (Func.isEmpty(testResultList)) {
            return null;
        }
        Map resultMap = new HashMap();
        List<RdAnalyteDO> analyteDOS = new ArrayList<>();
        for (RdTestLineDO testLineDO : testLineList) {
            List<RdAnalyteDO> analyteList = testLineDO.getAnalyteList();
            if (Func.isNotEmpty(analyteList)) {
                analyteDOS.addAll(analyteList);
            }
        }
        Map<String, List<RdAnalyteDO>> analyteMap = new HashMap<>(analyteDOS.stream().collect(Collectors.groupingBy(RdAnalyteDO::getAnalyteInstanceId)));
        Map<String, List<TestDataMatrixInfoPO>> testDataMap = new HashMap<>(testMatrixInfoList.stream().collect(Collectors.groupingBy(TestDataMatrixInfoPO::getTestMatrixId)));
        List<TestDataInfoPO> list = new ArrayList<>();
        List<RdReportTestResultLangPO> testResultLangPOList = new ArrayList<>();
        testResultList.forEach(
                l -> {
                    String testMatrixId = l.getTestMatrixId();
                    Integer testResultSeq = l.getTestResultSeq();
                    String subReportNo = l.getSubReportNo();

                    TestDataInfoPO testDataInfoPO = new TestDataInfoPO();
                    testDataInfoPO.setId(ID_GENERATOR.nextId());
                    testDataInfoPO.setObjectRelId(objectRelMap.get(subReportNo));
                    testDataInfoPO.setRdReportId(reportId);
                    List<TestDataMatrixInfoPO> testMatrixPOS = testDataMap.get(testMatrixId);
                    if (Func.isNotEmpty(testMatrixPOS)) {
                        testDataInfoPO.setTestDataMatrixId(testMatrixPOS.get(0).getId());
                    } else {
                        return;
                    }
                    testDataInfoPO.setTestMatrixId(testMatrixId);
//                    testDataInfoPO.setAnalyteId();
                    // TODO 待处理
//                    testDataInfoPO.setAnalyteCode();
                    testDataInfoPO.setActiveIndicator(ActiveType.Enable.getStatus());
                    testDataInfoPO.setCreatedBy(DEFAULT_USER);
                    testDataInfoPO.setCreatedDate(DateUtils.getNow());
                    testDataInfoPO.setModifiedBy(DEFAULT_USER);
                    testDataInfoPO.setModifiedDate(DateUtils.getNow());
                    testDataInfoPO.setLabId(labId);
                    testDataInfoPO.setOrderNo(orderNo);
                    testDataInfoPO.setReportNo(reportNo);
                    testDataInfoPO.setTestResultSeq(testResultSeq);

                    RdTestResultResultDO testResult = l.getTestResult();
                    if (Func.isNotEmpty(testResult)) {
                        String resultValue = testResult.getResultValue();
                        String resultUnit = testResult.getResultUnit();
                        Integer failFlag = testResult.getFailFlag();
                        String testResultFullName = testResult.getTestResultFullName();
                        String resultValueRemark = testResult.getResultValueRemark();

                        testDataInfoPO.setResultValue(resultValue);
                        testDataInfoPO.setResultUnit(resultUnit);
                        testDataInfoPO.setFailFlag(failFlag);
                        testDataInfoPO.setTestResultFullName(testResultFullName);
                        testDataInfoPO.setResultValueRemark(resultValueRemark);

                        RdTestResultNameDO testResultFullNameRel = testResult.getTestResultFullNameRel();
                        if (Func.isNotEmpty(testResultFullNameRel)) {
                            String analyteInstanceId = testResultFullNameRel.getAnalyteInstanceId();
                            List<RdAnalyteDO> doList = analyteMap.get(analyteInstanceId);
                            if (Func.isNotEmpty(doList)) {
                                testDataInfoPO.setCasNo(doList.get(0).getCasNo());
                            }
                        }
                    }

                    RdReportLimitDO reportLimit = l.getReportLimit();
                    if (Func.isNotEmpty(reportLimit)) {
                        String limitValueFullName = reportLimit.getLimitValueFullName();
                        String limitUnit = reportLimit.getLimitUnit();

                        testDataInfoPO.setLimitUnit(limitUnit);
                        testDataInfoPO.setLimitValueFullName(limitValueFullName);
                    }

                    List<RdTestResultLanguageDO> languageList = l.getLanguageList();
                    if (Func.isNotEmpty(languageList)) {
                        languageList.forEach(
                                lang -> {
                                    String testResultFullName = lang.getTestResultFullName();
                                    String limitValueFullName = lang.getLimitValueFullName();
                                    Integer languageId = lang.getLanguageId();

                                    RdReportTestResultLangPO testResultLangPO = new RdReportTestResultLangPO();
                                    testResultLangPO.setId(ID_GENERATOR.nextId());
                                    testResultLangPO.setRdReportTestResultId(testDataInfoPO.getId());
                                    testResultLangPO.setLanguageId(languageId);
                                    testResultLangPO.setTestResultFullName(testResultFullName);
                                    testResultLangPO.setResultValueRemark(testDataInfoPO.getResultValueRemark());
                                    testResultLangPO.setResultUnit(testDataInfoPO.getResultUnit());
                                    testResultLangPO.setLimitValueFullName(limitValueFullName);
                                    testResultLangPO.setLimitUnit(testDataInfoPO.getLimitUnit());
                                    testResultLangPO.setCreatedBy(DEFAULT_USER);
                                    testResultLangPO.setCreatedDate(DateUtils.getNow());
                                    testResultLangPO.setModifiedBy(DEFAULT_USER);
                                    testResultLangPO.setModifiedDate(DateUtils.getNow());
                                    testResultLangPOList.add(testResultLangPO);
                                }
                        );
                    }
                    testDataInfoPO.setBizVersionId(getTestDataReportMd5(testDataInfoPO));
                    list.add(testDataInfoPO);
                }
        );
        resultMap.put("testResult", list);
        resultMap.put("testResultLang", testResultLangPOList);
        return resultMap;
    }

    public String getTestDataReportMd5(TestDataInfoPO td) {
        td.setObjectRelId(StringUtil.isNullOrEmpty(td.getObjectRelId()));
        td.setTestMatrixId(StringUtil.isNullOrEmpty(td.getTestMatrixId()));
//        td.setAnalyteId(StringUtil.isNullOrEmpty(td.getAnalyteId()));
//        td.setAnalyteName(StringUtil.isNullOrEmpty(td.getAnalyteName()));
//        td.setPosition(StringUtil.isNullOrEmpty(td.getPosition()));
//        td.setAnalyteType(NumberUtil.toInt(td.getAnalyteType()));
        td.setAnalyteCode(StringUtil.isNullOrEmpty(td.getAnalyteCode()));
//        td.setAnalyteSeq(NumberUtil.toInt(td.getAnalyteSeq()));
//        td.setReportUnit(StringUtil.isNullOrEmpty(td.getReportUnit()));
//        td.setTestValue(StringUtil.isNullOrEmpty(td.getTestValue()));
        td.setCasNo(StringUtil.isNullOrEmpty(td.getCasNo()));
//        td.setReportLimit(StringUtil.isNullOrEmpty(td.getReportLimit()));
        td.setLimitUnit(StringUtil.isNullOrEmpty(td.getLimitUnit()));
//        td.setConclusionId(StringUtil.isNullOrEmpty(td.getConclusionId()));
        td.setTestDataMatrixId(td.getTestDataMatrixId());
        td.setLanguages(StringUtil.isNullOrEmpty(td.getLanguages()));
        td.setTestResultFullName(StringUtil.isNullOrEmpty(td.getTestResultFullName()));
        td.setResultUnit(StringUtil.isNullOrEmpty(td.getResultUnit()));
        td.setResultValue(StringUtil.isNullOrEmpty(td.getResultValue()));
        td.setLimitValueFullName(StringUtil.isNullOrEmpty(td.getLimitValueFullName()));

        StringBuilder append = new StringBuilder();
        append.append(td.getObjectRelId())
                .append(td.getTestMatrixId())
//                .append(td.getAnalyteId())
                .append(td.getTestResultFullName())
                //.append(td.getPosition())
//                .append(td.getAnalyteType())
                .append(td.getAnalyteCode())
                //.append(td.getAnalyteSeq())
                .append(td.getResultUnit())
                .append(td.getResultValue())
                .append(td.getCasNo())
                .append(td.getLimitValueFullName())
                .append(td.getLimitUnit())
//                .append(td.getConclusionId())
                .append(td.getTestDataMatrixId())
                .append(td.getLanguages());
        return DigestUtils.md5Hex(append.toString().toLowerCase());
    }
}
