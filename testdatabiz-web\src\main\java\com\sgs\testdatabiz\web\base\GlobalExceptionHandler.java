package com.sgs.testdatabiz.web.base;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 * @author: shawn.yang
 * @create: 2023-06-25 09:22
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public ResponseEntity<BaseResponse<Object>> handleException(Exception ex) {
        BaseResponse<Object> errResp = new BaseResponse<>();

        if (ex instanceof MaxUploadSizeExceededException) {
            log.error("上传文件过大,",ex);
            errResp.setStatus(ResponseCode.UNKNOWN.getCode());
            errResp.setMessage(ex.getCause().getCause().getMessage());
            return new ResponseEntity<>(errResp, HttpStatus.OK);
        }

        // 已知错误
        if (ex instanceof BizException){
            ResponseCode errorCode = ((BizException) ex).getErrorCode();
            errResp.setStatus(errorCode.getCode());
            errResp.setMessage(ex.getMessage());
            return new ResponseEntity<>(errResp, HttpStatus.OK);
        }

        //todo 未知错误,精细化处理
        errResp.setStatus(ResponseCode.UNKNOWN.getCode());
        errResp.setMessage(ex.getMessage());
        log.error(ex.getMessage());

        return new ResponseEntity<>(errResp, HttpStatus.OK);
    }

}
