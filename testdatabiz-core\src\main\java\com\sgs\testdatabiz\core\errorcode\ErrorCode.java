package com.sgs.testdatabiz.core.errorcode;

import com.sgs.testdatabiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorTypeEnum;
import lombok.Data;

@Data
public class ErrorCode {
    private ErrorBizModelEnum module;
    private ErrorFunctionTypeEnum function;
    private ErrorTypeEnum errorType;
    private ErrorCategoryEnum category;
    private String code;

    public ErrorCode(ErrorCategoryEnum errorCategory,ErrorBizModelEnum module, ErrorFunctionTypeEnum function, ErrorTypeEnum errorType) {
        this.module = module;
        this.function = function;
        this.errorType = errorType;
        this.category = errorCategory;
        this.code = ErrorCodeRegistry.generateCode(errorCategory,module, function, errorType);
    }
    public String getDescription() {
        return String.format("%s-%s-%s-%s",
                category.getDescription(),
                module.getDescription(),
                function.getDescription(),
                errorType.getDescription());
    }
}
