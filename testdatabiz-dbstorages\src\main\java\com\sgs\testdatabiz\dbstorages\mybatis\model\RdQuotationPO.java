package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.math.BigDecimal;
import java.util.Date;

public class RdQuotationPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 唯一标识
     */
    private Long id;

    /**
     * lab_id BIGINT(19)<br>
     * Trims系统实验室标识
     */
    private Long labId;

    /**
     * system_id BIGINT(19)<br>
     * 
     */
    private Long systemId;

    /**
     * order_no VARCHAR(50)<br>
     * 订单号
     */
    private String orderNo;

    /**
     * report_no VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * quotation_no VARCHAR(50)<br>
     * 报价单号
     */
    private String quotationNo;

    /**
     * currency_code VARCHAR(50)<br>
     * 币种编码
     */
    private String currencyCode;

    /**
     * payer_customer_name VARCHAR(300)<br>
     * 付款方公司名称
     */
    private String payerCustomerName;

    /**
     * payer_boss_number BIGINT(19)<br>
     * 付款方Boss编码
     */
    private Long payerBossNumber;

    /**
     * service_item_type VARCHAR(50)<br>
     * 服务类型
     */
    private String serviceItemType;

    /**
     * service_item_name VARCHAR(2000)<br>
     * 服务名称
     */
    private String serviceItemName;

    /**
     * service_item_seq INTEGER(10)<br>
     * 顺序
     */
    private Integer serviceItemSeq;

    /**
     * special_offer_flag INTEGER(10)<br>
     * 是否为特价
     */
    private Integer specialOfferFlag;

    /**
     * quantity INTEGER(10)<br>
     * 数量
     */
    private Integer quantity;

    /**
     * service_item_list_unit_price DECIMAL(10,2)<br>
     * Trims 单价
     */
    private BigDecimal serviceItemListUnitPrice;

    /**
     * service_item_sales_unit_price DECIMAL(10,2)<br>
     * 销售单价
     */
    private BigDecimal serviceItemSalesUnitPrice;

    /**
     * service_item_discount DECIMAL(18,5)<br>
     * 行折扣
     */
    private BigDecimal serviceItemDiscount;

    /**
     * service_item_exchange_rate_price DECIMAL(38,19)<br>
     * 汇率
     */
    private BigDecimal serviceItemExchangeRatePrice;

    /**
     * service_item_sur_charge_price DECIMAL(18,5)<br>
     * charge费用
     */
    private BigDecimal serviceItemSurChargePrice;

    /**
     * service_item_net_amount DECIMAL(18,5)<br>
     * 行上税前金额
     */
    private BigDecimal serviceItemNetAmount;

    /**
     * service_item_vat_amount DECIMAL(18,5)<br>
     * 行上税额
     */
    private BigDecimal serviceItemVatAmount;

    /**
     * service_item_total_amount DECIMAL(18,5)<br>
     * 行上总金额
     */
    private BigDecimal serviceItemTotalAmount;

    /**
     * sum_net_amount DECIMAL(10,2)<br>
     * 汇总金额
     */
    private BigDecimal sumNetAmount;

    /**
     * sum_vat_amount DECIMAL(10,2)<br>
     * 税费
     */
    private BigDecimal sumVatAmount;

    /**
     * total_amount DECIMAL(10,2)<br>
     * 汇总金额
     */
    private BigDecimal totalAmount;

    /**
     * adjustment_amount DECIMAL(10,2)<br>
     * 调整金额
     */
    private BigDecimal adjustmentAmount;

    /**
     * adjustment_discount DECIMAL(10,2)<br>
     * 调整折扣
     */
    private BigDecimal adjustmentDiscount;

    /**
     * final_amount DECIMAL(10,2)<br>
     * 最终金额
     */
    private BigDecimal finalAmount;

    /**
     * pp_no INTEGER(10)<br>
     * 关联PP标识
     */
    private Integer ppNo;

    /**
     * test_line_id BIGINT(19)<br>
     * 关联TL标识
     */
    private Long testLineId;

    /**
     * citation_type TINYINT(3)<br>
     * 测试标准类型
     */
    private Integer citationType;

    /**
     * citation_id INTEGER(10)<br>
     * 测试标准标识
     */
    private Integer citationId;

    /**
     * citation_name VARCHAR(255)<br>
     * 测试标准名称
     */
    private String citationName;

    /**
     * citation_full_name VARCHAR(255)<br>
     * 测试标准拼接名称
     */
    private String citationFullName;

    /**
     * quotation_version_id VARCHAR(100)<br>
     * 报价单版本
     */
    private String quotationVersionId;

    /**
     * quotation_status TINYINT(3)<br>
     * 报价单状态
     */
    private Integer quotationStatus;

    /**
     * active_indicator TINYINT(3)<br>
     * 是否有效
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 唯一标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 唯一标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 获得 Trims系统实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 设置 Trims系统实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * system_id BIGINT(19)<br>
     * 获得 
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * system_id BIGINT(19)<br>
     * 设置 
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * report_no VARCHAR(50)<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50)<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * quotation_no VARCHAR(50)<br>
     * 获得 报价单号
     */
    public String getQuotationNo() {
        return quotationNo;
    }

    /**
     * quotation_no VARCHAR(50)<br>
     * 设置 报价单号
     */
    public void setQuotationNo(String quotationNo) {
        this.quotationNo = quotationNo == null ? null : quotationNo.trim();
    }

    /**
     * currency_code VARCHAR(50)<br>
     * 获得 币种编码
     */
    public String getCurrencyCode() {
        return currencyCode;
    }

    /**
     * currency_code VARCHAR(50)<br>
     * 设置 币种编码
     */
    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode == null ? null : currencyCode.trim();
    }

    /**
     * payer_customer_name VARCHAR(300)<br>
     * 获得 付款方公司名称
     */
    public String getPayerCustomerName() {
        return payerCustomerName;
    }

    /**
     * payer_customer_name VARCHAR(300)<br>
     * 设置 付款方公司名称
     */
    public void setPayerCustomerName(String payerCustomerName) {
        this.payerCustomerName = payerCustomerName == null ? null : payerCustomerName.trim();
    }

    /**
     * payer_boss_number BIGINT(19)<br>
     * 获得 付款方Boss编码
     */
    public Long getPayerBossNumber() {
        return payerBossNumber;
    }

    /**
     * payer_boss_number BIGINT(19)<br>
     * 设置 付款方Boss编码
     */
    public void setPayerBossNumber(Long payerBossNumber) {
        this.payerBossNumber = payerBossNumber;
    }

    /**
     * service_item_type VARCHAR(50)<br>
     * 获得 服务类型
     */
    public String getServiceItemType() {
        return serviceItemType;
    }

    /**
     * service_item_type VARCHAR(50)<br>
     * 设置 服务类型
     */
    public void setServiceItemType(String serviceItemType) {
        this.serviceItemType = serviceItemType == null ? null : serviceItemType.trim();
    }

    /**
     * service_item_name VARCHAR(2000)<br>
     * 获得 服务名称
     */
    public String getServiceItemName() {
        return serviceItemName;
    }

    /**
     * service_item_name VARCHAR(2000)<br>
     * 设置 服务名称
     */
    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName == null ? null : serviceItemName.trim();
    }

    /**
     * service_item_seq INTEGER(10)<br>
     * 获得 顺序
     */
    public Integer getServiceItemSeq() {
        return serviceItemSeq;
    }

    /**
     * service_item_seq INTEGER(10)<br>
     * 设置 顺序
     */
    public void setServiceItemSeq(Integer serviceItemSeq) {
        this.serviceItemSeq = serviceItemSeq;
    }

    /**
     * special_offer_flag INTEGER(10)<br>
     * 获得 是否为特价
     */
    public Integer getSpecialOfferFlag() {
        return specialOfferFlag;
    }

    /**
     * special_offer_flag INTEGER(10)<br>
     * 设置 是否为特价
     */
    public void setSpecialOfferFlag(Integer specialOfferFlag) {
        this.specialOfferFlag = specialOfferFlag;
    }

    /**
     * quantity INTEGER(10)<br>
     * 获得 数量
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * quantity INTEGER(10)<br>
     * 设置 数量
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * service_item_list_unit_price DECIMAL(10,2)<br>
     * 获得 Trims 单价
     */
    public BigDecimal getServiceItemListUnitPrice() {
        return serviceItemListUnitPrice;
    }

    /**
     * service_item_list_unit_price DECIMAL(10,2)<br>
     * 设置 Trims 单价
     */
    public void setServiceItemListUnitPrice(BigDecimal serviceItemListUnitPrice) {
        this.serviceItemListUnitPrice = serviceItemListUnitPrice;
    }

    /**
     * service_item_sales_unit_price DECIMAL(10,2)<br>
     * 获得 销售单价
     */
    public BigDecimal getServiceItemSalesUnitPrice() {
        return serviceItemSalesUnitPrice;
    }

    /**
     * service_item_sales_unit_price DECIMAL(10,2)<br>
     * 设置 销售单价
     */
    public void setServiceItemSalesUnitPrice(BigDecimal serviceItemSalesUnitPrice) {
        this.serviceItemSalesUnitPrice = serviceItemSalesUnitPrice;
    }

    /**
     * service_item_discount DECIMAL(18,5)<br>
     * 获得 行折扣
     */
    public BigDecimal getServiceItemDiscount() {
        return serviceItemDiscount;
    }

    /**
     * service_item_discount DECIMAL(18,5)<br>
     * 设置 行折扣
     */
    public void setServiceItemDiscount(BigDecimal serviceItemDiscount) {
        this.serviceItemDiscount = serviceItemDiscount;
    }

    /**
     * service_item_exchange_rate_price DECIMAL(38,19)<br>
     * 获得 汇率
     */
    public BigDecimal getServiceItemExchangeRatePrice() {
        return serviceItemExchangeRatePrice;
    }

    /**
     * service_item_exchange_rate_price DECIMAL(38,19)<br>
     * 设置 汇率
     */
    public void setServiceItemExchangeRatePrice(BigDecimal serviceItemExchangeRatePrice) {
        this.serviceItemExchangeRatePrice = serviceItemExchangeRatePrice;
    }

    /**
     * service_item_sur_charge_price DECIMAL(18,5)<br>
     * 获得 charge费用
     */
    public BigDecimal getServiceItemSurChargePrice() {
        return serviceItemSurChargePrice;
    }

    /**
     * service_item_sur_charge_price DECIMAL(18,5)<br>
     * 设置 charge费用
     */
    public void setServiceItemSurChargePrice(BigDecimal serviceItemSurChargePrice) {
        this.serviceItemSurChargePrice = serviceItemSurChargePrice;
    }

    /**
     * service_item_net_amount DECIMAL(18,5)<br>
     * 获得 行上税前金额
     */
    public BigDecimal getServiceItemNetAmount() {
        return serviceItemNetAmount;
    }

    /**
     * service_item_net_amount DECIMAL(18,5)<br>
     * 设置 行上税前金额
     */
    public void setServiceItemNetAmount(BigDecimal serviceItemNetAmount) {
        this.serviceItemNetAmount = serviceItemNetAmount;
    }

    /**
     * service_item_vat_amount DECIMAL(18,5)<br>
     * 获得 行上税额
     */
    public BigDecimal getServiceItemVatAmount() {
        return serviceItemVatAmount;
    }

    /**
     * service_item_vat_amount DECIMAL(18,5)<br>
     * 设置 行上税额
     */
    public void setServiceItemVatAmount(BigDecimal serviceItemVatAmount) {
        this.serviceItemVatAmount = serviceItemVatAmount;
    }

    /**
     * service_item_total_amount DECIMAL(18,5)<br>
     * 获得 行上总金额
     */
    public BigDecimal getServiceItemTotalAmount() {
        return serviceItemTotalAmount;
    }

    /**
     * service_item_total_amount DECIMAL(18,5)<br>
     * 设置 行上总金额
     */
    public void setServiceItemTotalAmount(BigDecimal serviceItemTotalAmount) {
        this.serviceItemTotalAmount = serviceItemTotalAmount;
    }

    /**
     * sum_net_amount DECIMAL(10,2)<br>
     * 获得 汇总金额
     */
    public BigDecimal getSumNetAmount() {
        return sumNetAmount;
    }

    /**
     * sum_net_amount DECIMAL(10,2)<br>
     * 设置 汇总金额
     */
    public void setSumNetAmount(BigDecimal sumNetAmount) {
        this.sumNetAmount = sumNetAmount;
    }

    /**
     * sum_vat_amount DECIMAL(10,2)<br>
     * 获得 税费
     */
    public BigDecimal getSumVatAmount() {
        return sumVatAmount;
    }

    /**
     * sum_vat_amount DECIMAL(10,2)<br>
     * 设置 税费
     */
    public void setSumVatAmount(BigDecimal sumVatAmount) {
        this.sumVatAmount = sumVatAmount;
    }

    /**
     * total_amount DECIMAL(10,2)<br>
     * 获得 汇总金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * total_amount DECIMAL(10,2)<br>
     * 设置 汇总金额
     */
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * adjustment_amount DECIMAL(10,2)<br>
     * 获得 调整金额
     */
    public BigDecimal getAdjustmentAmount() {
        return adjustmentAmount;
    }

    /**
     * adjustment_amount DECIMAL(10,2)<br>
     * 设置 调整金额
     */
    public void setAdjustmentAmount(BigDecimal adjustmentAmount) {
        this.adjustmentAmount = adjustmentAmount;
    }

    /**
     * adjustment_discount DECIMAL(10,2)<br>
     * 获得 调整折扣
     */
    public BigDecimal getAdjustmentDiscount() {
        return adjustmentDiscount;
    }

    /**
     * adjustment_discount DECIMAL(10,2)<br>
     * 设置 调整折扣
     */
    public void setAdjustmentDiscount(BigDecimal adjustmentDiscount) {
        this.adjustmentDiscount = adjustmentDiscount;
    }

    /**
     * final_amount DECIMAL(10,2)<br>
     * 获得 最终金额
     */
    public BigDecimal getFinalAmount() {
        return finalAmount;
    }

    /**
     * final_amount DECIMAL(10,2)<br>
     * 设置 最终金额
     */
    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }

    /**
     * pp_no INTEGER(10)<br>
     * 获得 关联PP标识
     */
    public Integer getPpNo() {
        return ppNo;
    }

    /**
     * pp_no INTEGER(10)<br>
     * 设置 关联PP标识
     */
    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    /**
     * test_line_id BIGINT(19)<br>
     * 获得 关联TL标识
     */
    public Long getTestLineId() {
        return testLineId;
    }

    /**
     * test_line_id BIGINT(19)<br>
     * 设置 关联TL标识
     */
    public void setTestLineId(Long testLineId) {
        this.testLineId = testLineId;
    }

    /**
     * citation_type TINYINT(3)<br>
     * 获得 测试标准类型
     */
    public Integer getCitationType() {
        return citationType;
    }

    /**
     * citation_type TINYINT(3)<br>
     * 设置 测试标准类型
     */
    public void setCitationType(Integer citationType) {
        this.citationType = citationType;
    }

    /**
     * citation_id INTEGER(10)<br>
     * 获得 测试标准标识
     */
    public Integer getCitationId() {
        return citationId;
    }

    /**
     * citation_id INTEGER(10)<br>
     * 设置 测试标准标识
     */
    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    /**
     * citation_name VARCHAR(255)<br>
     * 获得 测试标准名称
     */
    public String getCitationName() {
        return citationName;
    }

    /**
     * citation_name VARCHAR(255)<br>
     * 设置 测试标准名称
     */
    public void setCitationName(String citationName) {
        this.citationName = citationName == null ? null : citationName.trim();
    }

    /**
     * citation_full_name VARCHAR(255)<br>
     * 获得 测试标准拼接名称
     */
    public String getCitationFullName() {
        return citationFullName;
    }

    /**
     * citation_full_name VARCHAR(255)<br>
     * 设置 测试标准拼接名称
     */
    public void setCitationFullName(String citationFullName) {
        this.citationFullName = citationFullName == null ? null : citationFullName.trim();
    }

    /**
     * quotation_version_id VARCHAR(100)<br>
     * 获得 报价单版本
     */
    public String getQuotationVersionId() {
        return quotationVersionId;
    }

    /**
     * quotation_version_id VARCHAR(100)<br>
     * 设置 报价单版本
     */
    public void setQuotationVersionId(String quotationVersionId) {
        this.quotationVersionId = quotationVersionId == null ? null : quotationVersionId.trim();
    }

    /**
     * quotation_status TINYINT(3)<br>
     * 获得 报价单状态
     */
    public Integer getQuotationStatus() {
        return quotationStatus;
    }

    /**
     * quotation_status TINYINT(3)<br>
     * 设置 报价单状态
     */
    public void setQuotationStatus(Integer quotationStatus) {
        this.quotationStatus = quotationStatus;
    }

    /**
     * active_indicator TINYINT(3)<br>
     * 获得 是否有效
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3)<br>
     * 设置 是否有效
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", labId=").append(labId);
        sb.append(", systemId=").append(systemId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", quotationNo=").append(quotationNo);
        sb.append(", currencyCode=").append(currencyCode);
        sb.append(", payerCustomerName=").append(payerCustomerName);
        sb.append(", payerBossNumber=").append(payerBossNumber);
        sb.append(", serviceItemType=").append(serviceItemType);
        sb.append(", serviceItemName=").append(serviceItemName);
        sb.append(", serviceItemSeq=").append(serviceItemSeq);
        sb.append(", specialOfferFlag=").append(specialOfferFlag);
        sb.append(", quantity=").append(quantity);
        sb.append(", serviceItemListUnitPrice=").append(serviceItemListUnitPrice);
        sb.append(", serviceItemSalesUnitPrice=").append(serviceItemSalesUnitPrice);
        sb.append(", serviceItemDiscount=").append(serviceItemDiscount);
        sb.append(", serviceItemExchangeRatePrice=").append(serviceItemExchangeRatePrice);
        sb.append(", serviceItemSurChargePrice=").append(serviceItemSurChargePrice);
        sb.append(", serviceItemNetAmount=").append(serviceItemNetAmount);
        sb.append(", serviceItemVatAmount=").append(serviceItemVatAmount);
        sb.append(", serviceItemTotalAmount=").append(serviceItemTotalAmount);
        sb.append(", sumNetAmount=").append(sumNetAmount);
        sb.append(", sumVatAmount=").append(sumVatAmount);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", adjustmentAmount=").append(adjustmentAmount);
        sb.append(", adjustmentDiscount=").append(adjustmentDiscount);
        sb.append(", finalAmount=").append(finalAmount);
        sb.append(", ppNo=").append(ppNo);
        sb.append(", testLineId=").append(testLineId);
        sb.append(", citationType=").append(citationType);
        sb.append(", citationId=").append(citationId);
        sb.append(", citationName=").append(citationName);
        sb.append(", citationFullName=").append(citationFullName);
        sb.append(", quotationVersionId=").append(quotationVersionId);
        sb.append(", quotationStatus=").append(quotationStatus);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}