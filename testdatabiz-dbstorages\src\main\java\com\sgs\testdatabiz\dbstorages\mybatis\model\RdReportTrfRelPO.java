package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdReportTrfRelPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 唯一标识
     */
    private Long id;

    /**
     * lab_id BIGINT(19) 必填<br>
     * Trims 实验室标识
     */
    private Long labId;

    /**
     * bu_id BIGINT(19)<br>
     * 产品线线标识
     */
    private Long buId;

    /**
     * trf_ref_system_id INTEGER(10) 默认值[0] 必填<br>
     * 测试申请单对接系统标识
     */
    private Integer trfRefSystemId;

    /**
     * trf_no VARCHAR(64) 默认值[] 必填<br>
     * 测试申请单编码
     */
    private String trfNo;

    /**
     * order_system_id INTEGER(10) 必填<br>
     * 订单执行系统标识
     */
    private Integer orderSystemId;

    /**
     * order_no VARCHAR(64) 必填<br>
     * 订单编码
     */
    private String orderNo;

    /**
     * report_no VARCHAR(64) 必填<br>
     * 报告编码
     */
    private String reportNo;

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 唯一标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 唯一标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * lab_id BIGINT(19) 必填<br>
     * 获得 Trims 实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19) 必填<br>
     * 设置 Trims 实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * bu_id BIGINT(19)<br>
     * 获得 产品线线标识
     */
    public Long getBuId() {
        return buId;
    }

    /**
     * bu_id BIGINT(19)<br>
     * 设置 产品线线标识
     */
    public void setBuId(Long buId) {
        this.buId = buId;
    }

    /**
     * trf_ref_system_id INTEGER(10) 默认值[0] 必填<br>
     * 获得 测试申请单对接系统标识
     */
    public Integer getTrfRefSystemId() {
        return trfRefSystemId;
    }

    /**
     * trf_ref_system_id INTEGER(10) 默认值[0] 必填<br>
     * 设置 测试申请单对接系统标识
     */
    public void setTrfRefSystemId(Integer trfRefSystemId) {
        this.trfRefSystemId = trfRefSystemId;
    }

    /**
     * trf_no VARCHAR(64) 默认值[] 必填<br>
     * 获得 测试申请单编码
     */
    public String getTrfNo() {
        return trfNo;
    }

    /**
     * trf_no VARCHAR(64) 默认值[] 必填<br>
     * 设置 测试申请单编码
     */
    public void setTrfNo(String trfNo) {
        this.trfNo = trfNo == null ? null : trfNo.trim();
    }

    /**
     * order_system_id INTEGER(10) 必填<br>
     * 获得 订单执行系统标识
     */
    public Integer getOrderSystemId() {
        return orderSystemId;
    }

    /**
     * order_system_id INTEGER(10) 必填<br>
     * 设置 订单执行系统标识
     */
    public void setOrderSystemId(Integer orderSystemId) {
        this.orderSystemId = orderSystemId;
    }

    /**
     * order_no VARCHAR(64) 必填<br>
     * 获得 订单编码
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(64) 必填<br>
     * 设置 订单编码
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * report_no VARCHAR(64) 必填<br>
     * 获得 报告编码
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(64) 必填<br>
     * 设置 报告编码
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", labId=").append(labId);
        sb.append(", buId=").append(buId);
        sb.append(", trfRefSystemId=").append(trfRefSystemId);
        sb.append(", trfNo=").append(trfNo);
        sb.append(", orderSystemId=").append(orderSystemId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}