package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.List;

public class RdReportLangExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RdReportLangExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRdReportIdIsNull() {
            addCriterion("rd_report_id is null");
            return (Criteria) this;
        }

        public Criteria andRdReportIdIsNotNull() {
            addCriterion("rd_report_id is not null");
            return (Criteria) this;
        }

        public Criteria andRdReportIdEqualTo(Long value) {
            addCriterion("rd_report_id =", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdNotEqualTo(Long value) {
            addCriterion("rd_report_id <>", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdGreaterThan(Long value) {
            addCriterion("rd_report_id >", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rd_report_id >=", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdLessThan(Long value) {
            addCriterion("rd_report_id <", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdLessThanOrEqualTo(Long value) {
            addCriterion("rd_report_id <=", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdIn(List<Long> values) {
            addCriterion("rd_report_id in", values, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdNotIn(List<Long> values) {
            addCriterion("rd_report_id not in", values, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdBetween(Long value1, Long value2) {
            addCriterion("rd_report_id between", value1, value2, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdNotBetween(Long value1, Long value2) {
            addCriterion("rd_report_id not between", value1, value2, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNull() {
            addCriterion("language_id is null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNotNull() {
            addCriterion("language_id is not null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdEqualTo(Integer value) {
            addCriterion("language_id =", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotEqualTo(Integer value) {
            addCriterion("language_id <>", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThan(Integer value) {
            addCriterion("language_id >", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("language_id >=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThan(Integer value) {
            addCriterion("language_id <", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThanOrEqualTo(Integer value) {
            addCriterion("language_id <=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIn(List<Integer> values) {
            addCriterion("language_id in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotIn(List<Integer> values) {
            addCriterion("language_id not in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdBetween(Integer value1, Integer value2) {
            addCriterion("language_id between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("language_id not between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andReportHeaderIsNull() {
            addCriterion("report_header is null");
            return (Criteria) this;
        }

        public Criteria andReportHeaderIsNotNull() {
            addCriterion("report_header is not null");
            return (Criteria) this;
        }

        public Criteria andReportHeaderEqualTo(String value) {
            addCriterion("report_header =", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderNotEqualTo(String value) {
            addCriterion("report_header <>", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderGreaterThan(String value) {
            addCriterion("report_header >", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderGreaterThanOrEqualTo(String value) {
            addCriterion("report_header >=", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderLessThan(String value) {
            addCriterion("report_header <", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderLessThanOrEqualTo(String value) {
            addCriterion("report_header <=", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderLike(String value) {
            addCriterion("report_header like", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderNotLike(String value) {
            addCriterion("report_header not like", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderIn(List<String> values) {
            addCriterion("report_header in", values, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderNotIn(List<String> values) {
            addCriterion("report_header not in", values, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderBetween(String value1, String value2) {
            addCriterion("report_header between", value1, value2, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderNotBetween(String value1, String value2) {
            addCriterion("report_header not between", value1, value2, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportAddressIsNull() {
            addCriterion("report_address is null");
            return (Criteria) this;
        }

        public Criteria andReportAddressIsNotNull() {
            addCriterion("report_address is not null");
            return (Criteria) this;
        }

        public Criteria andReportAddressEqualTo(String value) {
            addCriterion("report_address =", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressNotEqualTo(String value) {
            addCriterion("report_address <>", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressGreaterThan(String value) {
            addCriterion("report_address >", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressGreaterThanOrEqualTo(String value) {
            addCriterion("report_address >=", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressLessThan(String value) {
            addCriterion("report_address <", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressLessThanOrEqualTo(String value) {
            addCriterion("report_address <=", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressLike(String value) {
            addCriterion("report_address like", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressNotLike(String value) {
            addCriterion("report_address not like", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressIn(List<String> values) {
            addCriterion("report_address in", values, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressNotIn(List<String> values) {
            addCriterion("report_address not in", values, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressBetween(String value1, String value2) {
            addCriterion("report_address between", value1, value2, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressNotBetween(String value1, String value2) {
            addCriterion("report_address not between", value1, value2, "reportAddress");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}