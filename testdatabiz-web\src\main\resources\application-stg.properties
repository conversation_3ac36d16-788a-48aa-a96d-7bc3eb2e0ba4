jdbc.datasource.read=get,select,count,list,query,find,search,sum
jdbc.datasource.write=add,create,update,delete,remove,insert

##\u6570\u636E\u5E93\u914D\u7F6E
validationQuery=SELECT 'x'


# Test Data Master DB
datasource.dynamic.common.product-lines=SL,HL,MR,AUTO,IND-PL
datasource.dynamic.common.schema.testdata.master.driver-class-name=com.mysql.jdbc.Driver
datasource.dynamic.common.schema.testdata.master.url=************************************************************************************************************************************************************************************************
datasource.dynamic.common.schema.testdata.master.username=datadb_read_prod@sgsdbcne2mysqltodolisttestdaprod
datasource.dynamic.common.schema.testdata.master.password=aHJk819_AhjdA452_ISsa

# Test Data slave DB
datasource.dynamic.common.schema.testdata.slave.driver-class-name=com.mysql.jdbc.Driver
datasource.dynamic.common.schema.testdata.slave.url=************************************************************************************************************************************************************************************************
datasource.dynamic.common.schema.testdata.slave.username=datadb_read_prod@sgsdbcne2mysqltodolisttestdaprod
datasource.dynamic.common.schema.testdata.slave.password=aHJk819_AhjdA452_ISsa


# redis.cluster.nodes
spring.redis.nodes=10.168.128.235:7000,10.168.128.235:7001,10.168.128.235:7002,10.168.128.234:7003,10.168.128.234:7004,10.168.128.234:7005
# Redis???????????? == redis.cluster.password
spring.redis.password=
# ?????????????
spring.redis.timeout=10000
# ?????????????????????????16??????? 0 ?? 15
spring.redis.database=0
# ??????�????????????????
spring.redis.max-redirects=6

# ????????????????????????�??????????????????????
spring.redis.pool.max-idle=20
# ??????????????????????????????
spring.redis.pool.min-idle=5
# ??????????????????�?????�???????==redis.pool.maxTotal
spring.redis.pool.max-active=20
# ????????????????????�?????�???????
spring.redis.pool.max-wait=1000
# redis.pool.testOnBorrow
spring.redis.pool.testOnBorrow=true


# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
tomcat.protocol=org.apache.coyote.http11.Http11Nio2Protocol
# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0738\uFFFD\uFFFD\u00E3\uFFFD\uFFFD\uFFFD\u04AA\uFFFD\uFFFD\u05F0
#tomcat.protocol=org.apache.coyote.http11.Http11AprProtocol
tomcat.connectionTimeout=20000
tomcat.maxConnections=2000
tomcat.maxThreads=700
tomcat.uriEncoding=UTF-8
tomcat.acceptCount=2000
#webEnvironment=false
tomcat.port=8092
# \uFFFD\uFFFDdubbo\u042D\uFFFD\uFFFD\uFFFD\uFFFD20880\uFFFD\u02FF\u06B1\uFFFD\u00B6\uFFFD\uFFFD\uFFFD\uFFFD
dubbo.port=29539
zookeeper.address=*************:2181,*************:2181,*************:2181
# kafka
kafka.bootstrap-servers=*************:9092,*************:9092,*************:9092

user.management.url=http://cnapp.sgs.net/UserManagementApi
localiLayer.url=http://cnlocalilayer.sgs.net
notification.url=http://cnapp.sgs.net/NotificationApi
frameWorkApi.url=http://cnapp.sgs.net/FrameWorkApi

#----------- Start Starlims -----------
starLims.url=https://starlims-cn.sgs.net/starlims11.sgs.prod
starLims.authId=f388779e23904e12b988e7059f2b2806
starLims.header.user.key=STARLIMSUser
starLims.header.user.val=TRIMS
starLims.header.pass.key=STARLIMSPass
starLims.header.pass.val=Z^bD@@KSb_:3#]sK
#----------- End Starlims -----------
swagger.is.enable=false