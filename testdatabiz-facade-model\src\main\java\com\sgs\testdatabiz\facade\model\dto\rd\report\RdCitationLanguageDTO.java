/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCitationLanguageDTO implements Serializable {
    @ApiModelProperty(value = "languageId",dataType = "integer", required = true)
    private Integer languageId;
    private String citationSectionName;
    private String citationName;
    @ApiModelProperty(value = "citationFullName",dataType = "string", required = true)
    private String citationFullName;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "activeIndicator",dataType = "integer", required = true)
    private Integer activeIndicator;
}