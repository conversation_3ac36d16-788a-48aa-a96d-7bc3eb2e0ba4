package com.sgs.testdatabiz.facade.model.req.backsubcontract;

import com.sgs.framework.core.common.PrintFriendliness;

public class TestDataResultReq extends PrintFriendliness {
    private String testMatrixId;
    private String analyteId;
    private String analyteName;
    private String position;
    private Integer analyteType;
    private String analyteCode;
    private Integer analyteSeq;
    private String reportUnit;
    private String testValue;
    private String casNo;
    private String reportLimit;
    private String conclusionId;
    private String languages;

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public String getAnalyteId() {
        return analyteId;
    }

    public void setAnalyteId(String analyteId) {
        this.analyteId = analyteId;
    }

    public String getAnalyteName() {
        return analyteName;
    }

    public void setAnalyteName(String analyteName) {
        this.analyteName = analyteName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Integer getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    public String getAnalyteCode() {
        return analyteCode;
    }

    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getConclusionId() {
        return conclusionId;
    }

    public void setConclusionId(String conclusionId) {
        this.conclusionId = conclusionId;
    }

    public String getLanguages() {
        return languages;
    }

    public void setLanguages(String languages) {
        this.languages = languages;
    }
}
