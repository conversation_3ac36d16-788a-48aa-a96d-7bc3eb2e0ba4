package com.sgs.testdatabiz.facade.model.testdata.slim;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: Jinx
 * @Date: 2019-06-12 15:39
 * @Description:
 **/
@XmlRootElement(name = "JOBBIOFIELD")
@XmlAccessorType(XmlAccessType.FIELD)
public class SlimJobBioInfo {

    @XmlAttribute(name = "BIOFIELD")
    private String bioField;

    @XmlAttribute(name = "BIOVALUE")
    private String bioValue;

    @XmlAttribute(name = "ISACTIVE")
    private String isActive;

    public String getBioField() {
        return bioField;
    }

    public void setBioField(String bioField) {
        this.bioField = bioField;
    }

    public String getBioValue() {
        return bioValue;
    }

    public void setBioValue(String bioValue) {
        this.bioValue = bioValue;
    }

    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }
}
