/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdProductDO {

    private String productInstanceId;
    private String templateId;
    // SCI-1490
    private String reportNo;
    private List<RdProductSampleAttrDO> productAttrList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}