package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class TestDataObjectInfoPO {
    /**
     * Id VARCHAR(50) 必填<br>
     * 主键Id
     */
    private String id;

    /**
     * ProductLineCode VARCHAR(50) 必填<br>
     * 订单的BU
     */
    private String productLineCode;

    /**
     * LabCode VARCHAR(10) 必填<br>
     * 订单所在的Lab
     */
    private String labCode;

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 订单号
     */
    private String orderNo;

    /**
     * ReportNo VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * ObjectNo VARCHAR(50)<br>
     * 1、当Slim数据时，该值为SubContractNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为SubContractNo
     */
    private String objectNo;

    /**
     * ExternalId VARCHAR(50)<br>
     * 外部系统主键Id
     */
    private String externalId;

    /**
     * ExternalNo VARCHAR(50)<br>
     * 1、当Slim数据时，该值为SlimJobNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为folderNo
     */
    private String externalNo;

    /**
     * SourceType INTEGER(10) 必填<br>
     * 1、slim 2、job 3、starlims 4、fast 5、subcontract 6、new
     */
    private Integer sourceType;

    /**
     * ActiveIndicator INTEGER(10) 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * CreatedBy VARCHAR(50)<br>
     * 
     */
    private String createdBy;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 
     */
    private Date createdDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 
     */
    private String modifiedBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 
     */
    private Date modifiedDate;

    /**
     * BizVersionId VARCHAR(100) 必填<br>
     * 表中字段MD5 hash 计算得出
     */
    private String bizVersionId;

    /**
     * Id VARCHAR(50) 必填<br>
     * 获得 主键Id
     */
    public String getId() {
        return id;
    }

    /**
     * Id VARCHAR(50) 必填<br>
     * 设置 主键Id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * ProductLineCode VARCHAR(50) 必填<br>
     * 获得 订单的BU
     */
    public String getProductLineCode() {
        return productLineCode;
    }

    /**
     * ProductLineCode VARCHAR(50) 必填<br>
     * 设置 订单的BU
     */
    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode == null ? null : productLineCode.trim();
    }

    /**
     * LabCode VARCHAR(10) 必填<br>
     * 获得 订单所在的Lab
     */
    public String getLabCode() {
        return labCode;
    }

    /**
     * LabCode VARCHAR(10) 必填<br>
     * 设置 订单所在的Lab
     */
    public void setLabCode(String labCode) {
        this.labCode = labCode == null ? null : labCode.trim();
    }

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * ReportNo VARCHAR(50)<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * ReportNo VARCHAR(50)<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * ObjectNo VARCHAR(50)<br>
     * 获得 1、当Slim数据时，该值为SubContractNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为SubContractNo
     */
    public String getObjectNo() {
        return objectNo;
    }

    /**
     * ObjectNo VARCHAR(50)<br>
     * 设置 1、当Slim数据时，该值为SubContractNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为SubContractNo
     */
    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo == null ? null : objectNo.trim();
    }

    /**
     * ExternalId VARCHAR(50)<br>
     * 获得 外部系统主键Id
     */
    public String getExternalId() {
        return externalId;
    }

    /**
     * ExternalId VARCHAR(50)<br>
     * 设置 外部系统主键Id
     */
    public void setExternalId(String externalId) {
        this.externalId = externalId == null ? null : externalId.trim();
    }

    /**
     * ExternalNo VARCHAR(50)<br>
     * 获得 1、当Slim数据时，该值为SlimJobNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为folderNo
     */
    public String getExternalNo() {
        return externalNo;
    }

    /**
     * ExternalNo VARCHAR(50)<br>
     * 设置 1、当Slim数据时，该值为SlimJobNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为folderNo
     */
    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo == null ? null : externalNo.trim();
    }

    /**
     * SourceType INTEGER(10) 必填<br>
     * 获得 1、slim 2、job 3、starlims 4、fast 5、subcontract 6、new
     */
    public Integer getSourceType() {
        return sourceType;
    }

    /**
     * SourceType INTEGER(10) 必填<br>
     * 设置 1、slim 2、job 3、starlims 4、fast 5、subcontract 6、new
     */
    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    /**
     * ActiveIndicator INTEGER(10) 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * ActiveIndicator INTEGER(10) 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * BizVersionId VARCHAR(100) 必填<br>
     * 获得 表中字段MD5 hash 计算得出
     */
    public String getBizVersionId() {
        return bizVersionId;
    }

    /**
     * BizVersionId VARCHAR(100) 必填<br>
     * 设置 表中字段MD5 hash 计算得出
     */
    public void setBizVersionId(String bizVersionId) {
        this.bizVersionId = bizVersionId == null ? null : bizVersionId.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", productLineCode=").append(productLineCode);
        sb.append(", labCode=").append(labCode);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", objectNo=").append(objectNo);
        sb.append(", externalId=").append(externalId);
        sb.append(", externalNo=").append(externalNo);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", bizVersionId=").append(bizVersionId);
        sb.append("]");
        return sb.toString();
    }
}