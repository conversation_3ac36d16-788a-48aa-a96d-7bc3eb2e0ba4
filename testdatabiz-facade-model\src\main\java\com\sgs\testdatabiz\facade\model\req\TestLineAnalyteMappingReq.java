package com.sgs.testdatabiz.facade.model.req;

import com.sgs.framework.core.base.BaseRequest;

import java.util.Set;

public final class TestLineAnalyteMappingReq extends BaseRequest {
    /**
     *
     */
    private Set<Integer> testLineMappingIds;

    /**
     *
     */
    private Set<String> analyteCodes;

    /**
     *
     */
    private String regionAccount;

    /**
     * 0无效，1有效
     */
    private Integer status;

    public Set<Integer> getTestLineMappingIds() {
        return testLineMappingIds;
    }

    public void setTestLineMappingIds(Set<Integer> testLineMappingIds) {
        this.testLineMappingIds = testLineMappingIds;
    }

    public Set<String> getAnalyteCodes() {
        return analyteCodes;
    }

    public void setAnalyteCodes(Set<String> analyteCodes) {
        this.analyteCodes = analyteCodes;
    }

    public String getRegionAccount() {
        return regionAccount;
    }

    public void setRegionAccount(String regionAccount) {
        this.regionAccount = regionAccount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
