package com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.filtermatrix;

import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.BaseMatrixInfoFilter;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.MatrixInfo;
import com.sgs.testdatabiz.facade.model.dto.starlims.MatrixFilterDTO;
import com.sgs.testdatabiz.facade.model.dto.starlims.SubContractMatrixDTO;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveReportData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@MatrixInfo(value = "StarLimsMatrix", sort = 1)
public class StarlimsMatrixFilter extends BaseMatrixInfoFilter {

    @Override
    public SubContractMatrixDTO filterStarlimsMatrix(MatrixFilterDTO matrixFilterDTO) {
        ReceiveReportData reportData = matrixFilterDTO.getReportData();
        List<SubContractTestMatrixInfo> subContractTestMatrixList = matrixFilterDTO.getSubContractTestMatrixList();

        SubContractTestMatrixInfo subContractTestMatrixInfo = null;
        if (StringUtils.isNotBlank(reportData.getExternalMatrixId())) {
            SubContractMatrixDTO testMatrixInfo = new SubContractMatrixDTO();
            // 匹配订单中的MatrixId
            subContractTestMatrixInfo = subContractTestMatrixList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getMatrixId(), reportData.getExternalMatrixId()))
                    .findFirst().orElse(null);
            if (subContractTestMatrixInfo != null) {
                buildTestMatrixIndo(testMatrixInfo, subContractTestMatrixInfo);
                return testMatrixInfo;
            }
        }

        return null;
    }

}
