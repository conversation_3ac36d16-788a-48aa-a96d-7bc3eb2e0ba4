package com.sgs.testdatabiz.domain.service.cache;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SlimConclusionMappingCacheService {
    private static final Logger logger = LoggerFactory.getLogger(SlimConclusionMappingCacheService.class);


}
