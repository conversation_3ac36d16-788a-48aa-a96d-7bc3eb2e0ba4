package com.sgs.testdatabiz.facade.model.req.config;

import com.sgs.framework.core.base.BaseRequest;

import java.util.List;

public final class CheckTestLineMappingReq extends BaseRequest {

    // SCI-134
    private Integer refSystemId;

    /**
     *
     */
    private String customerGroupCode;

    /**
     *
     */
    private List<TestLineInfoReq> testLines;

    public Integer getRefSystemId() {
        return refSystemId;
    }

    public void setRefSystemId(Integer refSystemId) {
        this.refSystemId = refSystemId;
    }

    public String getCustomerGroupCode() {
        return customerGroupCode;
    }

    public void setCustomerGroupCode(String customerGroupCode) {
        this.customerGroupCode = customerGroupCode;
    }

    public List<TestLineInfoReq> getTestLines() {
        return testLines;
    }

    public void setTestLines(List<TestLineInfoReq> testLines) {
        this.testLines = testLines;
    }
}
