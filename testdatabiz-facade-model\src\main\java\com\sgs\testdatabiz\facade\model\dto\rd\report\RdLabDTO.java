/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdLabDTO implements Serializable{
    private Integer labId;
    @ApiModelProperty(value = "labCode",dataType = "string", required = true)
    private String labCode;
    private String labName;
    private Integer locationId;
    private String locationCode;
    private Integer buId;
    @ApiModelProperty(value = "buCode",dataType = "string", required = true)
    private String buCode;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "activeIndicator",dataType = "integer", required = true)
    private Integer activeIndicator;

    private String labAddress;
    private String labTelephoneNumber;
    private String labEmail;
    private String labCountry;
    private String labCity;
}