/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RdDeliveryDTO implements Serializable{

    private String required;
    private String deliveryTo;
    private String failedReportDeliverdTo;
    private String deliveryCc;
    private String deliveryWay;

    private String deliveryOthers;


    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
