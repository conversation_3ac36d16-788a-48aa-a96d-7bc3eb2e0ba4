package com.sgs.testdatabiz.sdk.util.mapper;

import com.alibaba.fastjson.JSON;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import com.sgs.testdatabiz.sdk.input.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.io.File;
import java.io.FileInputStream;

@Mapper
public interface ReportDataMapper {

    ReportDataMapper INSTANCE = Mappers.getMapper( ReportDataMapper.class );

//======out to  rdc=====
    @Mapping(target ="header", source = "header")
    @Mapping(target ="order", source = "order")
    ReportDataInput convert2ReportDataInput(ReportDataDTO reportDataDO);

    @Mapping(target ="orderList", source = "orderList")
    RdTrfInput convert2RdTrfInput(RdTrfDTO rdTrfDO);

    @Mapping(target = "orderNo",
            expression = "java(rdOrderRelDO.getRealOrderNo() != null&& rdOrderRelDO.getRealOrderNo().length() > 0 ? rdOrderRelDO.getRealOrderNo() : rdOrderRelDO.getOrderNo())")
    RdOrderRelInput convert2RdOrderRelDTO(RdOrderRelDTO rdOrderRelDO);


    @Mapping(source = "orderNo", target = "rootOrderNo")
    @Mapping(target = "orderNo",
            expression = "java(rdOrderDO.getRealOrderNo() != null && rdOrderDO.getRealOrderNo().length() > 0? rdOrderDO.getRealOrderNo() : rdOrderDO.getOrderNo())")
    RdOrderDTO convert2RdOrderDTO(RdOrderDTO rdOrderDO);

    @Mapping(source = "orderNo", target = "rootOrderNo")
    @Mapping(target = "orderNo",
            expression = "java(rdReportDO.getRealOrderNo() != null&& rdReportDO.getRealOrderNo().length() > 0 ? rdReportDO.getRealOrderNo() : rdReportDO.getOrderNo())")
    @Named("convert2RdReportInput")
    RdReportInput convert2RdReportInput(RdReportDTO rdReportDO);

    @Mapping(target = "orderNo",
            expression = "java(rdTestSampleDO.getRealOrderNo() != null&& rdTestSampleDO.getRealOrderNo().length() > 0 ? rdTestSampleDO.getRealOrderNo() : rdTestSampleDO.getOrderNo())")
    RdTestSampleInput convert2RdTestSampleInput(RdTestSampleDTO rdTestSampleDO);

    @Mapping(target = "orderNo",
            expression = "java(rdTestLineDO.getRealOrderNo() != null&& rdTestLineDO.getRealOrderNo().length() > 0&& rdTestLineDO.getRealOrderNo().length() > 0 ? rdTestLineDO.getRealOrderNo() : rdTestLineDO.getOrderNo())")
    RdTestLineInput convert2RdTestLineInput(RdTestLineDTO rdTestLineDO);

    @Mapping(target = "orderNo",
            expression = "java(rdTestResultDO.getRealOrderNo() != null&& rdTestResultDO.getRealOrderNo().length() > 0 ? rdTestResultDO.getRealOrderNo() : rdTestResultDO.getOrderNo())")
    RdTestResultInput convert2RdTestResultInput(RdTestResultDTO rdTestResultDO);

    @Mapping(target = "orderNo",
            expression = "java(rdReportConclusionDO.getRealOrderNo() != null&& rdReportConclusionDO.getRealOrderNo().length() > 0 ? rdReportConclusionDO.getRealOrderNo() : rdReportConclusionDO.getOrderNo())")
    RdReportConclusionInput convert2RdReportConclusionInput(RdReportConclusionDTO rdReportConclusionDO);

    @Mapping(target = "orderNo",
            expression = "java(rdConditionGroupDO.getRealOrderNo() != null&& rdConditionGroupDO.getRealOrderNo().length() > 0 ? rdConditionGroupDO.getRealOrderNo() : rdConditionGroupDO.getOrderNo())")
    RdConditionGroupInput convert2RdConditionGroupInput(RdConditionGroupDTO rdConditionGroupDO);

    @Mapping(target = "orderNo",
            expression = "java(rdQuotationDO.getRealOrderNo() != null&& rdQuotationDO.getRealOrderNo().length() > 0 ? rdQuotationDO.getRealOrderNo() : rdQuotationDO.getOrderNo())")
    RdQuotationInput convert2RdQuotationInput(RdQuotationDTO rdQuotationDO);

    @Mapping(target = "orderNo",
            expression = "java(rdInvoiceDO.getRealOrderNo() != null&& rdInvoiceDO.getRealOrderNo().length() > 0 ? rdInvoiceDO.getRealOrderNo() : rdInvoiceDO.getOrderNo())")
    RdInvoiceInput convert2RdInvoiceInput(RdInvoiceDTO rdInvoiceDO);


    public  static String readFile(File file, String encoding) {
        try {
            byte[] buffer = new byte[(int) file.length()];
            FileInputStream fis = new FileInputStream(file);
            fis.read(buffer);
            fis.close();
            return new String(buffer, encoding);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        /**
         * 从文件读取json信息并解析成对象对象
         */
        File  file = new File("/Users/<USER>/Documents/test/test4.md");
        //从文件读取内容
        String json = readFile(file, "UTF-8");

        //System.out.println("json = " + json);
        ReportDataBatchDTO reportDataBatchDTO = JSON.parseObject(json, ReportDataBatchDTO.class);

        //System.out.println("convert="+convert);
    }

}
