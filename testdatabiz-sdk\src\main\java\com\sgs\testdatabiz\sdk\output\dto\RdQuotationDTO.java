package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdQuotationDTO extends BaseModel{


    private Long labId;
    private Long systemId;
    private String orderNo;
    private String rootOrderNo;
    private String reportNo;
    private String quotationNo;
    private String currencyCode;
    private String payerCustomerName;
    private Long payerBossNumber;
    private String serviceItemType;
    private String serviceItemName;
    private Integer serviceItemSeq;
    private Integer specialOfferFlag;
    private Integer quantity;
    private BigDecimal serviceItemListUnitPrice;
    private BigDecimal serviceItemSalesUnitPrice;
    private BigDecimal serviceItemDiscount;
    private BigDecimal serviceItemExchangeRatePrice;
    private BigDecimal serviceItemSurChargePrice;
    private BigDecimal serviceItemNetAmount;
    private BigDecimal serviceItemVatAmount;
    private BigDecimal serviceItemTotalAmount;
    private BigDecimal sumNetAmount;
    private BigDecimal sumVatAmount;
    private String totalAmount;
    private BigDecimal adjustmentAmount;
    private BigDecimal adjustmentDiscount;
    private BigDecimal finalAmount;
    private Integer ppNo;
    private String ppName;
    private Integer testLineId;
    private String citationType;
    private Integer citationId;
    private String citationName;
    private String citationFullName;
    private String quotationVersionId;
    private Integer quotationStatus;
    private Integer activeIndicator;
    private Long rdCustomerId;
    private String serviceItemInstanceId;
    private String serviceItemTypeLabel;
    private String citationTypeLabel;
    private String citationIdLabel;
    private String quotationStatusLabel;

    private String bossOrderNo;

    private String refBossOrderNo;

    private List<RdQuotationLangDTO> quotationLangList;
    private String quotationInstanceId;

    private Date lastModifiedTimestamp;
}
