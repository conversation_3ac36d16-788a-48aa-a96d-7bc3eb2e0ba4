package com.sgs.testdatabiz.domain.service.rd.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.*;
import com.sgs.testdatabiz.domain.service.rd.assembler.ReportDataAssembler;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-04-20 20:32
 */
@Slf4j
@Component
public class ReportDataManager {

    private final RdReportMapper reportMapper;

    @Autowired
    private ReportTrfRelManager reportTrfRelManager;

    @Autowired
    private RdReportExtMapper reportExtMapper;

    @Autowired
    private ReportInvoiceManager invoiceManager;

    @Autowired
    private QuotationManager quotationManager;

    @Autowired
    private ReportDataAssembler reportDataAssembler;


    public RdReportPO getReport(Long labId, String reportNo) {
        RdReportExample reportExample = new RdReportExample();
        reportExample.createCriteria()
                .andLabIdEqualTo(labId)
                .andReportNoEqualTo(reportNo)
                .andActiveIndicatorEqualTo(ActiveType.Enable.getStatus())
                .andReportStatusNotEqualTo(ReportStatus.Cancelled.getCode());
        List<RdReportPO> list = reportMapper.selectByExample(reportExample);
        return list.isEmpty() ? null : list.get(0);
    }

    public RdReportPO getReportByReportNo(String reportNo) {
        RdReportExample reportExample = new RdReportExample();
        reportExample.createCriteria()
                .andReportNoEqualTo(reportNo);
        List<RdReportPO> list = reportMapper.selectByExample(reportExample);
        return list.isEmpty() ? null : list.get(0);
    }

    public RdReportPO getReportNotByReportStatus(Long labId, String reportNo) {
        RdReportExample reportExample = new RdReportExample();
        reportExample.createCriteria()
                .andLabIdEqualTo(labId)
                .andReportNoEqualTo(reportNo)
                .andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        List<RdReportPO> list = reportMapper.selectByExample(reportExample);
        return list.isEmpty() ? null : list.get(0);
    }

    public List<RdReportPO> getReportList(Long labId, List<String> reportNo) {
        RdReportExample reportExample = new RdReportExample();
        reportExample.createCriteria().andLabIdEqualTo(labId).andReportNoIn(reportNo);
        return reportMapper.selectByExample(reportExample);
    }

    public List<RdReportPO> getEnableReportList(Long labId, List<String> reportNo) {
        RdReportExample reportExample = new RdReportExample();
        reportExample.createCriteria().andLabIdEqualTo(labId).andReportNoIn(reportNo).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportMapper.selectByExample(reportExample);
    }

    public List<RdReportPO> getByTrfNoAndLabId(Long labId, String trfNo) {
        List<String> reportList = new ArrayList<>();
        List<RdReportTrfRelPO> rdReportTrfRelPOS = reportTrfRelManager.getByTrfNoAndLabId(labId, trfNo);
        if (Func.isNotEmpty(rdReportTrfRelPOS)) {
            rdReportTrfRelPOS.forEach(
                    l -> {
                        String reportNo = l.getReportNo();
                        reportList.add(reportNo);
                    }
            );
        }
        if (Func.isNotEmpty(reportList)) {
            return this.getReportList(labId, reportList);
        }
        return null;
    }

    public List<RdReportPO> getByReportNoAndLabId(Long labId, String reportNo) {
        RdReportExample reportExample = new RdReportExample();
        reportExample.createCriteria().andLabIdEqualTo(labId).andReportNoEqualTo(reportNo).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus()).andReportStatusNotEqualTo(ReportStatus.Cancelled.getCode());
        return reportMapper.selectByExample(reportExample);
    }

    public List<RdReportPO> getByReportNoAndLabIdAllStatus(Long labId, String reportNo) {
        RdReportExample reportExample = new RdReportExample();
        reportExample.createCriteria().andLabIdEqualTo(labId).andReportNoEqualTo(reportNo).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportMapper.selectByExample(reportExample);
    }

    public boolean save(RdReportPO reportPO) {
        if (reportPO == null) {
            return false;
        }
        return reportMapper.insertSelective(reportPO) == 1;
    }


    public boolean updateById(RdReportPO reportPO) {
        if (reportPO == null) {
            return false;
        }
        return reportMapper.updateByPrimaryKeySelective(reportPO) == 1;
    }

    public ReportDataDO selectByReportId(Long reportId) {
        List<RdReportExtPO> rdReportExtPOS = getReportExtByReportId(reportId);
        if (Func.isNotEmpty(rdReportExtPOS)) {
            RdReportExtPO rdReportExtPO = rdReportExtPOS.stream().max(Comparator.comparingInt(RdReportExtPO::getUpdateVersion)).get();
            if (Func.isEmpty(rdReportExtPO)) {
                return new ReportDataDO();
            }
            ReportDataDO dataDO = JSONObject.parseObject(rdReportExtPO.getRequestJson(), ReportDataDO.class);
            if (Func.isNotEmpty(dataDO)) {
                dataDO.getHeader().setId(reportId);
            }
            if (Func.isNotEmpty(dataDO.getOrder())) {
                List<RdProductDO> productList = dataDO.getOrder().getProductList();
                List<RdSampleDO> sampleList = dataDO.getOrder().getSampleList();
                dataDO.getHeader().setProductList(productList);
                dataDO.getHeader().setSampleList(sampleList);
            }
//            if (Func.isNotEmpty(dataDO)) {
//                List<RdInvoiceDO> invoiceList = dataDO.getInvoiceList();
//                if (Func.isEmpty(invoiceList)) {
//                    List<RdInvoiceDO> invoiceDOS = invoiceManager.exportInvoiceList(reportId);
//                    if (Func.isNotEmpty(invoiceDOS)) {
//                        dataDO.setInvoiceList(invoiceDOS);
//                        List<String> quotationNos = new ArrayList<>();
//                        for (RdInvoiceDO invoiceDO : invoiceDOS) {
//                            quotationNos.addAll(invoiceDO.getQuotationNos());
//                        }
//                        List<RdQuotationDO> quotationList = dataDO.getQuotationList();
//                        if (Func.isEmpty(quotationList)) {
//                            List<RdQuotationPO> quotationPOS = quotationManager.selectByReportNoAndQuotationNos(dataDO.getReportList().get(0).getReportNo(), quotationNos);
//                            if (Func.isNotEmpty(quotationPOS)) {
//                                List<RdQuotationDO> rdQuotationDOS = reportDataAssembler.assemblerQuotationDO(quotationPOS);
//                                dataDO.setQuotationList(rdQuotationDOS);
//                            }
//                        }
//                    }
//                }
//
//            }

            return dataDO;
        }
        return new ReportDataDO();
    }

    public List<RdReportExtPO> getReportExtByReportId(Long reportId) {
        RdReportExtExample rdReportExtExample = new RdReportExtExample();
        rdReportExtExample.createCriteria().andRdReportIdEqualTo(reportId);
        List<RdReportExtPO> rdReportExtPOS = reportExtMapper.selectByExample(rdReportExtExample);
        return rdReportExtPOS;
    }

    public List<ReportDataDO> selectByVersion(Long reportId) {
        List<ReportDataDO> list = new ArrayList<>();
        List<RdReportExtPO> rdReportExtPOS = getReportExtByReportId(reportId);
        if (Func.isNotEmpty(rdReportExtPOS)) {
            RdReportExtPO rdReportExtPO = rdReportExtPOS.stream().max(Comparator.comparingInt(RdReportExtPO::getUpdateVersion)).get();
            if (Func.isEmpty(rdReportExtPO)) {
                return list;
            }
            ReportDataDO dataDO = JSONObject.parseObject(rdReportExtPO.getRequestJson(), ReportDataDO.class);
            list.add(dataDO);
            if (rdReportExtPOS.size() > 1) {
                RdReportExtPO po = rdReportExtPOS.get(1);
                list.add(JSONObject.parseObject(po.getRequestJson(), ReportDataDO.class));
            }
        }
        return list;
    }

    public boolean updateQuotationListByReportId(Long id, List<RdQuotationDO> quotationList) {
        List<RdReportExtPO> rdReportExtPOS = getReportExtByReportId(id);
        if (Func.isNotEmpty(rdReportExtPOS)) {

            RdReportExtPO rdReportExtPO = rdReportExtPOS.stream().max(Comparator.comparingInt(RdReportExtPO::getUpdateVersion)).orElseGet(null);
            if (Func.isEmpty(rdReportExtPO)) {
                return false;
            }

            ReportDataDO dataDO = JSONObject.parseObject(rdReportExtPO.getRequestJson(), ReportDataDO.class);
            dataDO.setQuotationList(quotationList);
            rdReportExtPO.setRequestJson(JSONObject.toJSONString(dataDO));
            return reportExtMapper.updateByPrimaryKeyWithBLOBs(rdReportExtPO) > 0;
        }
        return false;
    }

    public boolean updateInvoiceListByReportId(Long id, List<RdInvoiceDO> invoiceList, RdOrderDTO orderDTO) {
        List<RdReportExtPO> rdReportExtPOS = getReportExtByReportId(id);
        if (Func.isNotEmpty(rdReportExtPOS)) {
            RdReportExtPO rdReportExtPO = rdReportExtPOS.stream().max(Comparator.comparingInt(RdReportExtPO::getUpdateVersion)).orElseGet(null);
            if (Func.isEmpty(rdReportExtPO)) {
                return false;
            }
            ReportDataDO dataDO = JSONObject.parseObject(rdReportExtPO.getRequestJson(), ReportDataDO.class);
            dataDO.setInvoiceList(invoiceList);
            RdOrderDO rdOrderDO = JSONObject.parseObject(JSONObject.toJSONString(orderDTO), RdOrderDO.class);
            rdOrderDO.setProductList(dataDO.getOrder().getProductList());
            rdOrderDO.setSampleList(dataDO.getOrder().getSampleList());
            dataDO.setOrder(rdOrderDO);

            rdReportExtPO.setRequestJson(JSONObject.toJSONString(dataDO));
            return reportExtMapper.updateByPrimaryKeyWithBLOBs(rdReportExtPO) > 0;
        }
        return false;
    }

    public boolean updateExtReportStatus(Long id, Integer reportStatus) {
        List<RdReportExtPO> rdReportExtPOS = getReportExtByReportId(id);
        if (Func.isNotEmpty(rdReportExtPOS)) {
            RdReportExtPO rdReportExtPO = rdReportExtPOS.stream().max(Comparator.comparingInt(RdReportExtPO::getUpdateVersion)).get();
            ReportDataDO dataDO = JSONObject.parseObject(rdReportExtPO.getRequestJson(), ReportDataDO.class);
            dataDO.getHeader().setReportStatus(reportStatus);
            rdReportExtPO.setRequestJson(JSONObject.toJSONString(dataDO));
            return reportExtMapper.updateByPrimaryKeyWithBLOBs(rdReportExtPO) > 0;
        }
        return false;

    }

    public ReportDataManager(RdReportMapper reportMapper) {
        this.reportMapper = reportMapper;
    }

    public List<ReportDataDO> getReportExtByOrderNoAndLabId(String labId, String orderNo) {
        List<ReportDataDO> list = new ArrayList<>();
        RdReportTrfRelExample example = new RdReportTrfRelExample();
        example.createCriteria().andLabIdEqualTo(Long.parseLong(labId)).andOrderNoEqualTo(orderNo);
        List<RdReportTrfRelPO> byOrderNoAndLabId = reportTrfRelManager.getByOrderNoAndLabId(Long.parseLong(labId), orderNo);
        if (Func.isEmpty(byOrderNoAndLabId)) {
            return list;
        }
        List<String> reportNos = byOrderNoAndLabId.stream().map(RdReportTrfRelPO::getReportNo).distinct().collect(Collectors.toList());
        reportNos.forEach(
                reportNo -> {
                    List<RdReportPO> rdReportPOS = getByReportNoAndLabId(Long.valueOf(labId), reportNo);
                    if (Func.isNotEmpty(rdReportPOS)) {
                        RdReportPO rdReportPO = rdReportPOS.get(0);
                        Long reportPOId = rdReportPO.getId();
                        ReportDataDO dataDO = selectByReportId(reportPOId);
                        if (Func.isNotEmpty(dataDO)) {
                            list.add(dataDO);
                        }
                    }
                }
        );
        return list;
    }
}
