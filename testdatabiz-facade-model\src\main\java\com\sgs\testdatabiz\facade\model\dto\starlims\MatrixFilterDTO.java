package com.sgs.testdatabiz.facade.model.dto.starlims;

import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveReportData;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class MatrixFilterDTO extends PrintFriendliness {
    private List<SubContractTestMatrixInfo> subContractTestMatrixList;
    private ChemPpArtifactTestLineInfoRsp chemTestLine;
    private ReceiveReportData reportData;
    private String orderNo;
}
