/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.framework.model.report.report.v2.ReportCertificateBO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportCertificateDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdHeaderDO {

    private Integer systemId;
    private String reportId;
    private String reportNo;
    private String originalReportNo;
    private Integer reportStatus;
    private Date reportDueDate;
    private String approveBy;
    private Date approveDate;
    private Date softCopyDeliveryDate;
    private String createBy;
    private Date createDate;
    private String certificateName;
    private RdLabDO lab;
    private RdConclusionDO conclusion;
    private List<ReportCertificateDO> reportCertificateList;
    private String reportRemark;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String reportInstanceId;

}
