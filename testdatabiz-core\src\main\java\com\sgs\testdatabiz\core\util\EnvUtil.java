package com.sgs.testdatabiz.core.util;

import com.sgs.otsnotes.facade.model.enums.EnvironmentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class EnvUtil {
    @Autowired
    private Environment env;

    /**
     *
     * @return
     */
    public EnvironmentType getActiveProfile(){
        String[] activeProfiles = env.getActiveProfiles();
        if (activeProfiles == null || activeProfiles.length <= 0){
            activeProfiles = env.getDefaultProfiles();
        }
        if (activeProfiles == null || activeProfiles.length <= 0){
            return EnvironmentType.Local;
        }
        return EnvironmentType.findCode(activeProfiles[0]);
    }

    /**
     *
     * @param envTypes
     * @return
     */
    public boolean isEnvType(EnvironmentType... envTypes){
        return EnvironmentType.check(this.getActiveProfile(), envTypes);
    }

    /**
     *
     * @return
     */
    public boolean isTrimsLocalWrite(){
        EnvironmentType envType = this.getActiveProfile();
        if (envType == null){
            return true;
        }
        return envType.isWrite();
    }
}
