package com.sgs.testdatabiz.domain.service.utils.convertor;

import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdOrderDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdReportDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdSubReportDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataBatchDO;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.sgs.testdatabiz.domain.service.constant.SystemConstants.DEFAULT_USER;

/**
 * <AUTHOR>
 */
@Component
public final class TestDataObjectRelBuilder {


    @Autowired
    private IdService ID_GENERATOR;

    public List<TestDataObjectRelPO> buildTestDataObjectRelPO(ReportDataBatchDO reportDataDO, Map<String, RdOrderDO> orderMap) {

        List<RdReportDO> reportList = reportDataDO.getReportList();
        for (RdReportDO report : reportList) {
//            RdRelationshipDO relationship = reportDataDO.getRelationship();
            List<RdSubReportDO> subReportList = report.getSubReportList();

            if (Func.isNotEmpty(subReportList)) {
                List<TestDataObjectRelPO> list = new ArrayList<>();
//                RdParentDO parent = relationship.getParent();

                String orderNo = report.getOrderNo();
                Integer systemId = report.getSystemId();

                RdOrderDO order = orderMap.get(systemId + report.getOrderNo());
                if (Func.isEmpty(order)) {
                    // TODO 此处判空后续待处理
                    return null;
                }

                String originalOrderNo = order.getOriginalOrderNo();
//                List<RdRelationshipSubReportDO> subReport = relationship.getSubReport();
                if (Func.isNotEmpty(subReportList)) {
                    subReportList.forEach(
                            l -> {
                                TestDataObjectRelPO testDataObjectRelPO = new TestDataObjectRelPO();
                                testDataObjectRelPO.setId(String.valueOf(ID_GENERATOR.nextId()));
                                testDataObjectRelPO.setProductLineCode(reportDataDO.getBuCode());
                                testDataObjectRelPO.setLabId(reportDataDO.getLabId());
                                testDataObjectRelPO.setLabCode(reportDataDO.getLabCode());
                                testDataObjectRelPO.setOrderNo(orderNo);
                                testDataObjectRelPO.setParentOrderNo(originalOrderNo);
                                //testDataObjectRelPO.setRealOrderNo(realOrderNo);
                                testDataObjectRelPO.setReportNo(report.getReportNo());
                                testDataObjectRelPO.setObjectNo(l.getObjectNo());
                                testDataObjectRelPO.setExternalId(l.getSubReportId());
                                testDataObjectRelPO.setExternalNo(l.getSubReportNo());
                                // TODO 待确认
                                testDataObjectRelPO.setExternalObjectNo(l.getSubReportNo());
                                testDataObjectRelPO.setSourceType(l.getSourceType());
//                            testDataObjectRelPO.setLanguageId();
                                testDataObjectRelPO.setCompleteDate(report.getSoftCopyDeliveryDate());
                                testDataObjectRelPO.setActiveIndicator(ActiveType.Enable.getStatus());
                                testDataObjectRelPO.setCreatedBy(DEFAULT_USER);
                                testDataObjectRelPO.setCreatedDate(DateUtils.getNow());
                                testDataObjectRelPO.setModifiedBy(DEFAULT_USER);
                                testDataObjectRelPO.setModifiedDate(DateUtils.getNow());
                                testDataObjectRelPO.setBizVersionId(getTestDataReportObjectRelMd5(testDataObjectRelPO));

                                list.add(testDataObjectRelPO);
                            }
                    );
                }
                return list;
            }
        }

        return null;
    }


    protected String getTestDataReportObjectRelMd5(TestDataObjectRelPO relPO) {
        relPO.setProductLineCode(StringUtil.isNullOrEmpty(relPO.getProductLineCode()));
        relPO.setLabCode(StringUtil.isNullOrEmpty(relPO.getLabCode()));
        relPO.setOrderNo(StringUtil.isNullOrEmpty(relPO.getOrderNo()));
        relPO.setParentOrderNo(StringUtil.isNullOrEmpty(relPO.getParentOrderNo()));
        relPO.setReportNo(StringUtil.isNullOrEmpty(relPO.getReportNo()));
        relPO.setObjectNo(StringUtil.isNullOrEmpty(relPO.getObjectNo()));
        relPO.setExternalId(StringUtil.isNullOrEmpty(relPO.getExternalId()));
        relPO.setExternalNo(StringUtil.isNullOrEmpty(relPO.getExternalNo()));
        relPO.setSourceType(NumberUtil.toInt(relPO.getSourceType()));
        relPO.setLanguageId(NumberUtil.toInt(relPO.getLanguageId()));
        relPO.setExternalObjectNo(StringUtil.isNullOrEmpty(relPO.getExternalObjectNo()));
        relPO.setCompleteDate(DateUtils.isNullOrEmpty(relPO.getCompleteDate()));

        StringBuilder append = new StringBuilder();
        append.append(relPO.getProductLineCode());
        append.append(relPO.getLabCode());
        append.append(relPO.getOrderNo());
        append.append(relPO.getParentOrderNo());
        append.append(relPO.getReportNo());
        append.append(relPO.getObjectNo());
        append.append(relPO.getExternalId());
        append.append(relPO.getExternalNo());
        append.append(relPO.getSourceType());
        append.append(relPO.getLanguageId());
        append.append(relPO.getExternalObjectNo());
//        append.append(relPO.getCompleteDate());
        return DigestUtils.md5Hex(append.toString());
    }

}
