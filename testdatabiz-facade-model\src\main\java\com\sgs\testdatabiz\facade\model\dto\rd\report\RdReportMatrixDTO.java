/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdReportMatrixDTO implements Serializable {

    private String subReportNo;
    @ApiModelProperty(value = "testMatrixId",dataType = "string", required = true)
    private String testMatrixId;
    private Integer testMatrixGroupId;
    private String testConditionGroupId;
    private List<RdConditionDTO> conditionList;
    // add 230922
    private RdReportMatrixExternalDTO external;
    @ApiModelProperty(value = "testSampleInstanceId",dataType = "string", required = true)
    private String testSampleInstanceId;
    private List<RdSpecimenDTO> specimenList;
    @ApiModelProperty(value = "testLineInstanceId",dataType = "string", required = true)
    private String testLineInstanceId;
//    private String analyteInstanceId;
    private List<RdPositionDTO> positionList;
    private RdConclusionDTO conclusion;
    private List<RdAttachmentDTO> testMatrixFileList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
    @ApiModelProperty(value = "matrixInstanceId",dataType = "string", required = true)
    private String matrixInstanceId;
    //SCI-1378
    private String applicationFactor;
    private String metaData;
    private String LabSectionName;
    private List<RdProductAttributeDTO> productAttributeList;
    private String remark;
}
