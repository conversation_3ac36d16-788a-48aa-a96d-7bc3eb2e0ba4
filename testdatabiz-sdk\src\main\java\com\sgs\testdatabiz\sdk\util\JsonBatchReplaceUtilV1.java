package com.sgs.testdatabiz.sdk.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.File;
import java.util.*;

public class JsonBatchReplaceUtilV1 {


    /**
     * 替换 JSON 中指定字段和值的值。
     *
     * @param jsonStr JSON 字符串
     * @param replacements 替换映射，key 为字段路径，value 为旧值和新值的数组
     * @return 替换后的 JSON 字符串
     */
    public static String batchReplaceJson(String jsonStr, Map<String, String[]> replacements) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            throw new IllegalArgumentException("JSON 字符串不能为空");
        }

        JSONObject originalJson = JSON.parseObject(jsonStr);
        JSONObject modifiedJson = new JSONObject();

        copyAndReplaceJson(originalJson, modifiedJson, replacements);

        return modifiedJson.toJSONString();
    }

    /**
     * 递归复制并替换 JSON 对象中的值。
     *
     * @param source 原始 JSON 对象
     * @param target 目标 JSON 对象
     * @param replacements 替换映射
     */
    private static void copyAndReplaceJson(JSONObject source, JSONObject target, Map<String, String[]> replacements) {
        for (String key : source.keySet()) {
            Object value = source.get(key);
            if (value instanceof JSONObject) {
                target.put(key, new JSONObject());
                copyAndReplaceJson((JSONObject) value, target.getJSONObject(key), replacements);
            } else if (value instanceof JSONArray) {
                target.put(key, new JSONArray());
                copyAndReplaceJsonArray((JSONArray) value, target.getJSONArray(key), replacements);
            } else {
                // 检查键和值是否在替换映射中
                if (replacements.containsKey(key)) {
                    String[] replacementPair = replacements.get(key);
                    Object oldValue = parseValue(replacementPair[0]);
                    Object newValue = parseValue(replacementPair[1]);
                    if (isValueEqual(value, oldValue)) {
                        target.put(key, newValue);
                    } else {
                        target.put(key, value);
                    }
                } else {
                    target.put(key, value);
                }
            }
        }
    }

    /**
     * 递归复制并替换 JSON 数组中的值。
     *
     * @param source 原始 JSON 数组
     * @param target 目标 JSON 数组
     * @param replacements 替换映射
     */
    private static void copyAndReplaceJsonArray(JSONArray source, JSONArray target, Map<String, String[]> replacements) {
        for (int i = 0; i < source.size(); i++) {
            Object value = source.get(i);
            if (value instanceof JSONObject) {
                target.add(new JSONObject());
                copyAndReplaceJson((JSONObject) value, target.getJSONObject(i), replacements);
            } else if (value instanceof JSONArray) {
                target.add(new JSONArray());
                copyAndReplaceJsonArray((JSONArray) value, target.getJSONArray(i), replacements);
            } else {
                // 检查值是否在替换映射中
                for (Map.Entry<String, String[]> entry : replacements.entrySet()) {
                    String[] replacementPair = entry.getValue();
                    Object oldValue = parseValue(replacementPair[0]);
                    Object newValue = parseValue(replacementPair[1]);
                    if (isValueEqual(value, oldValue)) {
                        target.add(newValue);
                        break;
                    }
                }
                if (!target.contains(value)) {
                    target.add(value);
                }
            }
        }
    }

    /**
     * 解析值为字符串或数值类型。
     *
     * @param value 值字符串
     * @return 解析后的新值
     */
    private static Object parseValue(String value) {
        try {
            // 尝试将值解析为数值类型
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            // 如果解析失败，返回原字符串
            return value;
        }
    }

    /**
     * 比较两个值是否相等，支持字符串和数值类型。
     *
     * @param currentValue 当前值
     * @param oldValue 旧值
     * @return 是否相等
     */
    private static boolean isValueEqual(Object currentValue, Object oldValue) {
        if (currentValue instanceof Number && oldValue instanceof Number) {
            return ((Number) currentValue).doubleValue() == ((Number) oldValue).doubleValue();
        } else if (currentValue instanceof String && oldValue instanceof String) {
            return currentValue.equals(oldValue);
        } else if (currentValue instanceof Number && oldValue instanceof String) {
            return currentValue.toString().equals(oldValue);
        } else if (currentValue instanceof String && oldValue instanceof Number) {
            return currentValue.equals(oldValue.toString());
        }
        return false;
    }


    public static void main(String[] args) {
        File file = new File("C:\\Users\\<USER>\\work\\test.txt");
        String requestJson = FileUtil.readFile(file, "UTF-8");
        // 创建一个包含多个路径和对应值的映射
//        replacements.put("orderNo", "Jane");
//        replacements.put("systemId", "25");
//        replacements.put("address", "456 Elm St");
        Map<String, String[]> replacements =new HashMap<>();
        replacements.put("orderNo", new String[]{"SZHL2411000744TY", "67890"});
        replacements.put("certificateName", new String[]{"Un-Accreditation", "2"});
        replacements.put("systemId", new String[]{"15", "25"});
        String modifiedJson = batchReplaceJson(requestJson, replacements);
        System.out.println(modifiedJson);
//        System.out.println(findFieldPaths(requestJson, "systemId"));
//        System.out.println(groupPathsByValue(findFieldPaths(requestJson, "systemId")));
    }

    // 新增的 groupPathsByValue 方法
    public static Map<Object, List<String>> groupPathsByValue(List<Map.Entry<String, Object>> entries) {
        Map<Object, List<String>> groupedPaths = new HashMap<>();
        for (Map.Entry<String, Object> entry : entries) {
            String path = entry.getKey();
            Object value = entry.getValue();

            // 如果 map 中还没有这个 value 对应的 key，先创建一个空的 list
            groupedPaths.computeIfAbsent(value, k -> new ArrayList<>()).add(path);
        }
        return groupedPaths;
    }

    public static List<Map.Entry<String, Object>> findFieldPaths(String json, String fieldName) {
        List<Map.Entry<String, Object>> result = new ArrayList<>();
        JSONObject jsonObject = JSON.parseObject(json);
        findFieldPathsRecursive(jsonObject, fieldName, "", result);
        return result;
    }

    private static void findFieldPathsRecursive(JSONObject json, String fieldName, String path, List<Map.Entry<String, Object>> result) {
        for (String key : json.keySet()) {
            Object value = json.get(key);
            String currentPath = path.isEmpty() ? key : path + "." + key;
            if (key.equals(fieldName)) {
                result.add(new AbstractMap.SimpleEntry<>(currentPath, value));
            }
            if (value instanceof JSONObject) {
                findFieldPathsRecursive((JSONObject) value, fieldName, currentPath, result);
            } else if (value instanceof JSONArray) {
                findFieldPathsInArray((JSONArray) value, fieldName, currentPath, result);
            }
        }
    }

    private static void findFieldPathsInArray(JSONArray jsonArray, String fieldName, String path, List<Map.Entry<String, Object>> result) {
        for (int i = 0; i < jsonArray.size(); i++) {
            Object value = jsonArray.get(i);
            String currentPath = path + "[" + i + "]";
            if (value instanceof JSONObject) {
                findFieldPathsRecursive((JSONObject) value, fieldName, currentPath, result);
            } else if (value instanceof JSONArray) {
                findFieldPathsInArray((JSONArray) value, fieldName, currentPath, result);
            }
        }
    }
}

