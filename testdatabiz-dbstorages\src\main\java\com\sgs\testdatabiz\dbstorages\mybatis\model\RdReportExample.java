package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RdReportExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RdReportExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNull() {
            addCriterion("lab_id is null");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNotNull() {
            addCriterion("lab_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabIdEqualTo(Long value) {
            addCriterion("lab_id =", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotEqualTo(Long value) {
            addCriterion("lab_id <>", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThan(Long value) {
            addCriterion("lab_id >", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThanOrEqualTo(Long value) {
            addCriterion("lab_id >=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThan(Long value) {
            addCriterion("lab_id <", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThanOrEqualTo(Long value) {
            addCriterion("lab_id <=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdIn(List<Long> values) {
            addCriterion("lab_id in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotIn(List<Long> values) {
            addCriterion("lab_id not in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdBetween(Long value1, Long value2) {
            addCriterion("lab_id between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotBetween(Long value1, Long value2) {
            addCriterion("lab_id not between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andBuIdIsNull() {
            addCriterion("bu_id is null");
            return (Criteria) this;
        }

        public Criteria andBuIdIsNotNull() {
            addCriterion("bu_id is not null");
            return (Criteria) this;
        }

        public Criteria andBuIdEqualTo(Long value) {
            addCriterion("bu_id =", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotEqualTo(Long value) {
            addCriterion("bu_id <>", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdGreaterThan(Long value) {
            addCriterion("bu_id >", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("bu_id >=", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdLessThan(Long value) {
            addCriterion("bu_id <", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdLessThanOrEqualTo(Long value) {
            addCriterion("bu_id <=", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdIn(List<Long> values) {
            addCriterion("bu_id in", values, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotIn(List<Long> values) {
            addCriterion("bu_id not in", values, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdBetween(Long value1, Long value2) {
            addCriterion("bu_id between", value1, value2, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotBetween(Long value1, Long value2) {
            addCriterion("bu_id not between", value1, value2, "buId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("system_id is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("system_id is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("system_id =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("system_id <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("system_id >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("system_id >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("system_id <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("system_id <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("system_id in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("system_id not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("system_id between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("system_id not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(String value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(String value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(String value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(String value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(String value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(String value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLike(String value) {
            addCriterion("report_id like", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotLike(String value) {
            addCriterion("report_id not like", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<String> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<String> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(String value1, String value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(String value1, String value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("report_no is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("report_no is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("report_no =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("report_no <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("report_no >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("report_no >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("report_no <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("report_no <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("report_no like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("report_no not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("report_no in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("report_no not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("report_no between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("report_no not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoIsNull() {
            addCriterion("original_report_no is null");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoIsNotNull() {
            addCriterion("original_report_no is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoEqualTo(String value) {
            addCriterion("original_report_no =", value, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoNotEqualTo(String value) {
            addCriterion("original_report_no <>", value, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoGreaterThan(String value) {
            addCriterion("original_report_no >", value, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("original_report_no >=", value, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoLessThan(String value) {
            addCriterion("original_report_no <", value, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoLessThanOrEqualTo(String value) {
            addCriterion("original_report_no <=", value, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoLike(String value) {
            addCriterion("original_report_no like", value, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoNotLike(String value) {
            addCriterion("original_report_no not like", value, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoIn(List<String> values) {
            addCriterion("original_report_no in", values, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoNotIn(List<String> values) {
            addCriterion("original_report_no not in", values, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoBetween(String value1, String value2) {
            addCriterion("original_report_no between", value1, value2, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andOriginalReportNoNotBetween(String value1, String value2) {
            addCriterion("original_report_no not between", value1, value2, "originalReportNo");
            return (Criteria) this;
        }

        public Criteria andReportHeaderIsNull() {
            addCriterion("report_header is null");
            return (Criteria) this;
        }

        public Criteria andReportHeaderIsNotNull() {
            addCriterion("report_header is not null");
            return (Criteria) this;
        }

        public Criteria andReportHeaderEqualTo(String value) {
            addCriterion("report_header =", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderNotEqualTo(String value) {
            addCriterion("report_header <>", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderGreaterThan(String value) {
            addCriterion("report_header >", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderGreaterThanOrEqualTo(String value) {
            addCriterion("report_header >=", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderLessThan(String value) {
            addCriterion("report_header <", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderLessThanOrEqualTo(String value) {
            addCriterion("report_header <=", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderLike(String value) {
            addCriterion("report_header like", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderNotLike(String value) {
            addCriterion("report_header not like", value, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderIn(List<String> values) {
            addCriterion("report_header in", values, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderNotIn(List<String> values) {
            addCriterion("report_header not in", values, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderBetween(String value1, String value2) {
            addCriterion("report_header between", value1, value2, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportHeaderNotBetween(String value1, String value2) {
            addCriterion("report_header not between", value1, value2, "reportHeader");
            return (Criteria) this;
        }

        public Criteria andReportAddressIsNull() {
            addCriterion("report_address is null");
            return (Criteria) this;
        }

        public Criteria andReportAddressIsNotNull() {
            addCriterion("report_address is not null");
            return (Criteria) this;
        }

        public Criteria andReportAddressEqualTo(String value) {
            addCriterion("report_address =", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressNotEqualTo(String value) {
            addCriterion("report_address <>", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressGreaterThan(String value) {
            addCriterion("report_address >", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressGreaterThanOrEqualTo(String value) {
            addCriterion("report_address >=", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressLessThan(String value) {
            addCriterion("report_address <", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressLessThanOrEqualTo(String value) {
            addCriterion("report_address <=", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressLike(String value) {
            addCriterion("report_address like", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressNotLike(String value) {
            addCriterion("report_address not like", value, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressIn(List<String> values) {
            addCriterion("report_address in", values, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressNotIn(List<String> values) {
            addCriterion("report_address not in", values, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressBetween(String value1, String value2) {
            addCriterion("report_address between", value1, value2, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportAddressNotBetween(String value1, String value2) {
            addCriterion("report_address not between", value1, value2, "reportAddress");
            return (Criteria) this;
        }

        public Criteria andReportStatusIsNull() {
            addCriterion("report_status is null");
            return (Criteria) this;
        }

        public Criteria andReportStatusIsNotNull() {
            addCriterion("report_status is not null");
            return (Criteria) this;
        }

        public Criteria andReportStatusEqualTo(Integer value) {
            addCriterion("report_status =", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotEqualTo(Integer value) {
            addCriterion("report_status <>", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusGreaterThan(Integer value) {
            addCriterion("report_status >", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_status >=", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusLessThan(Integer value) {
            addCriterion("report_status <", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusLessThanOrEqualTo(Integer value) {
            addCriterion("report_status <=", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusIn(List<Integer> values) {
            addCriterion("report_status in", values, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotIn(List<Integer> values) {
            addCriterion("report_status not in", values, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusBetween(Integer value1, Integer value2) {
            addCriterion("report_status between", value1, value2, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("report_status not between", value1, value2, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportDueDateIsNull() {
            addCriterion("report_due_date is null");
            return (Criteria) this;
        }

        public Criteria andReportDueDateIsNotNull() {
            addCriterion("report_due_date is not null");
            return (Criteria) this;
        }

        public Criteria andReportDueDateEqualTo(Date value) {
            addCriterion("report_due_date =", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateNotEqualTo(Date value) {
            addCriterion("report_due_date <>", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateGreaterThan(Date value) {
            addCriterion("report_due_date >", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateGreaterThanOrEqualTo(Date value) {
            addCriterion("report_due_date >=", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateLessThan(Date value) {
            addCriterion("report_due_date <", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateLessThanOrEqualTo(Date value) {
            addCriterion("report_due_date <=", value, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateIn(List<Date> values) {
            addCriterion("report_due_date in", values, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateNotIn(List<Date> values) {
            addCriterion("report_due_date not in", values, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateBetween(Date value1, Date value2) {
            addCriterion("report_due_date between", value1, value2, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportDueDateNotBetween(Date value1, Date value2) {
            addCriterion("report_due_date not between", value1, value2, "reportDueDate");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameIsNull() {
            addCriterion("report_certificate_name is null");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameIsNotNull() {
            addCriterion("report_certificate_name is not null");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameEqualTo(String value) {
            addCriterion("report_certificate_name =", value, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameNotEqualTo(String value) {
            addCriterion("report_certificate_name <>", value, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameGreaterThan(String value) {
            addCriterion("report_certificate_name >", value, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameGreaterThanOrEqualTo(String value) {
            addCriterion("report_certificate_name >=", value, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameLessThan(String value) {
            addCriterion("report_certificate_name <", value, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameLessThanOrEqualTo(String value) {
            addCriterion("report_certificate_name <=", value, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameLike(String value) {
            addCriterion("report_certificate_name like", value, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameNotLike(String value) {
            addCriterion("report_certificate_name not like", value, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameIn(List<String> values) {
            addCriterion("report_certificate_name in", values, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameNotIn(List<String> values) {
            addCriterion("report_certificate_name not in", values, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameBetween(String value1, String value2) {
            addCriterion("report_certificate_name between", value1, value2, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCertificateNameNotBetween(String value1, String value2) {
            addCriterion("report_certificate_name not between", value1, value2, "reportCertificateName");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByIsNull() {
            addCriterion("report_created_by is null");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByIsNotNull() {
            addCriterion("report_created_by is not null");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByEqualTo(String value) {
            addCriterion("report_created_by =", value, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByNotEqualTo(String value) {
            addCriterion("report_created_by <>", value, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByGreaterThan(String value) {
            addCriterion("report_created_by >", value, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("report_created_by >=", value, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByLessThan(String value) {
            addCriterion("report_created_by <", value, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByLessThanOrEqualTo(String value) {
            addCriterion("report_created_by <=", value, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByLike(String value) {
            addCriterion("report_created_by like", value, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByNotLike(String value) {
            addCriterion("report_created_by not like", value, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByIn(List<String> values) {
            addCriterion("report_created_by in", values, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByNotIn(List<String> values) {
            addCriterion("report_created_by not in", values, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByBetween(String value1, String value2) {
            addCriterion("report_created_by between", value1, value2, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedByNotBetween(String value1, String value2) {
            addCriterion("report_created_by not between", value1, value2, "reportCreatedBy");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateIsNull() {
            addCriterion("report_created_date is null");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateIsNotNull() {
            addCriterion("report_created_date is not null");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateEqualTo(Date value) {
            addCriterion("report_created_date =", value, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateNotEqualTo(Date value) {
            addCriterion("report_created_date <>", value, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateGreaterThan(Date value) {
            addCriterion("report_created_date >", value, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("report_created_date >=", value, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateLessThan(Date value) {
            addCriterion("report_created_date <", value, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("report_created_date <=", value, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateIn(List<Date> values) {
            addCriterion("report_created_date in", values, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateNotIn(List<Date> values) {
            addCriterion("report_created_date not in", values, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateBetween(Date value1, Date value2) {
            addCriterion("report_created_date between", value1, value2, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("report_created_date not between", value1, value2, "reportCreatedDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverByIsNull() {
            addCriterion("report_approver_by is null");
            return (Criteria) this;
        }

        public Criteria andReportApproverByIsNotNull() {
            addCriterion("report_approver_by is not null");
            return (Criteria) this;
        }

        public Criteria andReportApproverByEqualTo(String value) {
            addCriterion("report_approver_by =", value, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByNotEqualTo(String value) {
            addCriterion("report_approver_by <>", value, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByGreaterThan(String value) {
            addCriterion("report_approver_by >", value, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByGreaterThanOrEqualTo(String value) {
            addCriterion("report_approver_by >=", value, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByLessThan(String value) {
            addCriterion("report_approver_by <", value, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByLessThanOrEqualTo(String value) {
            addCriterion("report_approver_by <=", value, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByLike(String value) {
            addCriterion("report_approver_by like", value, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByNotLike(String value) {
            addCriterion("report_approver_by not like", value, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByIn(List<String> values) {
            addCriterion("report_approver_by in", values, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByNotIn(List<String> values) {
            addCriterion("report_approver_by not in", values, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByBetween(String value1, String value2) {
            addCriterion("report_approver_by between", value1, value2, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverByNotBetween(String value1, String value2) {
            addCriterion("report_approver_by not between", value1, value2, "reportApproverBy");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateIsNull() {
            addCriterion("report_approver_date is null");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateIsNotNull() {
            addCriterion("report_approver_date is not null");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateEqualTo(Date value) {
            addCriterion("report_approver_date =", value, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateNotEqualTo(Date value) {
            addCriterion("report_approver_date <>", value, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateGreaterThan(Date value) {
            addCriterion("report_approver_date >", value, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateGreaterThanOrEqualTo(Date value) {
            addCriterion("report_approver_date >=", value, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateLessThan(Date value) {
            addCriterion("report_approver_date <", value, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateLessThanOrEqualTo(Date value) {
            addCriterion("report_approver_date <=", value, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateIn(List<Date> values) {
            addCriterion("report_approver_date in", values, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateNotIn(List<Date> values) {
            addCriterion("report_approver_date not in", values, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateBetween(Date value1, Date value2) {
            addCriterion("report_approver_date between", value1, value2, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andReportApproverDateNotBetween(Date value1, Date value2) {
            addCriterion("report_approver_date not between", value1, value2, "reportApproverDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateIsNull() {
            addCriterion("softcopy_delivery_date is null");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateIsNotNull() {
            addCriterion("softcopy_delivery_date is not null");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateEqualTo(Date value) {
            addCriterion("softcopy_delivery_date =", value, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateNotEqualTo(Date value) {
            addCriterion("softcopy_delivery_date <>", value, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateGreaterThan(Date value) {
            addCriterion("softcopy_delivery_date >", value, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateGreaterThanOrEqualTo(Date value) {
            addCriterion("softcopy_delivery_date >=", value, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateLessThan(Date value) {
            addCriterion("softcopy_delivery_date <", value, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateLessThanOrEqualTo(Date value) {
            addCriterion("softcopy_delivery_date <=", value, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateIn(List<Date> values) {
            addCriterion("softcopy_delivery_date in", values, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateNotIn(List<Date> values) {
            addCriterion("softcopy_delivery_date not in", values, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateBetween(Date value1, Date value2) {
            addCriterion("softcopy_delivery_date between", value1, value2, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andSoftcopyDeliveryDateNotBetween(Date value1, Date value2) {
            addCriterion("softcopy_delivery_date not between", value1, value2, "softcopyDeliveryDate");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIsNull() {
            addCriterion("conclusion_code is null");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIsNotNull() {
            addCriterion("conclusion_code is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeEqualTo(String value) {
            addCriterion("conclusion_code =", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotEqualTo(String value) {
            addCriterion("conclusion_code <>", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeGreaterThan(String value) {
            addCriterion("conclusion_code >", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeGreaterThanOrEqualTo(String value) {
            addCriterion("conclusion_code >=", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLessThan(String value) {
            addCriterion("conclusion_code <", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLessThanOrEqualTo(String value) {
            addCriterion("conclusion_code <=", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeLike(String value) {
            addCriterion("conclusion_code like", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotLike(String value) {
            addCriterion("conclusion_code not like", value, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeIn(List<String> values) {
            addCriterion("conclusion_code in", values, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotIn(List<String> values) {
            addCriterion("conclusion_code not in", values, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeBetween(String value1, String value2) {
            addCriterion("conclusion_code between", value1, value2, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andConclusionCodeNotBetween(String value1, String value2) {
            addCriterion("conclusion_code not between", value1, value2, "conclusionCode");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIsNull() {
            addCriterion("customer_conclusion is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIsNotNull() {
            addCriterion("customer_conclusion is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionEqualTo(String value) {
            addCriterion("customer_conclusion =", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotEqualTo(String value) {
            addCriterion("customer_conclusion <>", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionGreaterThan(String value) {
            addCriterion("customer_conclusion >", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("customer_conclusion >=", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLessThan(String value) {
            addCriterion("customer_conclusion <", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLessThanOrEqualTo(String value) {
            addCriterion("customer_conclusion <=", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionLike(String value) {
            addCriterion("customer_conclusion like", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotLike(String value) {
            addCriterion("customer_conclusion not like", value, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionIn(List<String> values) {
            addCriterion("customer_conclusion in", values, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotIn(List<String> values) {
            addCriterion("customer_conclusion not in", values, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionBetween(String value1, String value2) {
            addCriterion("customer_conclusion between", value1, value2, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andCustomerConclusionNotBetween(String value1, String value2) {
            addCriterion("customer_conclusion not between", value1, value2, "customerConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionIsNull() {
            addCriterion("review_conclusion is null");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionIsNotNull() {
            addCriterion("review_conclusion is not null");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionEqualTo(String value) {
            addCriterion("review_conclusion =", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionNotEqualTo(String value) {
            addCriterion("review_conclusion <>", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionGreaterThan(String value) {
            addCriterion("review_conclusion >", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("review_conclusion >=", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionLessThan(String value) {
            addCriterion("review_conclusion <", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionLessThanOrEqualTo(String value) {
            addCriterion("review_conclusion <=", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionLike(String value) {
            addCriterion("review_conclusion like", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionNotLike(String value) {
            addCriterion("review_conclusion not like", value, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionIn(List<String> values) {
            addCriterion("review_conclusion in", values, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionNotIn(List<String> values) {
            addCriterion("review_conclusion not in", values, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionBetween(String value1, String value2) {
            addCriterion("review_conclusion between", value1, value2, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andReviewConclusionNotBetween(String value1, String value2) {
            addCriterion("review_conclusion not between", value1, value2, "reviewConclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIsNull() {
            addCriterion("conclusion_remark is null");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIsNotNull() {
            addCriterion("conclusion_remark is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkEqualTo(String value) {
            addCriterion("conclusion_remark =", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotEqualTo(String value) {
            addCriterion("conclusion_remark <>", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkGreaterThan(String value) {
            addCriterion("conclusion_remark >", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("conclusion_remark >=", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLessThan(String value) {
            addCriterion("conclusion_remark <", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLessThanOrEqualTo(String value) {
            addCriterion("conclusion_remark <=", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkLike(String value) {
            addCriterion("conclusion_remark like", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotLike(String value) {
            addCriterion("conclusion_remark not like", value, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkIn(List<String> values) {
            addCriterion("conclusion_remark in", values, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotIn(List<String> values) {
            addCriterion("conclusion_remark not in", values, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkBetween(String value1, String value2) {
            addCriterion("conclusion_remark between", value1, value2, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andConclusionRemarkNotBetween(String value1, String value2) {
            addCriterion("conclusion_remark not between", value1, value2, "conclusionRemark");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("last_modified_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("last_modified_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("last_modified_timestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("last_modified_timestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("last_modified_timestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("last_modified_timestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("last_modified_timestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("last_modified_timestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceIsNull() {
            addCriterion("exclude_customer_interface is null");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceIsNotNull() {
            addCriterion("exclude_customer_interface is not null");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceEqualTo(String value) {
            addCriterion("exclude_customer_interface =", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceNotEqualTo(String value) {
            addCriterion("exclude_customer_interface <>", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceGreaterThan(String value) {
            addCriterion("exclude_customer_interface >", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceGreaterThanOrEqualTo(String value) {
            addCriterion("exclude_customer_interface >=", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceLessThan(String value) {
            addCriterion("exclude_customer_interface <", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceLessThanOrEqualTo(String value) {
            addCriterion("exclude_customer_interface <=", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceLike(String value) {
            addCriterion("exclude_customer_interface like", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceNotLike(String value) {
            addCriterion("exclude_customer_interface not like", value, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceIn(List<String> values) {
            addCriterion("exclude_customer_interface in", values, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceNotIn(List<String> values) {
            addCriterion("exclude_customer_interface not in", values, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceBetween(String value1, String value2) {
            addCriterion("exclude_customer_interface between", value1, value2, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andExcludeCustomerInterfaceNotBetween(String value1, String value2) {
            addCriterion("exclude_customer_interface not between", value1, value2, "excludeCustomerInterface");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeIsNull() {
            addCriterion("report_source_type is null");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeIsNotNull() {
            addCriterion("report_source_type is not null");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeEqualTo(Integer value) {
            addCriterion("report_source_type =", value, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeNotEqualTo(Integer value) {
            addCriterion("report_source_type <>", value, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeGreaterThan(Integer value) {
            addCriterion("report_source_type >", value, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_source_type >=", value, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeLessThan(Integer value) {
            addCriterion("report_source_type <", value, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("report_source_type <=", value, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeIn(List<Integer> values) {
            addCriterion("report_source_type in", values, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeNotIn(List<Integer> values) {
            addCriterion("report_source_type not in", values, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeBetween(Integer value1, Integer value2) {
            addCriterion("report_source_type between", value1, value2, "reportSourceType");
            return (Criteria) this;
        }

        public Criteria andReportSourceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("report_source_type not between", value1, value2, "reportSourceType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}