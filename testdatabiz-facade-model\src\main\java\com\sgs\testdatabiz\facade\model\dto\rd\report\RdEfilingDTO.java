package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.Data;

import java.util.List;

@Data
public class RdEfilingDTO {

    private List<String> exclusionCitationCode;
    private List<String> rbsrCitationCode;
    private String disclaimCode;
    private String cpscLabId;
    private String labType;
    private String labName;
    private String labEmail;
    private String labTelephoneNumber;
    private EfilingAddressInfo addressInfo;


    @Data
    public static class EfilingAddressInfo {
        private String address;
        private String city;
        private String country;
        private String province;
        private String postal;
    }
}
