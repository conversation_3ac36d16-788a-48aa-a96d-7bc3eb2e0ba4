package com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.filtermatrix;

import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.BaseMatrixInfoFilter;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.MatrixInfo;
import com.sgs.testdatabiz.facade.model.dto.starlims.MatrixFilterDTO;
import com.sgs.testdatabiz.facade.model.dto.starlims.SubContractMatrixDTO;
import com.sgs.testdatabiz.facade.model.enums.MatrixSource;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveReportData;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *  匹配 TestLine
 */
@Component
@MatrixInfo(value = "StarLimsDefault", sort = 4)
public class StarlimsDefaultFilter extends BaseMatrixInfoFilter {
    @Override
    public SubContractMatrixDTO filterStarlimsMatrix(MatrixFilterDTO matrixFilterDTO) {
        ReceiveReportData reportData = matrixFilterDTO.getReportData();
        ChemPpArtifactTestLineInfoRsp chemTestLine = matrixFilterDTO.getChemTestLine();
        String orderNo = matrixFilterDTO.getOrderNo();

        SubContractMatrixDTO testMatrixInfo = new SubContractMatrixDTO();

        String matrixId = StringUtils.defaultIfBlank(reportData.getExternalMatrixId(), reportData.getId());
        String testLineMd5Key = String.format("%s_%s", orderNo, findPpTestLineCitationKey(chemTestLine).toLowerCase());
        String testLineInstanceId = DigestUtils.md5Hex(testLineMd5Key);
        String testSampleId = StringUtils.defaultIfBlank(reportData.getExternalId(), reportData.getSampleNo());
        Integer citationType = NumberUtil.toInt(0);
        String matrixSource = MatrixSource.StarLims.getSource();
        setTestMatrixInfo(testMatrixInfo, matrixId, testLineInstanceId, testSampleId, citationType, matrixSource);

        return testMatrixInfo;
    }

}
