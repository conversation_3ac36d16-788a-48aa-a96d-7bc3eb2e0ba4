<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdAttachmentMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="object_type" property="objectType" jdbcType="INTEGER" />
    <result column="object_no" property="objectNo" jdbcType="VARCHAR" />
    <result column="object_id" property="objectId" jdbcType="VARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="system_id" property="systemId" jdbcType="BIGINT" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="language_id" property="languageId" jdbcType="INTEGER" />
    <result column="biz_type" property="bizType" jdbcType="VARCHAR" />
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <result column="cloud_id" property="cloudId" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, lab_id, object_type, object_no, object_id, order_no, system_id, report_no, language_id, 
    biz_type, file_name, cloud_id, active_indicator, created_by, created_date, modified_by, 
    modified_date, last_modified_timestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_attachment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_attachment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_attachment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentExample" >
    delete from tb_attachment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentPO" >
    insert into tb_attachment (id, lab_id, object_type, 
      object_no, object_id, order_no, 
      system_id, report_no, language_id, 
      biz_type, file_name, cloud_id, 
      active_indicator, created_by, created_date, 
      modified_by, modified_date, last_modified_timestamp
      )
    values (#{id,jdbcType=BIGINT}, #{labId,jdbcType=BIGINT}, #{objectType,jdbcType=INTEGER}, 
      #{objectNo,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{systemId,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{languageId,jdbcType=INTEGER}, 
      #{bizType,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{cloudId,jdbcType=VARCHAR}, 
      #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, now(), 
      #{modifiedBy,jdbcType=VARCHAR}, now(), #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentPO" >
    insert into tb_attachment
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="objectType != null" >
        object_type,
      </if>
      <if test="objectNo != null" >
        object_no,
      </if>
      <if test="objectId != null" >
        object_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="systemId != null" >
        system_id,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="languageId != null" >
        language_id,
      </if>
      <if test="bizType != null" >
        biz_type,
      </if>
      <if test="fileName != null" >
        file_name,
      </if>
      <if test="cloudId != null" >
        cloud_id,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="objectType != null" >
        #{objectType,jdbcType=INTEGER},
      </if>
      <if test="objectNo != null" >
        #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null" >
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="cloudId != null" >
        #{cloudId,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentExample" resultType="java.lang.Integer" >
    select count(*) from tb_attachment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_attachment
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.objectType != null" >
        object_type = #{record.objectType,jdbcType=INTEGER},
      </if>
      <if test="record.objectNo != null" >
        object_no = #{record.objectNo,jdbcType=VARCHAR},
      </if>
      <if test="record.objectId != null" >
        object_id = #{record.objectId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.systemId != null" >
        system_id = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        language_id = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.bizType != null" >
        biz_type = #{record.bizType,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null" >
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.cloudId != null" >
        cloud_id = #{record.cloudId,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_attachment
    set id = #{record.id,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      object_type = #{record.objectType,jdbcType=INTEGER},
      object_no = #{record.objectNo,jdbcType=VARCHAR},
      object_id = #{record.objectId,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      system_id = #{record.systemId,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      language_id = #{record.languageId,jdbcType=INTEGER},
      biz_type = #{record.bizType,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      cloud_id = #{record.cloudId,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentPO" >
    update tb_attachment
    <set >
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=INTEGER},
      </if>
      <if test="objectNo != null" >
        object_no = #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        object_id = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null" >
        system_id = #{systemId,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        language_id = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null" >
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="cloudId != null" >
        cloud_id = #{cloudId,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentPO" >
    update tb_attachment
    set lab_id = #{labId,jdbcType=BIGINT},
      object_type = #{objectType,jdbcType=INTEGER},
      object_no = #{objectNo,jdbcType=VARCHAR},
      object_id = #{objectId,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      system_id = #{systemId,jdbcType=BIGINT},
      report_no = #{reportNo,jdbcType=VARCHAR},
      language_id = #{languageId,jdbcType=INTEGER},
      biz_type = #{bizType,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      cloud_id = #{cloudId,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_attachment
      (`id`,`lab_id`,`object_type`,
      `object_no`,`object_id`,`order_no`,
      `system_id`,`report_no`,`language_id`,
      `biz_type`,`file_name`,`cloud_id`,
      `active_indicator`,`created_by`,`created_date`,
      `modified_by`,`modified_date`,`last_modified_timestamp`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.objectType, jdbcType=INTEGER},
      #{ item.objectNo, jdbcType=VARCHAR},#{ item.objectId, jdbcType=VARCHAR},#{ item.orderNo, jdbcType=VARCHAR},
      #{ item.systemId, jdbcType=BIGINT},#{ item.reportNo, jdbcType=VARCHAR},#{ item.languageId, jdbcType=INTEGER},
      #{ item.bizType, jdbcType=VARCHAR},#{ item.fileName, jdbcType=VARCHAR},#{ item.cloudId, jdbcType=VARCHAR},
      #{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_attachment 
      <set>
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.objectType != null"> 
          `object_type` = #{item.objectType, jdbcType = INTEGER},
        </if> 
        <if test="item.objectNo != null"> 
          `object_no` = #{item.objectNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.objectId != null"> 
          `object_id` = #{item.objectId, jdbcType = VARCHAR},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.systemId != null"> 
          `system_id` = #{item.systemId, jdbcType = BIGINT},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.languageId != null"> 
          `language_id` = #{item.languageId, jdbcType = INTEGER},
        </if> 
        <if test="item.bizType != null"> 
          `biz_type` = #{item.bizType, jdbcType = VARCHAR},
        </if> 
        <if test="item.fileName != null"> 
          `file_name` = #{item.fileName, jdbcType = VARCHAR},
        </if> 
        <if test="item.cloudId != null"> 
          `cloud_id` = #{item.cloudId, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>