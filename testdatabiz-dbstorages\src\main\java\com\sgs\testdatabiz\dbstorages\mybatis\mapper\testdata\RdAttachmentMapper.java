package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdAttachmentMapper {
    int countByExample(RdAttachmentExample example);

    int deleteByExample(RdAttachmentExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdAttachmentPO record);

    int insertSelective(RdAttachmentPO record);

    List<RdAttachmentPO> selectByExample(RdAttachmentExample example);

    RdAttachmentPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdAttachmentPO record, @Param("example") RdAttachmentExample example);

    int updateByExample(@Param("record") RdAttachmentPO record, @Param("example") RdAttachmentExample example);

    int updateByPrimaryKeySelective(RdAttachmentPO record);

    int updateByPrimaryKey(RdAttachmentPO record);

    int batchInsert(List<RdAttachmentPO> list);

    int batchUpdate(List<RdAttachmentPO> list);
}