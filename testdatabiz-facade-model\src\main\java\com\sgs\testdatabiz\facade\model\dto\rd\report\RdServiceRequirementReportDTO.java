/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceRequirementReportDTO implements Serializable {
    @ApiModelProperty(value = "reportLanguage",dataType = "integer", required = true)
    private Integer reportLanguage;
    @ApiModelProperty(value = "reportHeader",dataType = "string", required = true)
    private String reportHeader;
    private String reportAddress;
    private Integer accreditation;
    private Integer needConclusion;
    private Integer needDraft;
    private Integer needPhoto;
    @ApiModelProperty(value = "languageList",dataType = "List", required = true)
    private List<RdReportLanguageDTO> languageList;
    private RdDeliveryDTO softcopy;
    private RdDeliveryDTO hardcopy;
    private String certificateRequired;
    private String commentFlag;
    private String confirmCoverPageFlag;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value="activeIndicator",dataType = "integer", required = true)
    private Integer activeIndicator;
}
