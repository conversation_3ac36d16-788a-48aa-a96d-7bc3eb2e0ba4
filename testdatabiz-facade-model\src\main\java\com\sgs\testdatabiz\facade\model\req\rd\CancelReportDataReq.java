package com.sgs.testdatabiz.facade.model.req.rd;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class CancelReportDataReq extends BaseModel {

    private String reportNo;

    private Integer reportStatus;

    @Override
    public String getExtId() {
        return reportNo;
    }
}
