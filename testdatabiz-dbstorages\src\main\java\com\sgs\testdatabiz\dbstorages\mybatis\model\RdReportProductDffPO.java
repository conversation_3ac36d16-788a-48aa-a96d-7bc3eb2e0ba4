package com.sgs.testdatabiz.dbstorages.mybatis.model;

public class RdReportProductDffPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 唯一标识
     */
    private Long id;

    /**
     * lab_id BIGINT(19) 必填<br>
     * Trims系统实验室标识
     */
    private Long labId;

    /**
     * rd_report_id BIGINT(19)<br>
     * report唯一标识
     */
    private Long rdReportId;

    /**
     * order_no VARCHAR(50)<br>
     * 订单号
     */
    private String orderNo;

    /**
     * report_no VARCHAR(50) 必填<br>
     * 报告号
     */
    private String reportNo;

    /**
     * object_type INTEGER(10)<br>
     * 1、Product ; 2、Sample；
     */
    private Integer objectType;

    /**
     * product_instance_id VARCHAR(36)<br>
     * 产品实例编码
     */
    private String productInstanceId;

    /**
     * form_id VARCHAR(50)<br>
     * DFF Form 标识
     */
    private String formId;

    /**
     * language_id INTEGER(10)<br>
     * 语言标识
     */
    private Integer languageId;

    /**
     * label_code VARCHAR(100)<br>
     * BU 标签编码
     */
    private String labelCode;

    /**
     * label_name VARCHAR(100)<br>
     * BU 标签名称
     */
    private String labelName;

    /**
     * field_code VARCHAR(50)<br>
     * DFF 字段编码
     */
    private String fieldCode;

    /**
     * customer_label VARCHAR(50)<br>
     * 客户标签名称
     */
    private String customerLabel;

    /**
     * data_type VARCHAR(50)<br>
     * 数据类型
     */
    private String dataType;

    /**
     * value VARCHAR(500) 必填<br>
     * 数值
     */
    private String value;

    /**
     * seq INTEGER(10)<br>
     * 显示顺序
     */
    private Integer seq;

    /**
     * display_in_report VARCHAR(100)<br>
     * 是否显示在报告
     */
    private String displayInReport;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 唯一标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 唯一标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * lab_id BIGINT(19) 必填<br>
     * 获得 Trims系统实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19) 必填<br>
     * 设置 Trims系统实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * rd_report_id BIGINT(19)<br>
     * 获得 report唯一标识
     */
    public Long getRdReportId() {
        return rdReportId;
    }

    /**
     * rd_report_id BIGINT(19)<br>
     * 设置 report唯一标识
     */
    public void setRdReportId(Long rdReportId) {
        this.rdReportId = rdReportId;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * report_no VARCHAR(50) 必填<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50) 必填<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * object_type INTEGER(10)<br>
     * 获得 1、Product ; 2、Sample；
     */
    public Integer getObjectType() {
        return objectType;
    }

    /**
     * object_type INTEGER(10)<br>
     * 设置 1、Product ; 2、Sample；
     */
    public void setObjectType(Integer objectType) {
        this.objectType = objectType;
    }

    /**
     * product_instance_id VARCHAR(36)<br>
     * 获得 产品实例编码
     */
    public String getProductInstanceId() {
        return productInstanceId;
    }

    /**
     * product_instance_id VARCHAR(36)<br>
     * 设置 产品实例编码
     */
    public void setProductInstanceId(String productInstanceId) {
        this.productInstanceId = productInstanceId == null ? null : productInstanceId.trim();
    }

    /**
     * form_id VARCHAR(50)<br>
     * 获得 DFF Form 标识
     */
    public String getFormId() {
        return formId;
    }

    /**
     * form_id VARCHAR(50)<br>
     * 设置 DFF Form 标识
     */
    public void setFormId(String formId) {
        this.formId = formId == null ? null : formId.trim();
    }

    /**
     * language_id INTEGER(10)<br>
     * 获得 语言标识
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 设置 语言标识
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * label_code VARCHAR(100)<br>
     * 获得 BU 标签编码
     */
    public String getLabelCode() {
        return labelCode;
    }

    /**
     * label_code VARCHAR(100)<br>
     * 设置 BU 标签编码
     */
    public void setLabelCode(String labelCode) {
        this.labelCode = labelCode == null ? null : labelCode.trim();
    }

    /**
     * label_name VARCHAR(100)<br>
     * 获得 BU 标签名称
     */
    public String getLabelName() {
        return labelName;
    }

    /**
     * label_name VARCHAR(100)<br>
     * 设置 BU 标签名称
     */
    public void setLabelName(String labelName) {
        this.labelName = labelName == null ? null : labelName.trim();
    }

    /**
     * field_code VARCHAR(50)<br>
     * 获得 DFF 字段编码
     */
    public String getFieldCode() {
        return fieldCode;
    }

    /**
     * field_code VARCHAR(50)<br>
     * 设置 DFF 字段编码
     */
    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode == null ? null : fieldCode.trim();
    }

    /**
     * customer_label VARCHAR(50)<br>
     * 获得 客户标签名称
     */
    public String getCustomerLabel() {
        return customerLabel;
    }

    /**
     * customer_label VARCHAR(50)<br>
     * 设置 客户标签名称
     */
    public void setCustomerLabel(String customerLabel) {
        this.customerLabel = customerLabel == null ? null : customerLabel.trim();
    }

    /**
     * data_type VARCHAR(50)<br>
     * 获得 数据类型
     */
    public String getDataType() {
        return dataType;
    }

    /**
     * data_type VARCHAR(50)<br>
     * 设置 数据类型
     */
    public void setDataType(String dataType) {
        this.dataType = dataType == null ? null : dataType.trim();
    }

    /**
     * value VARCHAR(500) 必填<br>
     * 获得 数值
     */
    public String getValue() {
        return value;
    }

    /**
     * value VARCHAR(500) 必填<br>
     * 设置 数值
     */
    public void setValue(String value) {
        this.value = value == null ? null : value.trim();
    }

    /**
     * seq INTEGER(10)<br>
     * 获得 显示顺序
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * seq INTEGER(10)<br>
     * 设置 显示顺序
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * display_in_report VARCHAR(100)<br>
     * 获得 是否显示在报告
     */
    public String getDisplayInReport() {
        return displayInReport;
    }

    /**
     * display_in_report VARCHAR(100)<br>
     * 设置 是否显示在报告
     */
    public void setDisplayInReport(String displayInReport) {
        this.displayInReport = displayInReport == null ? null : displayInReport.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", labId=").append(labId);
        sb.append(", rdReportId=").append(rdReportId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", objectType=").append(objectType);
        sb.append(", productInstanceId=").append(productInstanceId);
        sb.append(", formId=").append(formId);
        sb.append(", languageId=").append(languageId);
        sb.append(", labelCode=").append(labelCode);
        sb.append(", labelName=").append(labelName);
        sb.append(", fieldCode=").append(fieldCode);
        sb.append(", customerLabel=").append(customerLabel);
        sb.append(", dataType=").append(dataType);
        sb.append(", value=").append(value);
        sb.append(", seq=").append(seq);
        sb.append(", displayInReport=").append(displayInReport);
        sb.append("]");
        return sb.toString();
    }
}