/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdParentDTO implements Serializable{

    private Integer systemId;
    private String orderNo;
    private String rootOrderNo;
    private String realOrderNo;
    private String orderId;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
