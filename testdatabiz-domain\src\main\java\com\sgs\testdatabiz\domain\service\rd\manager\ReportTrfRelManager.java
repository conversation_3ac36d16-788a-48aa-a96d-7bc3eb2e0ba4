package com.sgs.testdatabiz.domain.service.rd.manager;

import com.sgs.framework.model.enums.ActiveType;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportTrfRelMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelPO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * @author: shawn.yang
 * @create: 2023-04-21 18:24
 */
@Slf4j
@Component
public class ReportTrfRelManager {

    private final RdReportTrfRelMapper reportTrfRelMapper;

    @Autowired
    private ReportDataManager reportDataManager;

    @Autowired
    private IdService ID_GENERATOR;

    public void delete(Long labId, String reportNo, Integer systemId) {
        if (labId == null || reportNo == null || systemId == null) {
            return;
        }
        RdReportTrfRelExample deleteExample = new RdReportTrfRelExample();
        deleteExample.createCriteria()
                .andLabIdEqualTo(labId)
                .andReportNoEqualTo(reportNo).andOrderSystemIdEqualTo(systemId);
        reportTrfRelMapper.deleteByExample(deleteExample);
    }

    public List<RdReportTrfRelPO> selectByReportNoAndLabId(String reportNo, Long labId) {
        RdReportTrfRelExample example = new RdReportTrfRelExample();
        example.createCriteria()
                .andLabIdEqualTo(labId)
                .andReportNoEqualTo(reportNo);
        return reportTrfRelMapper.selectByExample(example);
    }


    public boolean batchSave(List<RdReportTrfRelPO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        delete(list.get(0).getLabId(), list.get(0).getReportNo(), list.get(0).getOrderSystemId());
        reportTrfRelMapper.batchInsert(list);
        return true;
    }

    public List<RdReportTrfRelPO> getByTrfNoAndLabId(Long labId, String trfNo) {
        RdReportTrfRelExample reportTrfRelExample = new RdReportTrfRelExample();
        reportTrfRelExample.createCriteria().andTrfNoEqualTo(trfNo).andLabIdEqualTo(labId).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportTrfRelMapper.selectByExample(reportTrfRelExample);
    }


    public List<RdReportTrfRelPO> getByTrfNoAndLabId(Long labId, List<String> trfNos) {
        RdReportTrfRelExample reportTrfRelExample = new RdReportTrfRelExample();
        reportTrfRelExample.createCriteria().andTrfNoIn(trfNos).andLabIdEqualTo(labId).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportTrfRelMapper.selectByExample(reportTrfRelExample);
    }


    public ReportTrfRelManager(RdReportTrfRelMapper reportTrfRelMapper) {
        this.reportTrfRelMapper = reportTrfRelMapper;
    }

    public List<RdReportTrfRelPO> getByOrderNoAndLabId(long labId, String orderNo) {
        RdReportTrfRelExample reportTrfRelExample = new RdReportTrfRelExample();
        reportTrfRelExample.createCriteria().andOrderNoEqualTo(orderNo).andLabIdEqualTo(labId).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportTrfRelMapper.selectByExample(reportTrfRelExample);
    }

//    public boolean importReportTrfRel(ReportDataDO reportDataDO) {

//        String reportNo = reportDataDO.getHeader().getReportNo();
//        Long buId = reportDataDO.getBuId();
//        Long labId = reportDataDO.getLabId();
//        RdOrderDO order = reportDataDO.getOrder();
//        if (Func.isEmpty(order)) {
//            log.error("order不能为空，reportNo：{}", reportNo);
//            return false;
//        }
//        Integer orderSystemId = order.getSystemId();
//        String orderNo = order.getOrderNo();
//
//        List<RdTrfDO> trfDOList = reportDataDO.getTrf();
//        if (Func.isEmpty(trfDOList)) {
//            log.error("trf不能为空，reportNo：{}", reportNo);
//            return false;
//        }
//        List<RdReportTrfRelPO> list = new ArrayList<>();
//        trfDOList.forEach(
//                trf -> {
//                    RdReportTrfRelPO reportTrfRelPO = new RdReportTrfRelPO();
//                    reportTrfRelPO.setId(ID_GENERATOR.nextId());
//                    reportTrfRelPO.setLabId(labId);
//                    reportTrfRelPO.setBuId(buId);
//                    reportTrfRelPO.setTrfRefSystemId(trf.getRefSystemId());
//                    reportTrfRelPO.setTrfNo(trf.getTrfNo());
//                    reportTrfRelPO.setOrderSystemId(orderSystemId);
//                    reportTrfRelPO.setOrderNo(orderNo);
//                    reportTrfRelPO.setReportNo(reportNo);
//                    reportTrfRelPO.setActiveIndicator(ActiveType.Enable.getStatus());
//                    reportTrfRelPO.setCreatedBy(DEFAULT_USER);
//                    reportTrfRelPO.setCreatedDate(DateUtils.getNow());
//                    reportTrfRelPO.setModifiedBy(DEFAULT_USER);
//                    reportTrfRelPO.setModifiedDate(DateUtils.getNow());
//                    list.add(reportTrfRelPO);
//                }
//        );
//        if (Func.isNotEmpty(list)) {
//            int count = reportTrfRelMapper.batchInsert(list);
//            if (count > 0) {
//                return true;
//            }
//        }
//
//        return false;
//    }
}
