<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestLineAnalyteExtMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.facade.model.rsp.TestLineAnalyteMappingRsp" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="testLineMappingId" property="testLineMappingId" jdbcType="INTEGER" />
        <result column="analyteCode" property="analyteCode" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, testLineMappingId, analyteCode, `status`, createdBy, createdDate, modifiedBy,
    modifiedDate
    </sql>

    <insert id="batchInsert">
        INSERT INTO `tb_test_line_analyte_mapping` (
            <include refid="Base_Column_List" />
        )
        VALUES
        <foreach collection="rels" item="rel" separator=",">
        (
            #{rel.id},
            #{rel.testLineMappingId},
            #{rel.analyteCode},
            #{rel.status},
            #{rel.createdBy},
            #{rel.createdDate},
            #{rel.modifiedBy},
            #{rel.modifiedDate}
        )
        </foreach>
        ON DUPLICATE KEY UPDATE
            analyteCode = VALUES(analyteCode),
            `status` = VALUES(status),
            modifiedBy = VALUES(modifiedBy),
            modifiedDate = VALUES(modifiedDate)
    </insert>

    <select id="getTestLineAnalyteMappingList" resultMap="BaseResultMap" >
        SELECT
            <include refid="Base_Column_List" />
        FROM tb_test_line_analyte_mapping
        WHERE testLineMappingId IN
        <foreach collection="testLineMappingIds" item="testLineMappingId" open="(" close=")" separator=",">
            ${testLineMappingId}
        </foreach>
        <if test="status != null">
            AND `status` = #{status}
        </if>
    </select>
</mapper>