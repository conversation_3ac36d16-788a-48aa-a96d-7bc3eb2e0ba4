package com.sgs.testdatabiz.sdk.convert.impl;

import com.sgs.testdatabiz.sdk.convert.DataConvertHandler;
import com.sgs.testdatabiz.sdk.input.dto.RdConvertRequestDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdReportDataDTO;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DataConvertV1Handler extends AbstractDataConvertHandler implements DataConvertHandler {

    @Override
    public void handler(RdConvertRequestDTO rdConvertRequestDTO) {
        super.handler(rdConvertRequestDTO);
    }

    @Override
    public List<String> acceptVersion() {
        List<String> versionList
                = new ArrayList<>();
        versionList.add("v1");

        return versionList;
    }
}
