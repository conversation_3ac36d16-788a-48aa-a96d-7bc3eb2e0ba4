package com.sgs.testdatabiz.domain.service.rd.manager;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportLangMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-04-23 14:21
 */
@Slf4j
@Component
public class ReportLangManager {

    private final RdReportLangMapper reportLangMapper;


    public void delete(Long rdReportId){
        if (rdReportId ==null ||rdReportId <=0){
            return;
        }
        RdReportLangExample langExample = new RdReportLangExample();
        langExample.createCriteria()
                .andRdReportIdEqualTo(rdReportId);
        reportLangMapper.deleteByExample(langExample);
    }

    public List<RdReportLangPO> getByReportId(Long reportId) {
        if (Func.isEmpty(reportId)) {
            return null;
        }
        RdReportLangExample rdReportLangExample = new RdReportLangExample();
        rdReportLangExample.createCriteria().andRdReportIdEqualTo(reportId);
        return reportLangMapper.selectByExample(rdReportLangExample);
    }


    public boolean batchSave(List<RdReportLangPO> list){
        if (CollectionUtils.isEmpty(list)){
            return true;
        }
        List<Long> rdReportId = list.stream().map(RdReportLangPO::getRdReportId).filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
        emptyData(rdReportId);

        return reportLangMapper.batchInsert(list) == list.size();
    }

    public void emptyData(List<Long> id) {
        RdReportLangExample example = new RdReportLangExample();
        example.createCriteria().andRdReportIdIn(id);
        reportLangMapper.deleteByExample(example);
    }


    public ReportLangManager(RdReportLangMapper reportLangMapper) {
        this.reportLangMapper = reportLangMapper;
    }
}
