/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProcessListDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdInvoiceDO {
    private Long rdInvoiceId;

    private Long labId;

    private String reportNo;

    private Long systemId;

    private String orderNo;
    private String realOrderNo;

    private String invoiceNo;
    private Date invoiceDate;
    private List<String> quotationNos;
    private String currency;
    private String bossOrderNo;
    private String productCode;
    private String costCenter;
    private String projectTemplate;
    private BigDecimal netAmount;
    private BigDecimal vatAmount;
    private BigDecimal totalAmount;
    // add 20230720
    private BigDecimal prePaidAmount;
    private Integer invoiceStatus;
    private List<RdAttachmentDO> invoiceFileList;

    private List<RdProcessListDTO> processList;

    private String sourceSystem;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String invoiceInstanceId;
}
