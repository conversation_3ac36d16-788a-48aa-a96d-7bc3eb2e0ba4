package com.sgs.testdatabiz.domain.service.validation.model;

import com.sgs.testdatabiz.core.errorcode.ErrorCode;

import lombok.Builder;
import lombok.Data;

/**
 * 校验结果数据传输对象
 */
@Data
@Builder
public class ValidationResultDTO {
    
    /**
     * 校验是否通过标志
     * true: 校验通过
     * false: 校验不通过
     */
    private boolean validFlag;
    
    /**
     * 校验结果代码
     * SUCCESS: 校验通过
     * FAIL: 校验失败
     */
    private String resultCode;
    
    /**
     * 校验结果消息
     * - 校验通过时的默认消息: "All validations passed"
     * - 校验失败时包含具体的错误信息
     */
    private String resultMessage;
    
    /**
     * 校验类型
     * 用于标识执行的是哪种类型的校验
     * 例如: REPORT_DATA, LAB_CODE, SYSTEM_ORDER等
     */
    private String validationType;
    
    /**
     * 校验目标系统ID
     * 用于标识校验针对的系统
     */
    private Integer systemId;
    
    /**
     * 创建成功的校验结果
     * @param message 成功消息
     * @return ValidationResultDTO
     */
    public static ValidationResultDTO success(String message) {
        return ValidationResultDTO.builder()
                .validFlag(true)
                .resultCode("SUCCESS")
                .resultMessage(message)
                .build();
    }

    /**
     * 创建失败的校验结果
     * @param message 失败消息
     * @return ValidationResultDTO
     */
    public static ValidationResultDTO fail(String message) {
        return ValidationResultDTO.builder()
                .validFlag(false)
                .resultCode("FAIL")
                .resultMessage(message)
                .build();
    }

    public static ValidationResultDTO fail(ErrorCode errorCode, String message) {
        return ValidationResultDTO.builder()
                .validFlag(false)
                .resultCode(errorCode.getCode())
                .resultMessage(message)
                .build();
    }
    
    /**
     * 创建带有类型和系统ID的校验结果
     * @param validFlag 校验标志
     * @param message 消息
     * @param validationType 校验类型
     * @param systemId 系统ID
     * @return ValidationResultDTO
     */
    public static ValidationResultDTO of(boolean validFlag, String message, 
            String validationType, Integer systemId) {
        return ValidationResultDTO.builder()
                .validFlag(validFlag)
                .resultCode(validFlag ? "SUCCESS" : "FAIL")
                .resultMessage(message)
                .validationType(validationType)
                .systemId(systemId)
                .build();
    }
}
