package com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataAndMatrixDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface TestDataInfoExtMapper {

    Integer batchInsert(@Param("testDatas") List<TestDataInfoPO> testDatas, @Param("suffix") String suffix);

    Integer batchInsertSlim(@Param("testDatas") List<TestDataInfoPO> testDatas);

    List<TestDataInfoPO> selectBySubcontractRelId(@Param("objectRelIds")List<String> objectRelIds, @Param("suffix") String suffix);

    Integer updateInvalidOrValid(@Param("objectRelIds") Set<String> objectRelIds,
                                 @Param("suffix") String suffix,
                                 @Param("modifiedBy") String modifiedBy);

    List<TestDataInfoPO> queryTestDataInfo(@Param("objectRelId")String objectRelId, @Param("suffix") String suffix);
    List<TestDataInfoPO> queryTestDataInfoByReportId(@Param("reportId")Long reportId, @Param("suffix")String suffix);
    List<TestDataInfoPO> queryTestDataInfoList(@Param("objectRelIds")List<String> objectRelIds, @Param("suffix") String suffix);

    /**
     *
     * @param testDatas
     * @param suffix
     * @return
     */
    Integer batchUpdate(@Param("testDatas")List<TestDataInfoPO> testDatas, @Param("suffix") String suffix);

    /**
     *
     * @param testDataId
     * @param suffix
     * @return
     */
    List<TestDataInfoPO> getTestDataInfoList(@Param("testDataId")Long testDataId, @Param("suffix") String suffix);

    /**
     *
     * @param objectRelId
     * @param ModifiedDate
     * @return
     */
    Integer delTestDataInvalid(@Param("objectRelId")String objectRelId, @Param("modifiedDate") Date ModifiedDate);

    List<TestDataInfoPO> queryTestDataByOrderNo(@Param("suffix") String suffix, @Param("orderNo") String orderNo);
    List<TestDataAndMatrixDTO> queryTestDataInfoByOrderNo(@Param("suffix") String suffix, @Param("orderNo") String orderNo);

    void emptyTestResultByReport(@Param("rdReportId") Long rdReportId, @Param("suffix") String suffix);

}
