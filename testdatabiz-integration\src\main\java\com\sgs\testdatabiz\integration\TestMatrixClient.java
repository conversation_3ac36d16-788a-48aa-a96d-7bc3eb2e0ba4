package com.sgs.testdatabiz.integration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractTestLineDTO;
import com.sgs.otsnotes.facade.model.req.matrix.SubContractTestMatrixReq;
import com.sgs.otsnotes.facade.model.rsp.matrix.SubContractTestMatrixRsp;
import com.sgs.testdatabiz.core.constant.ApiUrlConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Component
public class TestMatrixClient {
    private static final Logger logger = LoggerFactory.getLogger(TestMatrixClient.class);
    
    @Value("${api.base.otsnotes}")
    private String otsnotesBaseUrl;
    
    @Autowired
    private TokenClient tokenClient;

    /**
     *
     * @param subContractNo
     * @param productLineCode
     * @param labCode
     * @return
     */
    public List<SubContractTestMatrixRsp> getSubContractTestMatrixList(String subContractNo, String productLineCode, String labCode) {
        try {
            String url = otsnotesBaseUrl + ApiUrlConstants.OtsNotes.TestMatrix.GET_TEST_MATRIX_LIST;
            
            SubContractTestMatrixReq reqObject = new SubContractTestMatrixReq();
            reqObject.setSubContractNo(subContractNo);
            reqObject.setLabCode(labCode);
            reqObject.setProductLineCode(productLineCode);

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            String response = HttpClientUtil.postJsonHeader(url, reqObject, headers);
            
            if (Func.isEmpty(response)) {
                logger.error("TestMatrixClient.getSubContractTestMatrixList error, subContractNo:{}, response is empty", subContractNo);
                return Lists.newArrayList();
            }

            BaseResponse baseResponse = JSON.parseObject(response, new TypeReference<BaseResponse>(){});

            if (baseResponse.getStatus() != 200 || baseResponse.getData() == null) {
                logger.error("SubContractClient.getSubContractTestMatrixList error, response:{}", response);
                return Lists.newArrayList();
            }
            return JSON.parseArray(JSON.toJSONString(baseResponse.getData()), SubContractTestMatrixRsp.class);

        } catch (Exception e) {
            logger.error("TestMatrixClient.getSubContractTestMatrixList exception, subContractNo:{}", subContractNo, e);
            return Lists.newArrayList();
        }
    }
}
