package com.sgs.testdatabiz.facade.model.dto;

import com.sgs.framework.core.common.PrintFriendliness;

public class TestLineMappingDTO extends PrintFriendliness {
    /**
     * Id INTEGER(10) 必填<br>
     * ID,Primary key
     */
    private Integer id;

    /**
     * TestLineId INTEGER(10) 必填<br>
     */
    private Integer ppNo;

    /**
     * TestLineId INTEGER(10) 必填<br>
     */
    private Integer testLineId;

    /**
     * SlimCode VARCHAR(200)<br>
     */
    private String slimCode;

    /**
     * LabCode VARCHAR(64)<br>
     */
    private String labCode;

    /**
     * ProductLineCode VARCHAR(50) 必填<br>
     */
    private String productLineCode;

    /**
     * TestLineEvaluation VARCHAR(500)<br>
     */
    private String testLineEvaluation;

    /**
     * StandardVersionId INTEGER(10)<br>
     */
    private Integer standardVersionId;

    /**
     * StandardId INTEGER(10)<br>
     */
    private Integer standardId;

    /**
     * StandardName VARCHAR(500)<br>
     */
    private String standardName;

    /**
     * SystemId INTEGER(10)<br>
     * 1:slim   2:fast
     */
    private Integer systemId;

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Boolean activeIndicator;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPpNo() {
        return ppNo;
    }

    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public String getSlimCode() {
        return slimCode;
    }

    public void setSlimCode(String slimCode) {
        this.slimCode = slimCode;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getProductLineCode() {
        return productLineCode;
    }

    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode;
    }

    public String getTestLineEvaluation() {
        return testLineEvaluation;
    }

    public void setTestLineEvaluation(String testLineEvaluation) {
        this.testLineEvaluation = testLineEvaluation;
    }

    public Integer getStandardVersionId() {
        return standardVersionId;
    }

    public void setStandardVersionId(Integer standardVersionId) {
        this.standardVersionId = standardVersionId;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    public Boolean getActiveIndicator() {
        return activeIndicator;
    }

    public void setActiveIndicator(Boolean activeIndicator) {
        this.activeIndicator = activeIndicator;
    }
}