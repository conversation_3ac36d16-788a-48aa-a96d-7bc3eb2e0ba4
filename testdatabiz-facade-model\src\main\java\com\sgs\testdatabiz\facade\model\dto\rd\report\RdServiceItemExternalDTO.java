/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceItemExternalDTO implements Serializable {
    private String testItemId;
    private String testItemName;
    private Integer testCitationId;
    private String testCitationName;
    private List<RdLanguageDTO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
