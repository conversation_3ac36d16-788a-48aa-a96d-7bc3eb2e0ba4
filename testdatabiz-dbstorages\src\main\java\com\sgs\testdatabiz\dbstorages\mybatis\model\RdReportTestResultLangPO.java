package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdReportTestResultLangPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 数据唯一标识
     */
    private Long id;

    /**
     * rd_report_test_result_id BIGINT(19)<br>
     * RD Report Test Result标识
     */
    private Long rdReportTestResultId;

    /**
     * language_id INTEGER(10)<br>
     * 语言标识
     */
    private Integer languageId;

    /**
     * test_result_full_name VARCHAR(500)<br>
     * 测试项目名称，拼接字段
     */
    private String testResultFullName;

    /**
     * result_value_remark VARCHAR(500)<br>
     * 测试结果备注
     */
    private String resultValueRemark;

    /**
     * result_unit VARCHAR(1024)<br>
     * 测试结果单位
     */
    private String resultUnit;

    /**
     * limit_value_full_name VARCHAR(500)<br>
     * 参考标准，拼接字段
     */
    private String limitValueFullName;

    /**
     * limit_unit VARCHAR(4000)<br>
     * 参考标准单位
     */
    private String limitUnit;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 数据唯一标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 数据唯一标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * rd_report_test_result_id BIGINT(19)<br>
     * 获得 RD Report Test Result标识
     */
    public Long getRdReportTestResultId() {
        return rdReportTestResultId;
    }

    /**
     * rd_report_test_result_id BIGINT(19)<br>
     * 设置 RD Report Test Result标识
     */
    public void setRdReportTestResultId(Long rdReportTestResultId) {
        this.rdReportTestResultId = rdReportTestResultId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 获得 语言标识
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 设置 语言标识
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * test_result_full_name VARCHAR(500)<br>
     * 获得 测试项目名称，拼接字段
     */
    public String getTestResultFullName() {
        return testResultFullName;
    }

    /**
     * test_result_full_name VARCHAR(500)<br>
     * 设置 测试项目名称，拼接字段
     */
    public void setTestResultFullName(String testResultFullName) {
        this.testResultFullName = testResultFullName == null ? null : testResultFullName.trim();
    }

    /**
     * result_value_remark VARCHAR(500)<br>
     * 获得 测试结果备注
     */
    public String getResultValueRemark() {
        return resultValueRemark;
    }

    /**
     * result_value_remark VARCHAR(500)<br>
     * 设置 测试结果备注
     */
    public void setResultValueRemark(String resultValueRemark) {
        this.resultValueRemark = resultValueRemark == null ? null : resultValueRemark.trim();
    }

    /**
     * result_unit VARCHAR(1024)<br>
     * 获得 测试结果单位
     */
    public String getResultUnit() {
        return resultUnit;
    }

    /**
     * result_unit VARCHAR(1024)<br>
     * 设置 测试结果单位
     */
    public void setResultUnit(String resultUnit) {
        this.resultUnit = resultUnit == null ? null : resultUnit.trim();
    }

    /**
     * limit_value_full_name VARCHAR(500)<br>
     * 获得 参考标准，拼接字段
     */
    public String getLimitValueFullName() {
        return limitValueFullName;
    }

    /**
     * limit_value_full_name VARCHAR(500)<br>
     * 设置 参考标准，拼接字段
     */
    public void setLimitValueFullName(String limitValueFullName) {
        this.limitValueFullName = limitValueFullName == null ? null : limitValueFullName.trim();
    }

    /**
     * limit_unit VARCHAR(4000)<br>
     * 获得 参考标准单位
     */
    public String getLimitUnit() {
        return limitUnit;
    }

    /**
     * limit_unit VARCHAR(4000)<br>
     * 设置 参考标准单位
     */
    public void setLimitUnit(String limitUnit) {
        this.limitUnit = limitUnit == null ? null : limitUnit.trim();
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", rdReportTestResultId=").append(rdReportTestResultId);
        sb.append(", languageId=").append(languageId);
        sb.append(", testResultFullName=").append(testResultFullName);
        sb.append(", resultValueRemark=").append(resultValueRemark);
        sb.append(", resultUnit=").append(resultUnit);
        sb.append(", limitValueFullName=").append(limitValueFullName);
        sb.append(", limitUnit=").append(limitUnit);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append("]");
        return sb.toString();
    }
}