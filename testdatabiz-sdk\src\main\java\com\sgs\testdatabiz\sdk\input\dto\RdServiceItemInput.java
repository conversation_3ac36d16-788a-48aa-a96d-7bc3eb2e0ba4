/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceItemInput implements Serializable {
    private String serviceItemName;
    private Integer ppNo;
    private String ppName;
    private Integer testLineId;
    private Integer citationId;
    private Integer citationType;
    private String citationTypeLabel;
    private String citationName;
    private String evaluationAlias;
    private BigDecimal serviceItemListUnitPrice;
    private BigDecimal serviceItemSalesUnitPrice;
    private BigDecimal serviceItemDiscount;
    private Integer quantity;
    private BigDecimal serviceItemNetAmount;
    private BigDecimal serviceItemVATAmount;
    private BigDecimal serviceItemTotalAmount;
    private List<RdServiceItemLanguageInput> languageList;
    private RdServiceItemExternalDTO externalInfo;
    private String serviceItemInstanceId;
    private Date lastModifiedTimestamp;
    private String serviceItemType;
    private Integer serviceItemSeq;
    private Integer activeIndicator;
    //SCI-1469 5位小数，汇率字段
    private BigDecimal exchangeRate;
    //SCI-1469 2 位小数，额外追加多少费用
    private BigDecimal surCharge;
}
