package com.sgs.testdatabiz.facade;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.req.ConclusionInfoReq;
import com.sgs.testdatabiz.facade.model.rsp.ConclusionInfoRsp;

import java.util.List;

public interface IConclusionFacade {
    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse<List<ConclusionInfoRsp>> getConclusionInfoList(ConclusionInfoReq reqObject);

    BaseResponse<List<ConclusionInfoRsp>> getAllConclusionInfoList(ConclusionInfoReq reqObject);
}
