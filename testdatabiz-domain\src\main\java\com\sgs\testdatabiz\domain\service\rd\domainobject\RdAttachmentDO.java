/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdAttachmentDO {

    private String fileName;
    private String fileType;
    private Integer toCustomerFlag;
    private Integer objectType;
    private String objectId;
    private String objectNo;
    private String bizType;
    private String cloudId;
    private String filePath;
    private Integer languageId;
    private String reportNo;
    private String attachmentInstanceId;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private Date issuedDate;
}
