/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdAttachmentDTO implements Serializable{
    @ApiModelProperty(value = "fileName",dataType = "string", required = true)
    private String fileName;
    @ApiModelProperty(value = "fileType",dataType = "string", required = true)
    private String fileType;
    @ApiModelProperty(value = "toCustomerFlag",dataType = "integer", required = true)
    private Integer toCustomerFlag;
    private Integer objectType;
    private String objectNo;
    private String bizType;
    private String objectId;
    @ApiModelProperty(value = "cloudId",dataType = "string", required = true)
    private String cloudId;
    private String filePath;
    private Integer languageId;
    private String attachmentInstanceId;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private Date issuedDate;
}
