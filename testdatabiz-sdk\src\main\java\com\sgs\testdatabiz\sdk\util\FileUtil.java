package com.sgs.testdatabiz.sdk.util;

import java.io.File;
import java.io.FileInputStream;

public class FileUtil {
    public static String readFile(File file, String encoding) {
        try {
            byte[] buffer = new byte[(int) file.length()];
            FileInputStream fis = new FileInputStream(file);
            fis.read(buffer);
            fis.close();
            return new String(buffer, encoding);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
