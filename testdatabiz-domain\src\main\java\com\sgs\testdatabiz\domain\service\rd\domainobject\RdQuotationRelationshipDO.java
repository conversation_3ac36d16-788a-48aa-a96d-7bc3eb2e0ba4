/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationRelationshipDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
public class RdQuotationRelationshipDO implements Serializable {

    private RdQuotationRelationshipDO.RdQuotationRelParentDO parent;

    private RdQuotationRelationshipDO.RdQuotationRelParallelDO parallel;

    private RdQuotationRelationshipDO.RdQuotationRelChildrenDO children;

    @Data
    public static class RdQuotationRelParentDO implements Serializable {

        private RdQuotationRelParentOrderDO order;

        private List<RdQuotationRelParentReportDO> reportList;

        @Data
        public static class RdQuotationRelParentOrderDO implements Serializable {
            private String orderId;

            private String orderNo;
            private String rootOrderNo;
        }

        @Data
        public static class RdQuotationRelParentReportDO implements Serializable{

            private String reportId;

            private String reportNo;
        }
    }

    @Data
    public static class RdQuotationRelParallelDO implements Serializable {

        private List<RdQuotationRelParallelOrderDO> bossOrderList;

        @Data
        public static class RdQuotationRelParallelOrderDO implements Serializable {
            private String bossOrderNo;

            private String refBossOrderNo;
        }
    }

    @Data
    public static class RdQuotationRelChildrenDO implements Serializable {

        private List<RdQuotationRelInvoiceDO> invoiceList;

        @Data
        public static class RdQuotationRelInvoiceDO implements Serializable {
            private String invoiceId;
        }
    }
}
