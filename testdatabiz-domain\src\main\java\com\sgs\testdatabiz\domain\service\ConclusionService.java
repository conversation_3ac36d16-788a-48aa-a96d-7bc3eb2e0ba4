package com.sgs.testdatabiz.domain.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestResultLanguageDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataAnalyteConclusionInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataConclusionInfoPO;
import com.sgs.testdatabiz.domain.service.rd.ReportDataService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdCitationDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdPpTestLineRelDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdReportDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdReportMatrixDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestLineDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestSampleDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.domain.service.validation.type.systemid.SystemIdValidationService;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataMatrixLangInfo;
import com.sgs.testdatabiz.facade.model.req.ConclusionInfoReq;
import com.sgs.testdatabiz.facade.model.rsp.ConclusionInfoRsp;
import com.sgs.testdatabiz.facade.model.rsp.conclusion.AnalyteConclusionInfo;
import com.sgs.testdatabiz.facade.model.rsp.conclusion.ConclusionLangInfo;

@Service
public class ConclusionService {
    private static final Logger logger = LoggerFactory.getLogger(ConclusionService.class);
    @Autowired
    private TestDataMatrixInfoExtMapper testDataReportMatrixExtMapper;
    @Autowired
    private ReportDataService reportDataService;
    @Autowired
    private SystemIdValidationService systemIdValidationService;
    /**
     * 物料名称
     */
    private static final String MATERIAL_NAME = "materialName";
    private static final String LANGUAGE_ID = "languageId";
    private static final String LANGUAGES = "languages";
    /**
     * @param reqObject
     * @return
     */
    public CustomResult<List<ConclusionInfoRsp>> getConclusionInfoList(ConclusionInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(reqObject.getOrderNo())) {
            return rspResult.fail("订单号不能为空");
        }
        // 1.处理LabCode（GZ SL）转SL_GZ
        String suffix = StringUtil.getTestDataSuffix(reqObject.getLabCode());
        if (StringUtils.isBlank(suffix)) {
            return rspResult.fail("Lab Code不能为空或无效.");
        }

        List<TestDataConclusionInfoPO> conclusionsList = testDataReportMatrixExtMapper.getConclusionInfoList(reqObject.getOrderNo(), reqObject.getSourceTypes(), suffix);
        List<ConclusionInfoRsp> conclusions =convertConclusionInfo(conclusionsList,suffix);

        rspResult.setData(conclusions);
        rspResult.setSuccess(true);

        return rspResult;
    }

    private List<ConclusionInfoRsp> convertConclusionInfo(List<TestDataConclusionInfoPO> conclusionsList,String suffix) {
        List<ConclusionInfoRsp> conclusions = new ArrayList<>();
        if(Func.isNotEmpty(conclusionsList)&& !conclusionsList.isEmpty()){
            // 1. 转换结论信息
             conclusionsList.forEach(conclusion -> {
                ConclusionInfoRsp conclusionInfoRsp = new ConclusionInfoRsp();
                BeanUtils.copyProperties(conclusion, conclusionInfoRsp);
                convertConclusionLanguage(conclusion, conclusionInfoRsp);
                 // 解析extFields中的JSON，获取materialName并设置到sampleDescription
                 setConclusionSampleDescriptin(conclusion, conclusionInfoRsp);
                // 2.根据conclusion的testDataMatrixId获取对应的AnalyteConclusionInfo结论信息
                List<TestDataAnalyteConclusionInfoPO> analyteConclusionInfoList = testDataReportMatrixExtMapper.getTestDataAnalyteConclusionInfoList(conclusion.getTestDataMatrixId(),suffix);
                // 3. 转换AnalyteConclusionInfo结论信息 
                conclusionInfoRsp.setAnalyteConclusionList(convertAnalyteConclusion(analyteConclusionInfoList));
                conclusions.add(conclusionInfoRsp);
            });
            
        }
        return conclusions;
    }

    private void setConclusionSampleDescriptin(TestDataConclusionInfoPO conclusion, ConclusionInfoRsp conclusionInfoRsp) {
        try {
            String extFields = conclusion.getExtFields();
            Integer languageType = conclusion.getLanguageType();

            if (Func.isNotEmpty(extFields)&& Func.isNotEmpty(languageType)) {

                JSONObject jsonObject = JSONObject.parseObject(extFields);
                String materialName = null;
                // 根据语言类型选择不同的解析方式
                if (Objects.equals(LanguageType.Chinese.getLanguageId(), languageType)) {
                    // 中文模式：从 languages 对象中获取中文的 materialName
                    materialName = getMaterialNameForChinese(jsonObject);
                } else {
                    // 英文模式：直接从根节点获取 materialName
                    materialName = jsonObject.getString(MATERIAL_NAME);
                }
                conclusionInfoRsp.setSampleDescription(materialName);
            }
        }
        catch (Exception e) {
            // 解析JSON出错或materialName不存在时不做处理
            logger.error("No materialName found in extFields or parsing error,reportNo={},",conclusion.getReportNo(),e);
        }
    }
    /**
     * 从 JSON 对象中获取中文的 materialName
     * 例如：{"languages":[{"languageId":2,"materialName":"TTTTT"}]}
     * @param jsonObject JSON 对象
     * @return 中文 materialName
     */
    /**
     * 从JSON对象中获取中文的materialName
     * 例如：{"languages":[{"languageId":2,"materialName":"TTTTT"}]}
     * @param jsonObject JSON对象
     * @return 中文materialName,如果未找到则返回null
     */
    private String getMaterialNameForChinese(JSONObject jsonObject) {
        if (jsonObject == null || !jsonObject.containsKey(LANGUAGES)) {
            return null;
        }

        // 获取languages数组
        JSONArray languagesArray = jsonObject.getJSONArray(LANGUAGES);
        if (languagesArray == null || languagesArray.isEmpty()) {
            return null;
        }

        // 遍历languages数组查找中文的materialName
        for (int i = 0; i < languagesArray.size(); i++) {
            JSONObject langObj = languagesArray.getJSONObject(i);
            // 判断是否为中文语言ID
            if (langObj.getIntValue(LANGUAGE_ID) == LanguageType.Chinese.getLanguageId()) {
                return langObj.getString(MATERIAL_NAME);
            }
        }
        return null;
    }
    /**
     * 转换AnalyteConclusionInfo结论信息
     * 只处理analyteConclusionInfo中analyteConclusionId不为空或者analyteId不为空的数据
     * @param analyteConclusionInfoList
     * @return
     */
    private List<AnalyteConclusionInfo> convertAnalyteConclusion(List<TestDataAnalyteConclusionInfoPO> analyteConclusionInfoList){
        List<AnalyteConclusionInfo> analyteConclusions = new ArrayList<>();
        // 只处理analyteConclusionInfo中analyteConclusionId不为空或者analyteId不为空的数据
        if (Func.isNotEmpty(analyteConclusionInfoList)) {
            analyteConclusionInfoList.stream()
                    .filter(info -> Func.isNotEmpty(info.getAnalyteConclusionId()) || Func.isNotEmpty(info.getAnalyteId()))
                    .forEach(info -> {
                        // 转换AnalyteConclusionInfo对象
                        AnalyteConclusionInfo analyteConclusionInfo = new AnalyteConclusionInfo();
                        analyteConclusionInfo.setAnalyteConclusionId(info.getAnalyteConclusionId());
                        analyteConclusionInfo.setAnalyteId(info.getAnalyteId());
                        buildAnalyteName(info, analyteConclusionInfo);
                        analyteConclusions.add(analyteConclusionInfo);
                    });
        }
        return analyteConclusions;
    }

    private static void buildAnalyteName(TestDataAnalyteConclusionInfoPO info, AnalyteConclusionInfo analyteConclusionInfo) {
    if (Func.isNotEmpty(info.getAnalyteName())) {
        analyteConclusionInfo.setAnalyteName(info.getAnalyteName());
        return;
    }

    String languages = info.getLanguages();
    if (Func.isEmpty(languages)) {
        return;
    }

    try {
        List<RdTestResultLanguageDTO> languagesList = JSON.parseArray(languages, RdTestResultLanguageDTO.class);
        languagesList.stream()
                .filter(lang -> LanguageType.Chinese.getLanguageId().equals(lang.getLanguageId()))
                .map(RdTestResultLanguageDTO::getTestAnalyteName)
                .findFirst()
                .ifPresent(analyteConclusionInfo::setAnalyteName);
    } catch (Exception e) {
        // 可选：记录日志
        logger.error("Error parsing languages JSON: " + e.getMessage());
    }
}



    private static void convertConclusionLanguage(TestDataConclusionInfoPO conclusion, ConclusionInfoRsp conclusionInfoRsp) {
        String languages = conclusion.getLanguages();
        if(Func.isNotEmpty(languages)){
            List<TestDataMatrixLangInfo> conclusionLanguageInfoList = JSON.parseArray(languages, TestDataMatrixLangInfo.class);
            if(Func.isNotEmpty(conclusionLanguageInfoList)){
                setConclusionLangInfoList(conclusionLanguageInfoList, conclusionInfoRsp);
            }
        }
    }

    private static void setConclusionLangInfoList(List<TestDataMatrixLangInfo> conclusionLanguageInfoList, ConclusionInfoRsp conclusionInfoRsp) {
        List<ConclusionLangInfo> conclusionLangInfoList = new ArrayList<>();
        conclusionLanguageInfoList.forEach(conclusionLangInfo -> {
            ConclusionLangInfo conclusionLangInfoRsp = new ConclusionLangInfo();
            BeanUtils.copyProperties(conclusionLangInfo, conclusionLangInfoRsp);
            conclusionLangInfoList.add(conclusionLangInfoRsp);
        });
        conclusionInfoRsp.setLanguageList(conclusionLangInfoList);
    }

    /**
     * @param reqObject
     * @return
     */
    public CustomResult<List<ConclusionInfoRsp>> getAllConclusionInfoList(ConclusionInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(reqObject.getOrderNo())) {
            return rspResult.fail("订单号不能为空");
        }
        // 系统ID校验
        ValidationRequestDTO validationRequestDTO = new ValidationRequestDTO();
        validationRequestDTO.setSystemId(reqObject.getSystemId() != null ? reqObject.getSystemId() : null);
        ValidationResultDTO validationResultDTO = systemIdValidationService.validate(validationRequestDTO);
        if (!validationResultDTO.isValidFlag()) {
            return rspResult.fail(validationResultDTO.getResultMessage());
        }
        // 1.处理LabCode（GZ SL）转SL_GZ
        String suffix = StringUtil.getTestDataSuffix(reqObject.getLabCode());
        if (StringUtils.isBlank(suffix)) {
            return rspResult.fail("Lab Code不能为空或无效.");
        }
        List<TestDataConclusionInfoPO> conclusionList = testDataReportMatrixExtMapper.getAllConclusionInfoList(reqObject.getOrderNo(), reqObject.getSourceTypes(), suffix);

        List<ConclusionInfoRsp> conclusions =convertConclusionInfo(conclusionList,suffix);
        // 2024-11-24 下午 getAllConclusionInfoList接口去掉 convertMainReportData 逻辑
        /*if (Func.isEmpty(conclusions)) {
            conclusions = new ArrayList<>();
        }
        List<ReportDataDO> reportList = reportDataService.queryReportByOrderNo(reqObject.getOrderNo(), reqObject.getLabCode());
        List<ConclusionInfoRsp> conclusionInfoRsps = convertMainReportData(conclusions, reportList);*/
        rspResult.setData(conclusions);
        rspResult.setSuccess(true);

        return rspResult;
    }

    private List<ConclusionInfoRsp> convertMainReportData(List<ConclusionInfoRsp> list, List<ReportDataDO> mainReportList) {
        if (Func.isEmpty(list)) {
            list = new ArrayList<>();
        }
        if (Func.isEmpty(mainReportList)) {
            return list;
        }
        // 使用 Map 存储 testSampleList，以便快速查找
        Map<String, RdTestSampleDO> testSampleMap = new HashMap<>();
        for (ReportDataDO mainReport : mainReportList) {
            List<RdTestSampleDO> testSampleList = mainReport.getTestSampleList();
            if (Func.isNotEmpty(testSampleList)) {
                testSampleMap.putAll(testSampleList.stream().collect(Collectors.toMap(RdTestSampleDO::getTestSampleInstanceId, sample -> sample)));
            }
        }
        for (ReportDataDO mainReport : mainReportList) {
            RdReportDO header = mainReport.getHeader();
            if (header == null) {
                continue;
            }
            String reportNo = header.getReportNo();
            List<RdTestLineDO> testLineList = mainReport.getTestLineList();
            if (Func.isEmpty(testLineList)) {
                testLineList = new ArrayList<>();
            }
            if (Func.isNotEmpty(header.getReportMatrixList())) {
                for (RdReportMatrixDO matrix : header.getReportMatrixList()) {
                    String testLineInstanceId = matrix.getTestLineInstanceId();
                    String testSampleInstanceId = matrix.getTestSampleInstanceId();
                    RdTestSampleDO rdTestSampleDO = testSampleMap.getOrDefault(testSampleInstanceId, new RdTestSampleDO());
                    try {
                        Integer conclusionCode = matrix.getConclusion() != null ? Func.toInt(matrix.getConclusion().getConclusionCode()) : null;
                        getConclusionInfoByTestLine(list, testLineList, testLineInstanceId, reportNo, matrix.getTestMatrixId(), rdTestSampleDO.getTestSampleInstanceId(), rdTestSampleDO.getTestSampleNo(), conclusionCode);
                    } catch (NumberFormatException e) {
                        // 处理 Func.toInt 转换失败的情况
                        getConclusionInfoByTestLine(list, testLineList, testLineInstanceId, reportNo, matrix.getTestMatrixId(), rdTestSampleDO.getTestSampleInstanceId(), rdTestSampleDO.getTestSampleNo(), null);
                    }
                }
            } else {
                ConclusionInfoRsp conclusionInfoRsp = new ConclusionInfoRsp();
                conclusionInfoRsp.setReportNo(reportNo);
                list.add(conclusionInfoRsp);
            }
        }
        return list;
    }

    private void getConclusionInfoByTestLine(List<ConclusionInfoRsp> list, List<RdTestLineDO> testLineDTOS, String testInstanceId, String reportNo, String testMatrixId, String sampleId, String sampleNo, Integer conclusionId) {
        if (Func.isEmpty(testLineDTOS)) {
            return;
        }
        RdTestLineDO rdTestLineDTO = testLineDTOS.stream().filter(
                testLine -> Objects.equals(testLine.getTestLineInstanceId(), testInstanceId)
        ).findFirst().orElse(new RdTestLineDO());
        List<RdPpTestLineRelDO> ppTestLineRelList = rdTestLineDTO.getPpTestLineRelList();
        RdCitationDO citation = rdTestLineDTO.getCitation();
        if (Func.isEmpty(citation)) {
            citation = new RdCitationDO();
        }
        if (Func.isEmpty(ppTestLineRelList)) {
            ConclusionInfoRsp conclusionInfoRsp = new ConclusionInfoRsp();
            conclusionInfoRsp.setReportNo(reportNo);
            conclusionInfoRsp.setTestMatrixId(testMatrixId);
            conclusionInfoRsp.setTestLineId(rdTestLineDTO.getTestLineId());
            conclusionInfoRsp.setCitationId(citation.getCitationId());
            conclusionInfoRsp.setCitationVersionId(citation.getCitationVersionId());
            conclusionInfoRsp.setCitationType(citation.getCitationType());
            conclusionInfoRsp.setSampleId(sampleId);
            conclusionInfoRsp.setSampleNo(sampleNo);
            conclusionInfoRsp.setConclusionId(conclusionId);
            list.add(conclusionInfoRsp);
        } else {
            RdCitationDO finalCitation = citation;
            ppTestLineRelList.forEach(
                    ppTestLineRel -> {
                        ConclusionInfoRsp conclusionInfoRsp = new ConclusionInfoRsp();
                        conclusionInfoRsp.setReportNo(reportNo);
                        conclusionInfoRsp.setTestMatrixId(testMatrixId);
                        conclusionInfoRsp.setPpVersionId(ppTestLineRel.getPpVersionId());
                        conclusionInfoRsp.setAid(ppTestLineRel.getAid());
                        conclusionInfoRsp.setTestLineId(rdTestLineDTO.getTestLineId());
                        conclusionInfoRsp.setCitationId(finalCitation.getCitationId());
                        conclusionInfoRsp.setCitationVersionId(finalCitation.getCitationVersionId());
                        conclusionInfoRsp.setCitationType(finalCitation.getCitationType());
                        conclusionInfoRsp.setSampleId(sampleId);
                        conclusionInfoRsp.setSampleNo(sampleNo);
                        conclusionInfoRsp.setConclusionId(conclusionId);
                        list.add(conclusionInfoRsp);
                    }
            );
        }
    }
}
