package com.sgs.testdatabiz.domain.service.utils;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.testdata.enums.ErrMsgFormatter;
import com.sgs.testdatabiz.domain.service.testdata.model.ErrorMsg;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sgs.testdatabiz.domain.service.constant.MixedConstants.BR_LINE_BREAK;

/**
 * @author: shawn.yang
 * @create: 2023-04-03 15:53
 */
public final class ErrorMsgUtils {

    /**
     *
     * @param sourceType
     * @param errorMsgList
     * @return
     */
    public static String defaultFormat(SourceTypeEnum sourceType, Collection<ErrorMsg> errorMsgList) {
        if (errorMsgList.isEmpty() || sourceType ==null) {
            return null;
        }
        // 按errCode分组
        Map<String, List<ErrorMsg>> groupByCodeErrMsg = errorMsgList.stream().collect(Collectors.groupingBy(msg -> StringUtils.defaultString(msg.getErrCode())));
        return groupByCodeErrMsg.entrySet().stream().map(e -> {
            String errCode = e.getKey();
            // 对ErrorMsg 中的 msg做拼接
            // 规则 String.join("<br/>",msg1,msg2,...)
            Map<String, Set<String>> errorMaps = Maps.newConcurrentMap();
            e.getValue().forEach(error->{
                String groupCode = StringUtils.defaultString(error.getGroupCode());
                if (!errorMaps.containsKey(groupCode)){
                    errorMaps.put(groupCode, Sets.newHashSet());
                }
                errorMaps.get(groupCode).add(StringUtils.defaultString(error.getMsg()));
            });
            // 根据code找不到formatter，返回 errorMsg + "<br/>"
            ErrMsgFormatter formatter = ErrMsgFormatter.findByCode(errCode);
            if (formatter == null) {
                return BR_LINE_BREAK;
            }
            StringBuffer append = new StringBuffer();
            errorMaps.entrySet().forEach(error->{
                append.append(formatter.format(error.getKey(), StringUtils.join(error.getValue(), "，")));
                append.append(BR_LINE_BREAK);
            });
            // title + "<br/>" + errorMsgs + "<br/>"
            return String.join(BR_LINE_BREAK, StringUtils.defaultString(formatter.getTitle(sourceType)), append.toString(), BR_LINE_BREAK);
        }).collect(Collectors.joining(BR_LINE_BREAK));
    }

}
