/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RdProcessDTO implements Serializable{

    private String nodePoint;
    @ApiModelProperty(value = "operationTime",dataType = "date", required = true)
    private Date operationTime;
    @ApiModelProperty(value = "operator",dataType = "string", required = true)
    private String operator;

    private String remark;

}
