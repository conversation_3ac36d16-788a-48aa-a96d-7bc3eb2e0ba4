package com.sgs.testdatabiz.integration;

import com.alibaba.fastjson.JSON;
import com.sgs.testdatabiz.core.util.HttpClientUtil;
import com.sgs.testdatabiz.integration.config.URLConfiguration;
import com.sgs.testdatabiz.integration.model.email.EMailRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 邮件发送工具
 */
@Component
public class EmailClient {
    private static final Logger logger = LoggerFactory.getLogger(EmailClient.class);
    @Autowired
    private URLConfiguration URLConfiguration;

    /**
     *
     * @param mailInfo
     * @return
     */
    public boolean sendEmail(EMailRequest mailInfo){
        if(mailInfo == null || StringUtils.isBlank(mailInfo.getMailSubject()) || CollectionUtils.isEmpty(mailInfo.getMailTo())){
            logger.error("SendEmail SEND_EMAIL_FAIL ,MailSubject or MailTo can't be empty. [{}]", mailInfo);
            return false;
        }
        String sendEmailUrl = String.format("%s/notification/sendMail", URLConfiguration.getNotificationUrl());
        try {
            logger.info("SendEmail({})，===调用邮件发送接口: URL:{}", mailInfo,sendEmailUrl);
            String response = HttpClientUtil.postJson(sendEmailUrl, JSON.toJSONString(mailInfo));
            logger.info("SendEmail({})，===邮件发送接口返回值：{}", mailInfo , response);

            boolean sendFailed = !StringUtils.equalsIgnoreCase(response, "true");
            if (sendFailed) {
                logger.error("SendEmail({})，SEND_EMAIL_FAIL ,[{}]", "发送邮件失败，请联系管理员...", mailInfo);
                return false;
            }
            return true;
        } catch (Exception ex) {
            logger.error("SendEmail({})，===调用邮件发送异常，{}", mailInfo, ex);
        }
        return false;
    }
}
