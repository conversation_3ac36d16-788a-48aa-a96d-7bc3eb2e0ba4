CREATE TABLE `tb_api_request` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `system_id` int(11) NOT NULL COMMENT '调用方系统id',
                                  `lab_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'lab代码',
                                  `request_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求id',
                                  `request_header` json DEFAULT NULL COMMENT '请求头',
                                  `request_body` json DEFAULT NULL COMMENT '请求body',
                                  `response_body` json DEFAULT NULL COMMENT '响应body',
                                  `response_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '响应状态码',
                                  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                  `created_date` datetime NOT NULL COMMENT '创建时间',
                                  `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
                                  `modified_date` datetime NOT NULL COMMENT '修改时间',
                                  `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_last_modified_timestamp` (`last_modified_timestamp`) USING BTREE,
                                  KEY `idx_system_lab` (`system_id`,`lab_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=63276834926813185 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
