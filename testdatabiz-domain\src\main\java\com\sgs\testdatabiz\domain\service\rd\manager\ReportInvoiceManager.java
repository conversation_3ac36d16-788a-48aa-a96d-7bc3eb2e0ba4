package com.sgs.testdatabiz.domain.service.rd.manager;

import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdQuotationInvoiceRelMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportInvoiceMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.*;
import com.sgs.testdatabiz.domain.service.rd.assembler.ReportDataAssembler;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdInvoiceDO;
import com.sgs.testdatabiz.facade.model.enums.AttachmentObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: shawn.qiWen_xue
 * @create: 2023-04-27 14:21
 */
@Slf4j
@Component
public class ReportInvoiceManager {

    private final RdReportInvoiceMapper reportInvoiceMapper;

    private final RdQuotationInvoiceRelMapper quotationInvoiceRelMapper;

    private final AttachmentManager attachmentManager;

    @Autowired
    private ReportDataAssembler reportDataAssembler;


    public boolean batchInsert(List<RdReportInvoicePO> list) {
        int i = reportInvoiceMapper.batchInsert(list);
        if (i > 0) {
            return true;
        }
        return false;
    }

    public List<RdReportInvoicePO> selectReportInvoice(Long labId, Long rdReportId) {
        RdReportInvoiceExample invoiceExample = new RdReportInvoiceExample();
        invoiceExample.createCriteria().andLabIdEqualTo(labId).andRdReportIdEqualTo(rdReportId);
        return reportInvoiceMapper.selectByExample(invoiceExample);
    }

    public List<RdReportInvoicePO> selectReportInvoice(Long labId, List<String> invoiceNos) {
        RdReportInvoiceExample invoiceExample = new RdReportInvoiceExample();
        invoiceExample.createCriteria().andLabIdEqualTo(labId).andInvoiceNoIn(invoiceNos).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportInvoiceMapper.selectByExample(invoiceExample);
    }

    public List<RdReportInvoicePO> selectReportInvoiceByReportNos(Long labId, List<String> reportNos) {
        RdReportInvoiceExample invoiceExample = new RdReportInvoiceExample();
        invoiceExample.createCriteria().andLabIdEqualTo(labId).andReportNoIn(reportNos).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportInvoiceMapper.selectByExample(invoiceExample);
    }

    public List<RdReportInvoicePO> selectReportInvoice(Long rdReportId) {
        RdReportInvoiceExample invoiceExample = new RdReportInvoiceExample();
        invoiceExample.createCriteria().andRdReportIdEqualTo(rdReportId).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportInvoiceMapper.selectByExample(invoiceExample);
    }

    public Map<Long, List<RdInvoiceDO>> exportInvoiceList(List<Long> reportIds) {
        Map<Long, List<RdInvoiceDO>> map = new HashMap<>();
        reportIds.forEach(
                reportId -> {
                    List<RdReportInvoicePO> reportInvoicePOList = this.selectReportInvoice(reportId);
                    if (Func.isNotEmpty(reportInvoicePOList)) {
                        Map<String, List<RdAttachmentPO>> reportFileMap = new HashMap<>();
                        if (Func.isNotEmpty(reportInvoicePOList)) {
                            List<String> invoiceNos = reportInvoicePOList.stream().map(RdReportInvoicePO::getInvoiceNo).collect(Collectors.toList());
                            List<RdAttachmentPO> invoiceFiles = attachmentManager.getByObjectNosAndType(invoiceNos, AttachmentObjectTypeEnum.INVOICE.getCode());
                            if (Func.isNotEmpty(invoiceFiles)) {
                                reportFileMap = invoiceFiles.stream().collect(Collectors.groupingBy(RdAttachmentPO::getObjectNo));
                            }
                        }
                        List<RdInvoiceDO> rdInvoiceDOS = reportDataAssembler.assemblerReportInvoiceDO(reportInvoicePOList, reportFileMap);
                        map.put(reportId, rdInvoiceDOS);
                    }
                }
        );
        return map;
    }

    public List<RdInvoiceDO> exportInvoiceList(Long reportId) {
        List<RdReportInvoicePO> reportInvoicePOList = this.selectReportInvoice(reportId);
        if (Func.isNotEmpty(reportInvoicePOList)) {
            Map<String, List<RdAttachmentPO>> reportFileMap = new HashMap<>();
            if (Func.isNotEmpty(reportInvoicePOList)) {
                List<String> invoiceNos = reportInvoicePOList.stream().map(RdReportInvoicePO::getInvoiceNo).collect(Collectors.toList());
                List<RdAttachmentPO> invoiceFiles = attachmentManager.getByObjectNosAndType(invoiceNos, AttachmentObjectTypeEnum.INVOICE.getCode());
                if (Func.isNotEmpty(invoiceFiles)) {
                    reportFileMap = invoiceFiles.stream().collect(Collectors.groupingBy(RdAttachmentPO::getObjectNo));
                }
            }
            return reportDataAssembler.assemblerReportInvoiceDO(reportInvoicePOList, reportFileMap);
        }
        return null;
    }

    public List<RdReportInvoicePO> selectReportInvoice(Long systemId, String reportNo) {
        RdReportInvoiceExample invoiceExample = new RdReportInvoiceExample();
        invoiceExample.createCriteria().andSystemIdEqualTo(systemId).andReportNoEqualTo(reportNo).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportInvoiceMapper.selectByExample(invoiceExample);
    }

    public List<RdReportInvoicePO> selectReportInvoice(String reportNo, Long labId) {
        RdReportInvoiceExample invoiceExample = new RdReportInvoiceExample();
        invoiceExample.createCriteria().andLabIdEqualTo(labId).andReportNoEqualTo(reportNo).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return reportInvoiceMapper.selectByExample(invoiceExample);
    }


    public ReportInvoiceManager(RdReportInvoiceMapper reportInvoiceMapper, RdQuotationInvoiceRelMapper quotationInvoiceRelMapper, AttachmentManager attachmentManager) {
        this.reportInvoiceMapper = reportInvoiceMapper;
        this.quotationInvoiceRelMapper = quotationInvoiceRelMapper;
        this.attachmentManager = attachmentManager;

    }

    public boolean batchInsertQuotationInvoiceRel(List<RdQuotationInvoiceRelPO> quotationInvoiceRelPOList) {
        int count = quotationInvoiceRelMapper.batchInsert(quotationInvoiceRelPOList);
        if (count > 0) {
            return true;
        }
        return false;
    }

    public void deleteInvoiceData(Long systemId, String reportNo) {
        List<RdReportInvoicePO> reportInvoicePOList = selectReportInvoice(systemId, reportNo);

        if (Func.isNotEmpty(reportInvoicePOList)) {
            List<String> invoiceNos = reportInvoicePOList.stream().map(RdReportInvoicePO::getInvoiceNo).collect(Collectors.toList());

            attachmentManager.deleteFiles(invoiceNos, systemId, AttachmentObjectTypeEnum.INVOICE.getCode());

            RdQuotationInvoiceRelExample quotationInvoiceRelExample = new RdQuotationInvoiceRelExample();
            quotationInvoiceRelExample.createCriteria().andInvoiceNoIn(invoiceNos).andSystemIdEqualTo(systemId);
            quotationInvoiceRelMapper.deleteByExample(quotationInvoiceRelExample);
        }

        RdReportInvoiceExample rdReportInvoiceExample = new RdReportInvoiceExample();
        rdReportInvoiceExample.createCriteria().andReportNoEqualTo(reportNo).andSystemIdEqualTo(systemId);
        reportInvoiceMapper.deleteByExample(rdReportInvoiceExample);
    }
}
