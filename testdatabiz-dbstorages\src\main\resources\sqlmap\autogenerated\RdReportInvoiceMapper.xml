<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportInvoiceMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoicePO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="rd_report_id" property="rdReportId" jdbcType="BIGINT" />
    <result column="system_id" property="systemId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="boss_order_no" property="bossOrderNo" jdbcType="VARCHAR" />
    <result column="product_code" property="productCode" jdbcType="VARCHAR" />
    <result column="cost_center" property="costCenter" jdbcType="VARCHAR" />
    <result column="project_template" property="projectTemplate" jdbcType="VARCHAR" />
    <result column="invoice_date" property="invoiceDate" jdbcType="TIMESTAMP" />
    <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
    <result column="currency" property="currency" jdbcType="VARCHAR" />
    <result column="net_amount" property="netAmount" jdbcType="DECIMAL" />
    <result column="vat_amount" property="vatAmount" jdbcType="DECIMAL" />
    <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
    <result column="pre_paid_amount" property="prePaidAmount" jdbcType="DECIMAL" />
    <result column="invoice_status" property="invoiceStatus" jdbcType="INTEGER" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="last_modified_date" property="lastModifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, lab_id, rd_report_id, system_id, order_no, report_no, boss_order_no, product_code, 
    cost_center, project_template, invoice_date, invoice_no, currency, net_amount, vat_amount, 
    total_amount, pre_paid_amount, invoice_status, active_indicator, created_by, created_date, 
    modified_by, modified_date, last_modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoiceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_invoice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_invoice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoiceExample" >
    delete from tb_report_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoicePO" >
    insert into tb_report_invoice (id, lab_id, rd_report_id, 
      system_id, order_no, report_no, 
      boss_order_no, product_code, cost_center, 
      project_template, invoice_date, invoice_no, 
      currency, net_amount, vat_amount, 
      total_amount, pre_paid_amount, invoice_status, 
      active_indicator, created_by, created_date, 
      modified_by, modified_date, last_modified_date
      )
    values (#{id,jdbcType=BIGINT}, #{labId,jdbcType=BIGINT}, #{rdReportId,jdbcType=BIGINT}, 
      #{systemId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{bossOrderNo,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{costCenter,jdbcType=VARCHAR}, 
      #{projectTemplate,jdbcType=VARCHAR}, #{invoiceDate,jdbcType=TIMESTAMP}, #{invoiceNo,jdbcType=VARCHAR}, 
      #{currency,jdbcType=VARCHAR}, #{netAmount,jdbcType=DECIMAL}, #{vatAmount,jdbcType=DECIMAL}, 
      #{totalAmount,jdbcType=DECIMAL}, #{prePaidAmount,jdbcType=DECIMAL}, #{invoiceStatus,jdbcType=INTEGER}, 
      #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, now(), 
      #{modifiedBy,jdbcType=VARCHAR}, now(), #{lastModifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoicePO" >
    insert into tb_report_invoice
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="rdReportId != null" >
        rd_report_id,
      </if>
      <if test="systemId != null" >
        system_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="bossOrderNo != null" >
        boss_order_no,
      </if>
      <if test="productCode != null" >
        product_code,
      </if>
      <if test="costCenter != null" >
        cost_center,
      </if>
      <if test="projectTemplate != null" >
        project_template,
      </if>
      <if test="invoiceDate != null" >
        invoice_date,
      </if>
      <if test="invoiceNo != null" >
        invoice_no,
      </if>
      <if test="currency != null" >
        currency,
      </if>
      <if test="netAmount != null" >
        net_amount,
      </if>
      <if test="vatAmount != null" >
        vat_amount,
      </if>
      <if test="totalAmount != null" >
        total_amount,
      </if>
      <if test="prePaidAmount != null" >
        pre_paid_amount,
      </if>
      <if test="invoiceStatus != null" >
        invoice_status,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="lastModifiedDate != null" >
        last_modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="rdReportId != null" >
        #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="bossOrderNo != null" >
        #{bossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null" >
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="costCenter != null" >
        #{costCenter,jdbcType=VARCHAR},
      </if>
      <if test="projectTemplate != null" >
        #{projectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDate != null" >
        #{invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceNo != null" >
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="currency != null" >
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="netAmount != null" >
        #{netAmount,jdbcType=DECIMAL},
      </if>
      <if test="vatAmount != null" >
        #{vatAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="prePaidAmount != null" >
        #{prePaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceStatus != null" >
        #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="lastModifiedDate != null" >
        #{lastModifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoiceExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_invoice
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.rdReportId != null" >
        rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      </if>
      <if test="record.systemId != null" >
        system_id = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bossOrderNo != null" >
        boss_order_no = #{record.bossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null" >
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenter != null" >
        cost_center = #{record.costCenter,jdbcType=VARCHAR},
      </if>
      <if test="record.projectTemplate != null" >
        project_template = #{record.projectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceDate != null" >
        invoice_date = #{record.invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.invoiceNo != null" >
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null" >
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.netAmount != null" >
        net_amount = #{record.netAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.vatAmount != null" >
        vat_amount = #{record.vatAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.totalAmount != null" >
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.prePaidAmount != null" >
        pre_paid_amount = #{record.prePaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.invoiceStatus != null" >
        invoice_status = #{record.invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.lastModifiedDate != null" >
        last_modified_date = #{record.lastModifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_invoice
    set id = #{record.id,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      system_id = #{record.systemId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      boss_order_no = #{record.bossOrderNo,jdbcType=VARCHAR},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      cost_center = #{record.costCenter,jdbcType=VARCHAR},
      project_template = #{record.projectTemplate,jdbcType=VARCHAR},
      invoice_date = #{record.invoiceDate,jdbcType=TIMESTAMP},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      net_amount = #{record.netAmount,jdbcType=DECIMAL},
      vat_amount = #{record.vatAmount,jdbcType=DECIMAL},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      pre_paid_amount = #{record.prePaidAmount,jdbcType=DECIMAL},
      invoice_status = #{record.invoiceStatus,jdbcType=INTEGER},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_date = #{record.lastModifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoicePO" >
    update tb_report_invoice
    <set >
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="rdReportId != null" >
        rd_report_id = #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="systemId != null" >
        system_id = #{systemId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="bossOrderNo != null" >
        boss_order_no = #{bossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null" >
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="costCenter != null" >
        cost_center = #{costCenter,jdbcType=VARCHAR},
      </if>
      <if test="projectTemplate != null" >
        project_template = #{projectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDate != null" >
        invoice_date = #{invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceNo != null" >
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="currency != null" >
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="netAmount != null" >
        net_amount = #{netAmount,jdbcType=DECIMAL},
      </if>
      <if test="vatAmount != null" >
        vat_amount = #{vatAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null" >
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="prePaidAmount != null" >
        pre_paid_amount = #{prePaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceStatus != null" >
        invoice_status = #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="lastModifiedDate != null" >
        last_modified_date = #{lastModifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoicePO" >
    update tb_report_invoice
    set lab_id = #{labId,jdbcType=BIGINT},
      rd_report_id = #{rdReportId,jdbcType=BIGINT},
      system_id = #{systemId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      boss_order_no = #{bossOrderNo,jdbcType=VARCHAR},
      product_code = #{productCode,jdbcType=VARCHAR},
      cost_center = #{costCenter,jdbcType=VARCHAR},
      project_template = #{projectTemplate,jdbcType=VARCHAR},
      invoice_date = #{invoiceDate,jdbcType=TIMESTAMP},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      net_amount = #{netAmount,jdbcType=DECIMAL},
      vat_amount = #{vatAmount,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      pre_paid_amount = #{prePaidAmount,jdbcType=DECIMAL},
      invoice_status = #{invoiceStatus,jdbcType=INTEGER},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_date = #{lastModifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report_invoice
      (`id`,`lab_id`,`rd_report_id`,
      `system_id`,`order_no`,`report_no`,
      `boss_order_no`,`product_code`,`cost_center`,
      `project_template`,`invoice_date`,`invoice_no`,
      `currency`,`net_amount`,`vat_amount`,
      `total_amount`,`pre_paid_amount`,`invoice_status`,
      `active_indicator`,`created_by`,`created_date`,
      `modified_by`,`modified_date`,`last_modified_date`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.rdReportId, jdbcType=BIGINT},
      #{ item.systemId, jdbcType=BIGINT},#{ item.orderNo, jdbcType=VARCHAR},#{ item.reportNo, jdbcType=VARCHAR},
      #{ item.bossOrderNo, jdbcType=VARCHAR},#{ item.productCode, jdbcType=VARCHAR},#{ item.costCenter, jdbcType=VARCHAR},
      #{ item.projectTemplate, jdbcType=VARCHAR},#{ item.invoiceDate, jdbcType=TIMESTAMP},#{ item.invoiceNo, jdbcType=VARCHAR},
      #{ item.currency, jdbcType=VARCHAR},#{ item.netAmount, jdbcType=DECIMAL},#{ item.vatAmount, jdbcType=DECIMAL},
      #{ item.totalAmount, jdbcType=DECIMAL},#{ item.prePaidAmount, jdbcType=DECIMAL},#{ item.invoiceStatus, jdbcType=INTEGER},
      #{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedDate, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_report_invoice 
      <set>
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.rdReportId != null"> 
          `rd_report_id` = #{item.rdReportId, jdbcType = BIGINT},
        </if> 
        <if test="item.systemId != null"> 
          `system_id` = #{item.systemId, jdbcType = BIGINT},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.bossOrderNo != null"> 
          `boss_order_no` = #{item.bossOrderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.productCode != null"> 
          `product_code` = #{item.productCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.costCenter != null"> 
          `cost_center` = #{item.costCenter, jdbcType = VARCHAR},
        </if> 
        <if test="item.projectTemplate != null"> 
          `project_template` = #{item.projectTemplate, jdbcType = VARCHAR},
        </if> 
        <if test="item.invoiceDate != null"> 
          `invoice_date` = #{item.invoiceDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.invoiceNo != null"> 
          `invoice_no` = #{item.invoiceNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.currency != null"> 
          `currency` = #{item.currency, jdbcType = VARCHAR},
        </if> 
        <if test="item.netAmount != null"> 
          `net_amount` = #{item.netAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.vatAmount != null"> 
          `vat_amount` = #{item.vatAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.totalAmount != null"> 
          `total_amount` = #{item.totalAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.prePaidAmount != null"> 
          `pre_paid_amount` = #{item.prePaidAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.invoiceStatus != null"> 
          `invoice_status` = #{item.invoiceStatus, jdbcType = INTEGER},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedDate != null"> 
          `last_modified_date` = #{item.lastModifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>