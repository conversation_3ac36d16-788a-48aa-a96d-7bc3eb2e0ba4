/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceRequirementReportInput implements Serializable {
    private Integer reportLanguage;
    private String reportHeader;
    private String reportAddress;
    private Integer accreditation;
    private Integer needConclusion;
    private Integer needDraft;
    private Integer needPhoto;
    private List<RdReportLanguageInput> languageList;

    private String certificateRequired;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
