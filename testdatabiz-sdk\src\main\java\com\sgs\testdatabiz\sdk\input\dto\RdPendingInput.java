/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPendingInput implements Serializable {

    private Integer pendingFlag;
    private Integer pendingType;
    private String pendingRemark;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
