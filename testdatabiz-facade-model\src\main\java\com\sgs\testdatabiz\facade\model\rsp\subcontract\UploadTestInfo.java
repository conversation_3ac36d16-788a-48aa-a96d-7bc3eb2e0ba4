package com.sgs.testdatabiz.facade.model.rsp.subcontract;


import com.sgs.framework.core.common.PrintFriendliness;

public class UploadTestInfo extends PrintFriendliness {

    private String analyteName;

    private String reportUnit;

    private String reportLimit;

    private String sampleNo;

    private String testValue;

    private Integer analyteSeq;

    private Integer sampleSeq;

    private String languages;

    public String getLanguages() {
        return languages;
    }

    public void setLanguages(String languages) {
        this.languages = languages;
    }

    public String getAnalyteName() {
        return analyteName;
    }

    public void setAnalyteName(String analyteName) {
        this.analyteName = analyteName;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    public Integer getSampleSeq() {
        return sampleSeq;
    }

    public void setSampleSeq(Integer sampleSeq) {
        this.sampleSeq = sampleSeq;
    }
}
