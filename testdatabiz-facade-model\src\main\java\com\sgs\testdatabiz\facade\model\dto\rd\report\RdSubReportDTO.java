/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdSubReportDTO implements Serializable {

    private Integer sourceType;
    private String objectNo;
    private String subReportId;
    private String subReportNo;
    private String externalObjectNo;
    private String testMatrixMergeMode;


    private List<RdAttachmentDTO> subReportFileList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
