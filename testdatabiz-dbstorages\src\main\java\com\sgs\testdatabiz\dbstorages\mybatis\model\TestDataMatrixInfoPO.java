package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class TestDataMatrixInfoPO {
    /**
     * id BIGINT(19) 必填<br>
     * 
     */
    private Long id;

    /**
     * ObjectRelId VARCHAR(36)<br>
     * 
     */
    private String objectRelId;

    /**
     * TestMatrixId VARCHAR(36)<br>
     * 
     */
    private String testMatrixId;

    /**
     * rd_report_id BIGINT(19)<br>
     *
     */
    private Long rdReportId;

    /**
     * TestLineMappingId INTEGER(10) 默认值[0] 必填<br>
     * 
     */
    private Integer testLineMappingId;

    /**
     * ExternalId VARCHAR(300)<br>
     * 
     */
    private String externalId;

    /**
     * ExternalCode VARCHAR(50)<br>
     * 
     */
    private String externalCode;

    /**
     * PpVersionId INTEGER(10)<br>
     * 
     */
    private Integer ppVersionId;

    /**
     * Aid BIGINT(19)<br>
     * 
     */
    private Long aid;

    /**
     * TestLineId INTEGER(10)<br>
     * 
     */
    private Integer testLineId;

    /**
     * CitationId INTEGER(10)<br>
     * 
     */
    private Integer citationId;

    /**
     * CitationVersionId INTEGER(10)<br>
     * 
     */
    private Integer citationVersionId;

    /**
     * CitationType INTEGER(10) 默认值[0] 必填<br>
     * 0：None、1：Method、2：Regulation、3：Standard
     */
    private Integer citationType;

    /**
     * CitationName VARCHAR(1000)<br>
     * 
     */
    private String citationName;

    /**
     * SampleId VARCHAR(36)<br>
     * 
     */
    private String sampleId;

    /**
     * SampleNo VARCHAR(20)<br>
     * 
     */
    private String sampleNo;

    /**
     * ExternalSampleNo VARCHAR(500)<br>
     * 
     */
    private String externalSampleNo;

    /**
     * TestLineSeq BIGINT(19)<br>
     * 
     */
    private Long testLineSeq;

    /**
     * SampleSeq VARCHAR(36)<br>
     * 
     */
    private String sampleSeq;

    /**
     * EvaluationAlias VARCHAR(500)<br>
     * 
     */
    private String evaluationAlias;

    /**
     * MethodDesc VARCHAR(512)<br>
     * 
     */
    private String methodDesc;

    /**
     * ConclusionId VARCHAR(50)<br>
     * 
     */
    private String conclusionId;

    /**
     * ConclusionDisplay VARCHAR(50)<br>
     * 
     */
    private String conclusionDisplay;

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 
     */
    private String bizVersionId;

    /**
     * ActiveIndicator INTEGER(10) 必填<br>
     * 0无效，1有效
     */
    private Integer activeIndicator;

    /**
     * CreatedBy VARCHAR(50)<br>
     * 
     */
    private String createdBy;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 
     */
    private Date createdDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 
     */
    private String modifiedBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 
     */
    private Date modifiedDate;

    /**
     * lab_id BIGINT(19)<br>
     * Trims系统实验室标识
     */
    private Long labId;

    /**
     * order_no VARCHAR(50)<br>
     * 订单号
     */
    private String orderNo;

    /**
     * report_no VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * test_matrix_group_id INTEGER(10)<br>
     * 测试单位分组标识
     */
    private Integer testMatrixGroupId;

    /**
     * test_line_instance_id INTEGER(10)<br>
     * 测试项实例标识
     */
    private String testLineInstanceId;

    /**
     * evaluation_name VARCHAR(255)<br>
     * 测试项名称
     */
    private String evaluationName;

    /**
     * test_line_status VARCHAR(255)<br>
     * 测试项状态
     */
    private String testLineStatus;

    /**
     * test_line_remark VARCHAR(255)<br>
     * 测试项备注
     */
    private String testLineRemark;

    /**
     * citation_full_name VARCHAR(255)<br>
     * 测试标准拼接名称
     */
    private String citationFullName;

    /**
     * sample_instance_id VARCHAR(36)<br>
     * 样品实例标识
     */
    private String sampleInstanceId;

    /**
     * sample_parent_id VARCHAR(36)<br>
     * 样品父级标识
     */
    private String sampleParentId;

    /**
     * sample_type VARCHAR(255)<br>
     * 样品类型
     */
    private String sampleType;

    /**
     * category VARCHAR(255)<br>
     * 样品分类
     */
    private String category;

    /**
     * material_color VARCHAR(255)<br>
     * 物料颜色
     */
    private String materialColor;

    /**
     * composition VARCHAR(255)<br>
     * 物料材质
     */
    private String composition;

    /**
     * material_description VARCHAR(255)<br>
     * 物料描述
     */
    private String materialDescription;

    /**
     * material_end_use VARCHAR(255)<br>
     * 物料用途
     */
    private String materialEndUse;

    /**
     * applicable_flag VARCHAR(255)<br>
     * NC样品标识
     */
    private String applicableFlag;

    /**
     * material_other_sample_info VARCHAR(255)<br>
     * 其他样品信息
     */
    private String materialOtherSampleInfo;

    /**
     * material_remark VARCHAR(255)<br>
     * 样品备注信息
     */
    private String materialRemark;

    /**
     * conclusion_code VARCHAR(255)<br>
     * 测试结论编码
     */
    private String conclusionCode;

    /**
     * customer_conclusion VARCHAR(50)<br>
     * 客户测试结论
     */
    private String customerConclusion;

    /**
     * conclusion_remark VARCHAR(255)<br>
     * 测试结论备注
     */
    private String conclusionRemark;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedTimestamp;

    /**
     * ExtFields LONGVARCHAR(16777215)<br>
     * 
     */
    private String extFields;

    /**
     * Condition LONGVARCHAR(16777215)<br>
     * 
     */
    private String condition;

    /**
     * Languages LONGVARCHAR(16777215)<br>
     * 
     */
    private String languages;

    /**
     * sample_group LONGVARCHAR(65535)<br>
     * 样品分组信息
     */
    private String sampleGroup;

    /**
     *
     */
    private String matrixSource;

    //SCI-1378
    private String referFromSampleNo;
    //SCI-1378
    private String referFromReportNo;

    public String getReferFromSampleNo() {
        return referFromSampleNo;
    }

    public void setReferFromSampleNo(String referFromSampleNo) {
        this.referFromSampleNo = referFromSampleNo;
    }

    public String getReferFromReportNo() {
        return referFromReportNo;
    }

    public void setReferFromReportNo(String referFromReportNo) {
        this.referFromReportNo = referFromReportNo;
    }

    public String getMatrixSource() {
        return matrixSource;
    }

    public void setMatrixSource(String matrixSource) {
        this.matrixSource = matrixSource;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * ObjectRelId VARCHAR(36)<br>
     * 获得 
     */
    public String getObjectRelId() {
        return objectRelId;
    }

    /**
     * ObjectRelId VARCHAR(36)<br>
     * 设置 
     */
    public void setObjectRelId(String objectRelId) {
        this.objectRelId = objectRelId == null ? null : objectRelId.trim();
    }

    public Long getRdReportId() {
        return rdReportId;
    }

    public void setRdReportId(Long rdReportId) {
        this.rdReportId = rdReportId;
    }

    /**
     * TestMatrixId VARCHAR(36)<br>
     * 获得 
     */
    public String getTestMatrixId() {
        return testMatrixId;
    }

    /**
     * TestMatrixId VARCHAR(36)<br>
     * 设置 
     */
    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId == null ? null : testMatrixId.trim();
    }

    /**
     * TestLineMappingId INTEGER(10) 默认值[0] 必填<br>
     * 获得 
     */
    public Integer getTestLineMappingId() {
        return testLineMappingId;
    }

    /**
     * TestLineMappingId INTEGER(10) 默认值[0] 必填<br>
     * 设置 
     */
    public void setTestLineMappingId(Integer testLineMappingId) {
        this.testLineMappingId = testLineMappingId;
    }

    /**
     * ExternalId VARCHAR(300)<br>
     * 获得 
     */
    public String getExternalId() {
        return externalId;
    }

    /**
     * ExternalId VARCHAR(300)<br>
     * 设置 
     */
    public void setExternalId(String externalId) {
        this.externalId = externalId == null ? null : externalId.trim();
    }

    /**
     * ExternalCode VARCHAR(50)<br>
     * 获得 
     */
    public String getExternalCode() {
        return externalCode;
    }

    /**
     * ExternalCode VARCHAR(50)<br>
     * 设置 
     */
    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode == null ? null : externalCode.trim();
    }

    /**
     * PpVersionId INTEGER(10)<br>
     * 获得 
     */
    public Integer getPpVersionId() {
        return ppVersionId;
    }

    /**
     * PpVersionId INTEGER(10)<br>
     * 设置 
     */
    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    /**
     * Aid BIGINT(19)<br>
     * 获得 
     */
    public Long getAid() {
        return aid;
    }

    /**
     * Aid BIGINT(19)<br>
     * 设置 
     */
    public void setAid(Long aid) {
        this.aid = aid;
    }

    /**
     * TestLineId INTEGER(10)<br>
     * 获得 
     */
    public Integer getTestLineId() {
        return testLineId;
    }

    /**
     * TestLineId INTEGER(10)<br>
     * 设置 
     */
    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    /**
     * CitationId INTEGER(10)<br>
     * 获得 
     */
    public Integer getCitationId() {
        return citationId;
    }

    /**
     * CitationId INTEGER(10)<br>
     * 设置 
     */
    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    /**
     * CitationVersionId INTEGER(10)<br>
     * 获得 
     */
    public Integer getCitationVersionId() {
        return citationVersionId;
    }

    /**
     * CitationVersionId INTEGER(10)<br>
     * 设置 
     */
    public void setCitationVersionId(Integer citationVersionId) {
        this.citationVersionId = citationVersionId;
    }

    /**
     * CitationType INTEGER(10) 默认值[0] 必填<br>
     * 获得 0：None、1：Method、2：Regulation、3：Standard
     */
    public Integer getCitationType() {
        return citationType;
    }

    /**
     * CitationType INTEGER(10) 默认值[0] 必填<br>
     * 设置 0：None、1：Method、2：Regulation、3：Standard
     */
    public void setCitationType(Integer citationType) {
        this.citationType = citationType;
    }

    /**
     * CitationName VARCHAR(1000)<br>
     * 获得 
     */
    public String getCitationName() {
        return citationName;
    }

    /**
     * CitationName VARCHAR(1000)<br>
     * 设置 
     */
    public void setCitationName(String citationName) {
        this.citationName = citationName == null ? null : citationName.trim();
    }

    /**
     * SampleId VARCHAR(36)<br>
     * 获得 
     */
    public String getSampleId() {
        return sampleId;
    }

    /**
     * SampleId VARCHAR(36)<br>
     * 设置 
     */
    public void setSampleId(String sampleId) {
        this.sampleId = sampleId == null ? null : sampleId.trim();
    }

    /**
     * SampleNo VARCHAR(20)<br>
     * 获得 
     */
    public String getSampleNo() {
        return sampleNo;
    }

    /**
     * SampleNo VARCHAR(20)<br>
     * 设置 
     */
    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo == null ? null : sampleNo.trim();
    }

    /**
     * ExternalSampleNo VARCHAR(500)<br>
     * 获得 
     */
    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    /**
     * ExternalSampleNo VARCHAR(500)<br>
     * 设置 
     */
    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo == null ? null : externalSampleNo.trim();
    }

    /**
     * TestLineSeq BIGINT(19)<br>
     * 获得 
     */
    public Long getTestLineSeq() {
        return testLineSeq;
    }

    /**
     * TestLineSeq BIGINT(19)<br>
     * 设置 
     */
    public void setTestLineSeq(Long testLineSeq) {
        this.testLineSeq = testLineSeq;
    }

    /**
     * SampleSeq VARCHAR(36)<br>
     * 获得 
     */
    public String getSampleSeq() {
        return sampleSeq;
    }

    /**
     * SampleSeq VARCHAR(36)<br>
     * 设置 
     */
    public void setSampleSeq(String sampleSeq) {
        this.sampleSeq = sampleSeq == null ? null : sampleSeq.trim();
    }

    /**
     * EvaluationAlias VARCHAR(500)<br>
     * 获得 
     */
    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    /**
     * EvaluationAlias VARCHAR(500)<br>
     * 设置 
     */
    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias == null ? null : evaluationAlias.trim();
    }

    /**
     * MethodDesc VARCHAR(512)<br>
     * 获得 
     */
    public String getMethodDesc() {
        return methodDesc;
    }

    /**
     * MethodDesc VARCHAR(512)<br>
     * 设置 
     */
    public void setMethodDesc(String methodDesc) {
        this.methodDesc = methodDesc == null ? null : methodDesc.trim();
    }

    /**
     * ConclusionId VARCHAR(50)<br>
     * 获得 
     */
    public String getConclusionId() {
        return conclusionId;
    }

    /**
     * ConclusionId VARCHAR(50)<br>
     * 设置 
     */
    public void setConclusionId(String conclusionId) {
        this.conclusionId = conclusionId == null ? null : conclusionId.trim();
    }

    /**
     * ConclusionDisplay VARCHAR(50)<br>
     * 获得 
     */
    public String getConclusionDisplay() {
        return conclusionDisplay;
    }

    /**
     * ConclusionDisplay VARCHAR(50)<br>
     * 设置 
     */
    public void setConclusionDisplay(String conclusionDisplay) {
        this.conclusionDisplay = conclusionDisplay == null ? null : conclusionDisplay.trim();
    }

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 获得 
     */
    public String getBizVersionId() {
        return bizVersionId;
    }

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 设置 
     */
    public void setBizVersionId(String bizVersionId) {
        this.bizVersionId = bizVersionId == null ? null : bizVersionId.trim();
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 获得 Trims系统实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 设置 Trims系统实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * report_no VARCHAR(50)<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50)<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * test_matrix_group_id INTEGER(10)<br>
     * 获得 测试单位分组标识
     */
    public Integer getTestMatrixGroupId() {
        return testMatrixGroupId;
    }

    /**
     * test_matrix_group_id INTEGER(10)<br>
     * 设置 测试单位分组标识
     */
    public void setTestMatrixGroupId(Integer testMatrixGroupId) {
        this.testMatrixGroupId = testMatrixGroupId;
    }

    /**
     * test_line_instance_id INTEGER(10)<br>
     * 获得 测试项实例标识
     */
    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    /**
     * test_line_instance_id INTEGER(10)<br>
     * 设置 测试项实例标识
     */
    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }

    /**
     * evaluation_name VARCHAR(255)<br>
     * 获得 测试项名称
     */
    public String getEvaluationName() {
        return evaluationName;
    }

    /**
     * evaluation_name VARCHAR(255)<br>
     * 设置 测试项名称
     */
    public void setEvaluationName(String evaluationName) {
        this.evaluationName = evaluationName == null ? null : evaluationName.trim();
    }

    /**
     * test_line_status VARCHAR(255)<br>
     * 获得 测试项状态
     */
    public String getTestLineStatus() {
        return testLineStatus;
    }

    /**
     * test_line_status VARCHAR(255)<br>
     * 设置 测试项状态
     */
    public void setTestLineStatus(String testLineStatus) {
        this.testLineStatus = testLineStatus == null ? null : testLineStatus.trim();
    }

    /**
     * test_line_remark VARCHAR(255)<br>
     * 获得 测试项备注
     */
    public String getTestLineRemark() {
        return testLineRemark;
    }

    /**
     * test_line_remark VARCHAR(255)<br>
     * 设置 测试项备注
     */
    public void setTestLineRemark(String testLineRemark) {
        this.testLineRemark = testLineRemark == null ? null : testLineRemark.trim();
    }

    /**
     * citation_full_name VARCHAR(255)<br>
     * 获得 测试标准拼接名称
     */
    public String getCitationFullName() {
        return citationFullName;
    }

    /**
     * citation_full_name VARCHAR(255)<br>
     * 设置 测试标准拼接名称
     */
    public void setCitationFullName(String citationFullName) {
        this.citationFullName = citationFullName == null ? null : citationFullName.trim();
    }

    /**
     * sample_instance_id VARCHAR(36)<br>
     * 获得 样品实例标识
     */
    public String getSampleInstanceId() {
        return sampleInstanceId;
    }

    /**
     * sample_instance_id VARCHAR(36)<br>
     * 设置 样品实例标识
     */
    public void setSampleInstanceId(String sampleInstanceId) {
        this.sampleInstanceId = sampleInstanceId == null ? null : sampleInstanceId.trim();
    }

    /**
     * sample_parent_id VARCHAR(36)<br>
     * 获得 样品父级标识
     */
    public String getSampleParentId() {
        return sampleParentId;
    }

    /**
     * sample_parent_id VARCHAR(36)<br>
     * 设置 样品父级标识
     */
    public void setSampleParentId(String sampleParentId) {
        this.sampleParentId = sampleParentId == null ? null : sampleParentId.trim();
    }

    /**
     * sample_type VARCHAR(255)<br>
     * 获得 样品类型
     */
    public String getSampleType() {
        return sampleType;
    }

    /**
     * sample_type VARCHAR(255)<br>
     * 设置 样品类型
     */
    public void setSampleType(String sampleType) {
        this.sampleType = sampleType == null ? null : sampleType.trim();
    }

    /**
     * category VARCHAR(255)<br>
     * 获得 样品分类
     */
    public String getCategory() {
        return category;
    }

    /**
     * category VARCHAR(255)<br>
     * 设置 样品分类
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * material_color VARCHAR(255)<br>
     * 获得 物料颜色
     */
    public String getMaterialColor() {
        return materialColor;
    }

    /**
     * material_color VARCHAR(255)<br>
     * 设置 物料颜色
     */
    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor == null ? null : materialColor.trim();
    }

    /**
     * composition VARCHAR(255)<br>
     * 获得 物料材质
     */
    public String getComposition() {
        return composition;
    }

    /**
     * composition VARCHAR(255)<br>
     * 设置 物料材质
     */
    public void setComposition(String composition) {
        this.composition = composition == null ? null : composition.trim();
    }

    /**
     * material_description VARCHAR(255)<br>
     * 获得 物料描述
     */
    public String getMaterialDescription() {
        return materialDescription;
    }

    /**
     * material_description VARCHAR(255)<br>
     * 设置 物料描述
     */
    public void setMaterialDescription(String materialDescription) {
        this.materialDescription = materialDescription == null ? null : materialDescription.trim();
    }

    /**
     * material_end_use VARCHAR(255)<br>
     * 获得 物料用途
     */
    public String getMaterialEndUse() {
        return materialEndUse;
    }

    /**
     * material_end_use VARCHAR(255)<br>
     * 设置 物料用途
     */
    public void setMaterialEndUse(String materialEndUse) {
        this.materialEndUse = materialEndUse == null ? null : materialEndUse.trim();
    }

    /**
     * applicable_flag VARCHAR(255)<br>
     * 获得 NC样品标识
     */
    public String getApplicableFlag() {
        return applicableFlag;
    }

    /**
     * applicable_flag VARCHAR(255)<br>
     * 设置 NC样品标识
     */
    public void setApplicableFlag(String applicableFlag) {
        this.applicableFlag = applicableFlag == null ? null : applicableFlag.trim();
    }

    /**
     * material_other_sample_info VARCHAR(255)<br>
     * 获得 其他样品信息
     */
    public String getMaterialOtherSampleInfo() {
        return materialOtherSampleInfo;
    }

    /**
     * material_other_sample_info VARCHAR(255)<br>
     * 设置 其他样品信息
     */
    public void setMaterialOtherSampleInfo(String materialOtherSampleInfo) {
        this.materialOtherSampleInfo = materialOtherSampleInfo == null ? null : materialOtherSampleInfo.trim();
    }

    /**
     * material_remark VARCHAR(255)<br>
     * 获得 样品备注信息
     */
    public String getMaterialRemark() {
        return materialRemark;
    }

    /**
     * material_remark VARCHAR(255)<br>
     * 设置 样品备注信息
     */
    public void setMaterialRemark(String materialRemark) {
        this.materialRemark = materialRemark == null ? null : materialRemark.trim();
    }

    /**
     * conclusion_code VARCHAR(255)<br>
     * 获得 测试结论编码
     */
    public String getConclusionCode() {
        return conclusionCode;
    }

    /**
     * conclusion_code VARCHAR(255)<br>
     * 设置 测试结论编码
     */
    public void setConclusionCode(String conclusionCode) {
        this.conclusionCode = conclusionCode == null ? null : conclusionCode.trim();
    }

    /**
     * customer_conclusion VARCHAR(50)<br>
     * 获得 客户测试结论
     */
    public String getCustomerConclusion() {
        return customerConclusion;
    }

    /**
     * customer_conclusion VARCHAR(50)<br>
     * 设置 客户测试结论
     */
    public void setCustomerConclusion(String customerConclusion) {
        this.customerConclusion = customerConclusion == null ? null : customerConclusion.trim();
    }

    /**
     * conclusion_remark VARCHAR(255)<br>
     * 获得 测试结论备注
     */
    public String getConclusionRemark() {
        return conclusionRemark;
    }

    /**
     * conclusion_remark VARCHAR(255)<br>
     * 设置 测试结论备注
     */
    public void setConclusionRemark(String conclusionRemark) {
        this.conclusionRemark = conclusionRemark == null ? null : conclusionRemark.trim();
    }

    /**
     * active_indicator TINYINT(3)<br>
     * 获得 0无效，1有效
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3)<br>
     * 设置 0无效，1有效
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    /**
     * ExtFields LONGVARCHAR(16777215)<br>
     * 获得 
     */
    public String getExtFields() {
        return extFields;
    }

    /**
     * ExtFields LONGVARCHAR(16777215)<br>
     * 设置 
     */
    public void setExtFields(String extFields) {
        this.extFields = extFields == null ? null : extFields.trim();
    }

    /**
     * Condition LONGVARCHAR(16777215)<br>
     * 获得 
     */
    public String getCondition() {
        return condition;
    }

    /**
     * Condition LONGVARCHAR(16777215)<br>
     * 设置 
     */
    public void setCondition(String condition) {
        this.condition = condition == null ? null : condition.trim();
    }

    /**
     * Languages LONGVARCHAR(16777215)<br>
     * 获得 
     */
    public String getLanguages() {
        return languages;
    }

    /**
     * Languages LONGVARCHAR(16777215)<br>
     * 设置 
     */
    public void setLanguages(String languages) {
        this.languages = languages == null ? null : languages.trim();
    }

    /**
     * sample_group LONGVARCHAR(65535)<br>
     * 获得 样品分组信息
     */
    public String getSampleGroup() {
        return sampleGroup;
    }

    /**
     * sample_group LONGVARCHAR(65535)<br>
     * 设置 样品分组信息
     */
    public void setSampleGroup(String sampleGroup) {
        this.sampleGroup = sampleGroup == null ? null : sampleGroup.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", objectRelId=").append(objectRelId);
        sb.append(", testMatrixId=").append(testMatrixId);
        sb.append(", testLineMappingId=").append(testLineMappingId);
        sb.append(", externalId=").append(externalId);
        sb.append(", externalCode=").append(externalCode);
        sb.append(", ppVersionId=").append(ppVersionId);
        sb.append(", aid=").append(aid);
        sb.append(", testLineId=").append(testLineId);
        sb.append(", citationId=").append(citationId);
        sb.append(", citationVersionId=").append(citationVersionId);
        sb.append(", citationType=").append(citationType);
        sb.append(", citationName=").append(citationName);
        sb.append(", sampleId=").append(sampleId);
        sb.append(", sampleNo=").append(sampleNo);
        sb.append(", externalSampleNo=").append(externalSampleNo);
        sb.append(", testLineSeq=").append(testLineSeq);
        sb.append(", sampleSeq=").append(sampleSeq);
        sb.append(", evaluationAlias=").append(evaluationAlias);
        sb.append(", methodDesc=").append(methodDesc);
        sb.append(", conclusionId=").append(conclusionId);
        sb.append(", conclusionDisplay=").append(conclusionDisplay);
        sb.append(", bizVersionId=").append(bizVersionId);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", extFields=").append(extFields);
        sb.append(", condition=").append(condition);
        sb.append(", languages=").append(languages);
        sb.append(", sampleGroup=").append(sampleGroup);
        sb.append("]");
        return sb.toString();
    }
}