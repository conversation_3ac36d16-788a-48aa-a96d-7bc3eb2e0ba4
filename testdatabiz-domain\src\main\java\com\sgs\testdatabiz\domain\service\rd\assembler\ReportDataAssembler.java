package com.sgs.testdatabiz.domain.service.rd.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.NumberUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.model.*;
import com.sgs.testdatabiz.domain.service.rd.convertor.ReportDataConvertor;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.domain.service.rd.manager.AttachmentManager;
import com.sgs.testdatabiz.domain.service.rd.manager.QuotationManager;
import com.sgs.testdatabiz.domain.service.rd.manager.ReportLangManager;
import com.sgs.testdatabiz.domain.service.rd.manager.ReportProductDffManager;
import com.sgs.testdatabiz.domain.service.utils.convertor.ReportDataBuilder;
import com.sgs.testdatabiz.facade.model.enums.AttachmentObjectTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ReportDataAssembler {

    @Autowired
    private ReportLangManager reportLangManager;

    @Autowired
    private AttachmentManager attachmentManager;

    @Autowired
    private QuotationManager quotationManager;

    @Autowired
    private ReportDataBuilder reportDataBuilder;

    @Autowired
    private ReportProductDffManager reportProductDffManager;

    public ReportDataBatchDO assemblerResultReportDataDO(RdReportPO rdReportPO,
                                                         List<RdReportLangPO> reportLangList,
                                                         List<RdReportProductDffPO> productDffList,
                                                         List<RdAttachmentPO> attachmentList,
                                                         List<RdReportTrfRelPO> rdReportTrfRelPOS,
                                                         List<TestDataObjectRelPO> objectRelPOs) {
        ReportDataBatchDO reportDataDO = new ReportDataBatchDO();

        RdReportDO headerDO = BeanUtil.copyProperties(rdReportPO, RdReportDO.class);
        headerDO.setApproveBy(rdReportPO.getReportApproverBy());
        headerDO.setApproveDate(rdReportPO.getReportApproverDate());
        headerDO.setSoftCopyDeliveryDate(rdReportPO.getSoftcopyDeliveryDate());
        headerDO.setCreateDate(rdReportPO.getCreatedDate());
        headerDO.setReportDueDate(rdReportPO.getReportDueDate());

        RdConclusionDO conclusionDO = new RdConclusionDO();
        conclusionDO.setConclusionCode(rdReportPO.getConclusionCode());
        conclusionDO.setCustomerConclusion(rdReportPO.getCustomerConclusion());
//        conclusionDO.setReviewConclusion(rdReportPO.getReviewConclusion());
        conclusionDO.setConclusionRemark(rdReportPO.getConclusionRemark());
        headerDO.setConclusion(conclusionDO);
        // head

        // attachment
        List<RdAttachmentDO> attachmentDOS = ReportDataConvertor.convertorAttachment(
                attachmentList.stream()
                        .filter(l -> Objects.equals(l.getObjectType(), AttachmentObjectTypeEnum.REPORT.getCode()) || Objects.equals(l.getObjectType(), AttachmentObjectTypeEnum.SUB_REPORT.getCode()))
                        .collect(Collectors.toList())
        );
        headerDO.setReportFileList(attachmentDOS);
        reportDataDO.setReportList(Collections.singletonList(headerDO));

        RdLabDO labDO = new RdLabDO();
        labDO.setLabId(NumberUtil.toInt(String.valueOf(rdReportPO.getLabId())));
//        labDO.setLabCode();
//        labDO.setLocationId();
//        labDO.setLocationCode();
        labDO.setBuId(NumberUtil.toInt(String.valueOf(rdReportPO.getBuId())));
//        labDO.setBuCode();
        headerDO.setLab(labDO);

        reportDataDO.setLabId(rdReportPO.getLabId());
        reportDataDO.setBuId(rdReportPO.getBuId());

        if (Func.isNotEmpty(rdReportTrfRelPOS)) {
            List<RdTrfDO> trfDOS = new ArrayList<>();
            RdReportTrfRelPO trfRelPO = rdReportTrfRelPOS.get(0);
            rdReportTrfRelPOS.forEach(
                    rdReportTrfRelPO -> {
                        RdTrfDO trfDO = new RdTrfDO();
                        trfDO.setRefSystemId(rdReportTrfRelPO.getTrfRefSystemId());
                        trfDO.setTrfNo(rdReportTrfRelPO.getTrfNo());
//            trfDO.setServiceLevel();
//            trfDO.setTrfTemplateOwner();
//            trfDO.setTrfTemplateId();
//            trfDO.setTrfTemplateName();
//            trfDO.setTrfSubmissionDate();
                        trfDOS.add(trfDO);
                    }
            );
            reportDataDO.setTrfList(trfDOS);
            RdOrderDO rdOrderDO = new RdOrderDO();
            rdOrderDO.setSystemId(NumberUtil.toInt(String.valueOf(rdReportPO.getSystemId())));
//            rdOrderDO.setOrderId();
            rdOrderDO.setOrderNo(trfRelPO.getOrderNo());
//            rdOrderDO.setOriginalOrderNo(objectRelPO.getParentOrderNo());
            reportDataDO.setOrderList(Collections.singletonList(rdOrderDO));
        }

        // fixme
//        if (Func.isNotEmpty(objectRelPOs)) {
//            TestDataObjectRelPO objectRelPO = objectRelPOs.get(0);
//            RdOrderDO rdOrderDO = new RdOrderDO();
//            rdOrderDO.setSystemId(NumberUtil.toInt(String.valueOf(rdReportPO.getSystemId())));
////            rdOrderDO.setOrderId();
//            rdOrderDO.setOrderNo(objectRelPO.getOrderNo());
//            rdOrderDO.setOriginalOrderNo(objectRelPO.getParentOrderNo());
////            rdOrderDO.setOrderStatus();
////            rdOrderDO.setServiceType();
////            rdOrderDO.setOrderType();
////            rdOrderDO.setOperationType();
////            rdOrderDO.setOperationMode();
////            rdOrderDO.setProductCategory();
////            rdOrderDO.setProductSubCategory();
////            rdOrderDO.setGroupId();
////            rdOrderDO.setIdbLab();
////            rdOrderDO.setTat();
////            rdOrderDO.setServiceStartDate();
////            rdOrderDO.setTestingStartDate();
////            rdOrderDO.setTestingEndDate();
////            rdOrderDO.setServiceConfirmDate();
////            rdOrderDO.setCuttingExpectDueDate();
////            rdOrderDO.setOrderExpectDueDate();
////            rdOrderDO.setJobExpectDueDate();
////            rdOrderDO.setSubcontractExpectDueDate();
////            rdOrderDO.setReportExpectDueDate();
////            rdOrderDO.setSoftCopyDeliveryDate();
////            rdOrderDO.setCreateBy();
////            rdOrderDO.setCreateDate();
////            rdOrderDO.setPayment();
////            rdOrderDO.setContactPersonList();
////            rdOrderDO.setFlags();
////            rdOrderDO.setOthers();
////            rdOrderDO.setCustomerList();
////            rdOrderDO.setProductList();
////            rdOrderDO.setSampleList();
////            rdOrderDO.setServiceRequirement();
////            rdOrderDO.setAttachmentList();
//
//            reportDataDO.setOrder(rdOrderDO);
//        }


        // report lang
//        List<RdReportLanguageDO> reportLanguageDOList = ReportDataConvertor.convertorReportLang(reportLangList);

//        RdOrderDO rdOrderDO = new RdOrderDO();
//        RdServiceRequirementDO rdServiceRequirementDO = new RdServiceRequirementDO();
//        RdServiceRequirementReportDO requirementReportDO = new RdServiceRequirementReportDO();
//        requirementReportDO.setLanguageList(requirementReportDO);

        // productDff
//        List<RdProductDO> productDOS = ReportDataConvertor.convertorProductDff(productDffList);
        return reportDataDO;
    }

    public List<RdInvoiceDO> assemblerReportInvoiceDO(List<RdReportInvoicePO> reportInvoicePOList, Map<String, List<RdAttachmentPO>> reportFileMap) {
        List<RdInvoiceDO> invoiceDOS = new ArrayList<>();
        List<String> invoices = reportInvoicePOList.stream().map(RdReportInvoicePO::getInvoiceNo).distinct().collect(Collectors.toList());
        Map<String, List<String>> stringListMap = quotationManager.selectMapByInvoiceNo(invoices);
        reportInvoicePOList.forEach(
                l -> {
                    RdInvoiceDO invoiceDO = new RdInvoiceDO();
                    invoiceDO.setLabId(l.getLabId());
                    invoiceDO.setSystemId(l.getSystemId());
                    invoiceDO.setOrderNo(l.getOrderNo());
                    invoiceDO.setInvoiceNo(l.getInvoiceNo());
                    invoiceDO.setQuotationNos(stringListMap.get(l.getInvoiceNo()));
                    invoiceDO.setCurrency(l.getCurrency());
                    invoiceDO.setNetAmount(l.getNetAmount());
                    invoiceDO.setVatAmount(l.getVatAmount());
                    invoiceDO.setTotalAmount(l.getTotalAmount());
                    invoiceDO.setPrePaidAmount(l.getPrePaidAmount());
                    invoiceDO.setInvoiceStatus(l.getInvoiceStatus());
                    invoiceDO.setReportNo(l.getReportNo());
//                    List<RdAttachmentPO> invoiceFilePOList = attachmentManager.getByObjectNoAndType(String.valueOf(l.getInvoiceNo()), AttachmentObjectTypeEnum.INVOICE.getCode());
                    List<RdAttachmentPO> invoiceFilePOList = reportFileMap.get(l.getInvoiceNo());
                    if (Func.isNotEmpty(invoiceFilePOList)) {
                        invoiceDO.setInvoiceFileList(reportDataBuilder.buildInvoiceQuotationFileDO(invoiceFilePOList));

                    }
                    invoiceDOS.add(invoiceDO);
                }
        );
        return invoiceDOS;
    }

    public List<RdQuotationDO> assemblerQuotationDO(List<RdQuotationPO> quotationPOList) {
        List<RdQuotationDO> quotationDOS = reportDataBuilder.buildRdQuotationDOList(quotationPOList);
        if (Func.isNotEmpty(quotationDOS)) {
            quotationDOS.forEach(
                    l -> l.setQuotationFileList(reportDataBuilder.buildInvoiceQuotationFileDO(
                            attachmentManager.getByObjectNoAndType(String.valueOf(l.getQuotationNo()), AttachmentObjectTypeEnum.QUOTATION.getCode())
                    ))
            );
        }
        return quotationDOS;
    }
}
