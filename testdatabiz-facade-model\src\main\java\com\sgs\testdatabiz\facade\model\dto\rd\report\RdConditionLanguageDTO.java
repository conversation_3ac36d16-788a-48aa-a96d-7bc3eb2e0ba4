/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdConditionLanguageDTO implements Serializable {
    private Integer languageId;
    private String conditionTypeName;
    private String conditionName;
    private String conditionDesc;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}