package com.sgs.testdatabiz.facade;

import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.req.starlims.FolderReportInfoReq;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveStarLimsReportReq;

public interface IStarlimsReportFacade {
    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse saveStarlimsReportData(ReceiveStarLimsReportReq reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse cleanStarlimsData(FolderReportInfoReq reqObject);

    BaseResponse washStarlimsData(JSONObject reqObject);
}
