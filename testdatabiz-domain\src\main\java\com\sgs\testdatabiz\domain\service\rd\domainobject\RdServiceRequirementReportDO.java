/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdDeliveryDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceRequirementReportDO {
    private Integer reportLanguage;
    private String reportHeader;
    private String reportAddress;
    private Integer accreditation;
    private Integer needConclusion;
    private Integer needDraft;
    private Integer needPhoto;
    private List<RdReportLanguageDO> languageList;
    private RdDeliveryDO softcopy;
    private RdDeliveryDO hardcopy;
    private String commentFlag;
    private String confirmCoverPageFlag;
    private String certificateRequired;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
