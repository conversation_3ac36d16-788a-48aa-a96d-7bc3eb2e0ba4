package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdReportTestResultDTO extends BaseModel{


    private String rdObjectRelId;

    private Long labId;

    private String orderNo;

    private String reportNo;

    private Long rdReportMatrixId;

    private String testMatrixId;

    private String testResultFullName;

    private String upSpecimen;

    private String specimen;

    private String procedureTestCondition;

    private String conditionParent;

    private String testCondition;

    private String testPosition;

    private String analyteName;

    private String casNo;

    private String resultValue;

    private String resultValueRemark;

    private String resultUnit;

    private Integer testResultSeq;

    private Integer failFlag;
        //SCI-1378
    private String failRemark;
    private String reportRemark;

    private String limitValueFullName;

    private String limitUnit;

    private String bizVersionId;

    private Integer analyteId;

    private Integer conclusionCode;

    private String  conclusionDesc;

    private Integer activeIndicator;

    private List<RdReportTestResultLangDTO> reportTestResultLangList;

    private String testResultInstanceId;

    private Date lastModifiedTimestamp;
    //SCI-1378
    private String mdl;
//    private String referFromSampleNo;// 从starlims
//    private String referFromReportNo;//Starlims"
//    private String referFromOrderNo;//"--- GPO"
    private String referFormSampleNo;
    private String referFormReportNo;
    private String referFormOrderNo;
    private String referFormSampleName;
    private String metaData;

}
