<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据转换SDK - 数据对象字段映射分析</title>
    
    <!-- 引入Markdown解析和代码高亮库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        /* 顶部导航 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .navbar h1 {
            text-align: center;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        /* 主容器 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        
        /* 目录导航 */
        .toc {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-radius: 8px;
        }
        
        .toc h2 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc ul li {
            margin: 0.5rem 0;
        }
        
        .toc ul li a {
            color: #495057;
            text-decoration: none;
            display: block;
            padding: 0.3rem 0.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .toc ul li a:hover {
            background: #667eea;
            color: white;
            transform: translateX(5px);
        }
        
        /* 标题样式 */
        #content h1 {
            color: #2c3e50;
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
            margin: 2rem 0 1rem 0;
            font-size: 2rem;
        }
        
        #content h2 {
            color: #34495e;
            margin: 1.5rem 0 1rem 0;
            font-size: 1.5rem;
            border-left: 4px solid #667eea;
            padding-left: 1rem;
        }
        
        #content h3 {
            color: #495057;
            margin: 1rem 0 0.5rem 0;
            font-size: 1.2rem;
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        td {
            padding: 0.8rem 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        tr:hover {
            background-color: #e3f2fd;
            transition: background-color 0.3s ease;
        }
        
        /* 代码块样式 */
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        code {
            background: #f1f3f4;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            color: #d63384;
        }
        
        pre code {
            background: transparent;
            padding: 0;
            color: inherit;
        }
        
        /* 列表样式 */
        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        
        li {
            margin: 0.5rem 0;
        }
        
        /* 段落样式 */
        p {
            margin: 1rem 0;
            text-align: justify;
        }
        
        /* 强调样式 */
        strong {
            color: #495057;
            font-weight: 600;
        }
        
        /* 块引用样式 */
        blockquote {
            border-left: 4px solid #667eea;
            margin: 1rem 0;
            padding: 1rem 1.5rem;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        /* Mermaid图表样式 */
        .mermaid {
            text-align: center;
            margin: 2rem 0;
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: #667eea;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }
        
        .back-to-top.show {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            background: #5a67d8;
            transform: translateY(-3px);
        }
        
        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 1.2rem;
            color: #667eea;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-right: 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1rem;
            }
            
            .navbar h1 {
                font-size: 1.4rem;
            }
            
            table {
                font-size: 0.9rem;
            }
            
            th, td {
                padding: 0.5rem;
            }
        }
        
        /* 打印样式 */
        @media print {
            .navbar, .back-to-top, .toc {
                display: none;
            }
            
            .container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
            
            pre {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="navbar">
        <h1>📊 测试数据转换SDK - 数据对象字段映射分析</h1>
    </nav>
    
    <!-- 主容器 -->
    <div class="container">
        <!-- 目录导航 -->
        <div class="toc">
            <h2>📋 文档目录</h2>
            <ul id="toc-list">
                <!-- 目录将通过JavaScript生成 -->
            </ul>
        </div>
        
        <!-- 加载动画 -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            正在加载文档内容...
        </div>
        
        <!-- Markdown内容 -->
        <div id="content"></div>
    </div>
    
    <!-- 返回顶部按钮 -->
    <div class="back-to-top" id="backToTop">
        ↑
    </div>
    
    <script>
        // 初始化Mermaid
        mermaid.initialize({ 
            startOnLoad: false,
            theme: 'neutral',
            themeVariables: {
                primaryColor: '#667eea',
                primaryTextColor: '#333',
                primaryBorderColor: '#667eea',
                lineColor: '#666',
                sectionBkgColor: '#f8f9fa',
                altSectionBkgColor: '#ffffff',
                gridColor: '#e9ecef',
                secondaryColor: '#764ba2',
                tertiaryColor: '#f8f9fa'
            }
        });
        
        // 读取Markdown文件内容
        async function loadMarkdownFile() {
            try {
                const response = await fetch('testdatabiz-sdk-数据对象字段映射分析.md');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const markdown = await response.text();
                await renderMarkdown(markdown);
            } catch (error) {
                console.error('Error loading markdown file:', error);
                document.getElementById('content').innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #dc3545;">
                        <h2>❌ 加载失败</h2>
                        <p>无法加载Markdown文件，请确保文件存在于同一目录下。</p>
                        <p>错误信息: ${error.message}</p>
                        <button onclick="location.reload()" style="
                            background: #667eea; 
                            color: white; 
                            border: none; 
                            padding: 0.5rem 1rem; 
                            border-radius: 4px; 
                            cursor: pointer;
                            margin-top: 1rem;
                        ">重新加载</button>
                    </div>
                `;
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }
        
        // 渲染Markdown内容
        async function renderMarkdown(markdown) {
            // 配置marked选项
            marked.setOptions({
                highlight: function(code, lang) {
                    if (Prism.languages[lang]) {
                        return Prism.highlight(code, Prism.languages[lang], lang);
                    }
                    return code;
                },
                breaks: true,
                gfm: true
            });
            
            // 转换Markdown为HTML
            const html = marked.parse(markdown);
            document.getElementById('content').innerHTML = html;
            
            // 渲染Mermaid图表
            await renderMermaidDiagrams();
            
            // 生成目录
            generateTableOfContents();
            
            // 初始化其他功能
            initializeFeatures();
        }
        
        // 渲染Mermaid图表
        async function renderMermaidDiagrams() {
            const mermaidElements = document.querySelectorAll('code.language-mermaid');
            for (let i = 0; i < mermaidElements.length; i++) {
                const element = mermaidElements[i];
                const graphDefinition = element.textContent;
                const graphId = `mermaid-${i}`;
                
                try {
                    const { svg } = await mermaid.render(graphId, graphDefinition);
                    element.parentElement.outerHTML = `<div class="mermaid">${svg}</div>`;
                } catch (error) {
                    console.error('Mermaid rendering error:', error);
                    element.parentElement.outerHTML = `
                        <div style="background: #fff3cd; border: 1px solid #ffeeba; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
                            <strong>⚠️ Mermaid图表渲染失败</strong><br>
                            <pre style="background: #f8f9fa; padding: 0.5rem; margin-top: 0.5rem; font-size: 0.9rem;">${graphDefinition}</pre>
                        </div>
                    `;
                }
            }
        }
        
        // 生成目录
        function generateTableOfContents() {
            const headings = document.querySelectorAll('#content h1, #content h2, #content h3');
            const tocList = document.getElementById('toc-list');
            
            headings.forEach((heading, index) => {
                // 为标题添加ID
                const id = `heading-${index}`;
                heading.id = id;
                
                // 创建目录项
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = `#${id}`;
                a.textContent = heading.textContent;
                a.style.paddingLeft = `${(parseInt(heading.tagName.substr(1)) - 1) * 1}rem`;
                
                // 平滑滚动
                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    heading.scrollIntoView({ behavior: 'smooth' });
                });
                
                li.appendChild(a);
                tocList.appendChild(li);
            });
        }
        
        // 初始化其他功能
        function initializeFeatures() {
            // 返回顶部按钮
            const backToTop = document.getElementById('backToTop');
            
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('show');
                } else {
                    backToTop.classList.remove('show');
                }
            });
            
            backToTop.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
            
            // 表格响应式处理
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                const wrapper = document.createElement('div');
                wrapper.style.overflowX = 'auto';
                wrapper.style.margin = '1rem 0';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            });
            
            // 代码块复制功能
            const codeBlocks = document.querySelectorAll('pre code');
            codeBlocks.forEach(block => {
                const button = document.createElement('button');
                button.textContent = '复制';
                button.style.cssText = `
                    position: absolute;
                    top: 0.5rem;
                    right: 0.5rem;
                    background: #667eea;
                    color: white;
                    border: none;
                    padding: 0.3rem 0.6rem;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 0.8rem;
                `;
                
                block.parentElement.style.position = 'relative';
                block.parentElement.appendChild(button);
                
                button.addEventListener('click', () => {
                    navigator.clipboard.writeText(block.textContent);
                    button.textContent = '已复制!';
                    setTimeout(() => button.textContent = '复制', 2000);
                });
            });
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', loadMarkdownFile);
    </script>
</body>
</html> 