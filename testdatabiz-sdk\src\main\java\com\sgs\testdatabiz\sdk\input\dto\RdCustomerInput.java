/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCustomerInput implements Serializable{
    private Integer customerUsage;
    private Long bossNo;
    private String customerGroupCode;
    private String customerName;
    private String customerGroupName;
    private String customerAddress;
    private List<RdCustomerLanguageInput> languageList;
    private List<RdCustomerContactInput> customerContactList;
    private String marketSegmentCode;
    private String marketSegmentName;
    private String customerRefId;
    private String customerInstanceId;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
