package com.sgs.testdatabiz.facade.model.info;

import com.sgs.framework.core.common.PrintFriendliness;

public class TestDataMatrixExtFieldLangInfo extends PrintFriendliness {
    /**
     * 多语言（1：英文、2：中文）
     */
    private Integer languageId;

    /**
     *
     */
    private String appFactorName;

    /**
     *
     */
    private String materialName;

    /**
     *
     */
    private String materialColor;

    /**
     *
     */
    private String usedPosition;

    /**
     *
     */
    private String materialTexture;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getAppFactorName() {
        return appFactorName;
    }

    public void setAppFactorName(String appFactorName) {
        this.appFactorName = appFactorName;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }
}
