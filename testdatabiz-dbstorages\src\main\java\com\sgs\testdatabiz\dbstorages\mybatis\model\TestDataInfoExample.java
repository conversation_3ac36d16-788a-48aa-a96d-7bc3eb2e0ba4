package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestDataInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TestDataInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andObjectrelidIsNull() {
            addCriterion("ObjectRelId is null");
            return (Criteria) this;
        }

        public Criteria andObjectrelidIsNotNull() {
            addCriterion("ObjectRelId is not null");
            return (Criteria) this;
        }

        public Criteria andObjectrelidEqualTo(String value) {
            addCriterion("ObjectRelId =", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidNotEqualTo(String value) {
            addCriterion("ObjectRelId <>", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidGreaterThan(String value) {
            addCriterion("ObjectRelId >", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidGreaterThanOrEqualTo(String value) {
            addCriterion("ObjectRelId >=", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidLessThan(String value) {
            addCriterion("ObjectRelId <", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidLessThanOrEqualTo(String value) {
            addCriterion("ObjectRelId <=", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidLike(String value) {
            addCriterion("ObjectRelId like", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidNotLike(String value) {
            addCriterion("ObjectRelId not like", value, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidIn(List<String> values) {
            addCriterion("ObjectRelId in", values, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidNotIn(List<String> values) {
            addCriterion("ObjectRelId not in", values, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidBetween(String value1, String value2) {
            addCriterion("ObjectRelId between", value1, value2, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andObjectrelidNotBetween(String value1, String value2) {
            addCriterion("ObjectRelId not between", value1, value2, "objectrelid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidIsNull() {
            addCriterion("TestDataMatrixId is null");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidIsNotNull() {
            addCriterion("TestDataMatrixId is not null");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidEqualTo(Long value) {
            addCriterion("TestDataMatrixId =", value, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidNotEqualTo(Long value) {
            addCriterion("TestDataMatrixId <>", value, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidGreaterThan(Long value) {
            addCriterion("TestDataMatrixId >", value, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidGreaterThanOrEqualTo(Long value) {
            addCriterion("TestDataMatrixId >=", value, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidLessThan(Long value) {
            addCriterion("TestDataMatrixId <", value, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidLessThanOrEqualTo(Long value) {
            addCriterion("TestDataMatrixId <=", value, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidIn(List<Long> values) {
            addCriterion("TestDataMatrixId in", values, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidNotIn(List<Long> values) {
            addCriterion("TestDataMatrixId not in", values, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidBetween(Long value1, Long value2) {
            addCriterion("TestDataMatrixId between", value1, value2, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestdatamatrixidNotBetween(Long value1, Long value2) {
            addCriterion("TestDataMatrixId not between", value1, value2, "testdatamatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidIsNull() {
            addCriterion("TestMatrixId is null");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidIsNotNull() {
            addCriterion("TestMatrixId is not null");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidEqualTo(String value) {
            addCriterion("TestMatrixId =", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidNotEqualTo(String value) {
            addCriterion("TestMatrixId <>", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidGreaterThan(String value) {
            addCriterion("TestMatrixId >", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidGreaterThanOrEqualTo(String value) {
            addCriterion("TestMatrixId >=", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidLessThan(String value) {
            addCriterion("TestMatrixId <", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidLessThanOrEqualTo(String value) {
            addCriterion("TestMatrixId <=", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidLike(String value) {
            addCriterion("TestMatrixId like", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidNotLike(String value) {
            addCriterion("TestMatrixId not like", value, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidIn(List<String> values) {
            addCriterion("TestMatrixId in", values, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidNotIn(List<String> values) {
            addCriterion("TestMatrixId not in", values, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidBetween(String value1, String value2) {
            addCriterion("TestMatrixId between", value1, value2, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andTestmatrixidNotBetween(String value1, String value2) {
            addCriterion("TestMatrixId not between", value1, value2, "testmatrixid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidIsNull() {
            addCriterion("AnalyteId is null");
            return (Criteria) this;
        }

        public Criteria andAnalyteidIsNotNull() {
            addCriterion("AnalyteId is not null");
            return (Criteria) this;
        }

        public Criteria andAnalyteidEqualTo(String value) {
            addCriterion("AnalyteId =", value, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidNotEqualTo(String value) {
            addCriterion("AnalyteId <>", value, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidGreaterThan(String value) {
            addCriterion("AnalyteId >", value, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidGreaterThanOrEqualTo(String value) {
            addCriterion("AnalyteId >=", value, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidLessThan(String value) {
            addCriterion("AnalyteId <", value, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidLessThanOrEqualTo(String value) {
            addCriterion("AnalyteId <=", value, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidLike(String value) {
            addCriterion("AnalyteId like", value, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidNotLike(String value) {
            addCriterion("AnalyteId not like", value, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidIn(List<String> values) {
            addCriterion("AnalyteId in", values, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidNotIn(List<String> values) {
            addCriterion("AnalyteId not in", values, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidBetween(String value1, String value2) {
            addCriterion("AnalyteId between", value1, value2, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalyteidNotBetween(String value1, String value2) {
            addCriterion("AnalyteId not between", value1, value2, "analyteid");
            return (Criteria) this;
        }

        public Criteria andAnalytenameIsNull() {
            addCriterion("AnalyteName is null");
            return (Criteria) this;
        }

        public Criteria andAnalytenameIsNotNull() {
            addCriterion("AnalyteName is not null");
            return (Criteria) this;
        }

        public Criteria andAnalytenameEqualTo(String value) {
            addCriterion("AnalyteName =", value, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameNotEqualTo(String value) {
            addCriterion("AnalyteName <>", value, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameGreaterThan(String value) {
            addCriterion("AnalyteName >", value, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameGreaterThanOrEqualTo(String value) {
            addCriterion("AnalyteName >=", value, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameLessThan(String value) {
            addCriterion("AnalyteName <", value, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameLessThanOrEqualTo(String value) {
            addCriterion("AnalyteName <=", value, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameLike(String value) {
            addCriterion("AnalyteName like", value, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameNotLike(String value) {
            addCriterion("AnalyteName not like", value, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameIn(List<String> values) {
            addCriterion("AnalyteName in", values, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameNotIn(List<String> values) {
            addCriterion("AnalyteName not in", values, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameBetween(String value1, String value2) {
            addCriterion("AnalyteName between", value1, value2, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytenameNotBetween(String value1, String value2) {
            addCriterion("AnalyteName not between", value1, value2, "analytename");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeIsNull() {
            addCriterion("AnalyteType is null");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeIsNotNull() {
            addCriterion("AnalyteType is not null");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeEqualTo(Integer value) {
            addCriterion("AnalyteType =", value, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeNotEqualTo(Integer value) {
            addCriterion("AnalyteType <>", value, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeGreaterThan(Integer value) {
            addCriterion("AnalyteType >", value, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("AnalyteType >=", value, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeLessThan(Integer value) {
            addCriterion("AnalyteType <", value, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeLessThanOrEqualTo(Integer value) {
            addCriterion("AnalyteType <=", value, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeIn(List<Integer> values) {
            addCriterion("AnalyteType in", values, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeNotIn(List<Integer> values) {
            addCriterion("AnalyteType not in", values, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeBetween(Integer value1, Integer value2) {
            addCriterion("AnalyteType between", value1, value2, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytetypeNotBetween(Integer value1, Integer value2) {
            addCriterion("AnalyteType not between", value1, value2, "analytetype");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeIsNull() {
            addCriterion("AnalyteCode is null");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeIsNotNull() {
            addCriterion("AnalyteCode is not null");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeEqualTo(String value) {
            addCriterion("AnalyteCode =", value, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeNotEqualTo(String value) {
            addCriterion("AnalyteCode <>", value, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeGreaterThan(String value) {
            addCriterion("AnalyteCode >", value, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeGreaterThanOrEqualTo(String value) {
            addCriterion("AnalyteCode >=", value, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeLessThan(String value) {
            addCriterion("AnalyteCode <", value, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeLessThanOrEqualTo(String value) {
            addCriterion("AnalyteCode <=", value, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeLike(String value) {
            addCriterion("AnalyteCode like", value, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeNotLike(String value) {
            addCriterion("AnalyteCode not like", value, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeIn(List<String> values) {
            addCriterion("AnalyteCode in", values, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeNotIn(List<String> values) {
            addCriterion("AnalyteCode not in", values, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeBetween(String value1, String value2) {
            addCriterion("AnalyteCode between", value1, value2, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalytecodeNotBetween(String value1, String value2) {
            addCriterion("AnalyteCode not between", value1, value2, "analytecode");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqIsNull() {
            addCriterion("AnalyteSeq is null");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqIsNotNull() {
            addCriterion("AnalyteSeq is not null");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqEqualTo(Integer value) {
            addCriterion("AnalyteSeq =", value, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqNotEqualTo(Integer value) {
            addCriterion("AnalyteSeq <>", value, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqGreaterThan(Integer value) {
            addCriterion("AnalyteSeq >", value, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqGreaterThanOrEqualTo(Integer value) {
            addCriterion("AnalyteSeq >=", value, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqLessThan(Integer value) {
            addCriterion("AnalyteSeq <", value, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqLessThanOrEqualTo(Integer value) {
            addCriterion("AnalyteSeq <=", value, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqIn(List<Integer> values) {
            addCriterion("AnalyteSeq in", values, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqNotIn(List<Integer> values) {
            addCriterion("AnalyteSeq not in", values, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqBetween(Integer value1, Integer value2) {
            addCriterion("AnalyteSeq between", value1, value2, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andAnalyteseqNotBetween(Integer value1, Integer value2) {
            addCriterion("AnalyteSeq not between", value1, value2, "analyteseq");
            return (Criteria) this;
        }

        public Criteria andReportunitIsNull() {
            addCriterion("ReportUnit is null");
            return (Criteria) this;
        }

        public Criteria andReportunitIsNotNull() {
            addCriterion("ReportUnit is not null");
            return (Criteria) this;
        }

        public Criteria andReportunitEqualTo(String value) {
            addCriterion("ReportUnit =", value, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitNotEqualTo(String value) {
            addCriterion("ReportUnit <>", value, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitGreaterThan(String value) {
            addCriterion("ReportUnit >", value, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitGreaterThanOrEqualTo(String value) {
            addCriterion("ReportUnit >=", value, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitLessThan(String value) {
            addCriterion("ReportUnit <", value, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitLessThanOrEqualTo(String value) {
            addCriterion("ReportUnit <=", value, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitLike(String value) {
            addCriterion("ReportUnit like", value, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitNotLike(String value) {
            addCriterion("ReportUnit not like", value, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitIn(List<String> values) {
            addCriterion("ReportUnit in", values, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitNotIn(List<String> values) {
            addCriterion("ReportUnit not in", values, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitBetween(String value1, String value2) {
            addCriterion("ReportUnit between", value1, value2, "reportunit");
            return (Criteria) this;
        }

        public Criteria andReportunitNotBetween(String value1, String value2) {
            addCriterion("ReportUnit not between", value1, value2, "reportunit");
            return (Criteria) this;
        }

        public Criteria andTestvalueIsNull() {
            addCriterion("TestValue is null");
            return (Criteria) this;
        }

        public Criteria andTestvalueIsNotNull() {
            addCriterion("TestValue is not null");
            return (Criteria) this;
        }

        public Criteria andTestvalueEqualTo(String value) {
            addCriterion("TestValue =", value, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueNotEqualTo(String value) {
            addCriterion("TestValue <>", value, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueGreaterThan(String value) {
            addCriterion("TestValue >", value, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueGreaterThanOrEqualTo(String value) {
            addCriterion("TestValue >=", value, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueLessThan(String value) {
            addCriterion("TestValue <", value, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueLessThanOrEqualTo(String value) {
            addCriterion("TestValue <=", value, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueLike(String value) {
            addCriterion("TestValue like", value, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueNotLike(String value) {
            addCriterion("TestValue not like", value, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueIn(List<String> values) {
            addCriterion("TestValue in", values, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueNotIn(List<String> values) {
            addCriterion("TestValue not in", values, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueBetween(String value1, String value2) {
            addCriterion("TestValue between", value1, value2, "testvalue");
            return (Criteria) this;
        }

        public Criteria andTestvalueNotBetween(String value1, String value2) {
            addCriterion("TestValue not between", value1, value2, "testvalue");
            return (Criteria) this;
        }

        public Criteria andCasnoIsNull() {
            addCriterion("CasNo is null");
            return (Criteria) this;
        }

        public Criteria andCasnoIsNotNull() {
            addCriterion("CasNo is not null");
            return (Criteria) this;
        }

        public Criteria andCasnoEqualTo(String value) {
            addCriterion("CasNo =", value, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoNotEqualTo(String value) {
            addCriterion("CasNo <>", value, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoGreaterThan(String value) {
            addCriterion("CasNo >", value, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoGreaterThanOrEqualTo(String value) {
            addCriterion("CasNo >=", value, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoLessThan(String value) {
            addCriterion("CasNo <", value, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoLessThanOrEqualTo(String value) {
            addCriterion("CasNo <=", value, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoLike(String value) {
            addCriterion("CasNo like", value, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoNotLike(String value) {
            addCriterion("CasNo not like", value, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoIn(List<String> values) {
            addCriterion("CasNo in", values, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoNotIn(List<String> values) {
            addCriterion("CasNo not in", values, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoBetween(String value1, String value2) {
            addCriterion("CasNo between", value1, value2, "casno");
            return (Criteria) this;
        }

        public Criteria andCasnoNotBetween(String value1, String value2) {
            addCriterion("CasNo not between", value1, value2, "casno");
            return (Criteria) this;
        }

        public Criteria andReportlimitIsNull() {
            addCriterion("ReportLimit is null");
            return (Criteria) this;
        }

        public Criteria andReportlimitIsNotNull() {
            addCriterion("ReportLimit is not null");
            return (Criteria) this;
        }

        public Criteria andReportlimitEqualTo(String value) {
            addCriterion("ReportLimit =", value, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitNotEqualTo(String value) {
            addCriterion("ReportLimit <>", value, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitGreaterThan(String value) {
            addCriterion("ReportLimit >", value, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitGreaterThanOrEqualTo(String value) {
            addCriterion("ReportLimit >=", value, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitLessThan(String value) {
            addCriterion("ReportLimit <", value, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitLessThanOrEqualTo(String value) {
            addCriterion("ReportLimit <=", value, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitLike(String value) {
            addCriterion("ReportLimit like", value, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitNotLike(String value) {
            addCriterion("ReportLimit not like", value, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitIn(List<String> values) {
            addCriterion("ReportLimit in", values, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitNotIn(List<String> values) {
            addCriterion("ReportLimit not in", values, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitBetween(String value1, String value2) {
            addCriterion("ReportLimit between", value1, value2, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andReportlimitNotBetween(String value1, String value2) {
            addCriterion("ReportLimit not between", value1, value2, "reportlimit");
            return (Criteria) this;
        }

        public Criteria andLimitunitIsNull() {
            addCriterion("LimitUnit is null");
            return (Criteria) this;
        }

        public Criteria andLimitunitIsNotNull() {
            addCriterion("LimitUnit is not null");
            return (Criteria) this;
        }

        public Criteria andLimitunitEqualTo(String value) {
            addCriterion("LimitUnit =", value, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitNotEqualTo(String value) {
            addCriterion("LimitUnit <>", value, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitGreaterThan(String value) {
            addCriterion("LimitUnit >", value, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitGreaterThanOrEqualTo(String value) {
            addCriterion("LimitUnit >=", value, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitLessThan(String value) {
            addCriterion("LimitUnit <", value, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitLessThanOrEqualTo(String value) {
            addCriterion("LimitUnit <=", value, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitLike(String value) {
            addCriterion("LimitUnit like", value, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitNotLike(String value) {
            addCriterion("LimitUnit not like", value, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitIn(List<String> values) {
            addCriterion("LimitUnit in", values, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitNotIn(List<String> values) {
            addCriterion("LimitUnit not in", values, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitBetween(String value1, String value2) {
            addCriterion("LimitUnit between", value1, value2, "limitunit");
            return (Criteria) this;
        }

        public Criteria andLimitunitNotBetween(String value1, String value2) {
            addCriterion("LimitUnit not between", value1, value2, "limitunit");
            return (Criteria) this;
        }

        public Criteria andConclusionidIsNull() {
            addCriterion("ConclusionId is null");
            return (Criteria) this;
        }

        public Criteria andConclusionidIsNotNull() {
            addCriterion("ConclusionId is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionidEqualTo(String value) {
            addCriterion("ConclusionId =", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidNotEqualTo(String value) {
            addCriterion("ConclusionId <>", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidGreaterThan(String value) {
            addCriterion("ConclusionId >", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidGreaterThanOrEqualTo(String value) {
            addCriterion("ConclusionId >=", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidLessThan(String value) {
            addCriterion("ConclusionId <", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidLessThanOrEqualTo(String value) {
            addCriterion("ConclusionId <=", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidLike(String value) {
            addCriterion("ConclusionId like", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidNotLike(String value) {
            addCriterion("ConclusionId not like", value, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidIn(List<String> values) {
            addCriterion("ConclusionId in", values, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidNotIn(List<String> values) {
            addCriterion("ConclusionId not in", values, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidBetween(String value1, String value2) {
            addCriterion("ConclusionId between", value1, value2, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andConclusionidNotBetween(String value1, String value2) {
            addCriterion("ConclusionId not between", value1, value2, "conclusionid");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorIsNull() {
            addCriterion("ActiveIndicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorIsNotNull() {
            addCriterion("ActiveIndicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorEqualTo(Integer value) {
            addCriterion("ActiveIndicator =", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorNotEqualTo(Integer value) {
            addCriterion("ActiveIndicator <>", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorGreaterThan(Integer value) {
            addCriterion("ActiveIndicator >", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("ActiveIndicator >=", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorLessThan(Integer value) {
            addCriterion("ActiveIndicator <", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorLessThanOrEqualTo(Integer value) {
            addCriterion("ActiveIndicator <=", value, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorIn(List<Integer> values) {
            addCriterion("ActiveIndicator in", values, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorNotIn(List<Integer> values) {
            addCriterion("ActiveIndicator not in", values, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorBetween(Integer value1, Integer value2) {
            addCriterion("ActiveIndicator between", value1, value2, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andActiveindicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("ActiveIndicator not between", value1, value2, "activeindicator");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLike(String value) {
            addCriterion("CreatedBy like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreateddateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreateddateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreateddateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createddate");
            return (Criteria) this;
        }

        public Criteria andCreateddateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createddate");
            return (Criteria) this;
        }

        public Criteria andModifiedbyIsNull() {
            addCriterion("ModifiedBy is null");
            return (Criteria) this;
        }

        public Criteria andModifiedbyIsNotNull() {
            addCriterion("ModifiedBy is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedbyEqualTo(String value) {
            addCriterion("ModifiedBy =", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyNotEqualTo(String value) {
            addCriterion("ModifiedBy <>", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyGreaterThan(String value) {
            addCriterion("ModifiedBy >", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyGreaterThanOrEqualTo(String value) {
            addCriterion("ModifiedBy >=", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyLessThan(String value) {
            addCriterion("ModifiedBy <", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyLessThanOrEqualTo(String value) {
            addCriterion("ModifiedBy <=", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyLike(String value) {
            addCriterion("ModifiedBy like", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyNotLike(String value) {
            addCriterion("ModifiedBy not like", value, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyIn(List<String> values) {
            addCriterion("ModifiedBy in", values, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyNotIn(List<String> values) {
            addCriterion("ModifiedBy not in", values, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyBetween(String value1, String value2) {
            addCriterion("ModifiedBy between", value1, value2, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifiedbyNotBetween(String value1, String value2) {
            addCriterion("ModifiedBy not between", value1, value2, "modifiedby");
            return (Criteria) this;
        }

        public Criteria andModifieddateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifieddateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifieddateEqualTo(Date value) {
            addCriterion("ModifiedDate =", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateNotEqualTo(Date value) {
            addCriterion("ModifiedDate <>", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateGreaterThan(Date value) {
            addCriterion("ModifiedDate >", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateGreaterThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate >=", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateLessThan(Date value) {
            addCriterion("ModifiedDate <", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateLessThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate <=", value, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateIn(List<Date> values) {
            addCriterion("ModifiedDate in", values, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateNotIn(List<Date> values) {
            addCriterion("ModifiedDate not in", values, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andModifieddateNotBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifieddate");
            return (Criteria) this;
        }

        public Criteria andBizversionidIsNull() {
            addCriterion("BizVersionId is null");
            return (Criteria) this;
        }

        public Criteria andBizversionidIsNotNull() {
            addCriterion("BizVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andBizversionidEqualTo(String value) {
            addCriterion("BizVersionId =", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidNotEqualTo(String value) {
            addCriterion("BizVersionId <>", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidGreaterThan(String value) {
            addCriterion("BizVersionId >", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidGreaterThanOrEqualTo(String value) {
            addCriterion("BizVersionId >=", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidLessThan(String value) {
            addCriterion("BizVersionId <", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidLessThanOrEqualTo(String value) {
            addCriterion("BizVersionId <=", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidLike(String value) {
            addCriterion("BizVersionId like", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidNotLike(String value) {
            addCriterion("BizVersionId not like", value, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidIn(List<String> values) {
            addCriterion("BizVersionId in", values, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidNotIn(List<String> values) {
            addCriterion("BizVersionId not in", values, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidBetween(String value1, String value2) {
            addCriterion("BizVersionId between", value1, value2, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andBizversionidNotBetween(String value1, String value2) {
            addCriterion("BizVersionId not between", value1, value2, "bizversionid");
            return (Criteria) this;
        }

        public Criteria andRdReportIdIsNull() {
            addCriterion("rd_report_id is null");
            return (Criteria) this;
        }

        public Criteria andRdReportIdIsNotNull() {
            addCriterion("rd_report_id is not null");
            return (Criteria) this;
        }

        public Criteria andRdReportIdEqualTo(Long value) {
            addCriterion("rd_report_id =", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdNotEqualTo(Long value) {
            addCriterion("rd_report_id <>", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdGreaterThan(Long value) {
            addCriterion("rd_report_id >", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rd_report_id >=", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdLessThan(Long value) {
            addCriterion("rd_report_id <", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdLessThanOrEqualTo(Long value) {
            addCriterion("rd_report_id <=", value, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdIn(List<Long> values) {
            addCriterion("rd_report_id in", values, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdNotIn(List<Long> values) {
            addCriterion("rd_report_id not in", values, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdBetween(Long value1, Long value2) {
            addCriterion("rd_report_id between", value1, value2, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andRdReportIdNotBetween(Long value1, Long value2) {
            addCriterion("rd_report_id not between", value1, value2, "rdReportId");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNull() {
            addCriterion("lab_id is null");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNotNull() {
            addCriterion("lab_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabIdEqualTo(Long value) {
            addCriterion("lab_id =", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotEqualTo(Long value) {
            addCriterion("lab_id <>", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThan(Long value) {
            addCriterion("lab_id >", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThanOrEqualTo(Long value) {
            addCriterion("lab_id >=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThan(Long value) {
            addCriterion("lab_id <", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThanOrEqualTo(Long value) {
            addCriterion("lab_id <=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdIn(List<Long> values) {
            addCriterion("lab_id in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotIn(List<Long> values) {
            addCriterion("lab_id not in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdBetween(Long value1, Long value2) {
            addCriterion("lab_id between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotBetween(Long value1, Long value2) {
            addCriterion("lab_id not between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("report_no is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("report_no is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("report_no =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("report_no <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("report_no >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("report_no >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("report_no <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("report_no <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("report_no like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("report_no not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("report_no in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("report_no not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("report_no between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("report_no not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameIsNull() {
            addCriterion("test_result_full_name is null");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameIsNotNull() {
            addCriterion("test_result_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameEqualTo(String value) {
            addCriterion("test_result_full_name =", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameNotEqualTo(String value) {
            addCriterion("test_result_full_name <>", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameGreaterThan(String value) {
            addCriterion("test_result_full_name >", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("test_result_full_name >=", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameLessThan(String value) {
            addCriterion("test_result_full_name <", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameLessThanOrEqualTo(String value) {
            addCriterion("test_result_full_name <=", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameLike(String value) {
            addCriterion("test_result_full_name like", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameNotLike(String value) {
            addCriterion("test_result_full_name not like", value, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameIn(List<String> values) {
            addCriterion("test_result_full_name in", values, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameNotIn(List<String> values) {
            addCriterion("test_result_full_name not in", values, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameBetween(String value1, String value2) {
            addCriterion("test_result_full_name between", value1, value2, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultFullNameNotBetween(String value1, String value2) {
            addCriterion("test_result_full_name not between", value1, value2, "testResultFullName");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqIsNull() {
            addCriterion("test_result_seq is null");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqIsNotNull() {
            addCriterion("test_result_seq is not null");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqEqualTo(Integer value) {
            addCriterion("test_result_seq =", value, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqNotEqualTo(Integer value) {
            addCriterion("test_result_seq <>", value, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqGreaterThan(Integer value) {
            addCriterion("test_result_seq >", value, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("test_result_seq >=", value, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqLessThan(Integer value) {
            addCriterion("test_result_seq <", value, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqLessThanOrEqualTo(Integer value) {
            addCriterion("test_result_seq <=", value, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqIn(List<Integer> values) {
            addCriterion("test_result_seq in", values, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqNotIn(List<Integer> values) {
            addCriterion("test_result_seq not in", values, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqBetween(Integer value1, Integer value2) {
            addCriterion("test_result_seq between", value1, value2, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andTestResultSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("test_result_seq not between", value1, value2, "testResultSeq");
            return (Criteria) this;
        }

        public Criteria andResultValueIsNull() {
            addCriterion("result_value is null");
            return (Criteria) this;
        }

        public Criteria andResultValueIsNotNull() {
            addCriterion("result_value is not null");
            return (Criteria) this;
        }

        public Criteria andResultValueEqualTo(String value) {
            addCriterion("result_value =", value, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueNotEqualTo(String value) {
            addCriterion("result_value <>", value, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueGreaterThan(String value) {
            addCriterion("result_value >", value, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueGreaterThanOrEqualTo(String value) {
            addCriterion("result_value >=", value, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueLessThan(String value) {
            addCriterion("result_value <", value, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueLessThanOrEqualTo(String value) {
            addCriterion("result_value <=", value, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueLike(String value) {
            addCriterion("result_value like", value, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueNotLike(String value) {
            addCriterion("result_value not like", value, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueIn(List<String> values) {
            addCriterion("result_value in", values, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueNotIn(List<String> values) {
            addCriterion("result_value not in", values, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueBetween(String value1, String value2) {
            addCriterion("result_value between", value1, value2, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueNotBetween(String value1, String value2) {
            addCriterion("result_value not between", value1, value2, "resultValue");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkIsNull() {
            addCriterion("result_value_remark is null");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkIsNotNull() {
            addCriterion("result_value_remark is not null");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkEqualTo(String value) {
            addCriterion("result_value_remark =", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkNotEqualTo(String value) {
            addCriterion("result_value_remark <>", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkGreaterThan(String value) {
            addCriterion("result_value_remark >", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("result_value_remark >=", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkLessThan(String value) {
            addCriterion("result_value_remark <", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkLessThanOrEqualTo(String value) {
            addCriterion("result_value_remark <=", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkLike(String value) {
            addCriterion("result_value_remark like", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkNotLike(String value) {
            addCriterion("result_value_remark not like", value, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkIn(List<String> values) {
            addCriterion("result_value_remark in", values, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkNotIn(List<String> values) {
            addCriterion("result_value_remark not in", values, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkBetween(String value1, String value2) {
            addCriterion("result_value_remark between", value1, value2, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultValueRemarkNotBetween(String value1, String value2) {
            addCriterion("result_value_remark not between", value1, value2, "resultValueRemark");
            return (Criteria) this;
        }

        public Criteria andResultUnitIsNull() {
            addCriterion("result_unit is null");
            return (Criteria) this;
        }

        public Criteria andResultUnitIsNotNull() {
            addCriterion("result_unit is not null");
            return (Criteria) this;
        }

        public Criteria andResultUnitEqualTo(String value) {
            addCriterion("result_unit =", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitNotEqualTo(String value) {
            addCriterion("result_unit <>", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitGreaterThan(String value) {
            addCriterion("result_unit >", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitGreaterThanOrEqualTo(String value) {
            addCriterion("result_unit >=", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitLessThan(String value) {
            addCriterion("result_unit <", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitLessThanOrEqualTo(String value) {
            addCriterion("result_unit <=", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitLike(String value) {
            addCriterion("result_unit like", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitNotLike(String value) {
            addCriterion("result_unit not like", value, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitIn(List<String> values) {
            addCriterion("result_unit in", values, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitNotIn(List<String> values) {
            addCriterion("result_unit not in", values, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitBetween(String value1, String value2) {
            addCriterion("result_unit between", value1, value2, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andResultUnitNotBetween(String value1, String value2) {
            addCriterion("result_unit not between", value1, value2, "resultUnit");
            return (Criteria) this;
        }

        public Criteria andFailFlagIsNull() {
            addCriterion("fail_flag is null");
            return (Criteria) this;
        }

        public Criteria andFailFlagIsNotNull() {
            addCriterion("fail_flag is not null");
            return (Criteria) this;
        }

        public Criteria andFailFlagEqualTo(Integer value) {
            addCriterion("fail_flag =", value, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagNotEqualTo(Integer value) {
            addCriterion("fail_flag <>", value, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagGreaterThan(Integer value) {
            addCriterion("fail_flag >", value, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("fail_flag >=", value, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagLessThan(Integer value) {
            addCriterion("fail_flag <", value, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagLessThanOrEqualTo(Integer value) {
            addCriterion("fail_flag <=", value, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagIn(List<Integer> values) {
            addCriterion("fail_flag in", values, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagNotIn(List<Integer> values) {
            addCriterion("fail_flag not in", values, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagBetween(Integer value1, Integer value2) {
            addCriterion("fail_flag between", value1, value2, "failFlag");
            return (Criteria) this;
        }

        public Criteria andFailFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("fail_flag not between", value1, value2, "failFlag");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameIsNull() {
            addCriterion("limit_value_full_name is null");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameIsNotNull() {
            addCriterion("limit_value_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameEqualTo(String value) {
            addCriterion("limit_value_full_name =", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameNotEqualTo(String value) {
            addCriterion("limit_value_full_name <>", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameGreaterThan(String value) {
            addCriterion("limit_value_full_name >", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("limit_value_full_name >=", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameLessThan(String value) {
            addCriterion("limit_value_full_name <", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameLessThanOrEqualTo(String value) {
            addCriterion("limit_value_full_name <=", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameLike(String value) {
            addCriterion("limit_value_full_name like", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameNotLike(String value) {
            addCriterion("limit_value_full_name not like", value, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameIn(List<String> values) {
            addCriterion("limit_value_full_name in", values, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameNotIn(List<String> values) {
            addCriterion("limit_value_full_name not in", values, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameBetween(String value1, String value2) {
            addCriterion("limit_value_full_name between", value1, value2, "limitValueFullName");
            return (Criteria) this;
        }

        public Criteria andLimitValueFullNameNotBetween(String value1, String value2) {
            addCriterion("limit_value_full_name not between", value1, value2, "limitValueFullName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}