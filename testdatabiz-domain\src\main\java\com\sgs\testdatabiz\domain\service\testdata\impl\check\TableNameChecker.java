package com.sgs.testdatabiz.domain.service.testdata.impl.check;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.core.exception.ReportDataCheckException;
import com.sgs.testdatabiz.domain.service.validation.ValidatinServiceFactory;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.model.SubContactTableNameValidationDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TableNameChecker {

    @Autowired
    public ValidatinServiceFactory validationServiceFactory;
    public void checkTableNameExist(String labCode) {
        List<ValidationService> validationServiceList = validationServiceFactory.getValidationServices(ValidationTypeEnum.SUBCONTRACT_TABLE.getName());
        if(Func.isNotEmpty(validationServiceList)) {
            validationServiceList.forEach(validationService -> {
                SubContactTableNameValidationDTO subContactTableNameValidationDTO =  new SubContactTableNameValidationDTO();
                subContactTableNameValidationDTO.setLabCode(labCode);
                ValidationResultDTO validationResultDTO = validationService.validate(subContactTableNameValidationDTO);
                if(Func.isNotEmpty(validationResultDTO) && !validationResultDTO.isValidFlag()) {
                    throw new ReportDataCheckException(validationResultDTO.getResultCode(), validationResultDTO.getResultMessage());
                }
            });
        }
    }
}
