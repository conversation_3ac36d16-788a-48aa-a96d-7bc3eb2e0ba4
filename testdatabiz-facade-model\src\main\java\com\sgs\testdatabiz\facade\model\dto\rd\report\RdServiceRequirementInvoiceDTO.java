/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceRequirementInvoiceDTO implements Serializable {
    private Integer invoiceType;
    private String invoiceTitle;
    private String taxNo;
    private String registerAddr;
    private String registerPhone;
    private String bankName;
    private String bankNumber;
    private String actualAmountPaid;
    private RdDeliveryDTO invoice;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
