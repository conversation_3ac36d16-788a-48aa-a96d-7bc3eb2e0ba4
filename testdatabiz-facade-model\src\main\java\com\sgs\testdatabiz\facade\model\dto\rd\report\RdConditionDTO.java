/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdConditionDTO implements Serializable{
    private String conditionInstanceId;
    private Integer conditionId;
    private Integer conditionType;
    private Integer conditionTypeId;
    private String conditionTypeName;
    private String conditionName;
    private String conditionDesc;
    private Integer conditionSeq;
    private List<RdConditionLanguageDTO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}