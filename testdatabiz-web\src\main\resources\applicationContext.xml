<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-4.2.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd
        http://www.springframework.org/schema/task
        http://www.springframework.org/schema/task/spring-task.xsd">

    <!--启动注解-->
    <context:annotation-config/>
    <!--开启spring对AspectJ的支持,也可通过注解：@EnableAspectJAutoProxy开启-->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <!-- 配置dubbo注解识别处理器，不指定包名的话会在spring bean中查找对应实例的类配置了dubbo注解的 -->

    <!--spring自动扫描的包下@Service、@Component、@repository和@Controller的类，并注册为Bean-->
    <context:component-scan base-package="com.sgs.testdatabiz,com.sgs.framework"/>

    <!--任务扫描注解 start-->
    <task:executor id="executor" pool-size="5"/>
    <task:scheduler id="scheduler" pool-size="5"/>
    <task:annotation-driven executor="executor" scheduler="scheduler"/>
    <!--任务扫描注解 end-->

    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath*:cache.properties</value>
            </list>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true" />
    </bean>

    <!-- Kafka 配置参数 -->
    <bean id="kafkaConsumerConfig" class="com.sgs.grus.kafka.client.KafkaConsumerConfig">
        <!-- Kafka服务集群地址，以逗号分隔的主机：端口对列表，如：10.205.138.135:9092,10.205.237.229:9092 -->
        <property name="brokers" value="${kafka.bootstrap-servers}"/>
        <!-- 在侦听器容器中运行的并发数 -->
        <property name="concurrency" value="2"/>
    </bean>

    <!-- kafka 客户端 -->
    <bean id="kafkaClient" class="com.sgs.grus.kafka.client.KafkaClient">
        <!-- 应用服务地址，注意：这个要每个应用唯一 -->
        <property name="appId" value="cnapp.sgs.net.testDatabizapi"/>
        <!-- 异步发送消息线程数，推荐2，可根据发送量及机器性能进行配置 -->
        <property name="asyncSendMaxThreadNum" value="2"/>
        <!-- 异步发送消息队列长度，推荐10000，可根据发送量及机器性能进行配置 -->
        <property name="asyncSendMaxQueueNum" value="10000"/>
        <!-- 初始Kafka 配置参数 -->
        <property name="kafkaConsumerConfig" ref="kafkaConsumerConfig"/>
    </bean>

    <!--使用httpclient的实现，带连接池-->
    <bean id="pollingConnectionManager" class="org.apache.http.impl.conn.PoolingHttpClientConnectionManager">
        <!--整个连接池的并发-->
        <property name="maxTotal" value="50" />
        <!--每个主机的并发-->
        <property name="defaultMaxPerRoute" value="50" />
    </bean>

    <bean id="httpClientBuilder" class="org.apache.http.impl.client.HttpClientBuilder" factory-method="create">
        <property name="connectionManager" ref="pollingConnectionManager" />
        <!--开启重试-->
        <property name="retryHandler">
            <bean class="org.apache.http.impl.client.DefaultHttpRequestRetryHandler">
                <constructor-arg value="2"/>
                <constructor-arg value="true"/>
            </bean>
        </property>
        <property name="defaultHeaders">
            <list>
                <bean class="org.apache.http.message.BasicHeader">
                    <constructor-arg value="Content-Type"/>
                    <constructor-arg value="text/html;charset=UTF-8"/>
                </bean>
                <bean class="org.apache.http.message.BasicHeader">
                    <constructor-arg value="Accept-Encoding"/>
                    <constructor-arg value="gzip,deflate"/>
                </bean>
                <bean class="org.apache.http.message.BasicHeader">
                    <constructor-arg value="Accept-Language"/>
                    <constructor-arg value="zh-CN"/>
                </bean>
            </list>
        </property>
    </bean>

    <bean id="httpClient" factory-bean="httpClientBuilder" factory-method="build" />

    <bean id="restTemplate" class="org.springframework.web.client.RestTemplate">
        <property name="messageConverters">
            <list value-type="org.springframework.http.converter.HttpMessageConverter">
                <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                    <property name="supportedMediaTypes">
                        <value>text/html;charset=UTF-8</value>
                    </property>
                </bean>
                <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
                    <property name="supportedMediaTypes">
                        <value>application/json;charset=UTF-8</value>
                    </property>
                </bean>
                <bean class="org.springframework.http.converter.ResourceHttpMessageConverter"/>
                <bean class="org.springframework.http.converter.support.AllEncompassingFormHttpMessageConverter"/>
                <bean class="org.springframework.http.converter.FormHttpMessageConverter"/>
                <!--<bean class="org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter"/>-->
            </list>
        </property>
        <property name="requestFactory">
            <bean class="org.springframework.http.client.HttpComponentsClientHttpRequestFactory">
                <constructor-arg ref="httpClient"/>
                <!--连接时间(毫秒)-->
                <property name="connectTimeout" value="20000"/>
                <!--读取时间(毫秒)-->
                <property name="readTimeout" value="20000"/>
            </bean>
        </property>
    </bean>

    <!-- 线程池 -->
    <bean id="taskExecutor" class="com.sgs.testdatabiz.core.thread.ThreadPoolContextTaskExecutor">
        <!-- 核心线程数，默认为1 -->
        <property name="corePoolSize" value="5"/>
        <!-- 最大线程数，默认为Integer.MAX_VALUE -->
        <property name="maxPoolSize" value="100"/>
        <!-- 队列最大长度，一般需要设置值>=notifyScheduledMainExecutor.maxNum；默认为Integer.MAX_VALUE -->
        <property name="queueCapacity" value="50000"/>
        <!-- 线程池维护线程所允许的空闲时间，默认为60s -->
        <property name="keepAliveSeconds" value="300"/>

        <!-- 线程池对拒绝任务（无线程可用）的处理策略 -->
        <property name="rejectedExecutionHandler">
            <!-- AbortPolicy:直接抛出java.util.concurrent.RejectedExecutionException异常 -->
            <!-- CallerRunsPolicy:主线程直接执行该任务，执行完之后尝试添加下一个任务到线程池中，可以有效降低向线程池内添加任务的速度 -->
            <!-- DiscardOldestPolicy:抛弃旧的任务 ；会导致被丢弃的任务无法再次被执行 -->
            <!-- DiscardPolicy:抛弃当前任务 ；会导致被丢弃的任务无法再次被执行 -->
            <bean class="com.sgs.testdatabiz.core.thread.LogRejectedPolicy"/>
        </property>
    </bean>

    <!--<import resource="classpath:spring/sqlserver-persistence.xml" />-->

    <!-- 提供方应用信息，用于计算依赖关系 -->
    <dubbo:application name="cnapp.sgs.net.testdatabizapi"/>
    <dubbo:registry protocol="zookeeper" address="${zookeeper.address}" timeout="30000"/>

    <!-- 协议注册 -->
    <dubbo:protocol name="dubbo" port="${dubbo.port}"/>
    <!--<dubbo:protocol name="rest" server="servlet" port="${tomcat.port}" extension="JacksonConfig"/>-->


    <dubbo:annotation package="com.sgs.testdatabiz.facade.impl"/>

    <!-- 服务注册 -->
    <dubbo:service group="SODA" ref="testDataFacade" protocol="dubbo" interface="com.sgs.testdatabiz.facade.TestDataFacade"/>
    <dubbo:service group="SODA" ref="fastFacade" protocol="dubbo" interface="com.sgs.testdatabiz.facade.FastFacade"/>
    <dubbo:service group="SODA" ref="slimTestDataFacade" protocol="dubbo" interface="com.sgs.testdatabiz.facade.ISlimTestDataFacade"/>
    <dubbo:service group="SODA" ref="starlimsReportFacade" protocol="dubbo" interface="com.sgs.testdatabiz.facade.IStarlimsReportFacade"/>
    <dubbo:service group="SODA" ref="testDataSubContractFacade" protocol="dubbo" interface="com.sgs.testdatabiz.facade.SubContractFacade"/>
    <dubbo:service group="SODA" ref="reportTestDataFacadeImpl" protocol="dubbo" interface="com.sgs.testdatabiz.facade.v2.ReportTestDataFacade" retries="0" timeout="30000"/>
    <dubbo:service group="SODA" ref="reportTestDataServiceImpl" protocol="dubbo" interface="com.sgs.testdatabiz.facade.v2.ReportTestDataService" retries="0" timeout="30000"/>
    <dubbo:service group="SODA" ref="conclusionFacade" protocol="dubbo" interface="com.sgs.testdatabiz.facade.IConclusionFacade" retries="0" timeout="30000"/>

    <!-- preOrder -->
    <dubbo:reference id="todoListFacade" group="SODA" protocol="dubbo" check="false" interface="com.sgs.preorder.facade.TodoListFacade" retries="0" timeout="50000"/>
    <dubbo:reference id="orderFacade" group="SODA" protocol="dubbo" check="false" interface="com.sgs.preorder.facade.OrderFacade" retries="0" timeout="50000"/>
    <dubbo:reference id="customerFacade" group="SODA" protocol="dubbo" check="false" interface="com.sgs.preorder.facade.CustomerFacade" retries="0" timeout="50000"/>

    <!-- 引用Trims Local服务 -->
    <dubbo:reference id="ppFacade" group="SODA" protocol="dubbo" check="false" interface="com.sgs.trimslocal.facade.IPpFacade" retries="0" timeout="50000"/>
    <dubbo:reference id="ppArtifactRelFacade" group="SODA" protocol="dubbo" check="false" interface="com.sgs.trimslocal.facade.IPpArtifactRelFacade" retries="0" timeout="50000"/>
    <dubbo:reference id="citationFacade" group="SODA" protocol="dubbo" check="false" interface="com.sgs.trimslocal.facade.ICitationFacade" retries="0" timeout="50000"/>

    <!-- fileService -->

    <!--  extsystem -->
    <dubbo:reference group="SODA" id="customerConfigFacade" protocol="dubbo" check="false" interface="com.sgs.extsystem.facade.CustomerConfigFacade" retries="0" timeout="5000"/>


</beans>