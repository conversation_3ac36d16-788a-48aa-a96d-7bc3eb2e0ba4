package com.sgs.testdatabiz.facade.model.testdata;

import io.swagger.annotations.ApiModelProperty;

public class TestDataConclusionRsp {
    /**
     *
     */
    @ApiModelProperty("conclusionId")
    private Integer conclusionId;

    /**
     *
     */
    @ApiModelProperty("conclusionCode")
    private String conclusionCode;

    /**
     *
     */
    @ApiModelProperty("conclusionRemark")
    private Integer conclusionRemark;

    /**
     *
     */
    @ApiModelProperty("customerConclusion")
    private String customerConclusion;

    public Integer getConclusionId() {
        return conclusionId;
    }

    public void setConclusionId(Integer conclusionId) {
        this.conclusionId = conclusionId;
    }

    public String getConclusionCode() {
        return conclusionCode;
    }

    public void setConclusionCode(String conclusionCode) {
        this.conclusionCode = conclusionCode;
    }

    public Integer getConclusionRemark() {
        return conclusionRemark;
    }

    public void setConclusionRemark(Integer conclusionRemark) {
        this.conclusionRemark = conclusionRemark;
    }

    public String getCustomerConclusion() {
        return customerConclusion;
    }

    public void setCustomerConclusion(String customerConclusion) {
        this.customerConclusion = customerConclusion;
    }
}
