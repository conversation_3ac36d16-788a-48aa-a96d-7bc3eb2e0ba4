<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestLineMappingExtMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineMappingInfoPO" >
    <id column="Id" property="id" jdbcType="INTEGER" />
    <result column="PpNo" property="ppNo" jdbcType="INTEGER" />
    <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
    <result column="SlimCode" property="slimCode" jdbcType="VARCHAR" />
    <result column="LabCode" property="labCode" jdbcType="VARCHAR" />
    <result column="ProductLineCode" property="productLineCode" jdbcType="VARCHAR" />
    <result column="TestLineEvaluation" property="testLineEvaluation" jdbcType="VARCHAR" />
    <result column="StandardId" property="standardId" jdbcType="INTEGER" />
    <result column="StandardName" property="standardName" jdbcType="VARCHAR" />
    <result column="SystemId" property="systemId" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List" >
    Id, PpNo, TestLineId, SlimCode, LabCode, ProductLineCode, TestLineEvaluation,
    StandardId, StandardName, SystemId
  </sql>

    <select id="getTestLineMappingByTestLineId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM tb_test_line_mapping
        <where>
            ActiveIndicator = 1 and SystemId = 1
            AND LabCode = #{labCode}
            AND TestLineId IN
            <foreach collection="testLineIds" item="testLineId" separator="," open="(" close=")">
                #{testLineId}
            </foreach>
        </where>
    </select>

  <select id="getTestLineMappingInfoList" resultMap="BaseResultMap">
      SELECT
          <include refid="Base_Column_List" />
      FROM tb_test_line_mapping
      WHERE ProductLineCode = #{productLineCode}
        AND LabCode = #{labCode}
        AND SystemId = 1
        AND ActiveIndicator = 1
      <if test="testLineIds != null and testLineIds.size > 0">
        AND TestLineId IN
        <foreach collection="testLineIds" item="testLineId" open="(" separator="," close=")">
          #{testLineId}
        </foreach>
      </if>
      <if test="slimCodes != null and slimCodes.size > 0">
        AND SlimCode IN
        <foreach collection="slimCodes" item="slimCode" open="(" separator="," close=")">
          #{slimCode}
        </foreach>
      </if>
  </select>


<select id="queryTestLineMappingByLabCode"
        resultType="com.sgs.testdatabiz.facade.model.dto.TestLineMappingDTO">
    SELECT
    <include refid="Base_Column_List" />
    FROM tb_test_line_mapping
    <where>
        ActiveIndicator = 1
        AND LabCode = #{labCode}
        <if test="systemIds != null and systemIds.size() > 0">
            AND SystemId in
            <foreach collection="systemIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="slimCodes != null and slimCodes.size() > 0">
            AND SlimCode in
            <foreach collection="slimCodes" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </where>
</select>

</mapper>