/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdReportConclusionDTO extends RdOrderHeaderDTO implements Serializable {
   
    @ApiModelProperty(value = "systemId",dataType = "integer", required = true)
    private Integer systemId;
    @ApiModelProperty(value = "conclusionInstanceId",dataType = "string", required = true)
    private String conclusionInstanceId;
    @ApiModelProperty(value = "conclusionLevelId",dataType = "integer", required = true)
    private Integer conclusionLevelId;
    private String objectId;
    private String objectName;
    private String reportId;
    private String conclusionId;
    @ApiModelProperty(value = "conclusionCode",dataType = "string", required = true)
    private String conclusionCode;
    private String customerConclusionId;
    private String customerConclusion;
    private String conclusionRemark;
    private String testLineInstanceId;
    private String sampleInstanceId;
    private Integer sectionId;
    private String ppArtifactRelId;
    private String ppSampleRelId;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required=true)
    private Date lastModifiedTimestamp;
    private String comments;
    @ApiModelProperty(value = "activeIndicator",dataType = "integer", required = true)
    private Integer activeIndicator;
    private Integer testRequestFlag;//SCI-1378
    private String testMatrixId; //SCI-1695
}
