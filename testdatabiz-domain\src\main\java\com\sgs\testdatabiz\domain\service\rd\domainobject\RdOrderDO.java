/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProcessListDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestItemMappingDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdOrderDO {

    private Integer systemId;
    private String orderId;
    private String orderNo;//logicOrderNo
    private String parentOrderNo;
    private String realOrderNo;
    private String originalOrderNo;
    private Integer orderStatus;
    private Integer serviceType;
    private String orderType;
    private Integer operationType;
    private Integer operationMode;
    private String productCategory;
    private String productSubCategory;
    private Integer groupId;
    private String idbLab;
    private Integer tat;
//    private Long actualTat;
    private Date serviceStartDate;
    private Date testingStartDate;
    private Date testingEndDate;
    private Date serviceConfirmDate;
    private Date cuttingExpectDueDate;
    private Date orderExpectDueDate;
    private Date jobExpectDueDate;
    private Date subcontractExpectDueDate;
    private Date reportExpectDueDate;
    private Date softCopyDeliveryDate;
    private String createBy;
    private Date createDate;
    private RdPaymentDO payment;
    private List<RdContactPersonDO> contactPersonList;
    private RdFlagsDO flags;
    private RdOrderOthersDO others;
    private List<RdCustomerDO> customerList;
    private List<RdProductDO> productList;
    private List<RdSampleDO> sampleList;
    private RdServiceRequirementDO serviceRequirement;
    // add 230922
    private List<RdTestItemMappingDO> testItemMappingList;
    private List<RdAttachmentDO> attachmentList;
    private Date sampleReceiveDate;
    // add 20230529
    private List<RdTrfRelDO> trfList;

    private String orderInstanceId;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
    //SCI-1378
    private String topsLabId;
    private String topsLabCode;

    private RdRelationshipDO relationship;

    private List<RdProcessListDTO> processList;

    @Data
    public static class RdRelationshipDO {
        private RdRelationshipParentDO parent;
        @Data
        public static class RdRelationshipParentDO {
            private List<String> parcelNoList;
        }
    }
}
