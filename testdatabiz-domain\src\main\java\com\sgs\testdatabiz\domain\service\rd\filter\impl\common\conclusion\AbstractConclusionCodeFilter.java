package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.conclusion;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.domain.service.rd.filter.AbstractReportDataFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.FilterContext;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.rd.filter.constants.ConclusionFilterConstants;

public abstract class AbstractConclusionCodeFilter extends AbstractReportDataFilter {

    @Override
    protected ReportDataDO doFilterInternal(ReportDataDO reportData, FilterContext context) {
        if (reportData == null) {
            return reportData;
        }

        // 处理Report级别的conclusion
        if (reportData.getHeader() != null && reportData.getHeader().getConclusion() != null) {
            String originalCode = reportData.getHeader().getConclusion().getConclusionCode();
            String convertedCode = convertConclusionCode(originalCode);
            reportData.getHeader().getConclusion().setConclusionCode(convertedCode);
            
            recordConversion(context, ConclusionFilterConstants.FieldPath.HEADER_CONCLUSION, 
                originalCode, convertedCode);
        }

        // 处理TestLine级别的conclusion
        if (Func.isNotEmpty(reportData.getTestLineList())) {
            reportData.getTestLineList().forEach(testLine -> {
                if (testLine.getConclusion() != null) {
                    String originalCode = testLine.getConclusion().getConclusionCode();
                    String convertedCode = convertConclusionCode(originalCode);
                    testLine.getConclusion().setConclusionCode(convertedCode);
                    
                    recordConversion(context, ConclusionFilterConstants.FieldPath.TESTLINE_CONCLUSION, 
                        originalCode, convertedCode);
                }
            });
        }

        return reportData;
    }

    protected void recordConversion(FilterContext context, String field, String originalCode, String convertedCode) {
        context.addFilterRecord(
            getFilterName(),
            field,
            1,
            1,
            String.format(ConclusionFilterConstants.Message.CONVERT, originalCode, convertedCode)
        );
    }

    protected abstract String getFilterName();
    protected abstract String convertConclusionCode(String code);
} 