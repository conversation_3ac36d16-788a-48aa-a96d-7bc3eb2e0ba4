ALTER TABLE `tb_report_ext`
    MODIFY COLUMN `request_json` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据体' AFTER `rd_report_id`,
    ADD COLUMN `version` varchar(20) NOT NULL COMMENT '对应数据的版本号' AFTER `request_json`;

ALTER TABLE `tb_report_invoice`
    ADD COLUMN `net_amount` decimal(20, 6) NULL AFTER `currency`,
ADD COLUMN `vat_amount` decimal(20, 6) NULL AFTER `net_amount`;
