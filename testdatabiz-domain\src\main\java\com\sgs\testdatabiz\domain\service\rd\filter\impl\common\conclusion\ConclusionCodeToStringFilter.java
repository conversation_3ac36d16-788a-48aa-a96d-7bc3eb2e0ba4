package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.conclusion;

import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.constants.ConclusionFilterConstants;
import com.sgs.testdatabiz.core.enums.FilterType;
import com.sgs.testdatabiz.domain.service.utils.ConclusionCodeToStringUtil;
import org.springframework.stereotype.Component;


public class ConclusionCodeToStringFilter extends AbstractConclusionCodeFilter {
    
    @Override
    protected String getFilterType() {
        return FilterType.CONCLUSION_CONVERTER.name();
    }

    @Override
    protected String getFilterName() {
        return ConclusionFilterConstants.FilterName.CODE_TO_STRING;
    }

    @Override
    protected String convertConclusionCode(String code) {
        return ConclusionCodeToStringUtil.convertConclusionCode(code);
    }
} 