package com.sgs.testdatabiz.facade.model.req.starlims;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@ApiModel
public class ReceiveStarLimsReportReq extends BaseModel {
    /**
     * 报告号
     * */
    private String reportNo;

    /*@J<PERSON>NField(name = "externalParentId")*/
    private String orderNo;

    private String parentOrderNo;

    /*@JSONField(name = "externalId")*/
    private String objectNo;

    /*@JSONField(name = "externalId")*/
    private String subContractNo;

    private String labCode;

    /*@JSONField(name = "reportId")*/
    private String externalId;

    /*@J<PERSON><PERSON>ield(name = "folderNo")*/
    private String externalNo;

    /*@J<PERSON><PERSON><PERSON>(name = "reportNo")*/
    private String externalObjectNo;

    /**
     *
     */
    private Date completedDate;

    /**
     *
     */
    private String originalReportNo;

    /**
     *
     */
    private String languageId;

    /**
     * 报表数据
     */
    /*@J<PERSON><PERSON>ield(name = "data")*/
    private List<ReceiveReportData> reportDatas;

    // add 231114
    private String excludeCustomerInterface;

    @Override
    public String getExtId() {
        return StringUtils.isNotBlank(reportNo) ? reportNo : orderNo;
    }

    public String getExcludeCustomerInterface() {
        return excludeCustomerInterface;
    }

    public void setExcludeCustomerInterface(String excludeCustomerInterface) {
        this.excludeCustomerInterface = excludeCustomerInterface;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getExternalObjectNo() {
        return externalObjectNo;
    }

    public void setExternalObjectNo(String externalObjectNo) {
        this.externalObjectNo = externalObjectNo;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }

    public String getOriginalReportNo() {
        return originalReportNo;
    }

    public void setOriginalReportNo(String originalReportNo) {
        this.originalReportNo = originalReportNo;
    }

    public String getLanguageId() {
        return languageId;
    }

    public void setLanguageId(String languageId) {
        this.languageId = languageId;
    }

    public List<ReceiveReportData> getReportDatas() {
        return reportDatas;
    }

    public void setReportDatas(List<ReceiveReportData> reportDatas) {
        this.reportDatas = reportDatas;
    }
}
