package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.visitor;

import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCertificateDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdDelayDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProcessDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProcessListDTO;
import java.util.Optional;

/**
 * 移除字段访问者
 * 负责在访问数据对象时移除指定的字段
 */
public class RemoveFieldsVisitor implements ReportDataVisitor {


    @Override
    public void visitReportHeader(RdReportDO header) {
        if (header != null) {
            header.setReportRemark(null);
            header.setCreateBy(null);
        }
    }

    @Override
    public void visitTestLine(RdTestLineDO testLine) {
        if (testLine != null) {
            testLine.setTestLineRemark(null);
        }
    }

    @Override
    public void visitTestResult(RdTestResultDO testResult) {
        if (testResult != null && testResult.getTestResult() != null) {
            testResult.getTestResult().setResultValueRemark(null);
            testResult.getTestResult().setReportRemark(null);
        }
    }

    @Override
    public void visitReportConclusion(RdReportConclusionDO conclusion) {
        if (conclusion != null) {
            conclusion.setConclusionRemark(null);
        }
    }

    @Override
    public void visitReportMatrix(RdReportMatrixDO matrix) {
        // 矩阵没有备注字段需要处理
    }

    @Override
    public void visitReportCertificate(ReportCertificateDO certificate) {
        // 证书没有备注字段需要处理
        if(certificate != null){
            certificate.setRemark(null);
        }
    }

    @Override
    public void visitCertificate(RdCertificateDTO certificate) {
        if(certificate != null){
            certificate.setAcceditationRemark(null);
        }
    }

    @Override
    public void visitConclusion(RdConclusionDO conclusion) {
        if (conclusion != null) {
            conclusion.setConclusionRemark(null);
        }
    }

    @Override
    public void visitTestSample(RdTestSampleDO testSample) {
        // 测试样本没有备注字段需要处理
    }

    @Override
    public void visitTestSampleGroup(RdTestSampleGroupDO sampleGroup) {
        // 样本组没有备注字段需要处理
    }

    @Override
    public void visitMaterialAttr(RdMaterialAttrDO materialAttr) {
        // 材料属性没有备注字段需要处理
        if(materialAttr != null){
            materialAttr.setMaterialRemark(null);
        }
    }

    @Override
    public void visitAttachment(RdAttachmentDO attachment) {
        // 附件没有备注字段需要处理
    }

    @Override
    public void visitConditionGroup(RdConditionGroupDO conditionGroup) {
        // 条件组没有备注字段需要处理
    }

    @Override
    public void visitCondition(RdConditionDO condition) {
        // 条件没有备注字段需要处理
    }

    @Override
    public void visitTrf(RdTrfDO trf) {
        // TRF 没有备注字段需要处理
    }

    @Override
    public void visitOrder(RdOrderDO order) {
        if (order != null) {
            order.setCreateBy(null);
            if (order.getOthers() != null) {
                visitOrderOthers(order.getOthers());
            }
            if (order.getServiceRequirement() != null) {
                visitServiceRequirement(order.getServiceRequirement());
            }
            Optional.ofNullable(order.getProcessList())
                .ifPresent(processList -> processList.forEach(process -> {
                    visitProcess(process.getCreate());
                    visitProcess(process.getGenerate());
                    visitProcess(process.getConfirm());
                    visitProcess(process.getDelivery());
                    visitProcess(process.getSampleReceive());
                    visitProcess(process.getTestingStart());
                    visitProcess(process.getTestingEnd());
                    visitProcess(process.getCancel());
                }));
        }
    }

    @Override
    public void visitProcess(RdProcessDTO process) {
        if (process != null) {
            process.setRemark(null);
        }
    }

    @Override
    public void visitOrderOthers(RdOrderOthersDO others) {
        if (others != null) {
            others.setOrderRemark(null);
            if (others.getDelay() != null) {
                visitOrderDelay(others.getDelay());
            }
        }
    }

    @Override
    public void visitOrderDelay(RdDelayDTO delay) {
        if (delay != null) {
            delay.setDelayRemark(null);
        }
    }

    @Override
    public void visitServiceRequirement(RdServiceRequirementDO serviceRequirement) {
        if (serviceRequirement != null) {
            serviceRequirement.setOtherRequestRemark(null);
        }
    }

    @Override
    public void visitQuotation(RdQuotationDO quotation) {
        // 报价没有备注字段需要处理
    }

    @Override
    public void visitInvoice(RdInvoiceDO invoice) {
        // 发票没有备注字段需要处理
    }

    @Override
    public void visitTestResultResult(RdTestResultResultDO testResultResult) {
        if (testResultResult != null) {
            testResultResult.setResultValueRemark(null);
            testResultResult.setReportRemark(null);
            testResultResult.setFailRemark(null);
        }
    }

    @Override
    public void visitTestLineExternal(RdTestLineExternalDO external) {
        // 外部测试线没有备注字段需要处理
    }

    @Override
    public void visitWi(RdWiDO wi) {
        // WI 没有备注字段需要处理
    }

    @Override
    public void visitAnalyte(RdAnalyteDO analyte) {
        // 分析物没有备注字段需要处理
    }

    @Override
    public void visitCitation(RdCitationDO citation) {
        // 引用没有备注字段需要处理
    }

    @Override
    public void visitPpTestLineRel(RdPpTestLineRelDO ppTestLineRel) {
       // 引用没有备注字段需要处理
    }

    @Override
    public void visitSpecimen(RdSpecimenDO specimen) {
       // 样本没有备注字段需要处理
    }
} 