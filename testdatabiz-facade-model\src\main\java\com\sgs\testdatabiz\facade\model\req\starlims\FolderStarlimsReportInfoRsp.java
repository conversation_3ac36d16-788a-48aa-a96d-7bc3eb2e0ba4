package com.sgs.testdatabiz.facade.model.req.starlims;

import com.sgs.framework.core.common.PrintFriendliness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FolderStarlimsReportInfoRsp extends PrintFriendliness {
    /**
     *
     */
    private String status;

    /**
     *
     */
    private String details;

    /**
     *
     */
    private List<FolderStarlimsReportRsp> report;

}
