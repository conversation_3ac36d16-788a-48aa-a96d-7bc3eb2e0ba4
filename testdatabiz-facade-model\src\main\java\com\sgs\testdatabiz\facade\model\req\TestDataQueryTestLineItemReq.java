package com.sgs.testdatabiz.facade.model.req;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @ClassName TestDataQueryTestLineReq
 * @Description test data testline 查询对象
 * <AUTHOR>
 * @Date 2022/5/31
 */
@ApiModel(value = "TestDataQueryTestLineItemReq", description = "TestDataQueryTestLineItemReq")
public class TestDataQueryTestLineItemReq extends PrintFriendliness {
    @ApiModelProperty("ppNo")
    private int ppNo;
    @ApiModelProperty("ppVersionId")
    private int ppVersionId;
    @ApiModelProperty("testLineId")
    private Integer testLineId;
    @ApiModelProperty("TestLine对应的standardId")
    private List<Integer> standardIdList;

    public int getPpNo() {
        return ppNo;
    }

    public void setPpNo(int ppNo) {
        this.ppNo = ppNo;
    }

    public int getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(int ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public List<Integer> getStandardIdList() {
        return standardIdList;
    }

    public void setStandardIdList(List<Integer> standardIdList) {
        this.standardIdList = standardIdList;
    }
}
