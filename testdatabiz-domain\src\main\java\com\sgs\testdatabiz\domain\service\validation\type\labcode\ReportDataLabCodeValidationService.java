package com.sgs.testdatabiz.domain.service.validation.type.labcode;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.model.ReportTestDataValidationDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdLabDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdReportDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataBatchDTO;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;
@Component("reportDataLabCodeValidationService")
public class ReportDataLabCodeValidationService implements ValidationService{


    @Override
    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO) {
        ReportTestDataValidationDTO reportTestDataValidationDTO = (ReportTestDataValidationDTO) validationRequestDTO;
        ReportDataBatchDTO reportData = reportTestDataValidationDTO.getReportDataBatchDTO();
        String labCode = reportData.getLabCode();

        if(Func.isNotEmpty(labCode)) {
            AtomicBoolean labCodeValid = getValidateResult(reportData, labCode);
            if(labCodeValid.get()){
                return ValidationResultDTO.success("Lab Code is valid");
            }else{
                return ValidationResultDTO.fail("Lab Code is not valid");
            }
        }
         return ValidationResultDTO.success("Lab Code is valid");
    }

    private AtomicBoolean getValidateResult(ReportDataBatchDTO reportData, String labCode) {
        AtomicBoolean labCodeValid = new AtomicBoolean(true);
        //判断reportData的reportList不为空
        if(reportData.getReportList()==null){
            return labCodeValid;
        }
        // 遍历报告列表,检查每个报告的实验室编号是否与输入的labCode一致
        reportData.getReportList().stream()
                .map(RdReportDTO::getLab)
                .filter(lab -> lab != null && Func.isNotEmpty(lab.getLabCode()))
                .forEach(lab -> {
                    if (!labCode.equals(lab.getLabCode())) {
                        labCodeValid.set(false);
                    }
                });
        return labCodeValid;
    }

    @Override
    public String getType() {
        return ValidationTypeEnum.LAB_CODE.getName();
    }

    @Override
    public Integer getOrder() {
        return 1;
    }
}
