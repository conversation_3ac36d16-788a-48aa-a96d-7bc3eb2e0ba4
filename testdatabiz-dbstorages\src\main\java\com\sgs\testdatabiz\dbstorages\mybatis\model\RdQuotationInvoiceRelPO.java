package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdQuotationInvoiceRelPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 附件标识
     */
    private Long id;

    /**
     * quotation_no VARCHAR(50)<br>
     * RD 报价单标识
     */
    private String quotationNo;

    /**
     * invoice_no VARCHAR(50)<br>
     * RD 发票标识
     */
    private String invoiceNo;

    /**
     * system_id BIGINT(19)<br>
     * 
     */
    private Long systemId;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 附件标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 附件标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * quotation_no VARCHAR(50)<br>
     * 获得 RD 报价单标识
     */
    public String getQuotationNo() {
        return quotationNo;
    }

    /**
     * quotation_no VARCHAR(50)<br>
     * 设置 RD 报价单标识
     */
    public void setQuotationNo(String quotationNo) {
        this.quotationNo = quotationNo == null ? null : quotationNo.trim();
    }

    /**
     * invoice_no VARCHAR(50)<br>
     * 获得 RD 发票标识
     */
    public String getInvoiceNo() {
        return invoiceNo;
    }

    /**
     * invoice_no VARCHAR(50)<br>
     * 设置 RD 发票标识
     */
    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo == null ? null : invoiceNo.trim();
    }

    /**
     * system_id BIGINT(19)<br>
     * 获得 
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * system_id BIGINT(19)<br>
     * 设置 
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", quotationNo=").append(quotationNo);
        sb.append(", invoiceNo=").append(invoiceNo);
        sb.append(", systemId=").append(systemId);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}