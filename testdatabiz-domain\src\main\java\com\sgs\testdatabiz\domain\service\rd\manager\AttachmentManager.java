package com.sgs.testdatabiz.domain.service.rd.manager;

import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdAttachmentMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentPO;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdAttachmentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

import static com.sgs.testdatabiz.domain.service.constant.SystemConstants.DEFAULT_USER;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 15:13
 */
@Slf4j
@Component
public class AttachmentManager {
    private final RdAttachmentMapper attachmentMapper;

    @Autowired
    private IdService ID_GENERATOR;

    public List<RdAttachmentPO> getByObjectNoAndType(String objectNo, Integer objectType) {
        RdAttachmentExample attachmentExample = new RdAttachmentExample();
        attachmentExample.createCriteria().andObjectNoEqualTo(objectNo).andObjectTypeEqualTo(objectType).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        List<RdAttachmentPO> rdAttachmentPOS = attachmentMapper.selectByExample(attachmentExample);
        return rdAttachmentPOS;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<RdAttachmentPO> attachmentList, List<String> reportNos, Long labId) {
        if (CollectionUtils.isEmpty(attachmentList)) {
            return true;
        }

        if (Func.isNotEmpty(reportNos)) {
            RdAttachmentExample example = new RdAttachmentExample();
            example.createCriteria().andReportNoIn(reportNos).andLabIdEqualTo(labId).andObjectTypeEqualTo(attachmentList.get(0).getObjectType());
            attachmentMapper.deleteByExample(example);
        }

        int insertCount = attachmentMapper.batchInsert(attachmentList);
        if (insertCount != attachmentList.size()) {
            log.error("attachment batch insert error. expectCount:{} ,insertCount:{}", attachmentList.size(), insertCount);
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, "attachment batch insert error. The expected and actual insertion counts do not match");
        }

        log.info("attachment batch insert success. count:{}", insertCount);
        return true;
    }


    public void delete(@NotNull Long labelId, @NotEmpty String reportNo, @NotNull Long systemId) {
        RdAttachmentExample deleteExample = new RdAttachmentExample();
        deleteExample.createCriteria()
                .andLabIdEqualTo(labelId)
                .andReportNoEqualTo(reportNo).andSystemIdEqualTo(systemId);
        attachmentMapper.deleteByExample(deleteExample);
    }


    public AttachmentManager(RdAttachmentMapper attachmentMapper) {
        this.attachmentMapper = attachmentMapper;
    }

    public void deleteFiles(List<String> nos, Long systemId, Integer type) {
        RdAttachmentExample attachmentExample = new RdAttachmentExample();
        attachmentExample.createCriteria().andObjectNoIn(nos).andObjectTypeEqualTo(type).andSystemIdEqualTo(systemId);
        attachmentMapper.deleteByExample(attachmentExample);
    }

    public void batchInsertAttachmentDOList(List<RdAttachmentDO> attachmentDOS, Long systemId, String reportNo, Long labId) {
        if (Func.isNotEmpty(attachmentDOS)) {
            List<RdAttachmentPO> list = new ArrayList<>();
            attachmentDOS.forEach(
                    attachment -> {
                        RdAttachmentPO attachmentPO = new RdAttachmentPO();
                        attachmentPO.setActiveIndicator(ActiveType.Enable.getStatus());
                        attachmentPO.setSystemId(systemId);
                        attachmentPO.setReportNo(reportNo);
                        // TODO 待确认
                        attachmentPO.setBizType(Func.isEmpty(attachment.getFileType()) ? null : String.valueOf(attachment.getFileType()));
                        attachmentPO.setLabId(labId);
                        attachmentPO.setFileName(attachment.getFileName());
                        attachmentPO.setCloudId(attachment.getCloudId());
                        attachmentPO.setId(ID_GENERATOR.nextId());
                        attachmentPO.setCreatedBy(DEFAULT_USER);
                        attachmentPO.setModifiedBy(DEFAULT_USER);
                        attachmentPO.setCreatedDate(DateUtils.getNow());
                        attachmentPO.setModifiedDate(DateUtils.getNow());
//                            attachmentPO.setFilePath(invoiceFile.getFilePath())
                        list.add(attachmentPO);
                    }
            );
            if (Func.isNotEmpty(list)) {
                attachmentMapper.batchInsert(list);
            }
        }
    }

    public List<RdAttachmentPO> getByObjectNosAndType(List<String> invoiceNos, Integer objectType) {
        RdAttachmentExample attachmentExample = new RdAttachmentExample();
        attachmentExample.createCriteria().andObjectNoIn(invoiceNos).andObjectTypeEqualTo(objectType).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        List<RdAttachmentPO> rdAttachmentPOS = attachmentMapper.selectByExample(attachmentExample);
        return rdAttachmentPOS;
    }
}
