package com.sgs.testdatabiz.core.util;

import com.sgs.core.domain.UserInfo;
import com.sgs.testdatabiz.core.constants.Constants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

/**
 *
 */
@Component
public class TokenUtils {
    @Autowired
    private RedisUtils redisUtils;

    /**
     *
     * @return
     */
    public HttpServletRequest getRequestContext() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        ServletRequestAttributes sra = (ServletRequestAttributes)requestAttributes;
        // DIG-8555  Change this condition so that it does not always evaluate to "false"
        /*if (sra == null) {
            return null;
        }*/
        return sra.getRequest();
    }

    /**
     * Get SGS Token From HTTP Request
     * @param request
     * @return sgsToken or null
     */
    public String getToken(HttpServletRequest request) {
        String sgsToken = null;
        try {
            sgsToken = (String)request.getAttribute(Constants.SGS_TOKEN);
            if (StringUtils.isNotBlank(sgsToken)) {
                return sgsToken;
            }
            sgsToken = request.getParameter(Constants.SGS_TOKEN);
            if (StringUtils.isNotBlank(sgsToken)) {
                return sgsToken;
            }
            sgsToken = request.getHeader(Constants.SGS_TOKEN);
            if (StringUtils.isNotBlank(sgsToken)) {
                return sgsToken;
            }
            sgsToken = request.getHeader("X-Cookie");
            if (StringUtils.isNotBlank(sgsToken)) {
                return sgsToken;
            }
            Cookie[] cookies = request.getCookies();
            if (cookies == null || cookies.length <= 0){
                return sgsToken;
            }
            for (Cookie cookie : cookies) {
                if (!StringUtils.equalsIgnoreCase(Constants.SGS_TOKEN, cookie.getName())) {
                    continue;
                }
                sgsToken = cookie.getValue();
            }
        } catch (Exception e) {
            /*throw new BizException("Failed to get cache userinfo");*/
        }
        return sgsToken;
    }

    /**
     * Get SGS UserInfo
     * @param token - token generated by user management
     * @return UserInfo
     */
    public UserInfo getUser(String token) {
        if (StringUtils.isBlank(token)){
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes instanceof  ServletRequestAttributes){
                token = getToken(((ServletRequestAttributes)requestAttributes).getRequest());
            }
            if (StringUtils.isBlank(token)){
                return null;
            }
        }
        return redisUtils.get(token);
    }

    /**
     *
     * @return
     */
    public String getToken(){
        return getToken(this.getRequestContext());
    }

    /**
     * Get SGS UserInfo
     * @param request
     * @return
     */
    public UserInfo getUser(HttpServletRequest request) {
        String sgsToken = getToken(request);
        if (StringUtils.isNotBlank(sgsToken)) {
            return getUser(sgsToken);
        }
        return null;
    }

    public String getUserLabCode(String token){
        UserInfo user = getUser(token);
        return user == null ? "" : user.getCurrentLabCode();
    }

    /**
     *
     * @return
     */
    public UserInfo getUser() {
        String sgsToken = getToken(this.getRequestContext());
        if (StringUtils.isNotBlank(sgsToken)) {
            return getUser(sgsToken);
        }
        return null;
    }
}
