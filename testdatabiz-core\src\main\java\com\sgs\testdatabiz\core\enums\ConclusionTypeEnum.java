package com.sgs.testdatabiz.core.enums;

import java.util.HashMap;
import java.util.Map;

public enum ConclusionTypeEnum {
    Matrix(601, "Individual Conclusion","Individual Conclusion"),
    TestLine(602, ConclusionDimType.MatrixDim, ConclusionCalcType.TestLine, "TestItem", "TestItem Conclusion for All Original Sample"),
    Report(603, ConclusionDimType.MatrixDim, ConclusionCalcType.Report,true,"Report Conclusion","Report Conclusion"),
    TestLine_OriginalSample(604, ConclusionDimType.MatrixDim, ConclusionCalcType.OriginalSample,"TL*OriginalSample","TestItem Conclusion for each Original Sample"),
    OriginalSample(605, ConclusionDimType.MatrixDim, ConclusionCalcType.OriginalSample, true,"OriginalSample","Original Sample Conclusion"),
    Section(606, ConclusionDimType.SectionDim, ConclusionCalcType.Section, ConclusionDimType.SectionDim, true,"Section","Section Conclusion"),
    Section_TL(607, ConclusionDimType.MatrixDim, ConclusionCalcType.TestLine, ConclusionDimType.SectionDim, "Section*TL","Section TL"),
    Section_TL_OriginalSample(608, ConclusionDimType.MatrixDim, ConclusionCalcType.OriginalSample, ConclusionDimType.SectionDim, "Section*TL*OriginalSample","Section TL OriginalSample"),
    PP_TL(609, ConclusionDimType.MatrixDim, ConclusionCalcType.TestLine, ConclusionDimType.PpDim, "PP*TL","PP TL"),
    PP_TL_OriginalSample(610, ConclusionDimType.MatrixDim, ConclusionCalcType.OriginalSample, ConclusionDimType.PpDim, "PP*TL*OriginalSample","PP TL OriginalSample"),
    PP(611, ConclusionDimType.PpDim, ConclusionCalcType.PP, ConclusionDimType.PpDim, true,"PP","PP Conclusion");

    private int code;
    private ConclusionDimType dimType;
    private ConclusionCalcType calcType;
    private ConclusionDimType bizId;
    private boolean isEmptyTestLineId;
    private String describe;
    private String message;

    ConclusionTypeEnum(int code, String message) {
        this.dimType = ConclusionDimType.None;
        this.bizId = ConclusionDimType.None;
        this.code = code;
        this.message = message;
    }

    ConclusionTypeEnum(int code, String describe, String message) {
        this.dimType = ConclusionDimType.None;
        this.bizId = ConclusionDimType.None;
        this.code = code;
        this.describe = describe;
        this.message = message;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, String describe, String message) {
        this(code, describe, message);
        this.dimType = dimType;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, ConclusionCalcType calcType, String describe, String message) {
        this(code, dimType, describe, message);
        this.calcType = calcType;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, ConclusionCalcType calcType, boolean isEmptyTestLineId, String describe, String message) {
        this(code, dimType, describe, message);
        this.calcType = calcType;
        this.isEmptyTestLineId = isEmptyTestLineId;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, ConclusionCalcType calcType, ConclusionDimType bizId, String describe, String message) {
        this(code, dimType, calcType, describe, message);
        this.bizId = bizId;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, ConclusionCalcType calcType, ConclusionDimType bizId, boolean isEmptyTestLineId, String describe, String message) {
        this(code, dimType, calcType, bizId, describe, message);
        this.isEmptyTestLineId = isEmptyTestLineId;
    }

    public int getCode() {
        return this.code;
    }

    public ConclusionDimType getDimType() {
        return dimType;
    }

    public ConclusionCalcType getCalcType() {
        return calcType;
    }

    public ConclusionDimType getBizId() {
        return bizId;
    }

    public boolean isEmptyTestLineId() {
        return isEmptyTestLineId;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getMessage() {
        return message;
    }

    private static final Map<Integer, ConclusionTypeEnum> maps = new HashMap<>();

    static {
        for (ConclusionTypeEnum type : ConclusionTypeEnum.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static ConclusionTypeEnum findCode(Integer code) {
        if (code == null || !maps.containsKey(code)) {
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(Integer code) {
        if (code == null || !maps.containsKey(code)) {
            return false;
        }
        return maps.get(code) != null;
    }

    public static boolean check(Integer code, ConclusionTypeEnum... conclusionTypes) {
        if (code == null || !maps.containsKey(code)) {
            return false;
        }
        for (ConclusionTypeEnum conclusionType : conclusionTypes) {
            if (code.equals(conclusionType.code)) {
                return true;
            }
        }
        return false;
    }

    public static boolean check(Integer code, ConclusionTypeEnum conclusionType) {
        if (code == null || !maps.containsKey(code)) {
            return false;
        }
        return maps.get(code) == conclusionType;
    }

    public boolean check(ConclusionTypeEnum... conclusionTypes) {
        if (conclusionTypes == null || conclusionTypes.length <= 0) {
            return false;
        }
        for (ConclusionTypeEnum conclusionType : conclusionTypes) {
            if (conclusionType == null) {
                continue;
            }
            if (this.getCode() == conclusionType.getCode()) {
                return true;
            }
        }
        return false;
    }

    public boolean checkType(Integer conclusionType) {
        if (conclusionType == null) {
            return false;
        }
        return this.getCode() == conclusionType;
    }

    public boolean checkDimType(ConclusionDimType currBizId) {
        return this.bizId.getCode() > 0 && this.bizId == currBizId;
    }
} 