/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdConditionGroupDTO extends RdOrderHeaderDTO implements Serializable{
    
    // add 20230529
    private Integer systemId;
    private String conditionGroupId;
    private String combinedConditionDescription;
    private String requirement;
    private List<RdPpConditionGroupDTO> ppConditionGroupList;
    private List<RdConditionGroupLanguageDTO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
