package com.sgs.testdatabiz.facade.model.testdata.slim;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 *
 */
@XmlRootElement(name = "SCHEME")
@XmlAccessorType(XmlAccessType.FIELD)
public class SlimSchemeInfo {
    /**
     *
     */
    @XmlAttribute(name = "SCH_CODE")
    private String slimCode;

    /**
     *
     */
    @XmlAttribute(name = "USERFIELD10")
    private String userField10;

    /**
     *
     */
    @XmlElement(name = "ANALYTE")
    private List<SlimAnalyteInfo> testAnalytes;

    public String getSlimCode() {
        return slimCode;
    }

    public void setSlimCode(String slimCode) {
        this.slimCode = slimCode;
    }

    public List<SlimAnalyteInfo> getTestAnalytes() {
        return testAnalytes;
    }

    public void setTestAnalytes(List<SlimAnalyteInfo> testAnalytes) {
        this.testAnalytes = testAnalytes;
    }

    public String getUserField10() {
        return userField10;
    }

    public void setUserField10(String userField10) {
        this.userField10 = userField10;
    }
}
