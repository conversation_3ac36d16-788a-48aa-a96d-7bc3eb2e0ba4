/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdReportMatrixDO {
    private String subReportNo;
    private String testMatrixId;
    private Integer testMatrixGroupId;
    private String testConditionGroupId;
    private List<RdConditionDO> conditionList;
    private RdReportMatrixExternalDO external;
    private String testSampleInstanceId;
    private List<RdSpecimenDO> specimenList;
    private String testLineInstanceId;
//    private String analyteInstanceId;
    private List<RdPositionDO> positionList;
    private RdConclusionDO conclusion;
    private List<RdAttachmentDO> testMatrixFileList;

    private Date lastModifiedTimestamp;
    private List<RdProductAttributeDO> productAttributeList;
    private Integer activeIndicator;
    private String matrixInstanceId;
    //SCI-1378
    private String applicationFactor;
    private String metaData;
    private String LabSectionName;
    private String remark;
}
