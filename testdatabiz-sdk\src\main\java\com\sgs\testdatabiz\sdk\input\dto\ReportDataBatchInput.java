/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportDataBatchInput extends BaseModel implements Serializable {
    private List<RdTrfInput> trfList;
    private List<RdOrderDTO> orderList;
    private List<RdReportInput> reportList;
    private RdRelationshipInput relationship;
    private List<RdTestSampleInput> testSampleList;
    private List<RdTestLineInput> testLineList;
    private List<RdTestResultInput> testResultList;
    private List<RdReportConclusionInput> reportConclusionList;
    private List<RdConditionGroupInput> conditionGroupList;
    private List<RdQuotationInput> quotationList;
    private List<RdInvoiceInput> invoiceList;
}
