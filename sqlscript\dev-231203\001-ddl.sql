CREATE TABLE `tb_test_data_info_sl_hcmc` (
                                             `Id` bigint(20) NOT NULL AUTO_INCREMENT,
                                             `ObjectRelId` varchar(36) DEFAULT NULL,
                                             `TestDataMatrixId` bigint(20) NOT NULL,
                                             `TestMatrixId` varchar(36) DEFAULT NULL,
                                             `AnalyteId` varchar(36) DEFAULT NULL,
                                             `AnalyteName` varchar(500) DEFAULT NULL COMMENT 'analyte别名',
                                             `Position` mediumtext COMMENT 'position Json',
                                             `AnalyteType` int(10) DEFAULT NULL COMMENT '0：General、1：Conclusion',
                                             `AnalyteCode` varchar(512) DEFAULT NULL,
                                             `AnalyteSeq` int(11) DEFAULT NULL,
                                             `ReportUnit` varchar(50) DEFAULT NULL,
                                             `TestValue` varchar(150) DEFAULT NULL,
                                             `CasNo` varchar(100) DEFAULT NULL,
                                             `ReportLimit` varchar(50) DEFAULT NULL,
                                             `LimitUnit` varchar(100) DEFAULT NULL COMMENT 'LimitUnit',
                                             `ConclusionId` varchar(50) DEFAULT NULL,
                                             `Languages` mediumtext COMMENT '多语言json',
                                             `ActiveIndicator` int(1) DEFAULT NULL COMMENT '0无效，1有效',
                                             `CreatedBy` varchar(50) DEFAULT NULL,
                                             `CreatedDate` datetime DEFAULT NULL,
                                             `ModifiedBy` varchar(50) DEFAULT NULL,
                                             `ModifiedDate` datetime DEFAULT NULL,
                                             `BizVersionId` char(32) NOT NULL COMMENT '表中字段MD5 hash 计算得出',
                                             `rd_report_id` bigint(20) DEFAULT NULL,
                                             `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                             `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                             `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                             `test_result_full_name` varchar(500) DEFAULT NULL COMMENT '测试项名称，拼接字段',
                                             `test_result_seq` int(11) DEFAULT NULL COMMENT '测试项顺序',
                                             `result_value` varchar(150) DEFAULT NULL COMMENT '测试结果',
                                             `result_value_remark` varchar(500) DEFAULT NULL COMMENT '测试结果备注',
                                             `result_unit` varchar(1024) DEFAULT NULL COMMENT '测试结果单位',
                                             `fail_flag` tinyint(1) DEFAULT NULL COMMENT '失败标识',
                                             `limit_value_full_name` varchar(500) DEFAULT NULL COMMENT '参考标准，拼接字段',
                                             PRIMARY KEY (`Id`) USING BTREE,
                                             UNIQUE KEY `idx_BizVersionId` (`BizVersionId`) USING BTREE,
                                             KEY `idx_ObjectRelId` (`ObjectRelId`)
) ENGINE=InnoDB AUTO_INCREMENT=1172174808920002561 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_test_data_info_sl_hp` (
                                           `Id` bigint(20) NOT NULL AUTO_INCREMENT,
                                           `ObjectRelId` varchar(36) DEFAULT NULL,
                                           `TestDataMatrixId` bigint(20) NOT NULL,
                                           `TestMatrixId` varchar(36) DEFAULT NULL,
                                           `AnalyteId` varchar(36) DEFAULT NULL,
                                           `AnalyteName` varchar(500) DEFAULT NULL COMMENT 'analyte别名',
                                           `Position` mediumtext COMMENT 'position Json',
                                           `AnalyteType` int(10) DEFAULT NULL COMMENT '0：General、1：Conclusion',
                                           `AnalyteCode` varchar(512) DEFAULT NULL,
                                           `AnalyteSeq` int(11) DEFAULT NULL,
                                           `ReportUnit` varchar(50) DEFAULT NULL,
                                           `TestValue` varchar(150) DEFAULT NULL,
                                           `CasNo` varchar(100) DEFAULT NULL,
                                           `ReportLimit` varchar(50) DEFAULT NULL,
                                           `LimitUnit` varchar(100) DEFAULT NULL COMMENT 'LimitUnit',
                                           `ConclusionId` varchar(50) DEFAULT NULL,
                                           `Languages` mediumtext COMMENT '多语言json',
                                           `ActiveIndicator` int(1) DEFAULT NULL COMMENT '0无效，1有效',
                                           `CreatedBy` varchar(50) DEFAULT NULL,
                                           `CreatedDate` datetime DEFAULT NULL,
                                           `ModifiedBy` varchar(50) DEFAULT NULL,
                                           `ModifiedDate` datetime DEFAULT NULL,
                                           `BizVersionId` char(32) NOT NULL COMMENT '表中字段MD5 hash 计算得出',
                                           `rd_report_id` bigint(20) DEFAULT NULL,
                                           `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                           `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                           `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                           `test_result_full_name` varchar(500) DEFAULT NULL COMMENT '测试项名称，拼接字段',
                                           `test_result_seq` int(11) DEFAULT NULL COMMENT '测试项顺序',
                                           `result_value` varchar(150) DEFAULT NULL COMMENT '测试结果',
                                           `result_value_remark` varchar(500) DEFAULT NULL COMMENT '测试结果备注',
                                           `result_unit` varchar(1024) DEFAULT NULL COMMENT '测试结果单位',
                                           `fail_flag` tinyint(1) DEFAULT NULL COMMENT '失败标识',
                                           `limit_value_full_name` varchar(500) DEFAULT NULL COMMENT '参考标准，拼接字段',
                                           PRIMARY KEY (`Id`) USING BTREE,
                                           UNIQUE KEY `idx_BizVersionId` (`BizVersionId`) USING BTREE,
                                           KEY `idx_ObjectRelId` (`ObjectRelId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_test_data_matrix_info_sl_hp` (
                                                  `id` bigint(20) NOT NULL,
                                                  `ObjectRelId` varchar(36) DEFAULT NULL,
                                                  `TestMatrixId` varchar(36) DEFAULT NULL,
                                                  `TestLineMappingId` int(11) DEFAULT NULL,
                                                  `ExternalId` varchar(300) DEFAULT NULL,
                                                  `ExternalCode` varchar(50) DEFAULT NULL,
                                                  `PpVersionId` int(11) DEFAULT NULL,
                                                  `Aid` bigint(20) DEFAULT NULL,
                                                  `TestLineId` int(11) DEFAULT NULL,
                                                  `CitationId` int(11) DEFAULT NULL,
                                                  `CitationVersionId` int(11) DEFAULT NULL,
                                                  `CitationType` int(11) DEFAULT '0' COMMENT '0：None、1：Method、2：Regulation、3：Standard',
                                                  `CitationName` varchar(1000) DEFAULT NULL,
                                                  `SampleId` varchar(36) DEFAULT NULL,
                                                  `SampleNo` varchar(255) DEFAULT NULL COMMENT '样品编号',
                                                  `ExternalSampleNo` varchar(500) DEFAULT NULL,
                                                  `TestLineSeq` bigint(20) DEFAULT NULL,
                                                  `SampleSeq` varchar(36) DEFAULT NULL,
                                                  `ExtFields` mediumtext,
                                                  `Condition` mediumtext,
                                                  `EvaluationAlias` varchar(500) DEFAULT NULL,
                                                  `MethodDesc` varchar(512) DEFAULT NULL,
                                                  `ConclusionId` varchar(50) DEFAULT NULL,
                                                  `ConclusionDisplay` varchar(50) DEFAULT NULL,
                                                  `Languages` mediumtext,
                                                  `BizVersionId` char(32) NOT NULL,
                                                  `ActiveIndicator` int(1) NOT NULL COMMENT '0无效，1有效',
                                                  `CreatedBy` varchar(50) DEFAULT NULL,
                                                  `CreatedDate` datetime DEFAULT NULL,
                                                  `ModifiedBy` varchar(50) CHARACTER SET utf8 DEFAULT NULL,
                                                  `ModifiedDate` datetime DEFAULT NULL,
                                                  `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                                  `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                                  `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                                  `test_matrix_group_id` int(11) DEFAULT NULL COMMENT '测试单位分组标识',
                                                  `test_line_instance_id` varchar(64) DEFAULT NULL COMMENT '测试项实例标识',
                                                  `evaluation_name` varchar(255) DEFAULT NULL COMMENT '测试项名称',
                                                  `test_line_status` varchar(255) DEFAULT NULL COMMENT '测试项状态',
                                                  `test_line_remark` varchar(4000) DEFAULT NULL COMMENT '测试项备注',
                                                  `citation_full_name` varchar(255) DEFAULT NULL COMMENT '测试标准拼接名称',
                                                  `sample_instance_id` varchar(36) DEFAULT NULL COMMENT '样品实例标识',
                                                  `sample_group` text COMMENT '样品分组信息',
                                                  `sample_parent_id` varchar(36) DEFAULT NULL COMMENT '样品父级标识',
                                                  `sample_type` varchar(255) DEFAULT NULL COMMENT '样品类型',
                                                  `category` varchar(255) DEFAULT NULL COMMENT '样品分类',
                                                  `material_color` varchar(255) DEFAULT NULL COMMENT '物料颜色',
                                                  `composition` varchar(255) DEFAULT NULL COMMENT '物料材质',
                                                  `material_description` varchar(255) DEFAULT NULL COMMENT '物料描述',
                                                  `material_end_use` varchar(255) DEFAULT NULL COMMENT '物料用途',
                                                  `applicable_flag` varchar(255) DEFAULT NULL COMMENT 'NC样品标识',
                                                  `material_other_sample_info` varchar(255) DEFAULT NULL COMMENT '其他样品信息',
                                                  `material_remark` varchar(255) DEFAULT NULL COMMENT '样品备注信息',
                                                  `conclusion_code` varchar(255) DEFAULT NULL COMMENT '测试结论编码',
                                                  `customer_conclusion` varchar(50) DEFAULT NULL COMMENT '客户测试结论',
                                                  `conclusion_remark` varchar(255) DEFAULT NULL COMMENT '测试结论备注',
                                                  `last_modified_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'DB 自动更新，不允许程序设置',
                                                  `rd_report_id` bigint(20) DEFAULT NULL,
                                                  PRIMARY KEY (`id`) USING BTREE,
                                                  UNIQUE KEY `idx_BizVersionId` (`BizVersionId`),
                                                  KEY `idx_ObjectRelId` (`ObjectRelId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_test_data_matrix_info_sl_hcmc` (
                                                    `id` bigint(20) NOT NULL,
                                                    `ObjectRelId` varchar(36) DEFAULT NULL,
                                                    `TestMatrixId` varchar(36) DEFAULT NULL,
                                                    `TestLineMappingId` int(11) DEFAULT NULL,
                                                    `ExternalId` varchar(300) DEFAULT NULL,
                                                    `ExternalCode` varchar(50) DEFAULT NULL,
                                                    `PpVersionId` int(11) DEFAULT NULL,
                                                    `Aid` bigint(20) DEFAULT NULL,
                                                    `TestLineId` int(11) DEFAULT NULL,
                                                    `CitationId` int(11) DEFAULT NULL,
                                                    `CitationVersionId` int(11) DEFAULT NULL,
                                                    `CitationType` int(11) DEFAULT '0' COMMENT '0：None、1：Method、2：Regulation、3：Standard',
                                                    `CitationName` varchar(1000) DEFAULT NULL,
                                                    `SampleId` varchar(36) DEFAULT NULL,
                                                    `SampleNo` varchar(255) DEFAULT NULL COMMENT '样品编号',
                                                    `ExternalSampleNo` varchar(500) DEFAULT NULL,
                                                    `TestLineSeq` bigint(20) DEFAULT NULL,
                                                    `SampleSeq` varchar(36) DEFAULT NULL,
                                                    `ExtFields` mediumtext,
                                                    `Condition` mediumtext,
                                                    `EvaluationAlias` varchar(500) DEFAULT NULL,
                                                    `MethodDesc` varchar(512) DEFAULT NULL,
                                                    `ConclusionId` varchar(50) DEFAULT NULL,
                                                    `ConclusionDisplay` varchar(50) DEFAULT NULL,
                                                    `Languages` mediumtext,
                                                    `BizVersionId` char(32) NOT NULL,
                                                    `ActiveIndicator` int(1) NOT NULL COMMENT '0无效，1有效',
                                                    `CreatedBy` varchar(50) DEFAULT NULL,
                                                    `CreatedDate` datetime DEFAULT NULL,
                                                    `ModifiedBy` varchar(50) CHARACTER SET utf8 DEFAULT NULL,
                                                    `ModifiedDate` datetime DEFAULT NULL,
                                                    `lab_id` bigint(20) DEFAULT NULL COMMENT 'Trims系统实验室标识',
                                                    `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
                                                    `report_no` varchar(50) DEFAULT NULL COMMENT '报告号',
                                                    `test_matrix_group_id` int(11) DEFAULT NULL COMMENT '测试单位分组标识',
                                                    `test_line_instance_id` varchar(64) DEFAULT NULL COMMENT '测试项实例标识',
                                                    `evaluation_name` varchar(255) DEFAULT NULL COMMENT '测试项名称',
                                                    `test_line_status` varchar(255) DEFAULT NULL COMMENT '测试项状态',
                                                    `test_line_remark` varchar(4000) DEFAULT NULL COMMENT '测试项备注',
                                                    `citation_full_name` varchar(255) DEFAULT NULL COMMENT '测试标准拼接名称',
                                                    `sample_instance_id` varchar(36) DEFAULT NULL COMMENT '样品实例标识',
                                                    `sample_group` text COMMENT '样品分组信息',
                                                    `sample_parent_id` varchar(36) DEFAULT NULL COMMENT '样品父级标识',
                                                    `sample_type` varchar(255) DEFAULT NULL COMMENT '样品类型',
                                                    `category` varchar(255) DEFAULT NULL COMMENT '样品分类',
                                                    `material_color` varchar(255) DEFAULT NULL COMMENT '物料颜色',
                                                    `composition` varchar(255) DEFAULT NULL COMMENT '物料材质',
                                                    `material_description` varchar(255) DEFAULT NULL COMMENT '物料描述',
                                                    `material_end_use` varchar(255) DEFAULT NULL COMMENT '物料用途',
                                                    `applicable_flag` varchar(255) DEFAULT NULL COMMENT 'NC样品标识',
                                                    `material_other_sample_info` varchar(255) DEFAULT NULL COMMENT '其他样品信息',
                                                    `material_remark` varchar(255) DEFAULT NULL COMMENT '样品备注信息',
                                                    `conclusion_code` varchar(255) DEFAULT NULL COMMENT '测试结论编码',
                                                    `customer_conclusion` varchar(50) DEFAULT NULL COMMENT '客户测试结论',
                                                    `conclusion_remark` varchar(255) DEFAULT NULL COMMENT '测试结论备注',
                                                    `last_modified_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'DB 自动更新，不允许程序设置',
                                                    `rd_report_id` bigint(20) DEFAULT NULL,
                                                    PRIMARY KEY (`id`) USING BTREE,
                                                    UNIQUE KEY `idx_BizVersionId` (`BizVersionId`),
                                                    KEY `idx_ObjectRelId` (`ObjectRelId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE  `tb_test_data_matrix_info_afl_gz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_afl_sh`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_ee_gz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_hl_aj`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_hl_cz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;



ALTER TABLE  `tb_test_data_matrix_info_hl_gz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;



ALTER TABLE  `tb_test_data_matrix_info_hl_hk`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;



ALTER TABLE  `tb_test_data_matrix_info_hl_hz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_hl_nb`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;



ALTER TABLE  `tb_test_data_matrix_info_hl_nj`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;



ALTER TABLE  `tb_test_data_matrix_info_hl_qd`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;



ALTER TABLE  `tb_test_data_matrix_info_hl_sd`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_hl_sh`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_hl_sz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_hl_tc`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_hl_tj`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_hl_tp`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_hl_xm`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_mr_cq`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_mr_gz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_mr_sh`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_mr_suz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;



ALTER TABLE  `tb_test_data_matrix_info_mr_tj`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_cz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_gz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_hcmc`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_hk`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_hp`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_hz`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_isb`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_nb`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_sl_nj`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_qd`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_sh`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

ALTER TABLE  `tb_test_data_matrix_info_sl_tj`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;


ALTER TABLE  `tb_test_data_matrix_info_sl_xm`
    MODIFY COLUMN `test_line_remark` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试项备注' AFTER `test_line_status`;

