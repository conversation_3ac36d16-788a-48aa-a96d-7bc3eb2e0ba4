package com.sgs.testdatabiz.sdk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.Maps;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.sdk.client.FrameWorkReq;
import com.sgs.testdatabiz.sdk.client.FrameWorkResp;
import com.sgs.testdatabiz.sdk.config.RdConverterFactory;
import com.sgs.testdatabiz.sdk.input.dto.RdConvertRequestDTO;
import com.sgs.testdatabiz.sdk.model.DictEnumInfo;
import com.sgs.testdatabiz.sdk.output.dto.*;
import com.sgs.testdatabiz.sdk.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/4 13:08
 */
@Slf4j
public class RdConvertUtil {

    private static final String UAT = "UAT";
    private static final String PROD = "PROD";
    private static final String TEST = "TEST";
    private static final Integer CUSTOMER_REPORT = 2;
    private static final String SYSTEM_ID = "2";
    private static final String KEY_PRE = "fw.";
    private static Map<String, String> requestUrlMap = new HashMap<>();
    private static List<String> dictEnumMappingList = new ArrayList<>();
    static {
        requestUrlMap.put(TEST, "https://cnapp-test.sgs.net/testdatabiz/api/subContract/getSubTestDataInfo");
        requestUrlMap.put(UAT, "https://cnapp-uat.sgs.net/testdatabiz/api/subContract/getSubTestDataInfo");
        requestUrlMap.put(PROD, "https://cnapp.sgs.net/testdatabiz/api/subContract/getSubTestDataInfo");
    }

    public static String convert(String requestJson, String version, String env, Integer updateVersion) {
        return convert(requestJson, version, env, updateVersion,true);
    }

    public static String convert(String requestJson, String version, String env,boolean dictionaryMapping,Integer updateVersion) {
        return convert(requestJson, version, env, updateVersion,false,dictionaryMapping);
    }

    public static String convert(String requestJson, String version, String env, Integer updateVersion,boolean orderNoMapping){
        return convert(requestJson, version, env, updateVersion,orderNoMapping,false);
    }

    public static String convert(String requestJson, String version, String env, Integer updateVersion,boolean orderNoMapping,boolean dictionaryMapping) {
        if (StringUtils.isBlank(requestJson) || StringUtils.isBlank(env)) {
            return null;
        }
        ReportDataDTO reportDataDO = JSON.parseObject(requestJson, ReportDataDTO.class);
        if (reportDataDO.getHeader() == null) {
            return null;
        }
        if(Func.isNotEmpty(reportDataDO.getHeader().getReportSourceType())&& CUSTOMER_REPORT.equals(reportDataDO.getHeader().getReportSourceType())){
            return null;
        }

        Object reportData = getReportData(requestJson, env);
        if (Func.isNotEmpty(reportData)) {
            requestJson = JSON.toJSONString(reportData);
        }

        if(orderNoMapping){
            requestJson= ReportDataDOConvertUtil.convertToReportDataDO(requestJson);
        }
        /**
         * 1、识别源JSON
         * 1.1 根据 ReportExtJson + version 识别成DTO
         * 1.2 注意：不同version 识别后应该是统一的DTO结构
         */
        RdReportDataDTO reportDataDTO = new RdReportDataDTO();
        RdConvertRequestDTO rdConvertRequestDTO = new RdConvertRequestDTO(requestJson, reportDataDTO, version, env);
        // TODO 此处类型问题待处理
        Object reportDataObj = RdConverterFactory.get(version).handler(rdConvertRequestDTO);

        /**
         * 2. Step1.DTO 转化为 目标 DTO结构 （只转化结构）
         * 2.1 目标DTO结构 是根据RDC&产品模型 统一预定的标准结构
         * 2.2 目标DTO结构中会体现对象关系
         * 2.3 目标DTO结构中会“冗余” label字段  - - - 枚举/元数据/配置信息等的翻译补充
         */

        /**
         * 3、根据Step2.DTO 补充“冗余”数据
         * 3.1 枚举翻译
         * 3.2 元数据查询/补充
         * 3.3 配置信息补充dataOutput = {RdReportDataDTO@17149} "RdReportDataDTO(reportTrfRelList=[RdReportTrfRelDTO(trfRefSystemId=2, trfNo=TRF4D005443, trfTemplateOwner=null, orderSystemId=2, orderNo=SL924092888129TX, reportNo=SL92409288812901TX, activeIndicator=null, trfTemplateId=null, trfTemplateName=null, lastModifiedTimestamp=null)], testDataObjectRelList=null, header=RdReportDTO(labId=46, reportId=4ebb23d0-aadb-4cab-a989-964b22f6f4a9, reportNo=SL92409288812901TX, originalReportNo=, reportHeader=null, reportAddress=null, reportStatus=203, reportStatusLabel=Approved, reportCertificateName=, reportCreatedBy=qiaoyun_zhuang, reportCreatedDate=Sun Apr 07 13:30:35 CST 2024, reportApproverBy=Ula_yang, reportApproverDate=Fri Apr 12 11:17:08 CST 2024, softcopyDeliveryDate=Fri Apr 12 11:18:24 CST 2024, conclusionCode=Data Only, customerConclusion=See Results, reviewConclusion=null, reportDeliveredTo=null, conclusionRemark=null, activeIndicator=null, reportLangList=null, lastModifiedTimestamp=null, reportInstanceId=null), reportExt=RdReportExtDTO(reques"... View
         */
        RdReportDataDTO dataOutput = JSON.parseObject(JSON.toJSONString(reportDataObj), RdReportDataDTO.class);
        JSONObject object = JSON.parseObject(requestJson);
        dataOutput.setDffMappingList(getDffMapping(
                JSONPath.eval(object, "$.header.lab.buCode") != null
                        ? JSONPath.eval(object, "$.header.lab.buCode").toString() : null,
                JSONPath.eval(object, "$.header.lab.labCode") != null
                        ? JSONPath.eval(object, "$.header.lab.labCode").toString() : null,
                JSONPath.eval(object, "$.order.orderNo") != null
                        ? JSONPath.eval(object, "$.order.orderNo").toString() : null,
                JSONPath.eval(object, "$.header.reportNo") != null
                        ? JSONPath.eval(object, "$.header.reportNo").toString() : null, env));
        dataOutput.setUpdateVersion(updateVersion);

        /**
         * 4、输出step3.DTO 至JSON结构 (可选）
         * 4.1 step3.DTO 结构应该调用方可识别的对象结构 ；（Java DTO）
         * 4.2 默认输出 JSON 形式（String）
         */
        String result = JSON.toJSONString(dataOutput);

        if(dictionaryMapping){
            result=  setDictEnumInfoMapping(env, result);
        }

        return result;
    }

    private static String  setDictEnumInfoMapping(String env, String result) {
        //获取需要做字典映射的枚举值SCI-1311
        List<String> intersectionList = getIntersectionList(env);

        if(CollectionUtils.isNotEmpty(intersectionList)){
            ConcurrentHashMap<String, List<DictEnumInfo>> dataDictionaryMap = getDataDictionaryMap(env, intersectionList);
            if(Objects.nonNull(dataDictionaryMap)&&!dataDictionaryMap.isEmpty()){
                return mappingDictEnum(dataDictionaryMap, result);
            }
        }
        return result;
    }

    private static ConcurrentHashMap<String, List<DictEnumInfo>> getDataDictionaryMap(String env, List<String> intersectionList) {
        List<FrameWorkReq> frameWorkReqList = new ArrayList<>();
        intersectionList.forEach(sysKeyGroup->{
            FrameWorkReq frameWorkReq = new FrameWorkReq();
            frameWorkReq.setSysKeyGroup(KEY_PRE+sysKeyGroup);
            frameWorkReq.setSystemID(SYSTEM_ID);
            frameWorkReqList.add(frameWorkReq);
        });
        return FrameWorkHandler.getDictionaryMap(frameWorkReqList, env);
    }

    private static List<String> getIntersectionList(String env) {
        List<String> intersectionList = Collections.emptyList();
        List<DictEnumInfo> dictEnumList = FrameWorkHandler.getDictionaryValue(env);
        if(CollectionUtils.isNotEmpty(dictEnumList)){
            if(CollectionUtils.isNotEmpty(dictEnumMappingList)){
               //获取两个list的交际，dictEnumMappingList和dictEnumList
                intersectionList = dictEnumList.stream().map(DictEnumInfo::getSysKey).filter(sysKey -> dictEnumMappingList.contains(sysKey)).collect(Collectors.toList());
            }else{
                intersectionList = dictEnumList.stream().map(DictEnumInfo::getSysKey).collect(Collectors.toList());
            }
        }
        return intersectionList;
    }

    private static String mappingDictEnum(ConcurrentHashMap<String, List<DictEnumInfo>> dictionaryMap, String result) {
        if(Objects.nonNull(dictionaryMap)&& !dictionaryMap.isEmpty()){
            Map<String, Map<String, String>> replacements = new HashMap<>();
            dictionaryMap.keySet().forEach(keyGroup->{
                String keyName = EnumUtils.extractEnumName(keyGroup);
                List<DictEnumInfo> frameWorkReqList = dictionaryMap.get(keyGroup);
                Map<String,String> values = frameWorkReqList.stream().map(frameWorkResp -> new AbstractMap.SimpleEntry<>( frameWorkResp.getSysKey(),frameWorkResp.getSysValue())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                replacements.put(keyName, values);
            });
            return JsonBatchReplaceUtil.batchReplaceJson(result, replacements);
        }
        return result;
    }


    // todo 临时方法，需要被移除
    public static Object getReportData(String requestJson, String env) {
        try {
            Map<String, String> httpHeader = Maps.newHashMap();
            httpHeader.put("requestId", String.valueOf(System.currentTimeMillis()));
            httpHeader.put("systemId", "0");
            //转换成大写的形式
            env = env.toUpperCase();
            String url = requestUrlMap.get(env);
            String jsonResult = HttpClientUtil.postJsonHeader(url, JSON.parseObject(requestJson).toString(), httpHeader);
            return JSONPath.eval(JSON.parseObject(jsonResult), "$.data");
        } catch (Exception e) {
            log.error("调用rd服务异常", e);
        }
        return requestJson;
    }

    public static Object getDffMapping(String productLineCode, String labCode, String orderNo, String reportNo, String env) {
        try {
            Map<String, String> httpHeader = Maps.newHashMap();
            httpHeader.put("productLineCode", productLineCode);
            httpHeader.put("labCode", labCode);
            httpHeader.put("orderNo", orderNo);
            httpHeader.put("reportNo", reportNo);
            String url = Objects.equals(PROD, env) ? "https://cnapp.sgs.net/dffdcs-api/dffData/unpivotProduct" : "https://cnapp-uat.sgs.net/dffdcs-api/dffData/unpivotProduct";
            String jsonResult = HttpClientUtil.postJson(url, JSON.toJSONString(httpHeader));
            return JSONPath.eval(JSON.parseObject(jsonResult), "$.data");
        } catch (Exception e) {
            log.error("调用rd服务异常", e);
        }
        return null;
    }

    
}
