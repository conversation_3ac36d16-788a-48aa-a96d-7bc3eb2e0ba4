jdbc.datasource.read=get,select,count,list,query,find,search,sum
jdbc.datasource.write=add,create,update,delete,remove,insert

##\u6570\u636E\u5E93\u914D\u7F6E
validationQuery=SELECT 'x'


# Test Data Master DB
datasource.dynamic.common.product-lines=SL,HL,MR,AUTO,IND-PL
datasource.dynamic.common.schema.testdata.master.driver-class-name=com.mysql.jdbc.Driver
datasource.dynamic.common.schema.testdata.master.url=*********************************************************************************************************************************************
datasource.dynamic.common.schema.testdata.master.username=datadb_user_dev
datasource.dynamic.common.schema.testdata.master.password=DaDB_sgs_0531DEV

# Test Data slave DB
datasource.dynamic.common.schema.testdata.slave.driver-class-name=com.mysql.jdbc.Driver
datasource.dynamic.common.schema.testdata.slave.url=*********************************************************************************************************************************************
datasource.dynamic.common.schema.testdata.slave.username=datadb_user_dev
datasource.dynamic.common.schema.testdata.slave.password=DaDB_sgs_0531DEV


# redis.cluster.nodes
spring.redis.nodes=*************:7000,*************:7001,*************:7002,*************:7003,*************:7004,*************:7005
# Redis\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD == redis.cluster.password
spring.redis.password=
# \uFFFD\uFFFD\uFFFD\u04F3\uFFFD\u02B1\u02B1\uFFFD\u48E8\uFFFD\uFFFD\uFFFD\uB8E9
spring.redis.timeout=10000
# \u02B9\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u077F\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0163\uFFFD\u04BB\uFFFD\uFFFD\u02BE\uFFFD\uFFFD\uFFFD\uFFFD16\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u077F\uFFFD 0 \uFFFD\uFFFD 15
spring.redis.database=0
# \uFFFD\uFFFD\u023A\u0123\u02BD\uFFFD\u00A3\uFFFD\uFFFD\uFFFD\u023A\uFFFD\uFFFD\uFFFD\u05EA\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
spring.redis.max-redirects=6

# \uFFFD\uFFFD\uFFFD\u04F3\u0635\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u02B9\uFFFD\u00F8\uFFFD\u05B5\uFFFD\uFFFD\u02BE\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u013F\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
spring.redis.pool.max-idle=20
# \uFFFD\uFFFD\u0421\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u02B9\uFFFD\uFFFD\uFFFD\uFFFD\u05B5\uFFFD\uFFFD\uFFFD\uFFFD\u0427\uFFFD\uFFFD
spring.redis.pool.min-idle=5
# \uFFFD\uFFFD\uFFFD\u04F3\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u02B9\uFFFD\u00F8\uFFFD\u05B5\uFFFD\uFFFD\u02BE\u00FB\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u01A3\uFFFD==redis.pool.maxTotal
spring.redis.pool.max-active=20
# \uFFFD\uFFFD\uFFFD\u04F3\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0234\uFFFD\u02B1\uFFFD\u48E8\u02B9\uFFFD\u00F8\uFFFD\u05B5\uFFFD\uFFFD\u02BE\u00FB\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u01A3\uFFFD
spring.redis.pool.max-wait=1000
# redis.pool.testOnBorrow
spring.redis.pool.testOnBorrow=true


# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
tomcat.protocol=org.apache.coyote.http11.Http11Nio2Protocol
# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0738\uFFFD\uFFFD\u00E3\uFFFD\uFFFD\uFFFD\u04AA\uFFFD\uFFFD\u05F0
# tomcat.protocol=org.apache.coyote.http11.Http11AprProtocol
tomcat.connectionTimeout=20000
tomcat.maxConnections=2000
tomcat.maxThreads=700
tomcat.uriEncoding=UTF-8
tomcat.acceptCount=2000
# webEnvironment=false
tomcat.port=8092
# \uFFFD\uFFFDdubbo\u042D\uFFFD\uFFFD\uFFFD\uFFFD20880\uFFFD\u02FF\u06B1\uFFFD\u00B6\uFFFD\uFFFD\uFFFD\uFFFD
dubbo.port=29539
zookeeper.address=*************:2181,*************:2181
# kafka
kafka.bootstrap-servers=*************:9092,*************:9092

user.management.url=http://cnapp-dev.sgs.net/UserManagementApi
localiLayer.url=http://cnlocalilayer-dev.sgs.net
frameWorkApi.url=http://cnapp-dev.sgs.net/FrameWorkApi
notification.url=http://cnapp-dev.sgs.net/NotificationApi

#----------- Start Starlims -----------
starLims.url=https://dev-starlims.sgs.net/starlims11.sgs.dev
starLims.authId=bdeb6142f767419fa6ec6446f734c361
starLims.header.user.key=STARLIMSUser
starLims.header.user.val=TRIMS
starLims.header.pass.key=STARLIMSPass
starLims.header.pass.val=Tr1m5
#----------- End Starlims -----------
swagger.is.enable=true

# Filter Configuration
report.filter.enabled=true
# systemId=0 means the configuration applies to all systems
report.filter.systems.0.enabled=true
# Define all possible filter types that should be enabled globally
report.filter.systems.0.filterTypes=ATTACHMENT_VISIBILITY,PRETREATMENT_TEST_LINE,TEST_LINE_REMARK,CONCLUSION_CONVERTER,TEST_LINE_STATUS