/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestResultResultDO {
    private String testResultFullName;

    private RdTestResultNameDO testResultFullNameRel;

    private String resultValue;
    private String resultValueRemark;
    private String resultUnit;
        //SCI-1378
    private String failRemark;
    private String reportRemark;
    private Integer failFlag;
    private Integer analyteConclusionId;
    private String  analyteCustomerConclusion;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
