package com.sgs.testdatabiz.domain.service.validation.config;

import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ValidationExcludeManager {
    
    private static final String GLOBAL_SYSTEM_ID = "0";
    
    @Autowired
    private ValidationExcludeConfig excludeConfig;
    
    @Autowired
    private Environment environment;
    
    @PostConstruct
    public void init() {
        log.info("[ValidationExcludeManager] 校验排除配置: excludeEnabled={}, excludeStrategies={}", 
            excludeConfig.isExcludeEnabled(), 
            excludeConfig.getExcludeStrategies());
    }
    
    /**
     * 检查指定的校验类型和系统是否需要执行校验
     * @param validationType 校验类型
     * @param systemId 系统ID
     * @return true-需要执行校验 false-无需执行校验
     */
    public boolean shouldValidate(String validationType, Integer systemId) {
        // 优先使用Nacos配置
        if (hasExcludeConfig()) {
            return shouldValidateByNacos(validationType, systemId);
        }
        
        // 回退到本地配置
        return shouldValidateByLocal(validationType, systemId);
    }
    
    private boolean hasExcludeConfig() {
        return excludeConfig != null && excludeConfig.getExcludeStrategies() != null;
    }
    
    private boolean shouldValidateByNacos(String validationType, Integer systemId) {
        // 排除开关未启用时，执行所有校验
        if (!excludeConfig.isExcludeEnabled()) {
            log.debug("校验排除开关未启用，执行所有校验");
            return true;
        }
        
        // 获取校验类型的排除策略
        ValidationExcludeConfig.ExcludeStrategy strategy = 
            excludeConfig.getExcludeStrategies().get(validationType);
            
        // 排除策略未配置或未启用时，执行校验
        if (strategy == null || !strategy.isStrategyEnabled()) {
            log.debug("校验类型{}的排除策略未启用，执行校验", validationType);
            return true;
        }
        
        // 检查系统ID是否在排除列表中
        Set<String> excludedSystems = strategy.getExcludedSystemIds();
        if (excludedSystems == null || !excludedSystems.contains(String.valueOf(systemId))) {
            return true;
        }
        
        log.debug("系统{}在校验类型{}的排除列表中，跳过校验", systemId, validationType);
        return false;
    }
    
    private boolean shouldValidateByLocal(String validationType, Integer systemId) {
        // 排除开关未启用时，执行所有校验
        Boolean excludeEnabled = environment.getProperty("validation.exclude.enabled", Boolean.class);
        if (excludeEnabled == null || !excludeEnabled) {
            log.debug("本地校验排除开关未启用，执行所有校验");
            return true;
        }
        
        String strategyPath = String.format("validation.exclude.strategies.%s", validationType);
        
        // 排除策略未启用时，执行校验
        Boolean strategyEnabled = environment.getProperty(strategyPath + ".enabled", Boolean.class);
        if (strategyEnabled == null || !strategyEnabled) {
            log.debug("本地校验类型{}的排除策略未启用，执行校验", validationType);
            return true;
        }
        
        // 检查系统ID是否在排除列表中
        List<String> excludedSystems = environment.getProperty(
            strategyPath + ".excludedSystemIds", List.class);
        if (excludedSystems == null || !excludedSystems.contains(String.valueOf(systemId))) {
            return true;
        }
        
        log.debug("系统{}在本地校验类型{}的排除列表中，跳过校验", systemId, validationType);
        return false;
    }
} 