package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoiceExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportInvoicePO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportInvoiceMapper {
    int countByExample(RdReportInvoiceExample example);

    int deleteByExample(RdReportInvoiceExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportInvoicePO record);

    int insertSelective(RdReportInvoicePO record);

    List<RdReportInvoicePO> selectByExample(RdReportInvoiceExample example);

    RdReportInvoicePO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportInvoicePO record, @Param("example") RdReportInvoiceExample example);

    int updateByExample(@Param("record") RdReportInvoicePO record, @Param("example") RdReportInvoiceExample example);

    int updateByPrimaryKeySelective(RdReportInvoicePO record);

    int updateByPrimaryKey(RdReportInvoicePO record);

    int batchInsert(List<RdReportInvoicePO> list);

    int batchUpdate(List<RdReportInvoicePO> list);
}