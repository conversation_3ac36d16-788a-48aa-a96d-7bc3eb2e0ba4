package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportMapper {
    int countByExample(RdReportExample example);

    int deleteByExample(RdReportExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportPO record);

    int insertSelective(RdReportPO record);

    List<RdReportPO> selectByExample(RdReportExample example);

    RdReportPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportPO record, @Param("example") RdReportExample example);

    int updateByExample(@Param("record") RdReportPO record, @Param("example") RdReportExample example);

    int updateByPrimaryKeySelective(RdReportPO record);

    int updateByPrimaryKey(RdReportPO record);

    int batchInsert(List<RdReportPO> list);

    int batchUpdate(List<RdReportPO> list);
}