package com.sgs.testdatabiz.domain.service.validation.type.conclusion;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.model.ReportTestDataValidationDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

public abstract class AbstractConclusionValidationService implements ValidationService {

    @Override
    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO) {
        ReportTestDataValidationDTO reportTestDataValidationDTO = (ReportTestDataValidationDTO) validationRequestDTO;
        ReportDataBatchDTO reportData = reportTestDataValidationDTO.getReportDataBatchDTO();
        
        AtomicBoolean isValid = new AtomicBoolean(true);
        List<String> errorMessages = new ArrayList<>();

        // 1. 校验report级别的conclusion
        if (!CollectionUtils.isEmpty(reportData.getReportList())) {
            reportData.getReportList().forEach(report -> {
                if (report.getConclusion() != null) {
                    validateConclusion(report.getConclusion(), isValid, errorMessages);
                }

                // 校验report matrix级别的conclusion
                if (!CollectionUtils.isEmpty(report.getReportMatrixList())) {
                    report.getReportMatrixList().forEach(matrix -> {
                        if (matrix.getConclusion() != null) {
                            validateConclusion(matrix.getConclusion(), isValid, errorMessages);
                        }
                    });
                }
            });
        }

        // 2. 校验test line级别的conclusion (在ReportDataBatchDTO层级)
        if (!CollectionUtils.isEmpty(reportData.getTestLineList())) {
            reportData.getTestLineList().forEach(testLine -> {
                if (testLine.getConclusion() != null) {
                    validateConclusion(testLine.getConclusion(), isValid, errorMessages);
                }
            });
        }

        if (isValid.get()) {
            return ValidationResultDTO.success("Conclusion validation passed");
        } else {
            return ValidationResultDTO.fail(String.join("; ", errorMessages));
        }
    }

    protected abstract void validateConclusion(RdConclusionDTO conclusion, AtomicBoolean isValid, List<String> errorMessages);

    @Override
    public String getType() {
        return ValidationTypeEnum.CONCLUSION_DATA.getName();
    }
} 