package com.sgs.testdatabiz.integration;

import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.ReportFacade;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.info.report.GetReportInfo;
import com.sgs.otsnotes.facade.model.req.report.ReportSimplifyInfoReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import java.util.HashMap;
import java.util.Map;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sgs.testdatabiz.core.constant.ApiUrlConstants;
import com.sgs.testdatabiz.core.util.HttpClientUtil;


/**
 *
 */
@Component
public class ReportClient {
    private static final Logger logger = LoggerFactory.getLogger(ReportClient.class);
    
    @Value("${api.base.otsnotes}")
    private String otsnotesBaseUrl;
    
    @Autowired
    private TokenClient tokenClient;

    public GetReportInfo getReportinfoByOrderNo(String orderNo) {
        try {
            String url = otsnotesBaseUrl + ApiUrlConstants.OtsNotes.Report.GET_REPORT_BY_ORDER;
            
            ReportSimplifyInfoReq reqObject = new ReportSimplifyInfoReq();
            reqObject.setOrderNo(orderNo);
            reqObject.setToken(tokenClient.getToken());

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("token", tokenClient.getToken());
            
            String response = HttpClientUtil.postJsonHeader(url, JSONObject.toJSONString(reqObject), headers);
            
            if (Func.isEmpty(response)) {
                logger.error("ReportClient.getReportinfoByOrderNo error, orderNo:{}, response is empty", orderNo);
                return null;
            }
            
            BaseResponse<GetReportInfo> baseResponse = 
                JSONObject.parseObject(response, new TypeReference<BaseResponse<GetReportInfo>>(){});
                
            if (baseResponse == null || baseResponse.getStatus() != 200) {
                logger.error("ReportClient.getReportinfoByOrderNo error, orderNo:{}, response:{}", orderNo, response);
                return null;
            }
            
            return baseResponse.getData();
            
        } catch (Exception e) {
            logger.error("ReportClient.getReportinfoByOrderNo exception, orderNo:{}", orderNo, e);
            return null;
        }
    }
}
