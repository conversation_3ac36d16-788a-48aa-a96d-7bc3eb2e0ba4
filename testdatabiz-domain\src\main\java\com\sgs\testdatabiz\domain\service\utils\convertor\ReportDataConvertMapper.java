package com.sgs.testdatabiz.domain.service.utils.convertor;

import com.sgs.testdatabiz.core.enums.ReportSourceTypeEnum;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

@Mapper
public interface ReportDataConvertMapper {

    ReportDataConvertMapper INSTANCE = Mappers.getMapper( ReportDataConvertMapper.class );

    /**
     * 特殊处理，解决logicOrderNo和OrderNo的问题
     * logicOrder是OrderNoGroup的概念，下面可以有多个order
     * 之前的概念是orderNO里面存储的是logicOrderNO
     * 现在是orderNO里面存储的是orderNO，rootOrderNo存储的是logicOrderNO
     * 这个逻辑主要为RDC的后续统计只用，在SCI的业务系统暂时不动
     * 所以需要做一个转换，转换逻辑是：
     * 1. 将ReportDataBatchDTO请求的rootOrderNo的值赋给orderNO
     * 2. 将ReportDataBatchDTO请求的OrderNo的值赋值给realOrderNo
     */

    @Mapping(source = "trfList", target = "trfList")
    @Mapping(source ="orderList", target = "orderList")
    ReportDataBatchDO convert2ReportDataBatchDO(ReportDataBatchDTO reportDataDTO);

    @Mapping(source = "orderList", target = "orderList")
    RdTrfDO convert2RdTrfDO(RdTrfDTO rdTrfDTO); // RdTrfDTO

    @Mapping(target = "realOrderNo",expression = "java(rdOrderRelDTO.getRealOrderNo() != null&& rdOrderRelDTO.getRealOrderNo().length() > 0 ? rdOrderRelDTO.getRealOrderNo() : rdOrderRelDTO.getOrderNo())")
    @Mapping(target = "orderNo",
            expression = "java(rdOrderRelDTO.getRootOrderNo() != null&& rdOrderRelDTO.getRootOrderNo().length() > 0 ? rdOrderRelDTO.getRootOrderNo() :  rdOrderRelDTO.getOrderNo())")
    RdOrderRelDO convert2RdOrderRelDTO(RdOrderRelDTO rdOrderRelDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdOrderDTO.getRootOrderNo() != null&& rdOrderDTO.getRootOrderNo().length() > 0 ? rdOrderDTO.getRootOrderNo() : rdOrderDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdOrderDTO.getRealOrderNo() != null&& rdOrderDTO.getRealOrderNo().length() > 0 ? rdOrderDTO.getRealOrderNo() : rdOrderDTO.getOrderNo())")
    RdOrderDO convert2RdOrderDO(RdOrderDTO rdOrderDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdReportDTO.getRootOrderNo() != null&& rdReportDTO.getRootOrderNo().length() > 0 ? rdReportDTO.getRootOrderNo() : rdReportDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdReportDTO.getRealOrderNo() != null&& rdReportDTO.getRealOrderNo().length() > 0 ? rdReportDTO.getRealOrderNo() : rdReportDTO.getOrderNo())")
    RdReportDO convert2RdReportDO(RdReportDTO rdReportDTO);

    ReportCertificateDO.TestReportInfo convert2RdReportCertificateDO(ReportCertificateDTO.TestReportInfo testReportInfo);

    RdEfilingDO.EfilingAddressInfo convert2RdEfilingDO(RdEfilingDTO.EfilingAddressInfo addressInfo);

    @Mapping(target = "parent.orderNo",
            expression = "java(rdParentDTO.getRootOrderNo() != null&& rdParentDTO.getRootOrderNo().length() > 0 ?rdParentDTO.getRootOrderNo() : rdParentDTO.getOrderNo())")
    @Mapping(target = "parent.realOrderNo",expression = "java(rdParentDTO.getRealOrderNo() != null&& rdParentDTO.getRealOrderNo().length() > 0 ? rdParentDTO.getRealOrderNo() : rdParentDTO.getOrderNo())")
    RdRelationshipDO convert2RdRelationshipDO(RdRelationshipDTO rdRelationshipDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdTestSampleDTO.getRootOrderNo() != null && rdTestSampleDTO.getRootOrderNo().length() > 0? rdTestSampleDTO.getRootOrderNo() : rdTestSampleDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdTestSampleDTO.getRealOrderNo() != null&& rdTestSampleDTO.getRealOrderNo().length() > 0 ? rdTestSampleDTO.getRealOrderNo() : rdTestSampleDTO.getOrderNo())")
    RdTestSampleDO convert2RdTestSampleDO(RdTestSampleDTO rdTestSampleDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdTestLineDTO.getRootOrderNo() != null&& rdTestLineDTO.getRootOrderNo().length() > 0 ? rdTestLineDTO.getRootOrderNo() : rdTestLineDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdTestLineDTO.getRealOrderNo() != null&& rdTestLineDTO.getRealOrderNo().length() > 0 ? rdTestLineDTO.getRealOrderNo() : rdTestLineDTO.getOrderNo())")
    RdTestLineDO convert2RdTestLineDO(RdTestLineDTO rdTestLineDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdTestResultDTO.getRootOrderNo() != null&& rdTestResultDTO.getRootOrderNo().length() > 0 ? rdTestResultDTO.getRootOrderNo() : rdTestResultDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdTestResultDTO.getRealOrderNo() != null&& rdTestResultDTO.getRealOrderNo().length() > 0 ? rdTestResultDTO.getRealOrderNo() : rdTestResultDTO.getOrderNo())")
    RdTestResultDO convert2RdTestResultDO(RdTestResultDTO rdTestResultDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdReportConclusionDTO.getRootOrderNo() != null&& rdReportConclusionDTO.getRootOrderNo().length() > 0 ? rdReportConclusionDTO.getRootOrderNo() : rdReportConclusionDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdReportConclusionDTO.getRealOrderNo() != null&& rdReportConclusionDTO.getRealOrderNo().length() > 0 ? rdReportConclusionDTO.getRealOrderNo() : rdReportConclusionDTO.getOrderNo())")
    RdReportConclusionDO convert2RdReportConclusionDO(RdReportConclusionDTO rdReportConclusionDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdConditionGroupDTO.getRootOrderNo() != null&& rdConditionGroupDTO.getRootOrderNo().length() > 0 ? rdConditionGroupDTO.getRootOrderNo() : rdConditionGroupDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdConditionGroupDTO.getRealOrderNo() != null&& rdConditionGroupDTO.getRealOrderNo().length() > 0 ? rdConditionGroupDTO.getRealOrderNo() : rdConditionGroupDTO.getOrderNo())")
    RdConditionGroupDO convert2RdConditionGroupDO(RdConditionGroupDTO rdConditionGroupDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdQuotationDTO.getRootOrderNo() != null&& rdQuotationDTO.getRootOrderNo().length() > 0 ? rdQuotationDTO.getRootOrderNo() : rdQuotationDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdQuotationDTO.getRealOrderNo() != null&& rdQuotationDTO.getRealOrderNo().length() > 0 ? rdQuotationDTO.getRealOrderNo() : rdQuotationDTO.getOrderNo())")
    RdQuotationDO convert2RdQuotationDO(RdQuotationDTO rdQuotationDTO);

    @Mapping(target = "orderNo",
            expression = "java(rdInvoiceDTO.getRootOrderNo() != null&& rdInvoiceDTO.getRootOrderNo().length() > 0 ? rdInvoiceDTO.getRootOrderNo() : rdInvoiceDTO.getOrderNo())")
    @Mapping(target = "realOrderNo",expression = "java(rdInvoiceDTO.getRealOrderNo() != null&& rdInvoiceDTO.getRealOrderNo().length() > 0 ? rdInvoiceDTO.getRealOrderNo() : rdInvoiceDTO.getOrderNo())")
    RdInvoiceDO convert2RdInvoiceDO(RdInvoiceDTO rdInvoiceDTO);


}
