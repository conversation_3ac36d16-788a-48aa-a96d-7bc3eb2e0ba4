package com.sgs.testdatabiz.facade.model.testdata;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class TestDataResultInfo extends PrintFriendliness {
    /**
     *
     */
    @ApiModelProperty("分析项Id 取值规则： \n \n " +
            "   StarLims：取id  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private Integer testAnalyteId;

    /**
     *
     */
    @ApiModelProperty("分析项编码 取值规则： \n \n " +
            "   StarLims：取analyte  \n "+
            "   SLIM：取analyteCode  \n "+
            "   FAST：？  \n "+
            "")
    private String analyteCode;

    /**
     *
     */
    @ApiModelProperty("分析项名称 取值规则： \n \n " +
            "   StarLims：取analyteAlias  \n "+
            "   SLIM：取SA_DESC  \n "+
            "   FAST：？ \n "+
            "")
    private String testAnalyteName;

    /**
     *
     */
    @ApiModelProperty("分析项类型（0：General、1：Conclusion） ")
    private Integer analyteType;

    /**
     *
     */
    @ApiModelProperty("测试单位")
    private String reportUnit;

    /**
     *
     */
    @ApiModelProperty("测试结果 取值规则： \n \n " +
            "   StarLims：取result  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String testValue;

    /**
     *
     */
    @ApiModelProperty("casNo")
    private String casNo;

    /**
     *
     */
    @ApiModelProperty("测试单位 取值规则： \n \n " +
            "   StarLims：取limit  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String limitUnit;

    /**
     *
     */
    @ApiModelProperty("测试标准范围 取值规则： \n \n " +
            "   StarLims：取unit  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String reportLimit;


    /**
     *
     */
    @ApiModelProperty("测试结论Id")
    private String conclusionId;

    /**
     *
     */
    @ApiModelProperty("分析项排序 取值规则： \n \n " +
            "   StarLims：取sorter  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private Integer analyteSeq;

    /**
     *
     */
    @ApiModelProperty("多语言")
    private List<TestDataResultLangInfo> languages;

    /**
     * SCI-1378 测试方法参考范围
     */
    private String methodLimit;

    public String getMethodLimit() {
        return methodLimit;
    }

    public void setMethodLimit(String methodLimit) {
        this.methodLimit = methodLimit;
    }

    public Integer getTestAnalyteId() {
        return testAnalyteId;
    }

    public void setTestAnalyteId(Integer testAnalyteId) {
        this.testAnalyteId = testAnalyteId;
    }

    public String getAnalyteCode() {
        return analyteCode;
    }

    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public Integer getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getLimitUnit() {
        return limitUnit;
    }

    public void setLimitUnit(String limitUnit) {
        this.limitUnit = limitUnit;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getConclusionId() {
        return conclusionId;
    }

    public void setConclusionId(String conclusionId) {
        this.conclusionId = conclusionId;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    public List<TestDataResultLangInfo> getLanguages() {
        return languages;
    }

    public void setLanguages(List<TestDataResultLangInfo> languages) {
        this.languages = languages;
    }
}
