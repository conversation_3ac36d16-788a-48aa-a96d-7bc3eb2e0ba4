package com.sgs.testdatabiz.domain.service.rd.manager;

import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.RdQuotationExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdQuotationInvoiceRelMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdQuotationLangMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdQuotationMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.*;
import com.sgs.testdatabiz.facade.model.enums.AttachmentObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: qiWen_xue
 * @create: 2023-04-27 14:21
 */
@Slf4j
@Component
public class QuotationManager {

    private final RdQuotationMapper quotationMapper;

    private final RdQuotationExtMapper quotationExtMapper;

    private final RdQuotationInvoiceRelMapper quotationInvoiceRelMapper;

    private final RdQuotationLangMapper quotationLangMapper;

    private final AttachmentManager attachmentManager;

    public boolean existQuotationNos(List<String> quotationNos, Long systemId) {
        if (Func.isEmpty(quotationNos)) {
            return false;
        }
        List<String> list = quotationNos.stream().distinct().collect(Collectors.toList());

        List<String> quotationNoList = quotationExtMapper.selectQuotationNos(list, systemId);
        if (Func.isEmpty(quotationNoList)) {
            return false;
        }
        if (Func.equals(quotationNoList.size(), list.size())) {
            return true;
        } else {
            return false;
        }
    }


    public QuotationManager(RdQuotationMapper quotationMapper, RdQuotationInvoiceRelMapper quotationInvoiceRelMapper,
                            RdQuotationExtMapper quotationExtMapper,
                            RdQuotationLangMapper quotationLangMapper,
                            AttachmentManager attachmentManager) {
        this.quotationMapper = quotationMapper;
        this.quotationInvoiceRelMapper = quotationInvoiceRelMapper;
        this.quotationExtMapper = quotationExtMapper;
        this.quotationLangMapper = quotationLangMapper;
        this.attachmentManager = attachmentManager;
    }

    public boolean batchInsert(List<RdQuotationPO> quotationPOList) {
        if (Func.isEmpty(quotationPOList)) {
            return false;
        }
        int count = quotationMapper.batchInsert(quotationPOList);
        if (count > 0) {
            return true;
        } else {
            return false;
        }
    }

    public boolean batchInsertLang(List<RdQuotationLangPO> quotationLangPOList) {
        if (Func.isEmpty(quotationLangPOList)) {
            return false;
        }
        int count = quotationLangMapper.batchInsert(quotationLangPOList);
        if (count > 0) {
            return true;
        } else {
            return false;
        }
    }

    public List<RdQuotationPO> selectByReportNo(Long labId, String reportNo, String orderNo, List<String> quotationNos) {
        RdQuotationExample quotationExample = new RdQuotationExample();
        RdQuotationExample.Criteria criteria = quotationExample.createCriteria();
        criteria.andLabIdEqualTo(labId).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        if (Func.isNotEmpty(reportNo)) {
            criteria.andReportNoEqualTo(reportNo);
        }
        if (Func.isNotEmpty(orderNo)) {
            criteria.andOrderNoEqualTo(orderNo);
        }
        if (Func.isNotEmpty(quotationNos)) {
            criteria.andQuotationNoIn(quotationNos);
        }
        return quotationMapper.selectByExample(quotationExample);
    }

    public List<RdQuotationPO> selectByReportNo(Long labId, List<String> reportNos) {
        RdQuotationExample quotationExample = new RdQuotationExample();
        RdQuotationExample.Criteria criteria = quotationExample.createCriteria();
        criteria.andLabIdEqualTo(labId).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus()).andReportNoIn(reportNos);
        return quotationMapper.selectByExample(quotationExample);
    }

    public Map<String, List<String>> selectMapByInvoiceNo(List<String> invoiceNos) {
        Map<String, List<String>> map = new HashMap<>();
        if (Func.isEmpty(invoiceNos)) {
            return map;
        }
        invoiceNos = invoiceNos.stream().distinct().collect(Collectors.toList());
        RdQuotationInvoiceRelExample quotationInvoiceRelExample = new RdQuotationInvoiceRelExample();
        quotationInvoiceRelExample.createCriteria().andInvoiceNoIn(invoiceNos);
        List<RdQuotationInvoiceRelPO> quotationInvoiceRelPOList = quotationInvoiceRelMapper.selectByExample(quotationInvoiceRelExample);
        Map<String, List<RdQuotationInvoiceRelPO>> listMap = quotationInvoiceRelPOList.stream().collect(Collectors.groupingBy(RdQuotationInvoiceRelPO::getInvoiceNo));
        if (Func.isNotEmpty(listMap)) {
            for (String key : listMap.keySet()) {
                List<RdQuotationInvoiceRelPO> invoiceRelPOS = listMap.get(key);
                List<String> quotations = invoiceRelPOS.stream().map(RdQuotationInvoiceRelPO::getQuotationNo).collect(Collectors.toList());
                map.put(key, quotations);
            }
        }
        return map;
    }

    public List<RdQuotationPO> getQuotationList(String reportNo, Long systemId) {
        RdQuotationExample quotationExample = new RdQuotationExample();
        quotationExample.createCriteria().andReportNoEqualTo(reportNo).andSystemIdEqualTo(systemId);
        return quotationMapper.selectByExample(quotationExample);
    }

    public void deleteQuotationData(String reportNo, Long systemId) {
        List<RdQuotationPO> quotationList = getQuotationList(reportNo, systemId);
        if (Func.isNotEmpty(quotationList)) {
            List<String> quotationNos = quotationList.stream().map(RdQuotationPO::getQuotationNo).collect(Collectors.toList());
            List<Long> quotationIds = quotationList.stream().map(RdQuotationPO::getId).collect(Collectors.toList());

            RdQuotationLangExample quotationLangExample = new RdQuotationLangExample();
            quotationLangExample.createCriteria().andRtQuotationIdIn(quotationIds);
            quotationLangMapper.deleteByExample(quotationLangExample);

            attachmentManager.deleteFiles(quotationNos, systemId, AttachmentObjectTypeEnum.QUOTATION.getCode());
        }
        RdQuotationExample quotationExample = new RdQuotationExample();
        quotationExample.createCriteria().andReportNoEqualTo(reportNo).andSystemIdEqualTo(systemId);
        quotationMapper.deleteByExample(quotationExample);
    }
}
