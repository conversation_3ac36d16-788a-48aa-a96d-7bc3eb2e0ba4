package com.sgs.testdatabiz.facade.model.req.entersubcontract;

import com.sgs.framework.core.common.PrintFriendliness;

public class SubTestDataMatrixInfo extends PrintFriendliness {

    private String testMatrixId;

    private Integer testLineId;

    private Integer citationId;

    private Integer citationVersionId;

    private String citationName;

    private String sampleId;

    private String sampleNo;

    private String evaluationAlias;

    private Integer sampleSeq;

    private Long aId;

    private Integer ppVersionId;

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public void setCitationVersionId(Integer citationVersionId) {
        this.citationVersionId = citationVersionId;
    }

    public String getCitationName() {
        return citationName;
    }

    public void setCitationName(String citationName) {
        this.citationName = citationName;
    }

    public String getSampleId() {
        return sampleId;
    }

    public void setSampleId(String sampleId) {
        this.sampleId = sampleId;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public Integer getCitationVersionId() {
        return citationVersionId;
    }

    public Integer getSampleSeq() {
        return sampleSeq;
    }

    public void setSampleSeq(Integer sampleSeq) {
        this.sampleSeq = sampleSeq;
    }

    public Long getaId() {
        return aId;
    }

    public void setaId(Long aId) {
        this.aId = aId;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }
}
