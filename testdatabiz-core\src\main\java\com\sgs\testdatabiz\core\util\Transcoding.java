package com.sgs.testdatabiz.core.util;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.Objects;

import javax.swing.text.Document;
import javax.swing.text.rtf.RTFEditorKit;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Transcoding {

    /**
     * unicode串转字符串
     * @param unicode
     * @return
     */
    public static String unicodeToChar(String unicode) {
        if (unicode == null || unicode.isEmpty()) {
            return unicode;
        }
        StringBuffer str = new StringBuffer();
        String[] hex = unicode.split("\\\\u");
        for (int index = 1; index < hex.length; index++) {
            int data = Integer.parseInt(hex[index], 16);
            str.append((char) data);
        }
        return str.toString();
    }

    /**
     * Rtf 转化为 String文本
     * @param strParm
     * @return
     */
    public static String rtfStrEsc(String strParm) {
        if (strParm == null || strParm.isEmpty()) {
            return strParm;
        }
        String replace = strParm.replace("\\\\", "\\");
        try(ByteArrayInputStream inputStream = new ByteArrayInputStream(replace.getBytes());
            InputStreamReader reader = new InputStreamReader(inputStream);) {
            RTFEditorKit rtfParser = new RTFEditorKit();
            Document document = rtfParser.createDefaultDocument();
            rtfParser.read(reader, document, 0);
            String docText = document.getText(0, document.getLength());
            //把docText的内容变化支持中文，解决乱码问题
            String encodeDocText = new String(docText.getBytes("ISO-8859-1"), "GBK");
            return StringUtils.isBlank(encodeDocText) ? strParm : encodeDocText;
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return strParm;
    }

//    public static void main(String[] args) {
//        String test = "{\\rtf1\\ansi\\ansicpg936\\deff0\\deflang1033\\deflangfe2052{\\fonttbl{\\f0\\fnil\\fprq2\\fcharset134 \\'cb\\'ce\\'cc\\'e5;}{\\f1\\fswiss\\fprq2\\fcharset0 Arial;}}\n\\viewkind4\\uc1\\pard\\nowidctlpar\\f0\\fs20\\'b2\\'ce\\'bf\\'bc\\f1 IEC 62321-4:2013+AMD1:2017\\f0\\'a3\\'ac\\f1 IEC 62321-5:2013\\f0\\'a3\\'ac\\f1 IEC 62321-7-2:2017\\f0\\'a3\\'ac\\f1 IEC 62321-6:2015\\f0\\'ba\\'cd\\f1 IEC 62321-8:2017\\f0\\'a3\\'ac\\'b2\\'c9\\'d3\\'c3\\f1 ICP-OES/AAS\\f0\\'a3\\'ac\\f1 UV-Vis\\f0\\'ba\\'cd\\f1 GC-MS\\f0\\'bd\\'f8\\'d0\\'d0\\'b7\\'d6\\'ce\\'f6\\'a1\\'a3\\kerning2\\par\n}\n";
//        System.out.println("out="+rtfStrEsc(test));
//    }

    /**
     * String 转化为 Rtf
     * @param strParm
     * @return
     * @throws UnsupportedEncodingException
     */

    public static String str2RtfStrEsc(String strParm) {
        String mStringParm = strParm;
        String strTemp = "", strToRtf = "";
        if (mStringParm == null) {
            return null;
        }
        strToRtf += "{\\rtf1\\ansi\\ansicpg936\\deff0\\nouicompat\\deflang1033\\deflangfe2052{\\fonttbl{\\f0\\fnil\\fcharset134 \\''cb\\''ce\\''cc\\''e5;}}{\\*\\generator Riched20 10.0.10586}\\viewkind4\\uc1 \\pard\\f0\\fs22\\lang2052 ";
        for (int i = 0;i< mStringParm.length();i++){
            char x= mStringParm.charAt(i);
            strTemp = (new Character(x)).toString();
            int tmpAsc = (int)x;
            if (tmpAsc > 126){ //转换非ASCII范围的文本为RTF格式
                strTemp = charTo16(x);
                // DIG-8555 A "NullPointerException" could be thrown; "strTemp" is nullable here.
                if (Objects.isNull(strTemp)) {
                    strTemp = "\\loch\\f1\\hich\\f1\\u" + (new Integer(tmpAsc)).toString() + " ?";
                }
                else if (strTemp.length() == 1){ //转换hex值小于2位的特殊控制符号
                    strTemp = "\\'0" + strTemp;
                }
                else if (strTemp.length() == 2){ //转换hex值等于2位的特殊符号
                    strTemp = "\\'" + strTemp;
                }else{
                    strTemp = "\\loch\\f1\\hich\\f1\\u" + (new Integer(tmpAsc)).toString() + " ?"; // '转换hex值等于4位的非英文字符内码
                }
            }
            strToRtf += strTemp;
        }
        strToRtf += "\\par }";
        return strToRtf;
    }


    /**
     * char 转化为16进制
     * @param ch
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String charTo16(char ch){
        byte[] bytes = new byte[0];
        try {
            bytes = new Character(ch).toString().getBytes("gb2312");
            String str = "";
            for (int i = 0; i < bytes.length; i++){
                str += String.format("%02X", bytes[i]);
            }
            return str.toLowerCase();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }
}
