/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdRelationshipSubReportDO {

    private Integer sourceType;
    private String objectNo;
    private String externalId;
    private String externalNo;
    private String subReportNo;
    private String externalObjectNo;


    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}