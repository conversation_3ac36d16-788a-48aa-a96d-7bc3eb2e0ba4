/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestResultDO {

    // add 20230529
    private String orderNo;//logicOrderNo
    private String realOrderNo;
    private String reportNo;
    // add 20230529
    private Integer systemId;
    private String testMatrixId;
    private String subReportNo;
    private RdTestResultResultDO testResult;
    private Integer testResultSeq;
    private RdReportLimitDO reportLimit;
    private RdShareDataReferDO shareDataRefer;
    // add 230922
    private RdMethodLimitDO methodLimit;
    private List<RdTestResultLanguageDO> languageList;
    private String testResultInstanceId;
    private Date lastModifiedTimestamp;
    private String metaData;
    private String casNo;
    private Integer activeIndicator;
}
