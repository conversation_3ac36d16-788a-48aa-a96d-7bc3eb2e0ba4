package com.sgs.testdatabiz.facade.model.info;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/6/2 17:36
 */
public class ReadXmlLogInfo extends PrintFriendliness {
    /**
     *
     */
    private Long id;
    /**
     *
     */
    private Integer productLineId;
    /**
     *
     */
    private Integer labId;
    /**
     *
     */
    private String fileName;
    /**
     *
     */
    private String cloudId;
    /**
     *
     */
    private String newFileName;
    /**
     *
     */
    private String slimJobNo;
    /**
     *
     */
    private String orderNo;
    /**
     *
     */
    private String subcontractNo;
    /**
     *
     */
    private Integer logCode;
    /**
     *
     */
    private String logRecord;

    private Integer systemId;
    /**
     *
     */
    private Date createdDate;
    /**
     *
     */
    private Date modifiedDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public Integer getLabId() {
        return labId;
    }

    public void setLabId(Integer labId) {
        this.labId = labId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getCloudId() {
        return cloudId;
    }

    public void setCloudId(String cloudId) {
        this.cloudId = cloudId;
    }

    public String getNewFileName() {
        return newFileName;
    }

    public void setNewFileName(String newFileName) {
        this.newFileName = newFileName;
    }

    public String getSlimJobNo() {
        return slimJobNo;
    }

    public void setSlimJobNo(String slimJobNo) {
        this.slimJobNo = slimJobNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSubcontractNo() {
        return subcontractNo;
    }

    public void setSubcontractNo(String subcontractNo) {
        this.subcontractNo = subcontractNo;
    }

    public Integer getLogCode() {
        return logCode;
    }

    public void setLogCode(Integer logCode) {
        this.logCode = logCode;
    }

    public String getLogRecord() {
        return logRecord;
    }

    public void setLogRecord(String logRecord) {
        this.logRecord = logRecord;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }
}
