package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.math.BigDecimal;
import java.util.Date;

public class RdReportInvoicePO {
    /**
     * id BIGINT(19) 必填<br>
     * 
     */
    private Long id;

    /**
     * lab_id BIGINT(19)<br>
     * Trims系统实验室标识
     */
    private Long labId;

    /**
     * rd_report_id BIGINT(19)<br>
     * 
     */
    private Long rdReportId;

    /**
     * system_id BIGINT(19)<br>
     * 
     */
    private Long systemId;

    /**
     * order_no VARCHAR(50)<br>
     * 订单号
     */
    private String orderNo;

    /**
     * report_no VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * boss_order_no VARCHAR(100)<br>
     * Boss订单号
     */
    private String bossOrderNo;

    /**
     * product_code VARCHAR(500)<br>
     * 产品编码
     */
    private String productCode;

    /**
     * cost_center VARCHAR(500)<br>
     * 费用中心
     */
    private String costCenter;

    /**
     * project_template VARCHAR(500)<br>
     * 项目模板
     */
    private String projectTemplate;

    /**
     * invoice_date TIMESTAMP(19)<br>
     * 发票开具时间
     */
    private Date invoiceDate;

    /**
     * invoice_no VARCHAR(50)<br>
     * Boss 发票号
     */
    private String invoiceNo;

    /**
     * currency VARCHAR(100)<br>
     * 币种
     */
    private String currency;

    /**
     * net_amount DECIMAL(20,6)<br>
     * 
     */
    private BigDecimal netAmount;

    /**
     * vat_amount DECIMAL(20,6)<br>
     * 
     */
    private BigDecimal vatAmount;

    /**
     * total_amount DECIMAL(20,6)<br>
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * pre_paid_amount DECIMAL(20,6)<br>
     * 预付金额
     */
    private BigDecimal prePaidAmount;

    /**
     * invoice_status INTEGER(10)<br>
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_date TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedDate;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 获得 Trims系统实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 设置 Trims系统实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * rd_report_id BIGINT(19)<br>
     * 获得 
     */
    public Long getRdReportId() {
        return rdReportId;
    }

    /**
     * rd_report_id BIGINT(19)<br>
     * 设置 
     */
    public void setRdReportId(Long rdReportId) {
        this.rdReportId = rdReportId;
    }

    /**
     * system_id BIGINT(19)<br>
     * 获得 
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * system_id BIGINT(19)<br>
     * 设置 
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * report_no VARCHAR(50)<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50)<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * boss_order_no VARCHAR(100)<br>
     * 获得 Boss订单号
     */
    public String getBossOrderNo() {
        return bossOrderNo;
    }

    /**
     * boss_order_no VARCHAR(100)<br>
     * 设置 Boss订单号
     */
    public void setBossOrderNo(String bossOrderNo) {
        this.bossOrderNo = bossOrderNo == null ? null : bossOrderNo.trim();
    }

    /**
     * product_code VARCHAR(500)<br>
     * 获得 产品编码
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     * product_code VARCHAR(500)<br>
     * 设置 产品编码
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    /**
     * cost_center VARCHAR(500)<br>
     * 获得 费用中心
     */
    public String getCostCenter() {
        return costCenter;
    }

    /**
     * cost_center VARCHAR(500)<br>
     * 设置 费用中心
     */
    public void setCostCenter(String costCenter) {
        this.costCenter = costCenter == null ? null : costCenter.trim();
    }

    /**
     * project_template VARCHAR(500)<br>
     * 获得 项目模板
     */
    public String getProjectTemplate() {
        return projectTemplate;
    }

    /**
     * project_template VARCHAR(500)<br>
     * 设置 项目模板
     */
    public void setProjectTemplate(String projectTemplate) {
        this.projectTemplate = projectTemplate == null ? null : projectTemplate.trim();
    }

    /**
     * invoice_date TIMESTAMP(19)<br>
     * 获得 发票开具时间
     */
    public Date getInvoiceDate() {
        return invoiceDate;
    }

    /**
     * invoice_date TIMESTAMP(19)<br>
     * 设置 发票开具时间
     */
    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    /**
     * invoice_no VARCHAR(50)<br>
     * 获得 Boss 发票号
     */
    public String getInvoiceNo() {
        return invoiceNo;
    }

    /**
     * invoice_no VARCHAR(50)<br>
     * 设置 Boss 发票号
     */
    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo == null ? null : invoiceNo.trim();
    }

    /**
     * currency VARCHAR(100)<br>
     * 获得 币种
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * currency VARCHAR(100)<br>
     * 设置 币种
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * net_amount DECIMAL(20,6)<br>
     * 获得 
     */
    public BigDecimal getNetAmount() {
        return netAmount;
    }

    /**
     * net_amount DECIMAL(20,6)<br>
     * 设置 
     */
    public void setNetAmount(BigDecimal netAmount) {
        this.netAmount = netAmount;
    }

    /**
     * vat_amount DECIMAL(20,6)<br>
     * 获得 
     */
    public BigDecimal getVatAmount() {
        return vatAmount;
    }

    /**
     * vat_amount DECIMAL(20,6)<br>
     * 设置 
     */
    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = vatAmount;
    }

    /**
     * total_amount DECIMAL(20,6)<br>
     * 获得 总金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * total_amount DECIMAL(20,6)<br>
     * 设置 总金额
     */
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * pre_paid_amount DECIMAL(20,6)<br>
     * 获得 预付金额
     */
    public BigDecimal getPrePaidAmount() {
        return prePaidAmount;
    }

    /**
     * pre_paid_amount DECIMAL(20,6)<br>
     * 设置 预付金额
     */
    public void setPrePaidAmount(BigDecimal prePaidAmount) {
        this.prePaidAmount = prePaidAmount;
    }

    /**
     * invoice_status INTEGER(10)<br>
     * 获得 发票状态
     */
    public Integer getInvoiceStatus() {
        return invoiceStatus;
    }

    /**
     * invoice_status INTEGER(10)<br>
     * 设置 发票状态
     */
    public void setInvoiceStatus(Integer invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_date TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    /**
     * last_modified_date TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", labId=").append(labId);
        sb.append(", rdReportId=").append(rdReportId);
        sb.append(", systemId=").append(systemId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", bossOrderNo=").append(bossOrderNo);
        sb.append(", productCode=").append(productCode);
        sb.append(", costCenter=").append(costCenter);
        sb.append(", projectTemplate=").append(projectTemplate);
        sb.append(", invoiceDate=").append(invoiceDate);
        sb.append(", invoiceNo=").append(invoiceNo);
        sb.append(", currency=").append(currency);
        sb.append(", netAmount=").append(netAmount);
        sb.append(", vatAmount=").append(vatAmount);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", prePaidAmount=").append(prePaidAmount);
        sb.append(", invoiceStatus=").append(invoiceStatus);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedDate=").append(lastModifiedDate);
        sb.append("]");
        return sb.toString();
    }
}