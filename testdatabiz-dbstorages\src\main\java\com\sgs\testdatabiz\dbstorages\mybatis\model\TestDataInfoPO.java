package com.sgs.testdatabiz.dbstorages.mybatis.model;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class TestDataInfoPO {
    /**
     * Id BIGINT(19) 必填<br>
     * 
     */
    private Long id;

    /**
     * ObjectRelId VARCHAR(36)<br>
     * 
     */
    private String objectRelId;

    /**
     * TestDataMatrixId BIGINT(19) 必填<br>
     * 
     */
    private Long testDataMatrixId;

    /**
     * TestMatrixId VARCHAR(36)<br>
     * 
     */
    private String testMatrixId;

    /**
     * AnalyteId VARCHAR(36)<br>
     * 
     */
    private String analyteId;

    /**
     * AnalyteName VARCHAR(500)<br>
     * analyte别名
     */
    private String analyteName;

    /**
     * AnalyteType INTEGER(10)<br>
     * 0：General、1：Conclusion
     */
    private Integer analyteType;

    /**
     * AnalyteCode VARCHAR(50)<br>
     * 
     */
    private String analyteCode;

    /**
     * AnalyteSeq INTEGER(10)<br>
     * 
     */
    private Integer analyteSeq;

    /**
     * ReportUnit VARCHAR(50)<br>
     * 
     */
    private String reportUnit;

    /**
     * TestValue VARCHAR(50)<br>
     * 
     */
    private String testValue;

    /**
     * CasNo VARCHAR(100)<br>
     * 
     */
    private String casNo;

    /**
     * ReportLimit VARCHAR(50)<br>
     * 
     */
    private String reportLimit;

    /**
     * LimitUnit VARCHAR(100)<br>
     * LimitUnit
     */
    private String limitUnit;

    /**
     * ConclusionId VARCHAR(50)<br>
     * 
     */
    private String conclusionId;

    /**
     * ActiveIndicator INTEGER(10)<br>
     * 0无效，1有效
     */
    private Integer activeIndicator;

    /**
     * CreatedBy VARCHAR(50)<br>
     * 
     */
    private String createdBy;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 
     */
    private Date createdDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 
     */
    private String modifiedBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 
     */
    private Date modifiedDate;

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 表中字段MD5 hash 计算得出
     */
    private String bizVersionId;

    /**
     * rd_report_id BIGINT(19)<br>
     * 
     */
    private Long rdReportId;

    /**
     * lab_id BIGINT(19)<br>
     * Trims系统实验室标识
     */
    private Long labId;

    /**
     * order_no VARCHAR(50)<br>
     * 订单号
     */
    private String orderNo;

    /**
     * report_no VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * test_result_full_name VARCHAR(500)<br>
     * 测试项名称，拼接字段
     */
    private String testResultFullName;

    /**
     * test_result_seq INTEGER(10)<br>
     * 测试项顺序
     */
    private Integer testResultSeq;

    /**
     * result_value VARCHAR(150)<br>
     * 测试结果
     */
    private String resultValue;

    /**
     * result_value_remark VARCHAR(500)<br>
     * 测试结果备注
     */
    private String resultValueRemark;

    /**
     * result_unit VARCHAR(1024)<br>
     * 测试结果单位
     */
    private String resultUnit;

    /**
     * fail_flag TINYINT(3)<br>
     * 失败标识
     */
    private Integer failFlag;

    /**
     * limit_value_full_name VARCHAR(500)<br>
     * 参考标准，拼接字段
     */
    private String limitValueFullName;

    /**
     * Position LONGVARCHAR(16777215)<br>
     * position Json
     */
    private String position;

    /**
     * Languages LONGVARCHAR(16777215)<br>
     * 多语言json
     */
    private String languages;
    /**
     * SCI-1378
     * 测试方法范围
     */
    private String methodLimit;

    public String getMethodLimit() {
        return methodLimit;
    }

    public void setMethodLimit(String methodLimit) {
        this.methodLimit = methodLimit;
    }

    /**
     * Method Detection Limit VARCHAR(50)<br>
     */
    private String mdl;

    public String getMdl() {
        return mdl;
    }

    public void setMdl(String mdl) {
        this.mdl = mdl;
    }

    /**
     * Id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getId() {
        return id;
    }

    /**
     * Id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * ObjectRelId VARCHAR(36)<br>
     * 获得 
     */
    public String getObjectRelId() {
        return objectRelId;
    }

    /**
     * ObjectRelId VARCHAR(36)<br>
     * 设置 
     */
    public void setObjectRelId(String objectRelId) {
        this.objectRelId = objectRelId == null ? null : objectRelId.trim();
    }

    /**
     * TestDataMatrixId BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getTestDataMatrixId() {
        return testDataMatrixId;
    }

    /**
     * TestDataMatrixId BIGINT(19) 必填<br>
     * 设置 
     */
    public void setTestDataMatrixId(Long testDataMatrixId) {
        this.testDataMatrixId = testDataMatrixId;
    }

    /**
     * TestMatrixId VARCHAR(36)<br>
     * 获得 
     */
    public String getTestMatrixId() {
        return testMatrixId;
    }

    /**
     * TestMatrixId VARCHAR(36)<br>
     * 设置 
     */
    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId == null ? null : testMatrixId.trim();
    }

    /**
     * AnalyteId VARCHAR(36)<br>
     * 获得 
     */
    public String getAnalyteId() {
        return analyteId;
    }

    /**
     * AnalyteId VARCHAR(36)<br>
     * 设置 
     */
    public void setAnalyteId(String analyteId) {
        this.analyteId = analyteId == null ? null : analyteId.trim();
    }

    /**
     * AnalyteName VARCHAR(500)<br>
     * 获得 analyte别名
     */
    public String getAnalyteName() {
        return analyteName;
    }

    /**
     * AnalyteName VARCHAR(500)<br>
     * 设置 analyte别名
     */
    public void setAnalyteName(String analyteName) {
        this.analyteName = analyteName == null ? null : analyteName.trim();
    }

    /**
     * AnalyteType INTEGER(10)<br>
     * 获得 0：General、1：Conclusion
     */
    public Integer getAnalyteType() {
        return analyteType;
    }

    /**
     * AnalyteType INTEGER(10)<br>
     * 设置 0：General、1：Conclusion
     */
    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    /**
     * AnalyteCode VARCHAR(50)<br>
     * 获得 
     */
    public String getAnalyteCode() {
        return analyteCode;
    }

    /**
     * AnalyteCode VARCHAR(50)<br>
     * 设置 
     */
    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode == null ? null : analyteCode.trim();
    }

    /**
     * AnalyteSeq INTEGER(10)<br>
     * 获得 
     */
    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    /**
     * AnalyteSeq INTEGER(10)<br>
     * 设置 
     */
    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    /**
     * ReportUnit VARCHAR(50)<br>
     * 获得 
     */
    public String getReportUnit() {
        return reportUnit;
    }

    /**
     * ReportUnit VARCHAR(50)<br>
     * 设置 
     */
    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit == null ? null : reportUnit.trim();
    }

    /**
     * TestValue VARCHAR(50)<br>
     * 获得 
     */
    public String getTestValue() {
        return testValue;
    }

    /**
     * TestValue VARCHAR(50)<br>
     * 设置 
     */
    public void setTestValue(String testValue) {
        this.testValue = testValue == null ? null : testValue.trim();
    }

    /**
     * CasNo VARCHAR(100)<br>
     * 获得 
     */
    public String getCasNo() {
        return casNo;
    }

    /**
     * CasNo VARCHAR(100)<br>
     * 设置 
     */
    public void setCasNo(String casNo) {
        this.casNo = casNo == null ? null : casNo.trim();
    }

    /**
     * ReportLimit VARCHAR(50)<br>
     * 获得 
     */
    public String getReportLimit() {
        return reportLimit;
    }

    /**
     * ReportLimit VARCHAR(50)<br>
     * 设置 
     */
    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit == null ? null : reportLimit.trim();
    }

    /**
     * LimitUnit VARCHAR(100)<br>
     * 获得 LimitUnit
     */
    public String getLimitUnit() {
        return limitUnit;
    }

    /**
     * LimitUnit VARCHAR(100)<br>
     * 设置 LimitUnit
     */
    public void setLimitUnit(String limitUnit) {
        this.limitUnit = limitUnit == null ? null : limitUnit.trim();
    }

    /**
     * ConclusionId VARCHAR(50)<br>
     * 获得 
     */
    public String getConclusionId() {
        return conclusionId;
    }

    /**
     * ConclusionId VARCHAR(50)<br>
     * 设置 
     */
    public void setConclusionId(String conclusionId) {
        this.conclusionId = conclusionId == null ? null : conclusionId.trim();
    }

    /**
     * ActiveIndicator INTEGER(10)<br>
     * 获得 0无效，1有效
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * ActiveIndicator INTEGER(10)<br>
     * 设置 0无效，1有效
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 获得 表中字段MD5 hash 计算得出
     */
    public String getBizVersionId() {
        return bizVersionId;
    }

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 设置 表中字段MD5 hash 计算得出
     */
    public void setBizVersionId(String bizVersionId) {
        this.bizVersionId = bizVersionId == null ? null : bizVersionId.trim();
    }

    /**
     * rd_report_id BIGINT(19)<br>
     * 获得 
     */
    public Long getRdReportId() {
        return rdReportId;
    }

    /**
     * rd_report_id BIGINT(19)<br>
     * 设置 
     */
    public void setRdReportId(Long rdReportId) {
        this.rdReportId = rdReportId;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 获得 Trims系统实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19)<br>
     * 设置 Trims系统实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * report_no VARCHAR(50)<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50)<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * test_result_full_name VARCHAR(500)<br>
     * 获得 测试项名称，拼接字段
     */
    public String getTestResultFullName() {
        return testResultFullName;
    }

    /**
     * test_result_full_name VARCHAR(500)<br>
     * 设置 测试项名称，拼接字段
     */
    public void setTestResultFullName(String testResultFullName) {
        this.testResultFullName = testResultFullName == null ? null : testResultFullName.trim();
    }

    /**
     * test_result_seq INTEGER(10)<br>
     * 获得 测试项顺序
     */
    public Integer getTestResultSeq() {
        return testResultSeq;
    }

    /**
     * test_result_seq INTEGER(10)<br>
     * 设置 测试项顺序
     */
    public void setTestResultSeq(Integer testResultSeq) {
        this.testResultSeq = testResultSeq;
    }

    /**
     * result_value VARCHAR(150)<br>
     * 获得 测试结果
     */
    public String getResultValue() {
        return resultValue;
    }

    /**
     * result_value VARCHAR(150)<br>
     * 设置 测试结果
     */
    public void setResultValue(String resultValue) {
        this.resultValue = resultValue == null ? null : resultValue.trim();
    }

    /**
     * result_value_remark VARCHAR(500)<br>
     * 获得 测试结果备注
     */
    public String getResultValueRemark() {
        return resultValueRemark;
    }

    /**
     * result_value_remark VARCHAR(500)<br>
     * 设置 测试结果备注
     */
    public void setResultValueRemark(String resultValueRemark) {
        this.resultValueRemark = resultValueRemark == null ? null : resultValueRemark.trim();
    }

    /**
     * result_unit VARCHAR(1024)<br>
     * 获得 测试结果单位
     */
    public String getResultUnit() {
        return resultUnit;
    }

    /**
     * result_unit VARCHAR(1024)<br>
     * 设置 测试结果单位
     */
    public void setResultUnit(String resultUnit) {
        this.resultUnit = resultUnit == null ? null : resultUnit.trim();
    }

    /**
     * fail_flag TINYINT(3)<br>
     * 获得 失败标识
     */
    public Integer getFailFlag() {
        return failFlag;
    }

    /**
     * fail_flag TINYINT(3)<br>
     * 设置 失败标识
     */
    public void setFailFlag(Integer failFlag) {
        this.failFlag = failFlag;
    }

    /**
     * limit_value_full_name VARCHAR(500)<br>
     * 获得 参考标准，拼接字段
     */
    public String getLimitValueFullName() {
        return limitValueFullName;
    }

    /**
     * limit_value_full_name VARCHAR(500)<br>
     * 设置 参考标准，拼接字段
     */
    public void setLimitValueFullName(String limitValueFullName) {
        this.limitValueFullName = limitValueFullName == null ? null : limitValueFullName.trim();
    }

    /**
     * Position LONGVARCHAR(16777215)<br>
     * 获得 position Json
     */
    public String getPosition() {
        return position;
    }

    /**
     * Position LONGVARCHAR(16777215)<br>
     * 设置 position Json
     */
    public void setPosition(String position) {
        this.position = position == null ? null : position.trim();
    }

    /**
     * Languages LONGVARCHAR(16777215)<br>
     * 获得 多语言json
     */
    public String getLanguages() {
        return languages;
    }

    /**
     * Languages LONGVARCHAR(16777215)<br>
     * 设置 多语言json
     */
    public void setLanguages(String languages) {
        this.languages = languages == null ? null : languages.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", objectRelId=").append(objectRelId);
        sb.append(", testDataMatrixId=").append(testDataMatrixId);
        sb.append(", testMatrixId=").append(testMatrixId);
        sb.append(", analyteId=").append(analyteId);
        sb.append(", analyteName=").append(analyteName);
        sb.append(", analyteType=").append(analyteType);
        sb.append(", analyteCode=").append(analyteCode);
        sb.append(", analyteSeq=").append(analyteSeq);
        sb.append(", reportUnit=").append(reportUnit);
        sb.append(", testValue=").append(testValue);
        sb.append(", casNo=").append(casNo);
        sb.append(", reportLimit=").append(reportLimit);
        sb.append(", limitUnit=").append(limitUnit);
        sb.append(", conclusionId=").append(conclusionId);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", bizVersionId=").append(bizVersionId);
        sb.append(", position=").append(position);
        sb.append(", languages=").append(languages);
        sb.append("]");
        return sb.toString();
    }
}