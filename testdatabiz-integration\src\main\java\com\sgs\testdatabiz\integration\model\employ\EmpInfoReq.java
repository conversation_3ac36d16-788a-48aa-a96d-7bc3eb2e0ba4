package com.sgs.testdatabiz.integration.model.employ;

import lombok.ToString;

@ToString
public final class EmpInfoReq {
    /**
     *
     */
    private String labCode;
    /**
     *
     */
    private String regionAccount;
    /**
     *
     */
    private int page;
    /**
     *
     */
    private int rows;

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getRegionAccount() {
        return regionAccount;
    }

    public void setRegionAccount(String regionAccount) {
        this.regionAccount = regionAccount;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
