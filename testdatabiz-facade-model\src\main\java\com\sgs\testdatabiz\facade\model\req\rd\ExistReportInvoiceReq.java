package com.sgs.testdatabiz.facade.model.req.rd;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ExistReportInvoiceReq extends BaseModel {

    private List<String> reportNos;

    @Override
    public String getExtId() {
        return CollectionUtils.isNotEmpty(reportNos) ? reportNos.stream().collect(Collectors.joining(",")) : null;
    }
}
