package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.testline;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.domain.service.rd.filter.AbstractReportDataFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.FilterContext;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdTestLineDO;
import java.util.List;
import java.util.stream.Collectors;

public abstract class AbstractTestLineFilter extends AbstractReportDataFilter {

    @Override
    protected ReportDataDO doFilterInternal(ReportDataDO reportData, FilterContext context) {
        if (reportData == null || Func.isEmpty(reportData.getTestLineList())) {
            return reportData;
        }

        List<RdTestLineDO> originalTestLines = reportData.getTestLineList();
        List<RdTestLineDO> filteredTestLines = originalTestLines.stream()
                .filter(this::filterTestLine)
                .collect(Collectors.toList());

        // 记录过滤操作
        if (originalTestLines.size() != filteredTestLines.size()) {
            context.addFilterRecord(
                getFilterName(),
                "testLineList",
                originalTestLines.size(),
                filteredTestLines.size(),
                getFilterMessage()
            );
        }

        reportData.setTestLineList(filteredTestLines);
        return reportData;
    }

    /**
     * 获取过滤器名称
     */
    protected abstract String getFilterName();

    /**
     * 获取过滤消息
     */
    protected abstract String getFilterMessage();

    /**
     * 过滤TestLine的具体逻辑
     * @param testLine 待过滤的测试线
     * @return true-保留 false-过滤掉
     */
    protected abstract boolean filterTestLine(RdTestLineDO testLine);
} 