/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceRequirementDTO implements Serializable {
    @ApiModelProperty(value = "report",dataType = "object", required = true)
    private RdServiceRequirementReportDTO report;
    private RdServiceRequirementSampleDTO sample;
    private RdServiceRequirementInvoiceDTO invoice;
    private String otherRequestRemark;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
