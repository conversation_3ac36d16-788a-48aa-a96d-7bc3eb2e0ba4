<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportTestResultLangMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="rd_report_test_result_id" property="rdReportTestResultId" jdbcType="BIGINT" />
    <result column="language_id" property="languageId" jdbcType="INTEGER" />
    <result column="test_result_full_name" property="testResultFullName" jdbcType="VARCHAR" />
    <result column="result_value_remark" property="resultValueRemark" jdbcType="VARCHAR" />
    <result column="result_unit" property="resultUnit" jdbcType="VARCHAR" />
    <result column="limit_value_full_name" property="limitValueFullName" jdbcType="VARCHAR" />
    <result column="limit_unit" property="limitUnit" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, rd_report_test_result_id, language_id, test_result_full_name, result_value_remark, 
    result_unit, limit_value_full_name, limit_unit, created_by, created_date, modified_by, 
    modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_test_result_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_test_result_lang
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_test_result_lang
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangExample" >
    delete from tb_report_test_result_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangPO" >
    insert into tb_report_test_result_lang (id, rd_report_test_result_id, language_id, 
      test_result_full_name, result_value_remark, 
      result_unit, limit_value_full_name, limit_unit, 
      created_by, created_date, modified_by, 
      modified_date)
    values (#{id,jdbcType=BIGINT}, #{rdReportTestResultId,jdbcType=BIGINT}, #{languageId,jdbcType=INTEGER}, 
      #{testResultFullName,jdbcType=VARCHAR}, #{resultValueRemark,jdbcType=VARCHAR}, 
      #{resultUnit,jdbcType=VARCHAR}, #{limitValueFullName,jdbcType=VARCHAR}, #{limitUnit,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, now(), #{modifiedBy,jdbcType=VARCHAR}, 
      now())
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangPO" >
    insert into tb_report_test_result_lang
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="rdReportTestResultId != null" >
        rd_report_test_result_id,
      </if>
      <if test="languageId != null" >
        language_id,
      </if>
      <if test="testResultFullName != null" >
        test_result_full_name,
      </if>
      <if test="resultValueRemark != null" >
        result_value_remark,
      </if>
      <if test="resultUnit != null" >
        result_unit,
      </if>
      <if test="limitValueFullName != null" >
        limit_value_full_name,
      </if>
      <if test="limitUnit != null" >
        limit_unit,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="rdReportTestResultId != null" >
        #{rdReportTestResultId,jdbcType=BIGINT},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="testResultFullName != null" >
        #{testResultFullName,jdbcType=VARCHAR},
      </if>
      <if test="resultValueRemark != null" >
        #{resultValueRemark,jdbcType=VARCHAR},
      </if>
      <if test="resultUnit != null" >
        #{resultUnit,jdbcType=VARCHAR},
      </if>
      <if test="limitValueFullName != null" >
        #{limitValueFullName,jdbcType=VARCHAR},
      </if>
      <if test="limitUnit != null" >
        #{limitUnit,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_test_result_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_test_result_lang
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.rdReportTestResultId != null" >
        rd_report_test_result_id = #{record.rdReportTestResultId,jdbcType=BIGINT},
      </if>
      <if test="record.languageId != null" >
        language_id = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.testResultFullName != null" >
        test_result_full_name = #{record.testResultFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.resultValueRemark != null" >
        result_value_remark = #{record.resultValueRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.resultUnit != null" >
        result_unit = #{record.resultUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.limitValueFullName != null" >
        limit_value_full_name = #{record.limitValueFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.limitUnit != null" >
        limit_unit = #{record.limitUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_test_result_lang
    set id = #{record.id,jdbcType=BIGINT},
      rd_report_test_result_id = #{record.rdReportTestResultId,jdbcType=BIGINT},
      language_id = #{record.languageId,jdbcType=INTEGER},
      test_result_full_name = #{record.testResultFullName,jdbcType=VARCHAR},
      result_value_remark = #{record.resultValueRemark,jdbcType=VARCHAR},
      result_unit = #{record.resultUnit,jdbcType=VARCHAR},
      limit_value_full_name = #{record.limitValueFullName,jdbcType=VARCHAR},
      limit_unit = #{record.limitUnit,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now()
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangPO" >
    update tb_report_test_result_lang
    <set >
      <if test="rdReportTestResultId != null" >
        rd_report_test_result_id = #{rdReportTestResultId,jdbcType=BIGINT},
      </if>
      <if test="languageId != null" >
        language_id = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="testResultFullName != null" >
        test_result_full_name = #{testResultFullName,jdbcType=VARCHAR},
      </if>
      <if test="resultValueRemark != null" >
        result_value_remark = #{resultValueRemark,jdbcType=VARCHAR},
      </if>
      <if test="resultUnit != null" >
        result_unit = #{resultUnit,jdbcType=VARCHAR},
      </if>
      <if test="limitValueFullName != null" >
        limit_value_full_name = #{limitValueFullName,jdbcType=VARCHAR},
      </if>
      <if test="limitUnit != null" >
        limit_unit = #{limitUnit,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTestResultLangPO" >
    update tb_report_test_result_lang
    set rd_report_test_result_id = #{rdReportTestResultId,jdbcType=BIGINT},
      language_id = #{languageId,jdbcType=INTEGER},
      test_result_full_name = #{testResultFullName,jdbcType=VARCHAR},
      result_value_remark = #{resultValueRemark,jdbcType=VARCHAR},
      result_unit = #{resultUnit,jdbcType=VARCHAR},
      limit_value_full_name = #{limitValueFullName,jdbcType=VARCHAR},
      limit_unit = #{limitUnit,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report_test_result_lang
      (`id`,`rd_report_test_result_id`,`language_id`,
      `test_result_full_name`,`result_value_remark`,`result_unit`,
      `limit_value_full_name`,`limit_unit`,`created_by`,
      `created_date`,`modified_by`,`modified_date`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.rdReportTestResultId, jdbcType=BIGINT},#{ item.languageId, jdbcType=INTEGER},
      #{ item.testResultFullName, jdbcType=VARCHAR},#{ item.resultValueRemark, jdbcType=VARCHAR},#{ item.resultUnit, jdbcType=VARCHAR},
      #{ item.limitValueFullName, jdbcType=VARCHAR},#{ item.limitUnit, jdbcType=VARCHAR},#{ item.createdBy, jdbcType=VARCHAR},
      #{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_report_test_result_lang 
      <set>
        <if test="item.rdReportTestResultId != null"> 
          `rd_report_test_result_id` = #{item.rdReportTestResultId, jdbcType = BIGINT},
        </if> 
        <if test="item.languageId != null"> 
          `language_id` = #{item.languageId, jdbcType = INTEGER},
        </if> 
        <if test="item.testResultFullName != null"> 
          `test_result_full_name` = #{item.testResultFullName, jdbcType = VARCHAR},
        </if> 
        <if test="item.resultValueRemark != null"> 
          `result_value_remark` = #{item.resultValueRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.resultUnit != null"> 
          `result_unit` = #{item.resultUnit, jdbcType = VARCHAR},
        </if> 
        <if test="item.limitValueFullName != null"> 
          `limit_value_full_name` = #{item.limitValueFullName, jdbcType = VARCHAR},
        </if> 
        <if test="item.limitUnit != null"> 
          `limit_unit` = #{item.limitUnit, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>