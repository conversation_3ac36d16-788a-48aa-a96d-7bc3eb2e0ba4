package com.sgs.testdatabiz.dbstorages.mybatis.model;

public class RdReport<PERSON>angPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 唯一标识
     */
    private Long id;

    /**
     * rd_report_id BIGINT(19)<br>
     * RD Report 标识
     */
    private Long rdReportId;

    /**
     * language_id TINYINT(3)<br>
     * 语言ID
     */
    private Integer languageId;

    /**
     * report_header VARCHAR(300)<br>
     * 报告抬头
     */
    private String reportHeader;

    /**
     * report_address VARCHAR(500)<br>
     * 报告地址
     */
    private String reportAddress;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 唯一标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 唯一标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * rd_report_id BIGINT(19)<br>
     * 获得 RD Report 标识
     */
    public Long getRdReportId() {
        return rdReportId;
    }

    /**
     * rd_report_id BIGINT(19)<br>
     * 设置 RD Report 标识
     */
    public void setRdReportId(Long rdReportId) {
        this.rdReportId = rdReportId;
    }

    /**
     * language_id TINYINT(3)<br>
     * 获得 语言ID
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * language_id TINYINT(3)<br>
     * 设置 语言ID
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * report_header VARCHAR(300)<br>
     * 获得 报告抬头
     */
    public String getReportHeader() {
        return reportHeader;
    }

    /**
     * report_header VARCHAR(300)<br>
     * 设置 报告抬头
     */
    public void setReportHeader(String reportHeader) {
        this.reportHeader = reportHeader == null ? null : reportHeader.trim();
    }

    /**
     * report_address VARCHAR(500)<br>
     * 获得 报告地址
     */
    public String getReportAddress() {
        return reportAddress;
    }

    /**
     * report_address VARCHAR(500)<br>
     * 设置 报告地址
     */
    public void setReportAddress(String reportAddress) {
        this.reportAddress = reportAddress == null ? null : reportAddress.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", rdReportId=").append(rdReportId);
        sb.append(", languageId=").append(languageId);
        sb.append(", reportHeader=").append(reportHeader);
        sb.append(", reportAddress=").append(reportAddress);
        sb.append("]");
        return sb.toString();
    }
}