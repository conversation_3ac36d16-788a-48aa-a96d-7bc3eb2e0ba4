package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdReportMatrixDTO extends BaseModel{


    private Long labId;

    private String orderNo;

    private String reportNo;

    private String testMatrixId;

    private Integer testMatrixGroupId;

    private String testLineInstanceId;

    private Integer testLineId;

    private Long testLineType;

    private String evaluationAlias;

    private String evaluationName;

    private String testLineStatus;

    private String testLineRemark;

    private Long testLineSeq;

    private Integer citationId;

    private Integer citationType;

    private String citationName;

    private String citationFullName;

    private Long citationVersionId;

    private Integer citationSectionId;

    private String citationSectionName;

    private String methodDesc;

    private String condition;

    private Integer testLineMappingId;

    private String externalId;

    private String externalCode;

    private String sampleInstanceId;

    private String sampleGroup;

    private String sampleParentId;

    private String sampleNo;

    private String sampleName;

    private String externalSampleNo;

    private String externalSampleName;

    private String sampleType;

    private String sampleSeq;

    private String category;

    private String color;

    private String composition;

    private String description;

    private String endUse;

    private String applicable;

    private String material;

    private String otherSampleInfo;

    private String sampleRemark;

    private Object extFields;

    private String conclusionCode;

    private String reviewConclusion;

    private String customerConclusion;

    private String conclusionRemark;

    private Long aid;

    private Long matrixStatus;

    private String bizVersionId;

    private String testLineAttribute;

    private String sampleTypeLabel;

    private String categoryLabel;

    private String conclusionCodeLabel;

    private String matrixStatusLabel;

    private Integer activeIndicator;

    private List<RdReportMatrixLangDTO> reportMatrixLangList;

    private List<RdPpTestLineDTO> rdPpTestLineList;

    private Date lastModifiedTimestamp;

    private String matrixInstanceId;
    //SCI-1378
    private String applicationFactor;
    private String metaData;
    private String labSectionName;
}
