package com.sgs.testdatabiz.domain.service.rd.filter.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import java.util.List;
import java.util.Map;
import com.sgs.testdatabiz.core.enums.FilterType;

@Data
@Configuration
@ConfigurationProperties(prefix = "report.filter")
@RefreshScope
public class FilterNacosConfig {
    
    private boolean enabled; // 总开关
    private Map<String, SystemConfig> systems; // 系统配置,key为systemId
    
    @Data
    public static class SystemConfig {
        private boolean enabled; // 系统级开关
        private List<FilterType> filterTypes; // 允许的过滤器类型,为空表示允许所有
    }
} 