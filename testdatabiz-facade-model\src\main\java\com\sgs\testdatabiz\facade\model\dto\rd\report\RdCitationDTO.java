/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCitationDTO implements Serializable{
    @ApiModelProperty(value = "citationId",dataType = "integer", required = true)
    private Integer citationId;
    @ApiModelProperty(value = "citationType",dataType = "integer", required = true)
    private Integer citationType;
    private Integer citationVersionId;
    private Integer citationSectionId;
    private String citationSectionName;
    @ApiModelProperty(value = "citationName",dataType = "string", required = true)
    private String citationName;
    @ApiModelProperty(value = "citationFullName",dataType = "string", required = true)
    private String citationFullName;
    private List<RdCitationLanguageDTO> languageList;

    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "activeIndicator",dataType = "integer", required = true)
    private Integer activeIndicator;
}