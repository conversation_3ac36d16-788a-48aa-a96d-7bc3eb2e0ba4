package com.sgs.testdatabiz.facade.model.enums;

import com.sgs.otsnotes.facade.model.common.NumberUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * slim 写入表时，externalCode 对应的conditionId
 * <AUTHOR>
 */
public enum ExternalCodeTransConditionIdEnum {
    CTX_GC_ISO14362_A1_Z("CTX_GC_ISO14362_A1_Z", 11122),
    CTX_GC_ISO14362_B1_Z("CTX_GC_ISO14362_B1_Z", 11121),
    ;

    private String externalCode;
    private Integer conditionId;

    ExternalCodeTransConditionIdEnum(String externalCode, Integer conditionId) {
        this.externalCode = externalCode;
        this.conditionId = conditionId;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public Integer getConditionId() {
        return conditionId;
    }

    public static Integer getConditionIdByExternalCode(String externalCode) {
        if (StringUtils.isBlank(externalCode)) {
            return null;
        }
        for (ExternalCodeTransConditionIdEnum value : ExternalCodeTransConditionIdEnum.values()) {
            if (value.externalCode.equalsIgnoreCase(externalCode)) {
                return value.conditionId;
            }
        }
        return null;
    }

    public static boolean check(String externalCode, ExternalCodeTransConditionIdEnum... enums) {
        if (StringUtils.isBlank(externalCode) || enums == null || enums.length == 0) {
            return false;
        }
        for (ExternalCodeTransConditionIdEnum value : enums) {
            if (value.externalCode.equalsIgnoreCase(externalCode)) {
                return true;
            }
        }
        return false;
    }

    public static boolean check(Integer conditionId, ExternalCodeTransConditionIdEnum... enums) {
        if (NumberUtils.toInt(conditionId) == 0 || enums == null || enums.length == 0) {
            return false;
        }
        for (ExternalCodeTransConditionIdEnum value : enums) {
            if (value.conditionId.compareTo(conditionId) == 0) {
                return true;
            }
        }
        return false;
    }
}
