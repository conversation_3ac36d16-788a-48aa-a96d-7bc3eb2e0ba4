package com.sgs.testdatabiz.web.controllers;

import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.IStarlimsReportFacade;
import com.sgs.testdatabiz.facade.model.req.starlims.FolderReportInfoReq;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveStarLimsReportReq;
import com.sgs.testdatabiz.facade.v2.ReportTestDataFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/starlimsReport")
@Api(value = "/starlimsReport", tags = "starlimsReport")
public class StarlimsReportController {
    @Autowired
    private IStarlimsReportFacade iStarlimsFacade;
    @Autowired
    private ReportTestDataFacade reportTestDataFacade;

    /**
     *
     * @param rawDataJson
     * @return
     */
    @PostMapping("/build4StarLims")
    @ApiOperation(value = "build4StarLims")
    public BaseResponse build4StarLims(@RequestBody JSONObject rawDataJson) {
        return reportTestDataFacade.build4StarLims(rawDataJson.toJSONString());
    }

    /**
     * 在starlims分包报告返回时，可以获取report data并存储至test_data表
     * @param reqObject
     * @return
     */
    @PostMapping("/saveStarlimsReportData")
    @ApiOperation(value = "saveStarlimsReportData")
    public BaseResponse saveStarlimsReportData(@RequestBody ReceiveStarLimsReportReq reqObject) {
        return iStarlimsFacade.saveStarlimsReportData(reqObject);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @PostMapping("/cleanStarlimsData")
    @ApiOperation(value = "cleanStarlimsData")
    public BaseResponse cleanStarlimsData(@RequestBody FolderReportInfoReq reqObject) {
        return iStarlimsFacade.cleanStarlimsData(reqObject);
    }

    /**
     *
     * @return
     */
    @PostMapping("/washStarlimsData")
    @ApiOperation(value = "washStarlimsData")
    public BaseResponse washStarlimsData(@RequestBody JSONObject reqObject) {
        return iStarlimsFacade.washStarlimsData(reqObject);
    }
}
