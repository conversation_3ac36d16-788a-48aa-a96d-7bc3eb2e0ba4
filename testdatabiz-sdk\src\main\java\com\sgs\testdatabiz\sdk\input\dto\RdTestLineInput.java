/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestLineInput implements Serializable {

    // add 20230529
    private String orderNo;
    // add 20230529
    private Integer systemId;
    private String testLineInstanceId;
    private String testItemNo;
    private Integer testLineType;
    private Long testLineBaseId;
    private Integer testLineId;
    private Integer testLineVersionId;
    private String evaluationAlias;
    private String evaluationName;
    private Integer testLineStatus;
    private Integer testLineSeq;
    private Long labSectionBaseId;
    private String labTeam;
    private String productLineAbbr;
    private String testLineRemark;
    private RdTestLineExternalInput external;
    private RdCitationInput citation;
    private RdWiInput wi;
    private List<RdAnalyteInput> analyteList;
    private List<RdPpTestLineRelInput> ppTestLineRelList;
    private RdConclusionInput conclusion;
    private List<RdTestLineLanguageInput> languageList;
    //SCI-1378 增加labSectionName
    private String labSectionName;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
