/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdSubReportDO {
    private Integer sourceType;
    private String objectNo;
    private String subReportId;
    private String subReportNo;
    private String externalObjectNo;
    private String testMatrixMergeMode;
    private List<RdAttachmentDO> subReportFileList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
