package com.sgs.testdatabiz.facade.model.req.slim;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.Date;

public class TestDataItemReq extends PrintFriendliness {
    /**
     *
     */
    private Integer testLineMappingId;

    /**
     *
     */
    private String externalCode;

    /**
     *
     */
    private String sampleNo;
    /**
     *
     */
    private String externalSampleNo;
    /**
     *
     */
    private Integer ppVersionId;
    /**
     *
     */
    private String testMatrixId;

    /**
     *
     */
    private String testSampleId;

    /**
     *
     */
    private Integer testLineId;
    /**
     *
     */
    private Integer citationId;
    /**
     *
     */
    private Integer citationVersionId;
    /**
     *
     */
    private String reportLimit;
    /**
     *
     */
    private String analyteCode;
    /**
     *
     */
    private Integer analyteType;
    /**
     *
     */
    private String testAnalyteName;
    /**
     *
     */
    private String testAnalyteNameCN;
    /**
     *
     */
    private String reportUnit;
    /**
     *
     */
    private String reportUnitCN;
    /**
     *
     */
    private String casNo;
    /**
     *
     */
    private String limitUnit;
    /**
     *
     */
    private String testValue;
    /**
     *
     */
    private Integer analyteSeq;
    /**
     *
     */
    private String materialName;

    /**
     *
     */
    private String materialTexture;
    /**
     *
     */
    private String usedPosition;
    /**
     *
     */
    private String materialColor;
    /**
     *
     */
    private Date createdDate;
    /**
     *
     */
    private Date modifiedDate;

    private Long testLineSeq;

    public Integer getTestLineMappingId() {
        return testLineMappingId;
    }

    public void setTestLineMappingId(Integer testLineMappingId) {
        this.testLineMappingId = testLineMappingId;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public String getTestSampleId() {
        return testSampleId;
    }

    public void setTestSampleId(String testSampleId) {
        this.testSampleId = testSampleId;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public Integer getCitationVersionId() {
        return citationVersionId;
    }

    public void setCitationVersionId(Integer citationVersionId) {
        this.citationVersionId = citationVersionId;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getAnalyteCode() {
        return analyteCode;
    }

    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode;
    }

    public Integer getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getTestAnalyteNameCN() {
        return testAnalyteNameCN;
    }

    public void setTestAnalyteNameCN(String testAnalyteNameCN) {
        this.testAnalyteNameCN = testAnalyteNameCN;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportUnitCN() {
        return reportUnitCN;
    }

    public void setReportUnitCN(String reportUnitCN) {
        this.reportUnitCN = reportUnitCN;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getLimitUnit() {
        return limitUnit;
    }

    public void setLimitUnit(String limitUnit) {
        this.limitUnit = limitUnit;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public Long getTestLineSeq() {
        return testLineSeq;
    }

    public void setTestLineSeq(Long testLineSeq) {
        this.testLineSeq = testLineSeq;
    }
}
