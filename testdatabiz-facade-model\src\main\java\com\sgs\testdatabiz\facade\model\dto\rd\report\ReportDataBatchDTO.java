/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportDataBatchDTO extends BaseModel implements Serializable {
    private boolean skipCheck = false;
    private List<RdTrfDTO> trfList;
    private List<RdOrderDTO> orderList;
    private List<RdReportDTO> reportList;
    private RdRelationshipDTO relationship;
    private List<RdTestSampleDTO> testSampleList;
    private List<RdTestLineDTO> testLineList;
    private List<RdTestResultDTO> testResultList;
    private List<RdReportConclusionDTO> reportConclusionList;
    private List<RdConditionGroupDTO> conditionGroupList;
    private List<RdQuotationDTO> quotationList;
    private List<RdInvoiceDTO> invoiceList;

    public boolean isNeedCheck() {
        return ! skipCheck;
    }

    public void skipCheck() {
        this.skipCheck = true;
    }
}
