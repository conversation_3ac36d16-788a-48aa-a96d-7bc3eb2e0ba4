/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestResultNameDTO implements Serializable {
    private String upSpecimenInstanceId;
    private String specimenInstanceId;
    private String procedureConditionInstanceId;
    private String parentConditionInstanceId;
    private String conditionInstanceId;
    private String positionInstanceId;
    private String analyteInstanceId;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}