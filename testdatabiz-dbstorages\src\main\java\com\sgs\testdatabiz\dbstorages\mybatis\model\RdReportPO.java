package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdReportPO {
    /**
     * id BIGINT(19) 必填<br>
     * Report Data 唯一标识
     */
    private Long id;

    /**
     * lab_id BIGINT(19) 必填<br>
     * Trims系统实验室标识
     */
    private Long labId;

    /**
     * bu_id BIGINT(19)<br>
     * Trims系统产品线ID
     */
    private Long buId;

    /**
     * system_id BIGINT(19)<br>
     * 
     */
    private Long systemId;

    /**
     * report_id VARCHAR(50)<br>
     * 执行系统报告标识
     */
    private String reportId;

    /**
     * report_no VARCHAR(50) 必填<br>
     * 执行系统报告号
     */
    private String reportNo;

    /**
     * original_report_no VARCHAR(50)<br>
     * 执行系统父级报告号
     */
    private String originalReportNo;

    /**
     * report_header VARCHAR(500)<br>
     * 报告抬头
     */
    private String reportHeader;

    /**
     * report_address VARCHAR(1000)<br>
     * 报告地址
     */
    private String reportAddress;

    /**
     * report_status INTEGER(10)<br>
     * 报告状态
     */
    private Integer reportStatus;

    /**
     * report_due_date TIMESTAMP(19)<br>
     * 报告到期时间
     */
    private Date reportDueDate;

    /**
     * report_certificate_name VARCHAR(300)<br>
     * 报告资质名称
     */
    private String reportCertificateName;

    /**
     * report_created_by VARCHAR(50)<br>
     * 报告创建人
     */
    private String reportCreatedBy;

    /**
     * report_created_date TIMESTAMP(19)<br>
     * 报告创建时间
     */
    private Date reportCreatedDate;

    /**
     * report_approver_by VARCHAR(500)<br>
     * 报告审批人
     */
    private String reportApproverBy;

    /**
     * report_approver_date TIMESTAMP(19)<br>
     * 报告审批时间
     */
    private Date reportApproverDate;

    /**
     * softcopy_delivery_date TIMESTAMP(19)<br>
     * 报告发送时间
     */
    private Date softcopyDeliveryDate;

    /**
     * conclusion_code VARCHAR(500)<br>
     * 报告整体结论
     */
    private String conclusionCode;

    /**
     * customer_conclusion VARCHAR(4000)<br>
     * 报告客户结论
     */
    private String customerConclusion;

    /**
     * review_conclusion VARCHAR(500)<br>
     * 报告客户审核结论
     */
    private String reviewConclusion;

    /**
     * conclusion_remark VARCHAR(1000)<br>
     * 结论备注信息
     */
    private String conclusionRemark;

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * DB 自动更新，不允许程序设置
     */
    private Date lastModifiedTimestamp;

    /**
     * exclude_customer_interface VARCHAR(255)<br>
     * 
     */
    private String excludeCustomerInterface;

    /**
     * report_source_type INTEGER(10) 默认值[1]<br>
     * 1:执行系统报告，2 customer报告
     */
    private Integer reportSourceType;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 Report Data 唯一标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 Report Data 唯一标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * lab_id BIGINT(19) 必填<br>
     * 获得 Trims系统实验室标识
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * lab_id BIGINT(19) 必填<br>
     * 设置 Trims系统实验室标识
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    /**
     * bu_id BIGINT(19)<br>
     * 获得 Trims系统产品线ID
     */
    public Long getBuId() {
        return buId;
    }

    /**
     * bu_id BIGINT(19)<br>
     * 设置 Trims系统产品线ID
     */
    public void setBuId(Long buId) {
        this.buId = buId;
    }

    /**
     * system_id BIGINT(19)<br>
     * 获得 
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * system_id BIGINT(19)<br>
     * 设置 
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * report_id VARCHAR(50)<br>
     * 获得 执行系统报告标识
     */
    public String getReportId() {
        return reportId;
    }

    /**
     * report_id VARCHAR(50)<br>
     * 设置 执行系统报告标识
     */
    public void setReportId(String reportId) {
        this.reportId = reportId == null ? null : reportId.trim();
    }

    /**
     * report_no VARCHAR(50) 必填<br>
     * 获得 执行系统报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50) 必填<br>
     * 设置 执行系统报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * original_report_no VARCHAR(50)<br>
     * 获得 执行系统父级报告号
     */
    public String getOriginalReportNo() {
        return originalReportNo;
    }

    /**
     * original_report_no VARCHAR(50)<br>
     * 设置 执行系统父级报告号
     */
    public void setOriginalReportNo(String originalReportNo) {
        this.originalReportNo = originalReportNo == null ? null : originalReportNo.trim();
    }

    /**
     * report_header VARCHAR(500)<br>
     * 获得 报告抬头
     */
    public String getReportHeader() {
        return reportHeader;
    }

    /**
     * report_header VARCHAR(500)<br>
     * 设置 报告抬头
     */
    public void setReportHeader(String reportHeader) {
        this.reportHeader = reportHeader == null ? null : reportHeader.trim();
    }

    /**
     * report_address VARCHAR(1000)<br>
     * 获得 报告地址
     */
    public String getReportAddress() {
        return reportAddress;
    }

    /**
     * report_address VARCHAR(1000)<br>
     * 设置 报告地址
     */
    public void setReportAddress(String reportAddress) {
        this.reportAddress = reportAddress == null ? null : reportAddress.trim();
    }

    /**
     * report_status INTEGER(10)<br>
     * 获得 报告状态
     */
    public Integer getReportStatus() {
        return reportStatus;
    }

    /**
     * report_status INTEGER(10)<br>
     * 设置 报告状态
     */
    public void setReportStatus(Integer reportStatus) {
        this.reportStatus = reportStatus;
    }

    /**
     * report_due_date TIMESTAMP(19)<br>
     * 获得 报告到期时间
     */
    public Date getReportDueDate() {
        return reportDueDate;
    }

    /**
     * report_due_date TIMESTAMP(19)<br>
     * 设置 报告到期时间
     */
    public void setReportDueDate(Date reportDueDate) {
        this.reportDueDate = reportDueDate;
    }

    /**
     * report_certificate_name VARCHAR(300)<br>
     * 获得 报告资质名称
     */
    public String getReportCertificateName() {
        return reportCertificateName;
    }

    /**
     * report_certificate_name VARCHAR(300)<br>
     * 设置 报告资质名称
     */
    public void setReportCertificateName(String reportCertificateName) {
        this.reportCertificateName = reportCertificateName == null ? null : reportCertificateName.trim();
    }

    /**
     * report_created_by VARCHAR(50)<br>
     * 获得 报告创建人
     */
    public String getReportCreatedBy() {
        return reportCreatedBy;
    }

    /**
     * report_created_by VARCHAR(50)<br>
     * 设置 报告创建人
     */
    public void setReportCreatedBy(String reportCreatedBy) {
        this.reportCreatedBy = reportCreatedBy == null ? null : reportCreatedBy.trim();
    }

    /**
     * report_created_date TIMESTAMP(19)<br>
     * 获得 报告创建时间
     */
    public Date getReportCreatedDate() {
        return reportCreatedDate;
    }

    /**
     * report_created_date TIMESTAMP(19)<br>
     * 设置 报告创建时间
     */
    public void setReportCreatedDate(Date reportCreatedDate) {
        this.reportCreatedDate = reportCreatedDate;
    }

    /**
     * report_approver_by VARCHAR(500)<br>
     * 获得 报告审批人
     */
    public String getReportApproverBy() {
        return reportApproverBy;
    }

    /**
     * report_approver_by VARCHAR(500)<br>
     * 设置 报告审批人
     */
    public void setReportApproverBy(String reportApproverBy) {
        this.reportApproverBy = reportApproverBy == null ? null : reportApproverBy.trim();
    }

    /**
     * report_approver_date TIMESTAMP(19)<br>
     * 获得 报告审批时间
     */
    public Date getReportApproverDate() {
        return reportApproverDate;
    }

    /**
     * report_approver_date TIMESTAMP(19)<br>
     * 设置 报告审批时间
     */
    public void setReportApproverDate(Date reportApproverDate) {
        this.reportApproverDate = reportApproverDate;
    }

    /**
     * softcopy_delivery_date TIMESTAMP(19)<br>
     * 获得 报告发送时间
     */
    public Date getSoftcopyDeliveryDate() {
        return softcopyDeliveryDate;
    }

    /**
     * softcopy_delivery_date TIMESTAMP(19)<br>
     * 设置 报告发送时间
     */
    public void setSoftcopyDeliveryDate(Date softcopyDeliveryDate) {
        this.softcopyDeliveryDate = softcopyDeliveryDate;
    }

    /**
     * conclusion_code VARCHAR(500)<br>
     * 获得 报告整体结论
     */
    public String getConclusionCode() {
        return conclusionCode;
    }

    /**
     * conclusion_code VARCHAR(500)<br>
     * 设置 报告整体结论
     */
    public void setConclusionCode(String conclusionCode) {
        this.conclusionCode = conclusionCode == null ? null : conclusionCode.trim();
    }

    /**
     * customer_conclusion VARCHAR(4000)<br>
     * 获得 报告客户结论
     */
    public String getCustomerConclusion() {
        return customerConclusion;
    }

    /**
     * customer_conclusion VARCHAR(4000)<br>
     * 设置 报告客户结论
     */
    public void setCustomerConclusion(String customerConclusion) {
        this.customerConclusion = customerConclusion == null ? null : customerConclusion.trim();
    }

    /**
     * review_conclusion VARCHAR(500)<br>
     * 获得 报告客户审核结论
     */
    public String getReviewConclusion() {
        return reviewConclusion;
    }

    /**
     * review_conclusion VARCHAR(500)<br>
     * 设置 报告客户审核结论
     */
    public void setReviewConclusion(String reviewConclusion) {
        this.reviewConclusion = reviewConclusion == null ? null : reviewConclusion.trim();
    }

    /**
     * conclusion_remark VARCHAR(1000)<br>
     * 获得 结论备注信息
     */
    public String getConclusionRemark() {
        return conclusionRemark;
    }

    /**
     * conclusion_remark VARCHAR(1000)<br>
     * 设置 结论备注信息
     */
    public void setConclusionRemark(String conclusionRemark) {
        this.conclusionRemark = conclusionRemark == null ? null : conclusionRemark.trim();
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1]<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 DB 自动更新，不允许程序设置
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 DB 自动更新，不允许程序设置
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    /**
     * exclude_customer_interface VARCHAR(255)<br>
     * 获得 
     */
    public String getExcludeCustomerInterface() {
        return excludeCustomerInterface;
    }

    /**
     * exclude_customer_interface VARCHAR(255)<br>
     * 设置 
     */
    public void setExcludeCustomerInterface(String excludeCustomerInterface) {
        this.excludeCustomerInterface = excludeCustomerInterface == null ? null : excludeCustomerInterface.trim();
    }

    /**
     * report_source_type INTEGER(10) 默认值[1]<br>
     * 获得 1:执行系统报告，2 customer报告
     */
    public Integer getReportSourceType() {
        return reportSourceType;
    }

    /**
     * report_source_type INTEGER(10) 默认值[1]<br>
     * 设置 1:执行系统报告，2 customer报告
     */
    public void setReportSourceType(Integer reportSourceType) {
        this.reportSourceType = reportSourceType;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", labId=").append(labId);
        sb.append(", buId=").append(buId);
        sb.append(", systemId=").append(systemId);
        sb.append(", reportId=").append(reportId);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", originalReportNo=").append(originalReportNo);
        sb.append(", reportHeader=").append(reportHeader);
        sb.append(", reportAddress=").append(reportAddress);
        sb.append(", reportStatus=").append(reportStatus);
        sb.append(", reportDueDate=").append(reportDueDate);
        sb.append(", reportCertificateName=").append(reportCertificateName);
        sb.append(", reportCreatedBy=").append(reportCreatedBy);
        sb.append(", reportCreatedDate=").append(reportCreatedDate);
        sb.append(", reportApproverBy=").append(reportApproverBy);
        sb.append(", reportApproverDate=").append(reportApproverDate);
        sb.append(", softcopyDeliveryDate=").append(softcopyDeliveryDate);
        sb.append(", conclusionCode=").append(conclusionCode);
        sb.append(", customerConclusion=").append(customerConclusion);
        sb.append(", reviewConclusion=").append(reviewConclusion);
        sb.append(", conclusionRemark=").append(conclusionRemark);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append(", excludeCustomerInterface=").append(excludeCustomerInterface);
        sb.append(", reportSourceType=").append(reportSourceType);
        sb.append("]");
        return sb.toString();
    }
}