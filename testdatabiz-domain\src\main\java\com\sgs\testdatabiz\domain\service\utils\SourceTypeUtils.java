package com.sgs.testdatabiz.domain.service.utils;

import com.sgs.framework.model.enums.SourceTypeEnum;

/**
 * @author: shawn.yang
 * @create: 2023-03-29 11:13
 */
public final class SourceTypeUtils {

    public static SourceTypeEnum toSourceTypeEnum(int sourceCode){
        switch (sourceCode){
            case (1):
                return SourceTypeEnum.SLIM;
            case (2):
                return SourceTypeEnum.JOB;
            case (3):
                return SourceTypeEnum.STARLIMS;
            case (4):
                return SourceTypeEnum.FAST;
            case (5):
                return SourceTypeEnum.SUBCONTRACT;
            case (6):
                return SourceTypeEnum.ENTERSUBCONTRACT;
            default:
                return null;
        }
    }
}
