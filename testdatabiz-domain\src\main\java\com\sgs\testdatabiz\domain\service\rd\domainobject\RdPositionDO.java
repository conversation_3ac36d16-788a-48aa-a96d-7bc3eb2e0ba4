/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPositionDO {

    private String positionInstanceId;
    private Integer usageTypeId;
    private String usageTypeName;
    private String positionName;
    private String positionDescription;
    private Integer positionSeq;
    private List<RdPositionLanguageDO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}