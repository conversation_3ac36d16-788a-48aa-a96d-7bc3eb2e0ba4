<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdQuotationLangMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="rt_quotation_id" property="rtQuotationId" jdbcType="BIGINT" />
    <result column="language_id" property="languageId" jdbcType="INTEGER" />
    <result column="payer_customer_name" property="payerCustomerName" jdbcType="VARCHAR" />
    <result column="citation_name" property="citationName" jdbcType="VARCHAR" />
    <result column="citation_full_name" property="citationFullName" jdbcType="VARCHAR" />
    <result column="service_item_name" property="serviceItemName" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, rt_quotation_id, language_id, payer_customer_name, citation_name, citation_full_name, 
    service_item_name, created_by, created_date, modified_by, modified_date, last_modified_timestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_quotation_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_quotation_lang
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_quotation_lang
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangExample" >
    delete from tb_quotation_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangPO" >
    insert into tb_quotation_lang (id, rt_quotation_id, language_id, 
      payer_customer_name, citation_name, citation_full_name, 
      service_item_name, created_by, created_date, 
      modified_by, modified_date, last_modified_timestamp
      )
    values (#{id,jdbcType=BIGINT}, #{rtQuotationId,jdbcType=BIGINT}, #{languageId,jdbcType=INTEGER}, 
      #{payerCustomerName,jdbcType=VARCHAR}, #{citationName,jdbcType=VARCHAR}, #{citationFullName,jdbcType=VARCHAR}, 
      #{serviceItemName,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, now(), 
      #{modifiedBy,jdbcType=VARCHAR}, now(), #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangPO" >
    insert into tb_quotation_lang
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="rtQuotationId != null" >
        rt_quotation_id,
      </if>
      <if test="languageId != null" >
        language_id,
      </if>
      <if test="payerCustomerName != null" >
        payer_customer_name,
      </if>
      <if test="citationName != null" >
        citation_name,
      </if>
      <if test="citationFullName != null" >
        citation_full_name,
      </if>
      <if test="serviceItemName != null" >
        service_item_name,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="rtQuotationId != null" >
        #{rtQuotationId,jdbcType=BIGINT},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="payerCustomerName != null" >
        #{payerCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="citationFullName != null" >
        #{citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemName != null" >
        #{serviceItemName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangExample" resultType="java.lang.Integer" >
    select count(*) from tb_quotation_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_quotation_lang
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.rtQuotationId != null" >
        rt_quotation_id = #{record.rtQuotationId,jdbcType=BIGINT},
      </if>
      <if test="record.languageId != null" >
        language_id = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.payerCustomerName != null" >
        payer_customer_name = #{record.payerCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="record.citationName != null" >
        citation_name = #{record.citationName,jdbcType=VARCHAR},
      </if>
      <if test="record.citationFullName != null" >
        citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceItemName != null" >
        service_item_name = #{record.serviceItemName,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_quotation_lang
    set id = #{record.id,jdbcType=BIGINT},
      rt_quotation_id = #{record.rtQuotationId,jdbcType=BIGINT},
      language_id = #{record.languageId,jdbcType=INTEGER},
      payer_customer_name = #{record.payerCustomerName,jdbcType=VARCHAR},
      citation_name = #{record.citationName,jdbcType=VARCHAR},
      citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      service_item_name = #{record.serviceItemName,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangPO" >
    update tb_quotation_lang
    <set >
      <if test="rtQuotationId != null" >
        rt_quotation_id = #{rtQuotationId,jdbcType=BIGINT},
      </if>
      <if test="languageId != null" >
        language_id = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="payerCustomerName != null" >
        payer_customer_name = #{payerCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        citation_name = #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="citationFullName != null" >
        citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemName != null" >
        service_item_name = #{serviceItemName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangPO" >
    update tb_quotation_lang
    set rt_quotation_id = #{rtQuotationId,jdbcType=BIGINT},
      language_id = #{languageId,jdbcType=INTEGER},
      payer_customer_name = #{payerCustomerName,jdbcType=VARCHAR},
      citation_name = #{citationName,jdbcType=VARCHAR},
      citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      service_item_name = #{serviceItemName,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_quotation_lang
      (`id`,`rt_quotation_id`,`language_id`,
      `payer_customer_name`,`citation_name`,`citation_full_name`,
      `service_item_name`,`created_by`,`created_date`,
      `modified_by`,`modified_date`,`last_modified_timestamp`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.rtQuotationId, jdbcType=BIGINT},#{ item.languageId, jdbcType=INTEGER},
      #{ item.payerCustomerName, jdbcType=VARCHAR},#{ item.citationName, jdbcType=VARCHAR},#{ item.citationFullName, jdbcType=VARCHAR},
      #{ item.serviceItemName, jdbcType=VARCHAR},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_quotation_lang 
      <set>
        <if test="item.rtQuotationId != null"> 
          `rt_quotation_id` = #{item.rtQuotationId, jdbcType = BIGINT},
        </if> 
        <if test="item.languageId != null"> 
          `language_id` = #{item.languageId, jdbcType = INTEGER},
        </if> 
        <if test="item.payerCustomerName != null"> 
          `payer_customer_name` = #{item.payerCustomerName, jdbcType = VARCHAR},
        </if> 
        <if test="item.citationName != null"> 
          `citation_name` = #{item.citationName, jdbcType = VARCHAR},
        </if> 
        <if test="item.citationFullName != null"> 
          `citation_full_name` = #{item.citationFullName, jdbcType = VARCHAR},
        </if> 
        <if test="item.serviceItemName != null"> 
          `service_item_name` = #{item.serviceItemName, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>