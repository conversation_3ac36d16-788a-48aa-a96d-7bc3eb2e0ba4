/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdSpecimenDTO implements Serializable {
    private Integer specimenType;
    private String specimenInstanceId;
    private String specimenNo;
    private String specimenDescription;
    private List<RdSpecimenLanguageDTO> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
