package com.sgs.testdatabiz.dbstorages.mybatis.config;

import com.alibaba.dubbo.common.utils.ConcurrentHashSet;
import com.google.common.collect.Maps;
import com.sgs.testdatabiz.core.constants.Constants;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.DatabaseTypeEnum;
import com.sgs.framework.model.enums.ProductLineType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 多数据源
 * <AUTHOR>
 * @date 2021/04/15 10:49
 */
public class DynamicDataSource extends AbstractRoutingDataSource {
    private static final Map<DatabaseTypeEnum, List<String>> METHOD_TYPE_MAP = Maps.newConcurrentMap();
    // primary
    private static final ConcurrentHashSet<String> DATASOURCE_KEYS = new ConcurrentHashSet<>();

    /**
     *
     * @return
     */
    @Override
    protected Object determineCurrentLookupKey() {
        ThreadDataSourceInfo dataSource = DatabaseContextHolder.getDataSource();
        if (dataSource == null){
            dataSource = new ThreadDataSourceInfo();
        }
        if (StringUtils.isBlank(dataSource.getDbName())) {
            dataSource.setDbName(Constants.TestDataDb);
        }
        DatabaseTypeEnum databaseType = dataSource.getDatabaseType();
        if (databaseType == null){
            databaseType = DatabaseTypeEnum.Master;
        }
        // TODO {SL/HL}_{DbName}_{Master/Slave}
        String dataSourceKey = String.format("%s-%s", dataSource.getDbName(), databaseType.getName()).toUpperCase();
        if (!DATASOURCE_KEYS.contains(dataSourceKey)){
            throw new RuntimeException(String.format("未找到对应的数据源(%s).", dataSourceKey));
        }
        return dataSourceKey;
    }

    /**
     *
     * @param type
     * @param content
     */
    void setMethodType(DatabaseTypeEnum type, String content) {
        if (content == null){
            return;
        }
        List<String> list = Arrays.asList(content.split(","));
        METHOD_TYPE_MAP.put(type, list);
    }

    /**
     *
     * @param dataSourceKey
     */
    void put(String dataSourceKey){
        if (StringUtils.isBlank(dataSourceKey)){
            return;
        }
        DATASOURCE_KEYS.add(dataSourceKey);
    }
}
