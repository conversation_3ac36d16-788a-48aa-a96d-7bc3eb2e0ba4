package com.sgs.testdatabiz.facade.model.testdata.slim;

import java.util.List;

public class SlimTestMatrixInfo {
    /**
     *
     */
    private String testSampleNo;

    /**
     *
     */
    private String externalSampleNo;

    /**
     *
     */
    private String slimCode;

    /**
     *
     */
    private String externalident;

    /**
     *
     */
    private String materialName;

    /**
     *
     */
    private String materialTexture;

    /**
     *
     */
    private String usedPosition;
    /**
     *
     */
    private String materialColor;

    /**
     *
     */
    private Long testLineSeq;

    /**
     *
     */
    private List<SlimTestAnalyteInfo> testAnalytes;

    public String getTestSampleNo() {
        return testSampleNo;
    }

    public void setTestSampleNo(String testSampleNo) {
        this.testSampleNo = testSampleNo;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public String getSlimCode() {
        return slimCode;
    }

    public void setSlimCode(String slimCode) {
        this.slimCode = slimCode;
    }

    public String getExternalident() {
        return externalident;
    }

    public void setExternalident(String externalident) {
        this.externalident = externalident;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public Long getTestLineSeq() {
        return testLineSeq;
    }

    public void setTestLineSeq(Long testLineSeq) {
        this.testLineSeq = testLineSeq;
    }

    public List<SlimTestAnalyteInfo> getTestAnalytes() {
        return testAnalytes;
    }

    public void setTestAnalytes(List<SlimTestAnalyteInfo> testAnalytes) {
        this.testAnalytes = testAnalytes;
    }
}
