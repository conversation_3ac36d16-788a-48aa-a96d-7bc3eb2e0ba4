package com.sgs.testdatabiz.dbstorages.mybatis.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 读写分离  类型枚举
 * Created by <PERSON> on 2019/4/28.
 */
public enum DatabaseTypeEnum {
    Master("Master"),
    Slave("Slave");

    DatabaseTypeEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static DatabaseTypeEnum findDatabaseType(String databaseType) {
        if (databaseType == null) {
            return null;
        }
        for (DatabaseTypeEnum enu : DatabaseTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(enu.getName(), databaseType)){
                return enu;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "DatabaseType{" +
                "name='" + name + '\'' +
                '}';
    }
}
