package com.sgs.testdatabiz.facade.model.testdata.slim;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "ANALYTE")
@XmlAccessorType(XmlAccessType.FIELD)
public class SlimAnalyteInfo {
    /**
     *
     */
    @XmlAttribute(name = "SA_DESC")
    private String analyteName;

    /**
     *
     */
    @XmlAttribute(name = "LSA_DESC")
    private String analyteNameCN;

    /**
     *
     */
    @XmlAttribute(name = "SA_CASNO")
    private String casNo;

    /**
     *
     */
    @XmlAttribute(name = "REPORTACTIVE")
    private int reportActive;

    /**
     *
     */
    @XmlAttribute(name = "ANALYTESTATUS")
    private String analyteStatus;

    /**
     *
     */
    @XmlAttribute(name = "ANALYTECODE")
    private String analyteCode;

    /**
     *
     */
    @XmlAttribute(name = "LVL1UPPERLIMIT")
    private String reportLimit;

    /**
     *
     */
    @XmlAttribute(name = "ROUNDEDVALUE")
    private String testValue;

    /**
     *
     */
    @XmlAttribute(name = "FINALVALUE")
    private String finalValue;

    /**
     *
     */
    @XmlAttribute(name = "SHOW_ND")
    private String showNd;

    /**
     *
     */
    @XmlAttribute(name = "REPUNIT")
    private String reportUnit;

    /**
     *
     */
    @XmlAttribute(name = "LU_REPUNIT")
    private String reportUnitCN;

    /**
     *
     */
    @XmlAttribute(name = "UNITNAME")
    private String limitUnit;

    /**
     *
     */
    @XmlAttribute(name = "PJSA_ANASEQUENCE")
    private Integer analyteSeq;

    public String getAnalyteName() {
        return analyteName;
    }

    public void setAnalyteName(String analyteName) {
        this.analyteName = analyteName;
    }

    public String getAnalyteNameCN() {
        return analyteNameCN;
    }

    public void setAnalyteNameCN(String analyteNameCN) {
        this.analyteNameCN = analyteNameCN;
    }

    public int getReportActive() {
        return reportActive;
    }

    public void setReportActive(int reportActive) {
        this.reportActive = reportActive;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getAnalyteStatus() {
        return analyteStatus;
    }

    public void setAnalyteStatus(String analyteStatus) {
        this.analyteStatus = analyteStatus;
    }

    public String getAnalyteCode() {
        return analyteCode;
    }

    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public String getFinalValue() {
        return finalValue;
    }

    public void setFinalValue(String finalValue) {
        this.finalValue = finalValue;
    }

    public String getShowNd() {
        return showNd;
    }

    public void setShowNd(String showNd) {
        this.showNd = showNd;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportUnitCN() {
        return reportUnitCN;
    }

    public void setReportUnitCN(String reportUnitCN) {
        this.reportUnitCN = reportUnitCN;
    }

    public String getLimitUnit() {
        return limitUnit;
    }

    public void setLimitUnit(String limitUnit) {
        this.limitUnit = limitUnit;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }
}
