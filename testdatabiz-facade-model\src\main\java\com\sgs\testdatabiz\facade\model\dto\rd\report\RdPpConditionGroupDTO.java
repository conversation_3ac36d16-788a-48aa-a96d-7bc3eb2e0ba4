/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPpConditionGroupDTO implements Serializable {
    private String id;
    private String conditionGroupId;
    private String ppTestLineRelId;
    private Integer languageType;
    private String groupFootNotes;
    private Integer ppNo;
    private String ppName;
    private String sampleNos;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}