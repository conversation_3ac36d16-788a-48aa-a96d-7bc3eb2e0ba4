<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestDataObjectRelMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO" >
    <id column="Id" property="id" jdbcType="VARCHAR" />
    <result column="ProductLineCode" property="productLineCode" jdbcType="VARCHAR" />
    <result column="LabCode" property="labCode" jdbcType="VARCHAR" />
    <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
    <result column="ParentOrderNo" property="parentOrderNo" jdbcType="VARCHAR" />
    <result column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
    <result column="ObjectNo" property="objectNo" jdbcType="VARCHAR" />
    <result column="ExternalId" property="externalId" jdbcType="VARCHAR" />
    <result column="ExternalNo" property="externalNo" jdbcType="VARCHAR" />
    <result column="ExternalObjectNo" property="externalObjectNo" jdbcType="VARCHAR" />
    <result column="ExcludeCustomerInterface" property="excludeCustomerInterface" jdbcType="VARCHAR" />
    <result column="SourceType" property="sourceType" jdbcType="INTEGER" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="CompleteDate" property="completeDate" jdbcType="TIMESTAMP" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
    <result column="LabId" property="labId" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, ProductLineCode, LabCode, OrderNo, ParentOrderNo, ReportNo, ObjectNo, ExternalId, 
    ExternalNo, ExternalObjectNo, ExcludeCustomerInterface, SourceType, LanguageId, CompleteDate, 
    BizVersionId, ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, 
    LastModifiedTimestamp, LabId
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_data_object_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_data_object_rel
    where Id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_data_object_rel
    where Id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelExample" >
    delete from tb_test_data_object_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO" >
    insert into tb_test_data_object_rel (Id, ProductLineCode, LabCode, 
      OrderNo, ParentOrderNo, ReportNo, 
      ObjectNo, ExternalId, ExternalNo, 
      ExternalObjectNo, ExcludeCustomerInterface, 
      SourceType, LanguageId, CompleteDate, 
      BizVersionId, ActiveIndicator, CreatedBy, 
      CreatedDate, ModifiedBy, ModifiedDate, 
      LastModifiedTimestamp, LabId)
    values (#{id,jdbcType=VARCHAR}, #{productLineCode,jdbcType=VARCHAR}, #{labCode,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{parentOrderNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{objectNo,jdbcType=VARCHAR}, #{externalId,jdbcType=VARCHAR}, #{externalNo,jdbcType=VARCHAR}, 
      #{externalObjectNo,jdbcType=VARCHAR}, #{excludeCustomerInterface,jdbcType=VARCHAR}, 
      #{sourceType,jdbcType=INTEGER}, #{languageId,jdbcType=INTEGER}, #{completeDate,jdbcType=TIMESTAMP}, 
      #{bizVersionId,jdbcType=CHAR}, #{activeIndicator,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{lastModifiedTimestamp,jdbcType=TIMESTAMP}, #{labId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO" >
    insert into tb_test_data_object_rel
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="productLineCode != null" >
        ProductLineCode,
      </if>
      <if test="labCode != null" >
        LabCode,
      </if>
      <if test="orderNo != null" >
        OrderNo,
      </if>
      <if test="parentOrderNo != null" >
        ParentOrderNo,
      </if>
      <if test="reportNo != null" >
        ReportNo,
      </if>
      <if test="objectNo != null" >
        ObjectNo,
      </if>
      <if test="externalId != null" >
        ExternalId,
      </if>
      <if test="externalNo != null" >
        ExternalNo,
      </if>
      <if test="externalObjectNo != null" >
        ExternalObjectNo,
      </if>
      <if test="excludeCustomerInterface != null" >
        ExcludeCustomerInterface,
      </if>
      <if test="sourceType != null" >
        SourceType,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="completeDate != null" >
        CompleteDate,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
      <if test="labId != null" >
        LabId,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="productLineCode != null" >
        #{productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="labCode != null" >
        #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="parentOrderNo != null" >
        #{parentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="objectNo != null" >
        #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="externalId != null" >
        #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="externalNo != null" >
        #{externalNo,jdbcType=VARCHAR},
      </if>
      <if test="externalObjectNo != null" >
        #{externalObjectNo,jdbcType=VARCHAR},
      </if>
      <if test="excludeCustomerInterface != null" >
        #{excludeCustomerInterface,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null" >
        #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="completeDate != null" >
        #{completeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_data_object_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_data_object_rel
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.productLineCode != null" >
        ProductLineCode = #{record.productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="record.labCode != null" >
        LabCode = #{record.labCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.parentOrderNo != null" >
        ParentOrderNo = #{record.parentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.objectNo != null" >
        ObjectNo = #{record.objectNo,jdbcType=VARCHAR},
      </if>
      <if test="record.externalId != null" >
        ExternalId = #{record.externalId,jdbcType=VARCHAR},
      </if>
      <if test="record.externalNo != null" >
        ExternalNo = #{record.externalNo,jdbcType=VARCHAR},
      </if>
      <if test="record.externalObjectNo != null" >
        ExternalObjectNo = #{record.externalObjectNo,jdbcType=VARCHAR},
      </if>
      <if test="record.excludeCustomerInterface != null" >
        ExcludeCustomerInterface = #{record.excludeCustomerInterface,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceType != null" >
        SourceType = #{record.sourceType,jdbcType=INTEGER},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.completeDate != null" >
        CompleteDate = #{record.completeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.labId != null" >
        LabId = #{record.labId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_data_object_rel
    set Id = #{record.id,jdbcType=VARCHAR},
      ProductLineCode = #{record.productLineCode,jdbcType=VARCHAR},
      LabCode = #{record.labCode,jdbcType=VARCHAR},
      OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      ParentOrderNo = #{record.parentOrderNo,jdbcType=VARCHAR},
      ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      ObjectNo = #{record.objectNo,jdbcType=VARCHAR},
      ExternalId = #{record.externalId,jdbcType=VARCHAR},
      ExternalNo = #{record.externalNo,jdbcType=VARCHAR},
      ExternalObjectNo = #{record.externalObjectNo,jdbcType=VARCHAR},
      ExcludeCustomerInterface = #{record.excludeCustomerInterface,jdbcType=VARCHAR},
      SourceType = #{record.sourceType,jdbcType=INTEGER},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      CompleteDate = #{record.completeDate,jdbcType=TIMESTAMP},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=INTEGER},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      LabId = #{record.labId,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO" >
    update tb_test_data_object_rel
    <set >
      <if test="productLineCode != null" >
        ProductLineCode = #{productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="labCode != null" >
        LabCode = #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="parentOrderNo != null" >
        ParentOrderNo = #{parentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        ReportNo = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="objectNo != null" >
        ObjectNo = #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="externalId != null" >
        ExternalId = #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="externalNo != null" >
        ExternalNo = #{externalNo,jdbcType=VARCHAR},
      </if>
      <if test="externalObjectNo != null" >
        ExternalObjectNo = #{externalObjectNo,jdbcType=VARCHAR},
      </if>
      <if test="excludeCustomerInterface != null" >
        ExcludeCustomerInterface = #{excludeCustomerInterface,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null" >
        SourceType = #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="completeDate != null" >
        CompleteDate = #{completeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="labId != null" >
        LabId = #{labId,jdbcType=BIGINT},
      </if>
    </set>
    where Id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO" >
    update tb_test_data_object_rel
    set ProductLineCode = #{productLineCode,jdbcType=VARCHAR},
      LabCode = #{labCode,jdbcType=VARCHAR},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      ParentOrderNo = #{parentOrderNo,jdbcType=VARCHAR},
      ReportNo = #{reportNo,jdbcType=VARCHAR},
      ObjectNo = #{objectNo,jdbcType=VARCHAR},
      ExternalId = #{externalId,jdbcType=VARCHAR},
      ExternalNo = #{externalNo,jdbcType=VARCHAR},
      ExternalObjectNo = #{externalObjectNo,jdbcType=VARCHAR},
      ExcludeCustomerInterface = #{excludeCustomerInterface,jdbcType=VARCHAR},
      SourceType = #{sourceType,jdbcType=INTEGER},
      LanguageId = #{languageId,jdbcType=INTEGER},
      CompleteDate = #{completeDate,jdbcType=TIMESTAMP},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=INTEGER},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      LabId = #{labId,jdbcType=BIGINT}
    where Id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_data_object_rel
      (`Id`,`ProductLineCode`,`LabCode`,
      `OrderNo`,`ParentOrderNo`,`ReportNo`,
      `ObjectNo`,`ExternalId`,`ExternalNo`,
      `ExternalObjectNo`,`ExcludeCustomerInterface`,`SourceType`,
      `LanguageId`,`CompleteDate`,`BizVersionId`,
      `ActiveIndicator`,`CreatedBy`,`CreatedDate`,
      `ModifiedBy`,`ModifiedDate`,`LastModifiedTimestamp`,
      `LabId`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=VARCHAR},#{ item.productLineCode, jdbcType=VARCHAR},#{ item.labCode, jdbcType=VARCHAR},
      #{ item.orderNo, jdbcType=VARCHAR},#{ item.parentOrderNo, jdbcType=VARCHAR},#{ item.reportNo, jdbcType=VARCHAR},
      #{ item.objectNo, jdbcType=VARCHAR},#{ item.externalId, jdbcType=VARCHAR},#{ item.externalNo, jdbcType=VARCHAR},
      #{ item.externalObjectNo, jdbcType=VARCHAR},#{ item.excludeCustomerInterface, jdbcType=VARCHAR},#{ item.sourceType, jdbcType=INTEGER},
      #{ item.languageId, jdbcType=INTEGER},#{ item.completeDate, jdbcType=TIMESTAMP},#{ item.bizVersionId, jdbcType=CHAR},
      #{ item.activeIndicator, jdbcType=INTEGER},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP},
      #{ item.labId, jdbcType=BIGINT}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_data_object_rel 
      <set>
        <if test="item.productLineCode != null"> 
          `ProductLineCode` = #{item.productLineCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.labCode != null"> 
          `LabCode` = #{item.labCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.orderNo != null"> 
          `OrderNo` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.parentOrderNo != null"> 
          `ParentOrderNo` = #{item.parentOrderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `ReportNo` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.objectNo != null"> 
          `ObjectNo` = #{item.objectNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.externalId != null"> 
          `ExternalId` = #{item.externalId, jdbcType = VARCHAR},
        </if> 
        <if test="item.externalNo != null"> 
          `ExternalNo` = #{item.externalNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.externalObjectNo != null"> 
          `ExternalObjectNo` = #{item.externalObjectNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.excludeCustomerInterface != null"> 
          `ExcludeCustomerInterface` = #{item.excludeCustomerInterface, jdbcType = VARCHAR},
        </if> 
        <if test="item.sourceType != null"> 
          `SourceType` = #{item.sourceType, jdbcType = INTEGER},
        </if> 
        <if test="item.languageId != null"> 
          `LanguageId` = #{item.languageId, jdbcType = INTEGER},
        </if> 
        <if test="item.completeDate != null"> 
          `CompleteDate` = #{item.completeDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.bizVersionId != null"> 
          `BizVersionId` = #{item.bizVersionId, jdbcType = CHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `ActiveIndicator` = #{item.activeIndicator, jdbcType = INTEGER},
        </if> 
        <if test="item.createdBy != null"> 
          `CreatedBy` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `CreatedDate` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `ModifiedBy` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `ModifiedDate` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `LastModifiedTimestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.labId != null"> 
          `LabId` = #{item.labId, jdbcType = BIGINT},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `Id` = #{item.id,jdbcType = VARCHAR}
        </if>
      </where>
    </foreach>
  </update>
</mapper>