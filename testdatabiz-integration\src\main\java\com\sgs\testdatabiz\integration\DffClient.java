package com.sgs.testdatabiz.integration;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.ImmutableMap;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.testdatabiz.core.config.InterfaceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
@Component
public class DffClient {
    private static final Logger logger = LoggerFactory.getLogger(DffClient.class);
    @Resource
    private InterfaceConfig interfaceConfig;

    public JSONArray queryDffFormGeneral(String templateId) {
        long startMillis = System.currentTimeMillis();
        String url = String.format("%s/DFFV2Api/dff/queryDffFormGeneral", interfaceConfig.getBaseUrl());
        try {
            String response = HttpClientUtil.post(url, ImmutableMap.of("id", templateId));
            return JSONArray.parseArray(response);
        } catch (Exception e) {
            logger.info("DffClient.queryDffFormGeneral Error : {}", e.getMessage());
        } finally {
            logger.info("DffClient.queryDffFormGeneral 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return null;
    }
}
