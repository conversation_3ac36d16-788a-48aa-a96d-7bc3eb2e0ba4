package com.sgs.testdatabiz.domain.service.rd.filter;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
public class FilterContext {
    private Long systemId;
    private Integer refSystemId;
    private List<FilterRecord> filterRecords = new ArrayList<>();
    private Map<String, Object> attributes = new ConcurrentHashMap<>();
    
    @Data
    @AllArgsConstructor
    public static class FilterRecord {
        private String filterName;
        private String fieldName;
        private int beforeCount;
        private int afterCount;
        private String message;
    }
    
    public void addFilterRecord(String filterName, String fieldName, int beforeCount, int afterCount, String message) {
        filterRecords.add(new FilterRecord(filterName, fieldName, beforeCount, afterCount, message));
    }
} 