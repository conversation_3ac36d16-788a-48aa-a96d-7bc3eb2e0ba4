UPDATE tb_test_data_matrix_info_ee_gz SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;



UPDATE tb_test_data_matrix_info_hl_aj SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_cz SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_gz SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_hk SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_hz SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_nb SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_nj SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_qd SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_sd SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_sh SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_sz SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_tc SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_tj SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_tp SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_hl_xm SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_cz SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_gz SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;

UPDATE tb_test_data_matrix_info_sl_hk SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_hz SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_isb SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_nb SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_nj SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_qd SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_sh SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;


UPDATE tb_test_data_matrix_info_sl_tj SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;

UPDATE tb_test_data_matrix_info_sl_xm SET ConclusionId =(
    CASE
        WHEN ConclusionDisplay = 'Pass' THEN 40
        WHEN ConclusionDisplay = 'Fail' THEN 10
        WHEN ConclusionDisplay = 'Inconclusive' THEN 30
        WHEN ConclusionDisplay = 'Warning' THEN 30
        WHEN ConclusionDisplay = 'Need' THEN 30
        WHEN ConclusionDisplay = 'Data Only' THEN 20
        WHEN ConclusionDisplay = 'NA' THEN 60
        WHEN ConclusionDisplay = 'Exempt' THEN 50
        ELSE NULL
        END
    ),ModifiedBy='yaofeng',ModifiedDate=NOW() where CreatedDate <='2023-07-23 10:00' AND ConclusionId IS NULL AND ConclusionDisplay IS NOT NULL;

