package com.sgs.testdatabiz.facade;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.req.CleanConclusionReq;
import com.sgs.testdatabiz.facade.model.req.slim.SlimSaveTestDataReq;

public interface ISlimTestDataFacade {

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse saveSlimTestData(SlimSaveTestDataReq reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse cleanConclusion(CleanConclusionReq reqObject);

    /**
     *
     * @param reqObject
     * @return
     */
    BaseResponse getMailTo(String productLineCode, String locationCode);
}
