package com.sgs.testdatabiz.integration;

import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.testdatabiz.core.config.InterfaceConfig;
import com.sgs.testdatabiz.facade.model.req.config.CheckTestLineMappingReq;
import com.sgs.testdatabiz.facade.model.req.config.TestLineInfoReq;
import com.sgs.testdatabiz.facade.model.rsp.config.CheckTestLineMappingExistsDTO;
import com.sgs.testdatabiz.facade.model.rsp.config.CheckTestLineMappingRsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LocaliLayerClient {
    private static final Logger logger = LoggerFactory.getLogger(LocaliLayerClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;

    /**
     *
     * @param customerGroupCode
     * @param testLines
     * @return
     */
    public BaseResponse<List<CheckTestLineMappingRsp>> getTestLineMappingList(String customerGroupCode,Integer refSystemId, List<TestLineInfoReq> testLines) {
        BaseResponse rspResult = new BaseResponse();
        String localiLayerUrl = String.format("%s/openapi/sync/checkTestLineMappingExists", interfaceConfig.getLocaliLayerUrl());
        try {
            CheckTestLineMappingReq reqObject = new CheckTestLineMappingReq();
            reqObject.setCustomerGroupCode(customerGroupCode);
            reqObject.setTestLines(testLines);
            reqObject.setRefSystemId(refSystemId);
            BaseResponse response = HttpClientUtil.post(localiLayerUrl, reqObject, BaseResponse.class);
            rspResult.setStatus(response.getStatus());
            if (response.getStatus() == 200) {
                List<CheckTestLineMappingRsp> testLineMappings = JSONObject.parseArray(response.getData().toString(), CheckTestLineMappingRsp.class);
                rspResult.setData(testLineMappings);
                return BaseResponse.newInstance(testLineMappings);
            }
            logger.error("LocaliLayerClient.getTestLineMappingList param({})  请求失败:{}", JSONObject.toJSONString(reqObject), response.getMessage());
        } catch (Exception ex) {
            logger.error("LocaliLayerClient.getTestLineMappingList， Error：{}", ex);
            rspResult.setMessage(ex.getMessage());
        }
        return rspResult;
    }

    public CustomResult<List<CheckTestLineMappingExistsDTO>> queryTestLineMappingExists(com.sgs.extsystem.facade.model.customer.req.CheckTestLineMappingReq reqObject) {
        CustomResult customResult = new CustomResult(false);

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/checkTestLineMappingExists");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(localiLayerUrl, reqObject, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (response.getStatus() == 200 ) {
                List<CheckTestLineMappingExistsDTO> checkTestLineMappingRsps = JSONObject.parseArray(response.getData().toString(), CheckTestLineMappingExistsDTO.class);
                customResult.setData(checkTestLineMappingRsps);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.checkTestLineMappingExists param({})  请求失败:{}", JSONObject.toJSONString(reqObject), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
        } catch (Exception e) {
            logger.error("Get trf no failure.{}", e);
        }
        return customResult;
    }

}
