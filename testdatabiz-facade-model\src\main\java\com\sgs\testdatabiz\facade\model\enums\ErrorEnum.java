package com.sgs.testdatabiz.facade.model.enums;

public enum ErrorEnum {
    Order(500, "订单不存在");

    private int status;
    private String text;

    ErrorEnum(int status, String text) {
        this.status = status;
        this.text = text;
    }

    public int getStatus() {
        return status;
    }

    public String getText() {
        return text;
    }

    public static String getText(Integer status) {
        ErrorEnum enu = getStatus(status);
        return enu != null ? enu.getText() : null;
    }

    /**
     *
     * @param status
     * @return
     */
    private static ErrorEnum getStatus(Integer status){
        if (status == null){
            return null;
        }
        for (ErrorEnum enu: ErrorEnum.values()) {
            if (status.intValue() == enu.getStatus()){
                return enu;
            }
        }
        return null;
    }

    /**
     *
     * @param status
     * @return
     */
    public boolean check(Integer status){
        ErrorEnum enu = getStatus(status);
        return enu != null && this == enu;
    }

    /**
     *
     * @param status
     * @param bizErrors
     * @return
     */
    public static boolean check(Integer status, ErrorEnum... bizErrors) {
        if (status == null || bizErrors == null || bizErrors.length <= 0){
            return false;
        }
        for (ErrorEnum enu: bizErrors) {
            if (enu.getStatus() == status.intValue()){
                return true;
            }
        }
        return false;
    }
}
