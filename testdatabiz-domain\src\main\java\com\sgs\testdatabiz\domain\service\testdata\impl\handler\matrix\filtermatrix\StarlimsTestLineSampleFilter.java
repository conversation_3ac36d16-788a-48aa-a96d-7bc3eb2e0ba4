package com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.filtermatrix;

import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.BaseMatrixInfoFilter;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.MatrixInfo;
import com.sgs.testdatabiz.facade.model.dto.starlims.MatrixFilterDTO;
import com.sgs.testdatabiz.facade.model.dto.starlims.SubContractMatrixDTO;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveReportData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * starlims 没有返回 matrixId,可以使用 TestLine +Sample 匹配
 */
@Component
@MatrixInfo(value = "StarLimsTestLineSample", sort = 2)
public class StarlimsTestLineSampleFilter extends BaseMatrixInfoFilter {
    @Override
    public SubContractMatrixDTO filterStarlimsMatrix(MatrixFilterDTO matrixFilterDTO) {
        ReceiveReportData reportData = matrixFilterDTO.getReportData();
        List<SubContractTestMatrixInfo> subContractTestMatrixList = matrixFilterDTO.getSubContractTestMatrixList();
        ChemPpArtifactTestLineInfoRsp chemTestLine = matrixFilterDTO.getChemTestLine();


        //  starlims 没有返回 matrixId,可以使用 TestLine +Sample 匹配
        SubContractTestMatrixInfo subContractTestMatrixInfo = subContractTestMatrixList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(findPpTestLineCitationKey(item), findPpTestLineCitationKey(chemTestLine)) &&
                        StringUtils.equalsIgnoreCase(item.getSampleNo(), reportData.getMaterialNumber()))
                .findFirst().orElse(null);
        if (subContractTestMatrixInfo != null) {
            SubContractMatrixDTO testMatrixInfo = new SubContractMatrixDTO();
            buildTestMatrixIndo(testMatrixInfo, subContractTestMatrixInfo);
            return testMatrixInfo;
        }

        return null;
    }

}
