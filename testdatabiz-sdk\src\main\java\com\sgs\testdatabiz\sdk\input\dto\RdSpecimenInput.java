/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdSpecimenInput implements Serializable {
    private Integer specimenType;
    private String specimenInstanceId;
    private String specimenNo;
    private String specimenDescription;
    private List<RdSpecimenLanguageInput> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
