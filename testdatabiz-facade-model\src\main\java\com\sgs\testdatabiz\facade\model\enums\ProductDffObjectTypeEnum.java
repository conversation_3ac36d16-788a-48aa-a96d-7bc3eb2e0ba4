package com.sgs.testdatabiz.facade.model.enums;

/**
 * @author: shawn.yang
 * @create: 2023-04-20 16:25
 */
public enum ProductDffObjectTypeEnum {
    PRODUCT(1,"Product"),
    SAMPLE(2,"Sample"),
    ;


    private final Integer code;
    private final String desc;


    ProductDffObjectTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static ProductDffObjectTypeEnum from(Integer code){
        for (ProductDffObjectTypeEnum value : values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
