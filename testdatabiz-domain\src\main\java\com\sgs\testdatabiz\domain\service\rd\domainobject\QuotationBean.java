package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationLangPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuotationBean {

    private List<RdQuotationPO> quotationPOList;

    private List<RdQuotationLangPO> quotationLangPOList;
}
