package com.sgs.testdatabiz.web.controllers;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: kevin-wj.wu
 * @create: 2024-01-05 15:20
 */
@RestController
@Api(value = "/health", tags = "health")
public class HealthController {

    @RequestMapping("/health")
    @ApiOperation(value = "health")
    public String health(){
        return "{\"msg\":\"ok\"}";
    }

}
