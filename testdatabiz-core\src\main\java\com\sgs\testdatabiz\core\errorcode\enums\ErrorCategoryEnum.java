package com.sgs.testdatabiz.core.errorcode.enums;

public enum ErrorCategoryEnum {

    SYSTEM_ERROR("500", "系统级错误"),
    REPORT_DATA_ERROR("400", "Report数据错误"),
    TEST_DATA_ERROR("300", "测试数据错误");

    private final String code;
    private final String description;

    ErrorCategoryEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ErrorCategoryEnum fromCode(String code) {
        for (ErrorCategoryEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
