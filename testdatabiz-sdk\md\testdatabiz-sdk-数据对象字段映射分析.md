# testdatabiz-sdk 数据对象字段映射分析文档

## 文档概述

【事实】本文档基于 testdatabiz-sdk 的源码分析，详细说明了输入数据结构（Input DTOs）与输出数据结构（Output DTOs）之间的字段映射关系。所有的映射关系都基于 `AbstractDataConvertHandler` 类中的实际转换逻辑。

【事实】主要转换处理器：`AbstractDataConvertHandler` 类包含了12个核心转换方法，负责将输入的 `ReportDataInput` 转换为输出的 `RdReportDataDTO`。

## 通用转换规则总结

### 字段路径层级分类

| 层级类型 | 路径模式 | 示例 | 说明 |
|----------|----------|------|------|
| **一级字段** | `ReportDataInput.字段名` | `ReportDataInput.header.reportNo` | 根对象直接属性 |
| **二级嵌套** | `ReportDataInput.对象.字段名` | `ReportDataInput.order.orderNo` | 嵌套对象属性 |
| **三级嵌套** | `ReportDataInput.对象.子对象.字段名` | `ReportDataInput.order.customerList[].customerName` | 深层嵌套对象 |
| **列表展开** | `ReportDataInput.列表[].字段名` | `ReportDataInput.quotationList[].quotationNo` | 列表中的元素属性 |
| **二级列表展开** | `ReportDataInput.列表[].子列表[].字段名` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemName` | 嵌套列表展开 |
| **跨对象关联** | 通过ID在不同列表中查找 | `analyteInstanceId` 关联 `analyteList[]` | 对象间关联查找 |

### 转换模式分类

| 转换模式 | 描述 | 使用场景 | 示例 |
|----------|------|----------|------|
| **直接映射** | 字段值直接复制 | 相同类型、相同语义的字段 | `customerName` → `customerName` |
| **类型转换** | 数据类型转换 | Integer→Long, Object→String等 | `systemId`的Integer→Long转换 |
| **枚举转换** | 枚举值转换为描述文本 | 枚举值→标签文本 | `contactUsage` → `contactUsageLabel` |
| **JSON转换** | 对象/列表转换为JSON字符串 | 复杂对象序列化 | `languageList` → JSON字符串 |
| **嵌套展开** | 嵌套对象字段提升到父级 | 对象扁平化 | `payer.customerName` → `payerCustomerName` |
| **列表展开** | 列表中每个元素生成独立记录 | 一对多关系处理 | `serviceItemList[]` 展开为多个报价单记录 |
| **跨对象引用** | 从其他对象获取字段值 | 补充关联信息 | 从`header`获取`reportNo`到其他对象 |
| **关联查找** | 通过ID在其他列表中查找信息 | 对象间关联 | 通过`analyteInstanceId`查找分析物信息 |

### 空值处理策略

```java
// 1. 使用Func.isNotEmpty()检查
if (Func.isNotEmpty(sourceValue)) {
    targetDTO.setTargetField(sourceValue);
}

// 2. 使用Optional进行安全访问
Optional.ofNullable(sourceObject)
    .map(Source::getNestedObject)
    .map(Nested::getFieldValue)
    .ifPresent(targetDTO::setTargetField);

// 3. 三元运算符处理默认值
targetDTO.setField(Func.isEmpty(sourceValue) ? defaultValue : sourceValue);
```

### 集合处理模式

| 处理模式 | 代码模式 | 应用场景 |
|----------|----------|----------|
| **一对多展开** | `sourceList.forEach(item -> {...})` | 列表中每个元素生成独立记录 |
| **属性合并** | `BeanUtils.copyProperties(source, target)` | 多个对象属性合并到目标对象 |
| **关联查找** | `list.stream().filter(...).findFirst()` | 在列表中按条件查找特定元素 |
| **条件过滤** | `list.stream().filter(condition)` | 按业务条件过滤列表 |
| **映射转换** | `list.stream().map(converter)` | 列表元素类型转换 |

### 业务规则总结

#### 1 数据来源优先级

1. **报告基础信息优先**：`reportNo`, `systemId`等从header获取
2. **订单信息补充**：`orderNo`, `rootOrderNo`等从order获取  
3. **局部信息覆盖**：对象自身字段优先于全局字段

#### 2 标识符生成规则

- **系统ID转换**：所有`systemId`统一转换为Long类型
- **订单号统一**：同一报告下所有对象使用相同的订单号
- **实例ID保持**：各种`instanceId`保持原值不变

#### 3 关系维护

- **父子关系**：通过`instanceId`维护对象间关联
- **跨对象引用**：通过ID在不同列表中查找关联对象
- **层级关系**：保持原始数据的嵌套层级关系

#### 4 状态管理

- **激活状态**：所有对象都包含`activeIndicator`字段
- **枚举转换**：状态枚举值转换为可读标签
- **一致性保证**：相关对象的状态保持一致

## 可视化图表

### 整体架构映射关系图

```mermaid
graph TB
    subgraph "输入数据结构"
        Input[ReportDataInput]
        Input --> Header["header: RdReportInput"]
        Input --> Order["order: RdOrderInput"] 
        Input --> TestSample["testSampleList: List RdTestSampleInput"]
        Input --> Quotation["quotationList: List RdQuotationInput"]
        Input --> TestLine["testLineList: List RdTestLineInput"]
        Input --> TestResult["reportTestResultList: List RdTestResultInput"]
        
        Order --> Customer["customerList: List RdCustomerInput"]
        Quotation --> ServiceItem["serviceItemList: List RdServiceItemInput"]
        TestLine --> Analyte["analyteList: List RdAnalyteInput"]
        TestResult --> ResultDetail["testResult: RdTestResultResultInput"]
        ResultDetail --> ResultRel["testResultFullNameRel: RdTestResultNameInput"]
    end
    
    subgraph "转换处理器"
        Handler[AbstractDataConvertHandler]
    end
    
    subgraph "输出数据结构"
        Output[RdReportDataDTO]
        Output --> ReportDTO["header: RdReportDTO"]
        Output --> OrderDTO["order: RdOrderDTO"]
        Output --> TestSampleDTO["testSampleList: List RdTestSampleDTO"]
        Output --> QuotationDTO["quotationList: List RdQuotationDTO"]
        Output --> CustomerDTO["customerList: List RdCustomerDTO"]
        Output --> TestResultDTO["reportTestResultList: List RdReportTestResultDTO"]
    end
    
    Input --> Handler
    Handler --> Output
```

### 核心字段映射关系图

```mermaid
graph LR
    subgraph "报价单映射"
        Q1["ReportDataInput.quotationList.quotationNo"]
        Q2["ReportDataInput.quotationList.serviceItemList.serviceItemName"]
        Q1 --> QO1["RdQuotationDTO.quotationNo"]
        Q2 --> QO2["RdQuotationDTO.serviceItemName"]
    end
    
    subgraph "客户映射"
        C1["ReportDataInput.order.customerList.customerName"]
        C2["ReportDataInput.order.customerList.customerContactList.contactName"]
        C1 --> CO1["RdCustomerDTO.customerName"]
        C2 --> CO2["RdCustomerDTO.contactPersonName"]
    end
    
    subgraph "测试结果映射"
        T1["ReportDataInput.reportTestResultList.testResult.resultValue"]
        T2["ReportDataInput.reportTestResultList.testResult.testResultFullNameRel.analyteInstanceId"]
        T3["ReportDataInput.testLineList.analyteList.analyteName"]
        T1 --> TO1["RdReportTestResultDTO.resultValue"]
        T2 -.->|关联查找| T3
        T3 --> TO2["RdReportTestResultDTO.analyteName"]
    end
```

## 整体对象对应关系概览

### 输入输出数据结构总体映射图

```mermaid
graph TB
    %% 输入数据结构
    subgraph "输入数据结构 (ReportDataInput)"
        A[ReportDataInput 根对象]
        A --> A1["header: RdReportInput<br/>报告头信息"]
        A --> A2["order: RdOrderInput<br/>订单信息"]
        A --> A3["testSampleList: List RdTestSampleInput<br/>测试样本列表"]
        A --> A4["quotationList: List RdQuotationInput<br/>报价单列表"]
        A --> A5["testLineList: List RdTestLineInput<br/>测试线列表"]
        A --> A6["reportTestResultList: List RdTestResultInput<br/>测试结果列表"]
        A --> A7["invoiceList: List RdInvoiceInput<br/>发票列表"]
        A --> A8["attachmentList: List RdAttachmentInput<br/>附件列表"]
        
        %% 嵌套对象展开
        A1 --> A1_1["lab: RdLabInput<br/>实验室信息"]
        A1 --> A1_2["certificate: RdCertificateInput<br/>证书信息"]
        A1 --> A1_3["reportMatrixList: List RdReportMatrixInput<br/>测试矩阵列表"]
        
        A2 --> A2_1["customerList: List RdCustomerInput<br/>客户列表"]
        A2 --> A2_2["contactPersonList: List RdContactPersonInput<br/>联系人列表"]
        A2 --> A2_3["others: RdOrderOthersInput<br/>其他信息"]
        
        A3 --> A3_1["materialAttr: RdMaterialAttrInput<br/>材料属性"]
        A3 --> A3_2["testSampleGroupList: List RdTestSampleGroupInput<br/>样本组列表"]
        
        A4 --> A4_1["serviceItemList: List RdServiceItemInput<br/>服务项目列表"]
        A4 --> A4_2["payer: RdCustomerInput<br/>付款方信息"]
        
        A5 --> A5_1["analyteList: List RdAnalyteInput<br/>分析物列表"]
        
        A6 --> A6_1["testResult: RdTestResultResultInput<br/>测试结果详情"]
        A6_1 --> A6_1_1["testResultFullNameRel: RdTestResultNameInput<br/>关联关系"]
    end
    
    %% 输出数据结构
    subgraph "输出数据结构 (RdReportDataDTO)"
        B[RdReportDataDTO 根对象]
        B --> B1["header: RdReportDTO<br/>报告基础信息"]
        B --> B2["order: RdOrderDTO<br/>订单信息"]
        B --> B3["testSampleList: List RdTestSampleDTO<br/>测试样本列表"]
        B --> B4["quotationList: List RdQuotationDTO<br/>报价单列表"]
        B --> B5["customerList: List RdCustomerDTO<br/>客户列表"]
        B --> B6["reportTestResultList: List RdReportTestResultDTO<br/>测试结果列表"]
        B --> B7["reportInvoiceList: List RdReportInvoiceDTO<br/>发票列表"]
        B --> B8["attachmentList: List RdAttachmentDTO<br/>附件列表"]
        B --> B9["reportMatrixList: List RdReportMatrixDTO<br/>测试矩阵列表"]
        B --> B10["reportTrfRelList: List RdReportTrfRelDTO<br/>TRF关联关系"]
        B --> B11["dffMappingList: List RdDffMappingDTO<br/>DFF映射列表"]
    end
    
    %% 映射关系
    A1 -.->|convertReport| B1
    A2 -.->|convertOrder| B2
    A3 -.->|convertTestSampleList| B3
    A4 -.->|convertQuotation| B4
    A2_1 -.->|convertCustomerList| B5
    A6 -.->|convertReportTestResultList| B6
    A7 -.->|convertReportInvoiceList| B7
    A8 -.->|convertAttachmentList| B8
    A1_3 -.->|convertReportMatrixList| B9
    A -.->|convertTrfRel| B10
    A -.->|getDffMapping| B11
```

### 核心转换方法与对象映射关系

| 转换方法 | 输入对象路径 | 输出对象 | 转换类型 | 说明 |
|----------|-------------|----------|----------|------|
| `convertReport()` | `ReportDataInput.header` | `RdReportDTO` | 1对1 | 报告基础信息转换 |
| `convertOrder()` | `ReportDataInput.order` | `RdOrderDTO` | 1对1+关联扩展 | 订单信息转换，包含客户关联 |
| `convertTestSampleList()` | `ReportDataInput.testSampleList` | `List<RdTestSampleDTO>` | 1对多 | 测试样本列表转换 |
| `convertQuotation()` | `ReportDataInput.quotationList` | `List<RdQuotationDTO>` | 1对多+展开 | 报价单转换，服务项目展开 |
| `convertCustomerList()` | `ReportDataInput.order.customerList` | `List<RdCustomerDTO>` | 1对多+展开 | 客户信息转换，联系人展开 |
| `convertReportTestResultList()` | `ReportDataInput.reportTestResultList` | `List<RdReportTestResultDTO>` | 1对多+关联 | 测试结果转换，多对象关联 |
| `convertReportInvoiceList()` | `ReportDataInput.invoiceList` | `List<RdReportInvoiceDTO>` | 1对多 | 发票信息转换 |
| `convertAttachmentList()` | `ReportDataInput.attachmentList` | `List<RdAttachmentDTO>` | 1对多 | 附件信息转换 |
| `convertReportMatrixList()` | `ReportDataInput.header.reportMatrixList` | `List<RdReportMatrixDTO>` | 1对多 | 测试矩阵转换 |
| `convertTrfRel()` | `ReportDataInput` (全局) | `List<RdReportTrfRelDTO>` | 复合生成 | TRF关联关系生成 |
| `getDffMapping()` | `ReportDataInput` (元数据) | `List<RdDffMappingDTO>` | 外部API调用 | DFF动态字段映射 |

### 字段路径表示约定

本文档中的字段路径表示遵循以下约定：

```
根对象.子对象.属性字段
例如：ReportDataInput.header.reportNo
     ReportDataInput.quotationList[].serviceItemList[].serviceItemInstanceId
     ReportDataInput.order.customerList[].customerContactList[].contactName
```

说明：
- `[]` 表示集合/列表中的元素
- `.` 表示对象属性访问路径
- 完整路径从 `ReportDataInput` 根对象开始

## 1. 报价单字段映射 (Quotation)

### 1.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 报价单信息 | `ReportDataInput.quotationList[]` → `RdQuotationInput` | `RdQuotationDTO` | `convertQuotation()` | 一对多展开 |
| 服务项目 | `ReportDataInput.quotationList[].serviceItemList[]` → `RdServiceItemInput` | 展开到`RdQuotationDTO` | 服务项目信息展开 | 嵌套展开 |

### 1.2 报价单对象层级结构

```
ReportDataInput.quotationList[] (报价单列表)
└── RdQuotationInput (单个报价单对象)
    ├── orderNo (订单号)
    ├── systemId (系统ID)
    ├── quotationNo (报价单编号)
    ├── payer: RdCustomerInput (付款方客户信息)
    │   ├── customerName (客户名称)
    │   └── bossNo (Boss编号)
    ├── currency (货币代码)
    ├── adjustmentAmount (调整金额)
    ├── finalAmount (最终金额)
    ├── serviceItemList[] (服务项目列表)
    │   └── RdServiceItemInput (单个服务项目)
    │       ├── serviceItemInstanceId (服务项目实例ID)
    │       ├── serviceItemType (服务项目类型)
    │       ├── serviceItemName (服务项目名称)
    │       ├── ppNo (PP编号)
    │       ├── citationType (引用类型)
    │       ├── serviceItemListUnitPrice (列表单价)
    │       ├── exchangeRate (汇率)
    │       └── ... (其他价格和数量字段)
    └── ... (其他报价单字段)
```

### 1.3 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **基础标识字段** |
| `systemId` | `ReportDataInput.quotationList[].systemId` | 类型转换 | `Long.parseLong(quotation.getSystemId().toString())` |
| `orderNo` | `ReportDataInput.order.orderNo` | 跨对象引用 | 真实订单号 |
| `rootOrderNo` | `ReportDataInput.order.rootOrderNo` | 跨对象引用 | 逻辑订单号 |
| `reportNo` | `ReportDataInput.header.reportNo` | 跨对象引用 | 报告编号 |
| `quotationNo` | `ReportDataInput.quotationList[].quotationNo` | 直接映射 | 报价单编号 |
| `quotationInstanceId` | `ReportDataInput.quotationList[].quotationInstanceId` | 直接映射 | 报价单实例ID |
| `quotationVersionId` | `ReportDataInput.quotationList[].quotationVersionId` | 直接映射 | 报价单版本ID |
| `quotationStatus` | `ReportDataInput.quotationList[].quotationStatus` | 直接映射 | 报价单状态 |
| **币种和支付方信息** |
| `currencyCode` | `ReportDataInput.quotationList[].currency` | 直接映射 | 货币代码 |
| `payerCustomerName` | `ReportDataInput.quotationList[].payer.customerName` | 嵌套对象映射 | 付款方客户名称 |
| `payerBossNumber` | `ReportDataInput.quotationList[].payer.bossNo` | 嵌套对象映射 | 付款方Boss编号 |
| **服务项目信息展开（二级嵌套）** |
| `serviceItemInstanceId` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemInstanceId` | 列表展开 | 服务项目实例ID |
| `serviceItemType` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemType` | 列表展开 | 服务项目类型 |
| `serviceItemName` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemName` | 列表展开 | 服务项目名称 |
| `serviceItemSeq` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemSeq` | 列表展开 | 服务项目序号 |
| `quantity` | `ReportDataInput.quotationList[].serviceItemList[].quantity` | 列表展开 | 数量 |
| **价格信息（二级嵌套）** |
| `serviceItemListUnitPrice` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemListUnitPrice` | 列表展开 | 列表单价 |
| `serviceItemSalesUnitPrice` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemSalesUnitPrice` | 列表展开 | 销售单价 |
| `serviceItemDiscount` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemDiscount` | 列表展开 | 折扣 |
| `serviceItemExchangeRatePrice` | `ReportDataInput.quotationList[].serviceItemList[].exchangeRate` | 列表展开 | 汇率价格 |
| `serviceItemSurChargePrice` | `ReportDataInput.quotationList[].serviceItemList[].surCharge` | 列表展开 | 附加费 |
| `serviceItemNetAmount` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemNetAmount` | 列表展开 | 净金额 |
| `serviceItemVatAmount` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemVATAmount` | 列表展开 | 增值税金额 |
| `serviceItemTotalAmount` | `ReportDataInput.quotationList[].serviceItemList[].serviceItemTotalAmount` | 列表展开 | 总金额 |
| `adjustmentAmount` | `ReportDataInput.quotationList[].adjustmentAmount` | 直接映射 | 调整金额 |
| `finalAmount` | `ReportDataInput.quotationList[].finalAmount` | 直接映射 | 最终金额 |
| **测试和引用信息（二级嵌套）** |
| `ppNo` | `ReportDataInput.quotationList[].serviceItemList[].ppNo` | 列表展开 | PP编号 |
| `ppName` | `ReportDataInput.quotationList[].serviceItemList[].ppName` | 列表展开 | PP名称 |
| `testLineId` | `ReportDataInput.quotationList[].serviceItemList[].testLineId` | 列表展开 | 测试线ID |
| `citationType` | `ReportDataInput.quotationList[].serviceItemList[].citationType` | 类型转换+列表展开 | `String.valueOf(serviceItem.getCitationType())` |
| `citationId` | `ReportDataInput.quotationList[].serviceItemList[].citationId` | 列表展开 | 引用ID |
| `citationName` | `ReportDataInput.quotationList[].serviceItemList[].citationName` | 列表展开 | 引用名称 |
| `citationTypeLabel` | `ReportDataInput.quotationList[].serviceItemList[].citationType` | 枚举转换+列表展开 | `CitationType.findType(citationType).getMessage()` |
| **状态字段** |
| `activeIndicator` | `ReportDataInput.quotationList[].activeIndicator` | 直接映射 | 激活指示器 |

### 1.4 特殊转换规则

1. **引用类型标签转换**：
   ```java
   // 引用类型枚举转换
   Integer citationType = serviceItem.getCitationType();
   CitationType type = CitationType.findType(citationType);
   if (Func.isNotEmpty(type)) {
       quotationDTO.setCitationTypeLabel(type.getMessage());
   }
   ```

2. **服务项目列表展开**：
   ```java
   // 每个服务项目展开为独立的报价单记录
   quotation.getServiceItemList().forEach(serviceItem -> {
       RdQuotationDTO quotationDTO = BeanUtils.copy(quotation, RdQuotationDTO.class);
       // 复制服务项目字段到报价单DTO
       BeanUtils.copyProperties(serviceItem, quotationDTO);
       quotationDTOList.add(quotationDTO);
   });
   ``` 

## 2. 客户信息字段映射 (Customer)

### 2.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 客户基础信息 | `ReportDataInput.order.customerList[]` → `RdCustomerInput` | `RdCustomerDTO` | `convertCustomerList()` | 一对多展开 |
| 联系人信息 | `ReportDataInput.order.customerList[].customerContactList[]` → `RdContactPersonInput` | 展开到`RdCustomerDTO` | 联系人信息展开 | 二级嵌套展开 |

### 2.2 客户对象层级结构

```
ReportDataInput.order.customerList[] (客户列表)
└── RdCustomerInput (单个客户对象)
    ├── customerName (客户名称)
    ├── bossNo (Boss编号)
    ├── customerUsage (客户用途)
    ├── languageList (语言列表)
    ├── marketSegmentCode (市场细分代码)
    ├── customerContactList[] (联系人列表)
    │   └── RdContactPersonInput (单个联系人)
    │       ├── contactName (联系人姓名)
    │       ├── contactEmail (联系人邮箱)
    │       ├── contactPhone (联系人电话)
    │       ├── contactUsage (联系用途)
    │       └── ... (其他联系人字段)
    └── ... (其他客户字段)
```

### 2.3 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **基础标识字段** |
| `systemId` | `ReportDataInput.order.systemId` | 跨对象引用 | 从订单获取系统ID |
| `orderNo` | `ReportDataInput.order.orderNo` | 跨对象引用+统一设置 | 所有客户记录统一设置相同订单号 |
| `rootOrderNo` | `ReportDataInput.order.rootOrderNo` | 跨对象引用 | 根订单号 |
| `reportNo` | `ReportDataInput.header.reportNo` | 跨对象引用 | 报告编号 |
| **客户基础信息** |
| `customerName` | `ReportDataInput.order.customerList[].customerName` | 直接映射 | 客户名称 |
| `bossNo` | `ReportDataInput.order.customerList[].bossNo` | 直接映射 | Boss编号 |
| `customerUsage` | `ReportDataInput.order.customerList[].customerUsage` | 直接映射 | 客户用途 |
| `customerBillingCode` | `ReportDataInput.order.customerList[].customerBillingCode` | 直接映射 | 客户账单代码 |
| `customerGroupName` | `ReportDataInput.order.customerList[].customerGroupName` | 直接映射 | 客户组名称 |
| `customerGroupCode` | `ReportDataInput.order.customerList[].customerGroupCode` | 直接映射 | 客户组代码 |
| `languageList` | `ReportDataInput.order.customerList[].languageList` | JSON转换 | `JSON.toJSONString(customer.getLanguageList())` |
| `marketSegmentCode` | `ReportDataInput.order.customerList[].marketSegmentCode` | 直接映射 | 市场细分代码 |
| **联系人信息展开（二级嵌套）** |
| `contactPersonName` | `ReportDataInput.order.customerList[].customerContactList[].contactName` | 二级列表展开 | 联系人姓名 |
| `contactPersonEmail` | `ReportDataInput.order.customerList[].customerContactList[].contactEmail` | 二级列表展开 | 联系人邮箱 |
| `contactPersonMobile` | `ReportDataInput.order.customerList[].customerContactList[].contactPhone` | 二级列表展开 | 联系人手机号 |
| `contactPersonSalutation` | `ReportDataInput.order.customerList[].customerContactList[].contactSalutation` | 二级列表展开 | 联系人称谓 |
| `contactUsage` | `ReportDataInput.order.customerList[].customerContactList[].contactUsage` | 二级列表展开 | 联系用途 |
| `contactUsageLabel` | `ReportDataInput.order.customerList[].customerContactList[].contactUsage` | 枚举转换+二级展开 | `ContactUsage.findType(contactUsage).getMessage()` |
| `responsibleTeamCode` | `ReportDataInput.order.customerList[].customerContactList[].responsibleTeamCode` | 二级列表展开 | 负责团队代码 |
| **地址信息（二级嵌套）** |
| `address` | `ReportDataInput.order.customerList[].customerContactList[].address` | 二级列表展开 | 联系地址 |
| `city` | `ReportDataInput.order.customerList[].customerContactList[].city` | 二级列表展开 | 城市 |
| `state` | `ReportDataInput.order.customerList[].customerContactList[].state` | 二级列表展开 | 州/省 |
| `country` | `ReportDataInput.order.customerList[].customerContactList[].country` | 二级列表展开 | 国家 |
| `postalCode` | `ReportDataInput.order.customerList[].customerContactList[].postalCode` | 二级列表展开 | 邮政编码 |
| **状态字段** |
| `activeIndicator` | `ReportDataInput.order.customerList[].activeIndicator` | 直接映射 | 激活指示器 |

### 2.4 特殊转换规则

1. **语言列表JSON转换**：
   ```java
   // 语言列表转换为JSON字符串
   if (Func.isNotEmpty(customer.getLanguageList())) {
       customerDTO.setLanguageList(JSON.toJSONString(customer.getLanguageList()));
   }
   ```

2. **联系人信息展开**：
   ```java
   // 每个联系人生成独立的客户记录
   customer.getCustomerContactList().forEach(contact -> {
       RdCustomerDTO customerDTO = BeanUtils.copy(customer, RdCustomerDTO.class);
       // 复制联系人字段到客户DTO
       BeanUtils.copyProperties(contact, customerDTO);
       customerDTOList.add(customerDTO);
   });
   ```

3. **联系用途标签转换**：
   ```java
   // 联系用途枚举转换
   String contactUsage = contact.getContactUsage();
   ContactUsage usage = ContactUsage.findType(contactUsage);
   if (Func.isNotEmpty(usage)) {
       customerDTO.setContactUsageLabel(usage.getMessage());
   }
   ```

4. **订单号统一设置**：
   ```java
   // 所有客户记录都设置相同的订单号
   customerDTOList.forEach(customerDTO -> {
       customerDTO.setOrderNo(orderNo);
       customerDTO.setRootOrderNo(rootOrderNo);
   });
   ``` 

## 3. 报告基础信息字段映射 (Report Header)

### 3.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 报告基础信息 | `ReportDataInput.header` → `RdReportInput` | `RdReportDTO` | `convertReport()` | 一对一转换 |
| 服务需求信息 | `ReportDataInput.order.serviceRequirement.report` → `RdServiceRequirementReportDTO` | 展开到`RdReportDTO` | 服务需求展开 | 跨对象引用 |
| 延迟信息 | `ReportDataInput.order.others.delay` → `RdDelayDTO` | 展开到`RdReportDTO` | 延迟信息展开 | 跨对象引用 |

### 3.2 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **基础标识字段** |
| `labId` | `ReportDataInput.header.lab.labId` | 安全空值检查 | `Func.isNotEmpty(lab.getLabId()) ? lab.getLabId() : null` |
| `reportId` | `ReportDataInput.header.reportId` | 直接映射 | 报告ID |
| `reportInstanceId` | `ReportDataInput.header.reportId` | 直接映射 | 报告实例ID（同reportId） |
| `reportNo` | `ReportDataInput.header.reportNo` | 直接映射 | 报告编号 |
| `originalReportNo` | `ReportDataInput.header.originalReportNo` | 直接映射 | 原始报告编号 |
| **状态相关字段** |
| `reportStatus` | `ReportDataInput.header.reportStatus` | 直接映射 | 报告状态代码 |
| `reportStatusLabel` | `ReportDataInput.header.reportStatus` | 枚举转换 | `ReportStatusEnum.getCode(reportStatus).getMessage()` |
| `activeIndicator` | `ReportDataInput.header.activeIndicator` | 直接映射 | 激活指示器 |
| **证书相关字段** |
| `reportCertificateName` | `ReportDataInput.header.certificateName` | 直接映射 | 证书名称 |
| `reportCertificateList` | `ReportDataInput.header.reportCertificateList` | 直接映射 | 证书列表 |
| **人员和时间字段** |
| `reportCreatedBy` | `ReportDataInput.header.createBy` | 直接映射 | 报告创建人 |
| `reportCreatedDate` | `ReportDataInput.header.createDate` | 直接映射 | 报告创建日期 |
| `reportApproveBy` | `ReportDataInput.header.approveBy` | 直接映射 | 报告批准人 |
| `reportApproveDate` | `ReportDataInput.header.approveDate` | 直接映射 | 报告批准日期 |
| `softcopyDeliveryDate` | `ReportDataInput.header.softCopyDeliveryDate` | 直接映射 | 软拷贝交付日期 |
| `lastModifiedTimestamp` | `ReportDataInput.header.lastModifiedTimestamp` | 直接映射 | 最后修改时间戳 |
| `actualTat` | `ReportDataInput.header.actualTat` | 直接映射 | 实际周转时间 |
| **服务需求信息（跨对象引用）** |
| `reportHeader` | `ReportDataInput.order.serviceRequirement.report.reportHeader` | 跨对象引用 | 报告头信息 |
| `reportAddress` | `ReportDataInput.order.serviceRequirement.report.reportAddress` | 跨对象引用 | 报告地址 |
| `reportDeliveredTo` | `ReportDataInput.order.serviceRequirement.report.softcopy.deliveryTo` | 嵌套跨对象引用 | 报告交付对象 |
| **延迟信息（跨对象引用）** |
| `delayDay` | `ReportDataInput.order.others.delay.delayDays` | 跨对象引用 | 延迟天数 |
| `delayReason` | `ReportDataInput.order.others.delay.delayRemark` | 跨对象引用 | 延迟原因 |
| **备注信息** |
| `reportRemark` | `ReportDataInput.header.reportRemark` | 直接映射 | 报告备注 |
| **SCI-1378扩展字段** |
| `reportType` | `ReportDataInput.header.reportType` | 直接映射 | 报告类型 |
| `reportSource` | `ReportDataInput.header.reportSource` | 直接映射 | 报告来源 |
| `testingType` | `ReportDataInput.header.testingType` | 直接映射 | 测试类型 |
| `countryOfDestination` | `ReportDataInput.header.countryOfDestination` | 直接映射 | 目的地国家 |
| `metaData` | `ReportDataInput.header.metaData` | 直接映射 | 元数据 |

### 3.3 特殊转换规则

1. **实验室ID安全访问**：
   ```java
   // 安全访问嵌套对象，避免空指针异常
   RdLabInput lab = header.getLab();
   Long labId = null;
   if(Func.isNotEmpty(lab)){
       labId = Func.isNotEmpty(lab.getLabId()) ? lab.getLabId() : null;
   }
   reportDTO.setLabId(labId);
   ```

2. **报告状态枚举转换**：
   ```java
   // 状态代码转换为标签
   Integer reportStatus = header.getReportStatus();
   ReportStatusEnum statusEnum = ReportStatusEnum.getCode(reportStatus);
   if (Func.isNotEmpty(statusEnum)) {
       reportDTO.setReportStatusLabel(statusEnum.getMessage());
   }
   ```

3. **跨对象服务需求信息获取**：
   ```java
   // 从订单的服务需求中获取报告相关信息
   if (Func.isNotEmpty(order.getServiceRequirement()) && 
       Func.isNotEmpty(order.getServiceRequirement().getReport())) {
       RdServiceRequirementReportDTO report = order.getServiceRequirement().getReport();
       reportDTO.setReportHeader(report.getReportHeader());
       reportDTO.setReportAddress(report.getReportAddress());
   }
   ```

## 4. 订单信息字段映射 (Order)

### 4.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 订单基础信息 | `ReportDataInput.order` → `RdOrderDTO` | `RdOrderDTO` | `convertOrder()` | 一对一转换 |
| 客户信息关联 | 通过`customerUsage`筛选不同角色客户 | 展开到`RdOrderDTO` | 客户信息关联 | 条件查找 |
| 联系人信息 | `ReportDataInput.order.contactPersonList[]`过滤CS用途 | 展开到`RdOrderDTO` | 联系人筛选 | 条件过滤 |
| 延迟取消暂停信息 | `ReportDataInput.order.others` → `RdOrderOthersDTO` | 展开到`RdOrderDTO` | 嵌套对象展开 | 嵌套映射 |

### 4.2 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **基础标识字段** |
| `systemId` | `ReportDataInput.order.systemId` | 直接映射 | 系统ID |
| `orderId` | `ReportDataInput.order.orderId` | 直接映射 | 订单ID |
| `orderInstanceId` | `ReportDataInput.order.orderId` | 直接映射 | 订单实例ID（同orderId） |
| `orderNo` | `ReportDataInput.order.orderNo` | 直接映射 | 订单号 |
| `rootOrderNo` | `ReportDataInput.order.rootOrderNo` | 直接映射 | 根订单号 |
| `orderType` | `ReportDataInput.order.orderType` | 直接映射 | 订单类型 |
| **实验室信息** |
| `labId` | `ReportDataInput.header.lab.labId` | 跨对象引用 | 实验室ID |
| `labCode` | `ReportDataInput.header.lab.labCode` | 跨对象引用 | 实验室代码 |
| `buCode` | `ReportDataInput.header.lab.buCode` | 跨对象引用 | 业务单元代码 |
| `idbLab` | `ReportDataInput.order.idbLab` | 直接映射 | IDB实验室 |
| `topsLabId` | `ReportDataInput.order.topsLabId` | 直接映射 | TOPS实验室ID |
| `execLabCode` | `ReportDataInput.order.topsLabCode` | 直接映射 | 执行实验室代码 |
| `locationCode` | `ReportDataInput.header.lab.locationCode` | 跨对象引用 | 位置代码 |
| **客户信息（通过customerUsage查找）** |
| `applicantName` | `ReportDataInput.order.customerList[customerUsage=Applicant].customerName` | 条件查找映射 | 申请人姓名 |
| `applicantBossNo` | `ReportDataInput.order.customerList[customerUsage=Applicant].bossNo` | 条件查找映射 | 申请人Boss编号 |
| `payerName` | `ReportDataInput.order.customerList[customerUsage=Payer].customerName` | 条件查找映射 | 付款方名称 |
| `payerBossNo` | `ReportDataInput.order.customerList[customerUsage=Payer].bossNo` | 条件查找映射 | 付款方Boss编号 |
| `buyerName` | `ReportDataInput.order.customerList[customerUsage=Buyer].customerName` | 条件查找映射 | 买方名称 |
| `buyerBossNo` | `ReportDataInput.order.customerList[customerUsage=Buyer].bossNo` | 条件查找映射 | 买方Boss编号 |
| `buyerGroupName` | `ReportDataInput.order.customerList[customerUsage=Buyer].customerGroupName` | 条件查找映射 | 买方组名称 |
| `buyerGroupCode` | `ReportDataInput.order.customerList[customerUsage=Buyer].customerGroupCode` | 条件查找映射 | 买方组代码 |
| `agentName` | `ReportDataInput.order.customerList[customerUsage=Agent].customerName` | 条件查找映射 | 代理商名称 |
| `agentBossNumber` | `ReportDataInput.order.customerList[customerUsage=Agent].bossNo` | 条件查找映射 | 代理商Boss编号 |
| `agentGroupName` | `ReportDataInput.order.customerList[customerUsage=Agent].customerGroupName` | 条件查找映射 | 代理商组名称 |
| `agentGroupCode` | `ReportDataInput.order.customerList[customerUsage=Agent].customerGroupCode` | 条件查找映射 | 代理商组代码 |
| `kaCustomerDeptCode` | `ReportDataInput.order.customerList[customerUsage=Buyer].marketSegmentCode` | 条件查找映射 | KA客户部门代码 |
| **联系人信息（通过contactUsage=CS筛选）** |
| `csName` | `ReportDataInput.order.contactPersonList[contactUsage=CS].contactName` | 条件过滤映射 | CS姓名 |
| `responsibleTeamCode` | `ReportDataInput.order.contactPersonList[contactUsage=CS].responsibleTeamCode` | 条件过滤映射 | 负责团队代码 |
| **支付和金额信息** |
| `quoteCurrencyId` | `ReportDataInput.order.payment.currency` | 嵌套对象映射 | 报价币种ID |
| `totalAmount` | `ReportDataInput.order.payment.totalAmount` | 嵌套对象映射 | 总金额 |
| **时间相关字段** |
| `createdDate` | `ReportDataInput.order.createDate` | 直接映射 | 创建日期 |
| `sampleConfirmDate` | `ReportDataInput.order.sampleReceiveDate` | 直接映射 | 样本确认日期 |
| `serviceStartDate` | `ReportDataInput.order.serviceStartDate` | 直接映射 | 服务开始日期 |
| `caseDueDate` | `ReportDataInput.order.orderExpectDueDate` | 直接映射 | 案例到期日期 |
| `actualTat` | `ReportDataInput.order.actualTat` | 直接映射 | 实际周转时间 |
| `tat` | `ReportDataInput.order.tat` | 直接映射 | 周转时间 |
| **状态和类别** |
| `orderStatus` | `ReportDataInput.order.orderStatus` | 直接映射 | 订单状态 |
| `serviceLevel` | `ReportDataInput.order.serviceType` | 直接映射 | 服务级别 |
| `operationType` | `ReportDataInput.order.operationType` | 直接映射 | 操作类型 |
| `productCategory` | `ReportDataInput.order.productCategory` | 直接映射 | 产品类别 |
| `productSubCategory` | `ReportDataInput.order.productSubCategory` | 直接映射 | 产品子类别 |
| **标识和备注** |
| `createdBy` | `ReportDataInput.order.createBy` | 直接映射 | 创建人 |
| `remark` | `ReportDataInput.order.others.orderRemark` | 嵌套对象映射 | 订单备注 |
| `activeIndicator` | `ReportDataInput.order.activeIndicator` | 直接映射 | 激活指示器 |
| **标志位信息** |
| `toDmFlag` | `ReportDataInput.order.flags.toDMFlag` | 嵌套对象映射 | 发送DM标志 |
| `selfTestFlag` | `ReportDataInput.order.flags.selfTestFlag` | 嵌套对象映射 | 自测标志 |
| **关系信息** |
| `parcelNo` | `ReportDataInput.order.relationship.parent.parcelNoList` | 深度嵌套+连接 | `parcelNoList.stream().collect(Collectors.joining(","))` |
| **延迟取消暂停信息** |
| `delayDay` | `ReportDataInput.order.others.delay.delayDays` | 深度嵌套映射 | 延迟天数 |
| `delayReason` | `ReportDataInput.order.others.delay.delayRemark` | 深度嵌套映射 | 延迟原因 |
| `reasonsForCancelOrder` | `ReportDataInput.order.others.cancel.cancelRemark` | 深度嵌套映射 | 取消订单原因 |
| `reasonsForOnHold` | `ReportDataInput.order.others.pending.pendingRemark` | 深度嵌套映射 | 暂停原因 |

### 4.3 特殊转换规则

1. **客户信息条件查找**：
   ```java
   // 根据客户用途查找对应角色的客户信息
   private static RdCustomerDTO findCustomer(List<RdCustomerDTO> customerDTOS, Integer usage) {
       if (Func.isEmpty(customerDTOS)) {
           return new RdCustomerDTO();
       }
       return customerDTOS.stream()
           .filter(l -> Objects.equals(l.getCustomerUsage(), usage))
           .findFirst()
           .orElse(new RdCustomerDTO());
   }
   ```

2. **联系人信息筛选**：
   ```java
   // 筛选CS用途的联系人
   if (Func.isNotEmpty(contactPersonList)) {
       RdContactPersonDTO csContact = contactPersonList.stream()
           .filter(l -> Objects.equals(l.getContactUsage(), ContactUsage.CS.getType().toString()))
           .findFirst()
           .orElse(new RdContactPersonDTO());
       orderDTO.setCsName(csContact.getContactName());
   }
   ```

3. **包裹号连接处理**：
   ```java
   // 多个包裹号连接为字符串
   if (Func.isNotEmpty(relationship.getParent().getParcelNoList())) {
       orderDTO.setParcelNo(relationship.getParent().getParcelNoList()
           .stream().collect(Collectors.joining(",")));
   }
   ```

## 5. 测试样本字段映射 (Test Sample)

### 5.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 测试样本基础信息 | `ReportDataInput.testSampleList[]` → `RdTestSampleInput` | `RdTestSampleDTO` | `convertTestSampleList()` | 一对多转换 |
| 材料属性信息 | `ReportDataInput.testSampleList[].materialAttr` → `RdMaterialAttrInput` | 展开到`RdTestSampleDTO` | 材料属性展开 | 嵌套对象展开 |
| 样本组信息 | `ReportDataInput.testSampleList[].testSampleGroupList[]` → `RdTestSampleGroupInput` | `RdTestSampleGroupDTO` | 样本组列表转换 | 嵌套列表转换 |

### 5.2 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **基础标识字段** |
| `orderNo` | `ReportDataInput.order.orderNo` | 跨对象引用 | 订单号 |
| `testSampleInstanceId` | `ReportDataInput.testSampleList[].testSampleInstanceId` | 直接映射 | 测试样本实例ID |
| `parentTestSampleId` | `ReportDataInput.testSampleList[].parentTestSampleId` | 直接映射 | 父测试样本ID |
| `sampleNo` | `ReportDataInput.testSampleList[].testSampleNo` | 直接映射 | 样本编号 |
| `sampleName` | `ReportDataInput.testSampleList[].testSampleName` | 直接映射 | 样本名称 |
| `sampleType` | `ReportDataInput.testSampleList[].testSampleType` | 直接映射 | 样本类型 |
| `sampleSeq` | `ReportDataInput.testSampleList[].testSampleSeq` | 直接映射 | 样本序号 |
| `category` | `ReportDataInput.testSampleList[].category` | 直接映射 | 类别 |
| **材料属性信息展开** |
| `description` | `ReportDataInput.testSampleList[].materialAttr.materialDescription` | 嵌套对象映射 | 材料描述 |
| `composition` | `ReportDataInput.testSampleList[].materialAttr.materialTexture` | 嵌套对象映射 | 材料成分（纹理） |
| `color` | `ReportDataInput.testSampleList[].materialAttr.materialColor` | 嵌套对象映射 | 材料颜色 |
| `sampleRemark` | `ReportDataInput.testSampleList[].materialAttr.materialRemark` | 嵌套对象映射 | 样本备注 |
| `endUse` | `ReportDataInput.testSampleList[].materialAttr.materialEndUse` | 嵌套对象映射 | 最终用途 |
| `material` | `ReportDataInput.testSampleList[].materialAttr.materialName` | 嵌套对象映射 | 材料名称 |
| `otherSampleInfo` | `ReportDataInput.testSampleList[].materialAttr.materialOtherSampleInfo` | 嵌套对象映射 | 其他样本信息 |
| `applicableFlag` | `ReportDataInput.testSampleList[].materialAttr.applicableFlag` | 嵌套对象映射 | 适用标志 |
| **状态字段** |
| `activeIndicator` | `ReportDataInput.testSampleList[].activeIndicator` | 直接映射 | 激活指示器 |
| `lastModifiedTimestamp` | `ReportDataInput.testSampleList[].lastModifiedTimestamp` | 直接映射 | 最后修改时间戳 |

### 5.3 样本组信息映射（独立对象）

| 输出对象 | 输入对象路径 | 转换方式 | 说明 |
|----------|-------------|----------|------|
| `RdTestSampleGroupDTO` | `ReportDataInput.testSampleList[].testSampleGroupList[]` | `BeanUtil.copyProperties()` | 样本组信息完整复制 |

### 5.4 特殊转换规则

1. **材料属性安全访问**：
   ```java
   // 安全访问材料属性信息
   RdMaterialAttrInput materialAttr = l.getMaterialAttr();
   if (Func.isNotEmpty(materialAttr)) {
       sampleDTO.setDescription(materialAttr.getMaterialDescription());
       sampleDTO.setComposition(materialAttr.getMaterialTexture());
       sampleDTO.setColor(materialAttr.getMaterialColor());
       // ... 其他字段设置
   }
   ```

2. **样本组列表处理**：
   ```java
   // 处理嵌套的样本组列表
   List<RdTestSampleGroupInput> testSampleGroupList = l.getTestSampleGroupList();
   if (Func.isNotEmpty(testSampleGroupList)) {
       testSampleGroupList.forEach(v -> {
           RdTestSampleGroupDTO rdTestSampleGroupDTO = new RdTestSampleGroupDTO();
           BeanUtil.copyProperties(v, rdTestSampleGroupDTO);
           sampleGroupDTOS.add(rdTestSampleGroupDTO);
       });
   }
   ```

## 6. 附件信息字段映射 (Attachment)

### 6.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 报告文件附件 | `ReportDataInput.header.reportFileList[]` → `RdAttachmentInput` | `RdAttachmentDTO` | `convertAttachmentList()` | JSON序列化转换 |
| 订单附件 | `ReportDataInput.order.attachmentList[]` → `RdAttachmentDTO` | `RdAttachmentDTO` | 附件列表合并 | JSON序列化转换 |

### 6.2 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **报告文件附件（JSON转换）** |
| `全部字段` | `ReportDataInput.header.reportFileList[]` | JSON序列化转换 | `JSONObject.parseArray(JSONObject.toJSONString(reportFileList), RdAttachmentDTO.class)` |
| **订单附件（JSON转换+字段补充）** |
| `全部字段` | `ReportDataInput.order.attachmentList[]` | JSON序列化转换 | `JSONObject.parseArray(JSONObject.toJSONString(attachmentList), RdAttachmentDTO.class)` |
| `labId` | `ReportDataInput.header.lab.labId` | 跨对象引用补充 | 实验室ID |
| `orderNo` | `ReportDataInput.order.orderNo` | 跨对象引用补充 | 订单号 |

### 6.3 特殊转换规则

1. **JSON序列化转换**：
   ```java
   // 报告文件列表JSON转换
   List<RdAttachmentInput> reportFileList = dataInput.getHeader().getReportFileList();
   List<RdAttachmentDTO> rdAttachmentDTOS = JSONObject.parseArray(
       JSONObject.toJSONString(reportFileList), RdAttachmentDTO.class);
   ```

2. **附件列表合并处理**：
   ```java
   // 订单附件列表处理并合并
   if (Func.isNotEmpty(dataInput.getOrder().getAttachmentList())) {
       List<RdAttachmentDTO> attachmentDTOS = JSONObject.parseArray(
           JSONObject.toJSONString(attachmentList), RdAttachmentDTO.class);
       
       // 补充实验室ID和订单号
       attachmentDTOS.forEach(l -> {
           l.setLabId(dataInput.getHeader().getLab().getLabId());
           l.setOrderNo(dataInput.getOrder().getOrderNo());
       });
       
       rdAttachmentDTOS.addAll(attachmentDTOS);
   }
   ```

## 7. 发票信息字段映射 (Invoice)

### 7.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 发票基础信息 | `ReportDataInput.invoiceList[]` → `RdInvoiceDTO` | `RdReportInvoiceDTO` | `convertReportInvoiceList()` | 一对多转换 |
| 报价单发票关联 | `ReportDataInput.invoiceList[].quotationNos[]` | `RdQuotationInvoiceRelDTO` | 关联关系生成 | 多对多关联 |

### 7.2 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **基础标识字段** |
| `labId` | `ReportDataInput.header.lab.labId` | 跨对象引用 | 实验室ID |
| `systemId` | `ReportDataInput.header.systemId` | 跨对象引用 | 系统ID |
| `orderNo` | `ReportDataInput.header.orderNo` | 跨对象引用 | 订单号 |
| `rootOrderNo` | `ReportDataInput.header.rootOrderNo` | 跨对象引用 | 根订单号 |
| `reportNo` | `ReportDataInput.header.reportNo` | 跨对象引用 | 报告号 |
| **发票基础信息** |
| `invoiceDate` | `ReportDataInput.invoiceList[].invoiceDate` | 直接映射 | 发票日期 |
| `bossOrderNo` | `ReportDataInput.invoiceList[].bossOrderNo` | 直接映射 | Boss订单号 |
| `productCode` | `ReportDataInput.invoiceList[].productCode` | 直接映射 | 产品代码 |
| `projectTemplate` | `ReportDataInput.invoiceList[].projectTemplate` | 直接映射 | 项目模板 |
| `costCenter` | `ReportDataInput.invoiceList[].costCenter` | 直接映射 | 成本中心 |
| `invoiceNo` | `ReportDataInput.invoiceList[].invoiceNo` | 直接映射 | 发票号 |
| `invoiceInstanceId` | `ReportDataInput.invoiceList[].invoiceInstanceId` | 直接映射 | 发票实例ID |
| `currencyCode` | `ReportDataInput.invoiceList[].currency` | 直接映射 | 货币代码 |
| **金额信息** |
| `netAmount` | `ReportDataInput.invoiceList[].netAmount` | 直接映射 | 净金额 |
| `vatAmount` | `ReportDataInput.invoiceList[].vatAmount` | 直接映射 | 增值税金额 |
| `totalAmount` | `ReportDataInput.invoiceList[].totalAmount` | 直接映射 | 总金额 |
| `prePaidAmount` | `ReportDataInput.invoiceList[].prePaidAmount` | 直接映射 | 预付金额 |
| **状态信息** |
| `invoiceStatus` | `ReportDataInput.invoiceList[].invoiceStatus` | 直接映射 | 发票状态 |

### 7.3 报价单发票关联关系映射

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| `systemId` | `ReportDataInput.header.systemId` | 跨对象引用 | 系统ID |
| `quotationNo` | `ReportDataInput.invoiceList[].quotationNos[]` | 列表展开 | 报价单号（来自发票的报价单号列表） |
| `invoiceNo` | `ReportDataInput.invoiceList[].invoiceNo` | 当前发票号 | 发票号 |
| `quotationInstanceId` | 通过`quotationNo`查找报价单信息 | 关联查找 | 从报价单DTO中获取 |
| `invoiceInstanceId` | `ReportDataInput.invoiceList[].invoiceInstanceId` | 当前发票实例ID | 发票实例ID |
| `activeIndicator` | 固定值 | 常量设置 | `ActiveType.Enable.getStatus()` |

### 7.4 特殊转换规则

1. **报价单发票关联关系生成**：
   ```java
   // 为每个发票的每个报价单号生成关联关系
   List<String> quotationNos = l.getQuotationNos();
   if (Func.isNotEmpty(quotationNos)) {
       quotationNos.forEach(quotationNo -> {
           RdQuotationDTO quotationDTO = quotationDTOMap.get(quotationNo);
           if (Func.isNotEmpty(quotationDTO)) {
               RdQuotationInvoiceRelDTO relDTO = new RdQuotationInvoiceRelDTO();
               relDTO.setQuotationNo(quotationNo);
               relDTO.setInvoiceNo(l.getInvoiceNo());
               relDTO.setQuotationInstanceId(quotationDTO.getQuotationInstanceId());
               relDTO.setInvoiceInstanceId(l.getInvoiceInstanceId());
               quotationInvoiceRelDTOS.add(relDTO);
           }
       });
   }
   ```

## 8. 报告结论字段映射 (Report Conclusion)

### 8.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 报告结论信息 | `ReportDataInput.reportConclusionList[]` → `RdReportConclusionInput` | `RdReportConclusionDTO` | `convertReportConclusionList()` | 一对多转换 |

### 8.2 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **完整对象复制** |
| `全部字段` | `ReportDataInput.reportConclusionList[]` | 属性复制 | `BeanUtil.copyProperties(input, output)` |
| **特殊字段处理** |
| `conclusionLevel` | `ReportDataInput.reportConclusionList[].conclusionLevelId` | 条件映射 | 当`conclusionLevelId`不为空时设置 |

### 8.3 特殊转换规则

1. **完整对象复制**：
   ```java
   // 使用BeanUtil进行完整属性复制
   reportConclusionList.forEach(l -> {
       RdReportConclusionDTO reportConclusionDTO = new RdReportConclusionDTO();
       BeanUtil.copyProperties(l, reportConclusionDTO);
       
       // 特殊字段处理
       if (Func.isNotEmpty(l.getConclusionLevelId())) {
           reportConclusionDTO.setConclusionLevel(l.getConclusionLevelId());
       }
       
       list.add(reportConclusionDTO);
   });
   ```

## 9. TRF关联关系字段映射 (TRF Relationship)

### 9.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| TRF基础信息 | `ReportDataInput.trfList[]` → `RdTrfInput` | `RdReportTrfRelDTO` | `convertTrfRel()` | 一对多展开 |
| 订单列表展开 | `ReportDataInput.trfList[].orderList[]` → `RdOrderRelInput` | 展开到`RdReportTrfRelDTO` | 嵌套列表展开 | 二级列表展开 |

### 9.2 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **TRF基础信息** |
| `trfRefSystemId` | `ReportDataInput.trfList[].refSystemId` | 直接映射 | TRF引用系统ID |
| `trfNo` | `ReportDataInput.trfList[].trfNo` | 直接映射 | TRF编号 |
| `trfTemplateOwner` | `ReportDataInput.trfList[].trfTemplateOwner` | 直接映射 | TRF模板所有者 |
| `trfTemplateId` | `ReportDataInput.trfList[].trfTemplateId` | 类型转换 | `Long.parseLong(trfTemplateId)` |
| `trfTemplateName` | `ReportDataInput.trfList[].trfTemplateName` | 直接映射 | TRF模板名称 |
| **订单关联信息（二级展开）** |
| `orderSystemId` | `ReportDataInput.trfList[].orderList[].systemId` | 二级列表展开 | 订单系统ID |
| `orderNo` | `ReportDataInput.trfList[].orderList[].orderNo` | 二级列表展开 | 订单号 |
| **报告关联信息** |
| `reportNo` | `ReportDataInput.header.reportNo` | 跨对象引用 | 报告号 |

### 9.3 特殊转换规则

1. **二级列表展开**：
   ```java
   // TRF列表中每个TRF的订单列表展开
   trfList.forEach(trf -> {
       List<RdOrderRelInput> orderList = trf.getOrderList();
       if (Func.isNotEmpty(orderList)) {
           orderList.forEach(order -> {
               RdReportTrfRelDTO trfRelDTO = new RdReportTrfRelDTO();
               // 设置TRF信息
               trfRelDTO.setTrfRefSystemId(trf.getRefSystemId());
               trfRelDTO.setTrfNo(trf.getTrfNo());
               
               // 设置订单信息
               trfRelDTO.setOrderSystemId(order.getSystemId());
               trfRelDTO.setOrderNo(order.getOrderNo());
               
               // 设置报告信息
               trfRelDTO.setReportNo(reportNo);
               
               reportTrfRelList.add(trfRelDTO);
           });
       }
   });
   ```

2. **模板ID类型转换**：
   ```java
   // 字符串转Long类型，安全转换
   trfRelDTO.setTrfTemplateId(
       Func.isNotEmpty(trf.getTrfTemplateId()) ? 
       Long.parseLong(trf.getTrfTemplateId()) : null
   );
   ```

## 10. 测试结果字段映射 (Test Result)

### 10.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| 测试结果基础 | `ReportDataInput.reportTestResultList[]` → `RdTestResultInput` | `RdReportTestResultDTO` | `convertReportTestResultList()` | 一对多展开 |
| 测试结果详情 | `ReportDataInput.reportTestResultList[].testResult` → `RdTestResultResultInput` | 展开到`RdReportTestResultDTO` | 嵌套对象展开 | 二级嵌套 |
| 关联关系 | `ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel` → `RdTestResultNameInput` | 提供关联ID | 三级嵌套 |
| 分析物信息 | `ReportDataInput.testLineList[].analyteList[]` → `RdAnalyteInput` | 通过ID关联查找 | 跨对象关联 |

### 10.2 测试结果对象层级结构

```
ReportDataInput
├── reportTestResultList[] (测试结果列表)
│   └── RdTestResultInput (单个测试结果对象)
│       ├── orderNo (订单号)
│       ├── systemId (系统ID)
│       ├── testMatrixId (测试矩阵ID)
│       ├── subReportNo (子报告号)
│       └── testResult: RdTestResultResultInput (测试结果详情 - 二级嵌套)
│           ├── testResultFullName (测试结果全名)
│           ├── resultValue (结果值)
│           ├── resultValueRemark (结果值备注)
│           ├── testResultFullNameRel: RdTestResultNameInput (关联关系 - 三级嵌套)
│           │   ├── upSpecimenInstanceId (上级样本实例ID)
│           │   ├── specimenInstanceId (样本实例ID)
│           │   ├── procedureConditionInstanceId (程序条件实例ID)
│           │   ├── parentConditionInstanceId (父条件实例ID)
│           │   ├── conditionInstanceId (条件实例ID)
│           │   ├── positionInstanceId (位置实例ID)
│           │   └── analyteInstanceId (分析物实例ID ★关键关联字段)
│           └── ... (其他测试结果字段)
│
└── testLineList[] (测试线列表 - 外部关联数据源)
    └── RdTestLineInput (单个测试线)
        └── analyteList[] (分析物列表)
            └── RdAnalyteInput (分析物对象)
                ├── analyteInstanceId (分析物实例ID ★关联键)
                ├── analyteName (分析物名称)
                ├── analyteDescription (分析物描述)
                ├── analyteAbbr (分析物缩写)
                └── ... (其他分析物字段)
```

### 10.3 层级关系说明

**【关键】RdTestResultInput 与 RdAnalyteInput 的关系：**

1. **RdTestResultInput** (第1层级)：测试结果根对象
2. **RdTestResultResultInput** (第2层级)：嵌套在测试结果中的详情对象
3. **RdTestResultNameInput** (第3层级)：嵌套在测试结果详情中的关联关系对象
4. **RdAnalyteInput** (外部对象)：位于 `testLineList[].analyteList[]` 中的独立对象

**关联逻辑：**
```
ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel.analyteInstanceId
                     ↓ (通过ID查找)
ReportDataInput.testLineList[].analyteList[].analyteInstanceId
```

### 10.4 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|--------------|
| **基础标识字段** |
| `systemId` | `ReportDataInput.reportTestResultList[].systemId` | 直接映射 | 系统ID |
| `orderNo` | `ReportDataInput.reportTestResultList[].orderNo` | 直接映射 | 订单号 |
| `reportNo` | `ReportDataInput.header.reportNo` | 跨对象引用 | 报告编号 |
| `testMatrixId` | `ReportDataInput.reportTestResultList[].testMatrixId` | 直接映射 | 测试矩阵ID |
| `subReportNo` | `ReportDataInput.reportTestResultList[].subReportNo` | 直接映射 | 子报告号 |
| **测试结果详情（二级嵌套）** |
| `testResultFullName` | `ReportDataInput.reportTestResultList[].testResult.testResultFullName` | 二级嵌套映射 | 测试结果全名 |
| `resultValue` | `ReportDataInput.reportTestResultList[].testResult.resultValue` | 二级嵌套映射 | 结果值 |
| `resultValueRemark` | `ReportDataInput.reportTestResultList[].testResult.resultValueRemark` | 二级嵌套映射 | 结果值备注 |
| `resultUnit` | `ReportDataInput.reportTestResultList[].testResult.resultUnit` | 二级嵌套映射 | 结果单位 |
| `limitValue` | `ReportDataInput.reportTestResultList[].testResult.limitValue` | 二级嵌套映射 | 限值 |
| `acceptanceCriteria` | `ReportDataInput.reportTestResultList[].testResult.acceptanceCriteria` | 二级嵌套映射 | 验收标准 |
| `testResultList` | `ReportDataInput.reportTestResultList[].testResult.testResultList` | JSON转换+二级嵌套 | `JSON.toJSONString(testResult.getTestResultList())` |
| **关联ID字段（三级嵌套）** |
| `upSpecimenInstanceId` | `ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel.upSpecimenInstanceId` | 三级嵌套映射 | 上级样本实例ID |
| `specimenInstanceId` | `ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel.specimenInstanceId` | 三级嵌套映射 | 样本实例ID |
| `procedureConditionInstanceId` | `ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel.procedureConditionInstanceId` | 三级嵌套映射 | 程序条件实例ID |
| `parentConditionInstanceId` | `ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel.parentConditionInstanceId` | 三级嵌套映射 | 父条件实例ID |
| `conditionInstanceId` | `ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel.conditionInstanceId` | 三级嵌套映射 | 条件实例ID |
| `positionInstanceId` | `ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel.positionInstanceId` | 三级嵌套映射 | 位置实例ID |
| `analyteInstanceId` | `ReportDataInput.reportTestResultList[].testResult.testResultFullNameRel.analyteInstanceId` | 三级嵌套映射 | 分析物实例ID（关联键） |
| **分析物信息（通过ID关联查找）** |
| `analyteName` | `ReportDataInput.testLineList[].analyteList[].analyteName` | 跨对象ID关联 | 通过analyteInstanceId查找获取 |
| `analyteDescription` | `ReportDataInput.testLineList[].analyteList[].analyteDescription` | 跨对象ID关联 | 分析物描述 |
| `analyteAbbr` | `ReportDataInput.testLineList[].analyteList[].analyteAbbr` | 跨对象ID关联 | 分析物缩写 |
| `analyteCommonName` | `ReportDataInput.testLineList[].analyteList[].analyteCommonName` | 跨对象ID关联 | 分析物通用名 |
| `analyteAlias` | `ReportDataInput.testLineList[].analyteList[].analyteAlias` | 跨对象ID关联 | 分析物别名 |
| **样本信息（通过ID关联查找）** |
| `sampleName` | `ReportDataInput.testSampleList[].sampleName` | 跨对象ID关联 | 通过specimenInstanceId查找 |
| `sampleDescription` | `ReportDataInput.testSampleList[].sampleDescription` | 跨对象ID关联 | 样本描述 |
| **状态字段** |
| `activeIndicator` | `ReportDataInput.reportTestResultList[].activeIndicator` | 直接映射 | 激活指示器 |

### 10.5 特殊转换规则

1. **分析物信息关联查找**：
   ```java
   // 通过分析物实例ID查找分析物信息
   String analyteInstanceId = testResultFullNameRel.getAnalyteInstanceId();
   RdAnalyteInput analyte = findAnalyteByInstanceId(dataInput.getTestLineList(), analyteInstanceId);
   if (analyte != null) {
       resultDTO.setAnalyteName(analyte.getAnalyteName());
       resultDTO.setAnalyteDescription(analyte.getAnalyteDescription());
       resultDTO.setAnalyteAbbr(analyte.getAnalyteAbbr());
   }
   ```

2. **多语言测试结果列表处理**：
   ```java
   // 测试结果列表转换为JSON
   if (Func.isNotEmpty(testResult.getTestResultList())) {
       resultDTO.setTestResultList(JSON.toJSONString(testResult.getTestResultList()));
   }
   ```

3. **样本信息关联查找**：
   ```java
   // 通过样本实例ID查找样本信息
   String specimenInstanceId = testResultFullNameRel.getSpecimenInstanceId();
   RdTestSampleInput sample = findSampleByInstanceId(dataInput.getTestSampleList(), specimenInstanceId);
   if (sample != null) {
       resultDTO.setSampleName(sample.getSampleName());
       resultDTO.setSampleDescription(sample.getSampleDescription());
   }
   ```

4. **三级嵌套字段安全访问**：
   ```java
   // 安全访问深层嵌套字段
   Optional.ofNullable(testResultInput.getTestResult())
       .map(RdTestResultResultInput::getTestResultFullNameRel)
       .ifPresent(rel -> {
           resultDTO.setAnalyteInstanceId(rel.getAnalyteInstanceId());
           resultDTO.setSpecimenInstanceId(rel.getSpecimenInstanceId());
           // ... 其他关联ID设置
       });
   ``` 

## 11. 报告矩阵字段映射 (Report Matrix)

### 11.1 输入输出对象概览

| 维度 | 输入对象路径 | 输出对象 | 转换方法 | 对象层级关系 |
|------|-------------|----------|----------|-------------|
| **报告矩阵主体** | `ReportDataInput.header.reportMatrixList[]` → `RdReportMatrixInput` | `RdReportMatrixDTO` | `convertReportMatrixList()` | 主对象转换 |
| **测试条线关联** | `ReportDataInput.testLineList[]` → `RdTestLineInput` | 通过ID关联查找 | 跨对象关联 |
| **测试样本关联** | `ReportDataInput.testSampleList[]` → `RdTestSampleInput` | 通过ID关联查找 | 跨对象关联 |
| **条件信息** | `RdReportMatrixInput.conditionList[]` → `RdConditionInput` | JSON序列化存储 | 列表转JSON |
| **结论信息** | `RdReportMatrixInput.conclusion` → `RdConclusionInput` | 嵌套对象展开 | 对象扁平化 |
| **外部信息** | `RdReportMatrixInput.external` → `RdReportMatrixExternalInput` | 嵌套对象展开 | 对象扁平化 |
| **引用信息** | `RdTestLineInput.citation` → `RdCitationInput` | 嵌套对象展开 | 对象扁平化 |
| **材料属性** | `RdTestSampleInput.materialAttr` → `RdMaterialAttrInput` | 嵌套对象展开 | 对象扁平化 |

### 11.2 报告矩阵对象层级结构

```
ReportDataInput
├── header
│   └── reportMatrixList[]              // 报告矩阵列表
│       ├── testMatrixId                // 矩阵ID
│       ├── testLineInstanceId          // 测试条线实例ID
│       ├── testSampleInstanceId        // 测试样本实例ID  
│       ├── conditionList[]             // 测试条件列表
│       ├── conclusion                  // 结论信息
│       └── external                    // 外部信息
├── testLineList[]                      // 测试条线列表（关联查找）
│   ├── testLineId                      // 条线ID
│   ├── citation                        // 引用标准信息
│   └── ppTestLineRelList[]             // PP测试条线关联
└── testSampleList[]                    // 测试样本列表（关联查找）
    ├── testSampleNo                    // 样本编号
    ├── testSampleName                  // 样本名称
    └── materialAttr                    // 材料属性
```

### 11.3 层级关系说明

**【关键】RdReportMatrixInput 与 RdTestLineInput/RdTestSampleInput 的关系：**

1. **测试条线关联**：通过 `testLineInstanceId` 在 `testLineList[]` 中查找对应的测试条线信息
2. **测试样本关联**：通过 `testSampleInstanceId` 在 `testSampleList[]` 中查找对应的测试样本信息
3. **层级结构**：报告矩阵作为主体，关联多个子对象的详细信息

**转换逻辑**：
```java
// 1. 构建测试条线映射表
Map<String, RdTestLineInput> testLineMap = new HashMap<>();
testLineList.forEach(l -> testLineMap.put(l.getTestLineInstanceId(), l));

// 2. 构建测试样本映射表  
Map<String, RdTestSampleInput> testSampleMap = new HashMap<>();
testSampleList.forEach(sample -> testSampleMap.put(sample.getTestSampleInstanceId(), sample));

// 3. 通过ID关联查找
RdTestLineInput testLine = testLineMap.get(matrix.getTestLineInstanceId());
RdTestSampleInput testSample = testSampleMap.get(matrix.getTestSampleInstanceId());
```

### 11.4 详细字段映射表（完整路径表示）

| 输出字段 | 输入字段完整路径 | 映射规则 | 数据来源说明 |
|----------|------------------|----------|-------------|
| **基础信息字段** |
| `labId` | `ReportDataInput.labId` | 直接映射 | 实验室ID |
| `orderNo` | `ReportDataInput.header.orderNo` | 直接映射 | 订单号 |
| `reportNo` | `ReportDataInput.header.reportNo` | 直接映射 | 报告号 |
| **矩阵标识字段** |
| `testMatrixId` | `ReportDataInput.header.reportMatrixList[].testMatrixId` | 直接映射 | 测试矩阵ID |
| `testMatrixGroupId` | `ReportDataInput.header.reportMatrixList[].testMatrixGroupId` | 直接映射 | 测试矩阵组ID |
| **测试条线字段（通过testLineInstanceId关联）** |
| `testLineInstanceId` | `ReportDataInput.header.reportMatrixList[].testLineInstanceId` | 直接映射 | 测试条线实例ID |
| `testLineId` | `ReportDataInput.testLineList[testLineInstanceId].testLineId` | 关联查找 | 测试条线ID |
| `testLineType` | `ReportDataInput.testLineList[testLineInstanceId].testLineType` | Integer→Long转换 | 测试条线类型 |
| `testLineStatus` | `ReportDataInput.testLineList[testLineInstanceId].testLineStatus` | Object→String转换 | 测试条线状态 |
| `testLineRemark` | `ReportDataInput.testLineList[testLineInstanceId].testLineRemark` | 直接映射 | 测试条线备注 |
| `testLineSeq` | `ReportDataInput.testLineList[testLineInstanceId].testLineSeq` | Integer→Long转换 | 测试条线序号 |
| `evaluationAlias` | `ReportDataInput.testLineList[testLineInstanceId].evaluationAlias` | 直接映射 | 评估别名 |
| `evaluationName` | `ReportDataInput.testLineList[testLineInstanceId].evaluationName` | 直接映射 | 评估名称 |
| `labSectionName` | `ReportDataInput.testLineList[testLineInstanceId].labSectionName` | 直接映射 | 实验室部门名称 |
| **引用标准字段（从关联的测试条线获取）** |
| `citationId` | `ReportDataInput.testLineList[testLineInstanceId].citation.citationId` | 直接映射 | 引用标准ID |
| `citationType` | `ReportDataInput.testLineList[testLineInstanceId].citation.citationType` | 直接映射 | 引用标准类型 |
| `citationName` | `ReportDataInput.testLineList[testLineInstanceId].citation.citationName` | 直接映射 | 引用标准名称 |
| `citationFullName` | `ReportDataInput.testLineList[testLineInstanceId].citation.citationFullName` | 直接映射 | 引用标准全名 |
| `citationVersionId` | `ReportDataInput.testLineList[testLineInstanceId].citation.citationVersionId` | Integer→Long转换 | 引用标准版本ID |
| `citationSectionId` | `ReportDataInput.testLineList[testLineInstanceId].citation.citationSectionId` | 直接映射 | 引用标准章节ID |
| `citationSectionName` | `ReportDataInput.testLineList[testLineInstanceId].citation.citationSectionName` | 直接映射 | 引用标准章节名称 |
| **测试条件字段** |
| `condition` | `ReportDataInput.header.reportMatrixList[].conditionList[]` | 列表→JSON转换 | 测试条件列表序列化 |
| **外部信息字段** |
| `externalId` | `ReportDataInput.header.reportMatrixList[].external.testMatrixId` | 直接映射 | 外部测试矩阵ID |
| **测试样本字段（通过testSampleInstanceId关联）** |
| `sampleInstanceId` | `ReportDataInput.header.reportMatrixList[].testSampleInstanceId` | 直接映射 | 样本实例ID |
| `sampleParentId` | `ReportDataInput.testSampleList[testSampleInstanceId].parentTestSampleId` | 关联查找 | 父样本ID |
| `sampleNo` | `ReportDataInput.testSampleList[testSampleInstanceId].testSampleNo` | 关联查找 | 样本编号 |
| `sampleName` | `ReportDataInput.testSampleList[testSampleInstanceId].testSampleName` | 关联查找 | 样本名称 |
| `externalSampleNo` | `ReportDataInput.testSampleList[testSampleInstanceId].externalSampleNo` | 关联查找 | 外部样本编号 |
| `externalSampleName` | `ReportDataInput.testSampleList[testSampleInstanceId].externalSampleName` | 关联查找 | 外部样本名称 |
| `sampleType` | `ReportDataInput.testSampleList[testSampleInstanceId].testSampleType` | Object→String转换 | 样本类型 |
| `sampleSeq` | `ReportDataInput.testSampleList[testSampleInstanceId].testSampleSeq` | Object→String转换 | 样本序号 |
| `category` | `ReportDataInput.testSampleList[testSampleInstanceId].category` | 关联查找 | 样本分类 |
| **材料属性字段（从关联的测试样本获取）** |
| `sampleRemark` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.materialRemark` | 嵌套对象映射 | 样本备注 |
| `composition` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.materialTexture` | 嵌套对象映射 | 材料成分 |
| `color` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.materialColor` | 嵌套对象映射 | 材料颜色 |
| `description` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.materialDescription` | 嵌套对象映射 | 材料描述 |
| `endUse` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.materialEndUse` | 嵌套对象映射 | 最终用途 |
| `applicable` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.applicableFlag` | Object→String转换 | 适用标志 |
| `material` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.materialName` | 嵌套对象映射 | 材料名称 |
| `otherSampleInfo` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.materialOtherSampleInfo` | 嵌套对象映射 | 其他样本信息 |
| `extFields` | `ReportDataInput.testSampleList[testSampleInstanceId].materialAttr.extFields` | 嵌套对象映射 | 扩展字段 |
| **结论字段** |
| `conclusionCode` | `ReportDataInput.header.reportMatrixList[].conclusion.conclusionCode` | 嵌套对象映射 | 结论代码 |
| `reviewConclusion` | `ReportDataInput.header.reportMatrixList[].conclusion.reviewConclusion` | 嵌套对象映射 | 审核结论 |
| `customerConclusion` | `ReportDataInput.header.reportMatrixList[].conclusion.customerConclusion` | 嵌套对象映射 | 客户结论 |
| `conclusionRemark` | `ReportDataInput.header.reportMatrixList[].conclusion.conclusionRemark` | 嵌套对象映射 | 结论备注 |
| **状态控制字段** |
| `activeIndicator` | `ReportDataInput.header.reportMatrixList[].activeIndicator` | 直接映射 | 激活指示器 |
| `metaData` | `ReportDataInput.header.reportMatrixList[].metaData` | 直接映射 | 元数据 |
| `applicationFactor` | `ReportDataInput.header.reportMatrixList[].applicationFactor` | 直接映射 | 应用因子 |
| **关联对象字段** |
| `rdPpTestLineList` | `ReportDataInput.testLineList[testLineInstanceId].ppTestLineRelList[]` | 列表对象转换 | PP测试条线关联列表 |

### 11.5 特殊转换规则

1. **跨对象关联查找**：
   ```java
   // 测试条线信息关联
   Map<String, RdTestLineInput> testLineMap = new HashMap<>();
   testLineList.forEach(l -> testLineMap.put(l.getTestLineInstanceId(), l));
   RdTestLineInput testLine = testLineMap.get(matrix.getTestLineInstanceId());
   
   // 测试样本信息关联
   Map<String, RdTestSampleInput> testSampleMap = new HashMap<>();
   testSampleList.forEach(sample -> testSampleMap.put(sample.getTestSampleInstanceId(), sample));
   RdTestSampleInput testSample = testSampleMap.get(matrix.getTestSampleInstanceId());
   ```

2. **条件列表JSON转换**：
   ```java
   List<RdConditionInput> conditionList = matrix.getConditionList();
   if (Func.isNotEmpty(conditionList)) {
       reportMatrixDTO.setCondition(JSONObject.toJSONString(conditionList));
   }
   ```

3. **PP测试条线关联对象转换**：
   ```java
   List<RdPpTestLineRelInput> ppTestLineRelList = testLine.getPpTestLineRelList();
   if (Func.isNotEmpty(ppTestLineRelList)) {
       List<RdPpTestLineDTO> rdPpTestLineDTOList = new ArrayList<>();
       ppTestLineRelList.forEach(ppTestLineRel -> {
           RdPpTestLineDTO rdPpTestLineDTO = new RdPpTestLineDTO();
           BeanUtil.copyProperties(ppTestLineRel, rdPpTestLineDTO);
           rdPpTestLineDTO.setTestMatrixId(matrix.getTestMatrixId());
           rdPpTestLineDTOList.add(rdPpTestLineDTO);
       });
       reportMatrixDTO.setRdPpTestLineList(rdPpTestLineDTOList);
   }
   ```

4. **空值安全处理**：
   ```java
   // 类型转换空值检查
   reportMatrixDTO.setTestLineType(
       Func.isNotEmpty(testLine.getTestLineType()) ? 
       testLine.getTestLineType().longValue() : null
   );
   
   // 对象转字符串空值检查
   reportMatrixDTO.setApplicable(
       Objects.toString(materialAttr.getApplicableFlag())
   );
   ```

## 总结

本文档详细分析了 testdatabiz-sdk 中数据对象的字段映射关系，主要特点：

1. **完整路径表示**：所有字段都使用从`ReportDataInput`根对象开始的完整路径表示
2. **层级关系清晰**：明确展示对象间的嵌套层级和关联关系  
3. **转换规则详细**：提供了具体的代码示例和业务规则说明
4. **关联关系明确**：特别说明了跨对象关联的实现方式

该文档为开发团队提供了：
- **准确的数据转换逻辑理解**
- **快速的字段来源定位**  
- **清晰的业务规则识别**
- **便捷的维护扩展指导**