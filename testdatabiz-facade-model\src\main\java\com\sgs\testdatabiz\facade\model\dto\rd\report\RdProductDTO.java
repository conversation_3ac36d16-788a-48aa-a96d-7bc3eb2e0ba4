/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdProductDTO implements Serializable {

    private String productInstanceId;
    private String templateId;
    // SCI-1490
    private String reportNo;
    private List<RdProductSampleAttrDTO> productAttrList;
    private Date lastModifiedTimestamp;
    private Integer activeIndicator;
}