/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportDataBatchDO extends BaseModel {
    private List<RdTrfDO> trfList;
    private List<RdOrderDO> orderList;
    private List<RdReportDO> reportList;
    private RdRelationshipDO relationship;
    private List<RdTestSampleDO> testSampleList;
    private List<RdTestLineDO> testLineList;
    private List<RdTestResultDO> testResultList;
    private List<RdReportConclusionDO> reportConclusionList;
    private List<RdConditionGroupDO> conditionGroupList;
    private List<RdQuotationDO> quotationList;
    private List<RdInvoiceDO> invoiceList;
}
