package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class TestSamplePO {
    /**
     * id BIGINT(19) 必填<br>
     * ID,Primary key
     */
    private Long id;

    /**
     * sample_instance_id VARCHAR(64)<br>
     * 
     */
    private String sampleInstanceId;

    /**
     * sample_parent_id VARCHAR(36)<br>
     * 
     */
    private String sampleParentId;

    /**
     * rd_report_matrix_id BIGINT(19)<br>
     * 
     */
    private Long rdReportMatrixId;

    /**
     * sample_no VARCHAR(60)<br>
     * 样品编号
     */
    private String sampleNo;

    /**
     * sample_type INTEGER(10)<br>
     * 样品类型
     */
    private Integer sampleType;

    /**
     * sample_seq INTEGER(10)<br>
     * 样品顺序
     */
    private Integer sampleSeq;

    /**
     * order_no VARCHAR(50)<br>
     * 订单编号
     */
    private String orderNo;

    /**
     * category VARCHAR(10)<br>
     * 物料分类
     */
    private String category;

    /**
     * description VARCHAR(2048)<br>
     * 物料描述
     */
    private String description;

    /**
     * composition VARCHAR(500)<br>
     * 物料材质
     */
    private String composition;

    /**
     * color VARCHAR(500)<br>
     * 物料颜色
     */
    private String color;

    /**
     * sample_remark VARCHAR(4000)<br>
     * 样品备注信息
     */
    private String sampleRemark;

    /**
     * end_use VARCHAR(500)<br>
     * 物料用途
     */
    private String endUse;

    /**
     * material VARCHAR(300)<br>
     * 物料名称
     */
    private String material;

    /**
     * applicable_flag TINYINT(3) 必填<br>
     * NC 样品标识
     */
    private Integer applicableFlag;

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_date TIMESTAMP(19)<br>
     * CreatedDate
     */
    private Date createdDate;

    /**
     * created_by VARCHAR(50)<br>
     * CreatedBy
     */
    private String createdBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * ModifiedDate
     */
    private Date modifiedDate;

    /**
     * modified_by VARCHAR(50)<br>
     * ModifiedBy
     */
    private String modifiedBy;

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 
     */
    private Date lastModifiedTimestamp;

    /**
     * other_sample_info LONGVARCHAR(65535)<br>
     * 其它样品信息
     */
    private String otherSampleInfo;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 ID,Primary key
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 ID,Primary key
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * sample_instance_id VARCHAR(64)<br>
     * 获得 
     */
    public String getSampleInstanceId() {
        return sampleInstanceId;
    }

    /**
     * sample_instance_id VARCHAR(64)<br>
     * 设置 
     */
    public void setSampleInstanceId(String sampleInstanceId) {
        this.sampleInstanceId = sampleInstanceId == null ? null : sampleInstanceId.trim();
    }

    /**
     * sample_parent_id VARCHAR(36)<br>
     * 获得 
     */
    public String getSampleParentId() {
        return sampleParentId;
    }

    /**
     * sample_parent_id VARCHAR(36)<br>
     * 设置 
     */
    public void setSampleParentId(String sampleParentId) {
        this.sampleParentId = sampleParentId == null ? null : sampleParentId.trim();
    }

    /**
     * rd_report_matrix_id BIGINT(19)<br>
     * 获得 
     */
    public Long getRdReportMatrixId() {
        return rdReportMatrixId;
    }

    /**
     * rd_report_matrix_id BIGINT(19)<br>
     * 设置 
     */
    public void setRdReportMatrixId(Long rdReportMatrixId) {
        this.rdReportMatrixId = rdReportMatrixId;
    }

    /**
     * sample_no VARCHAR(60)<br>
     * 获得 样品编号
     */
    public String getSampleNo() {
        return sampleNo;
    }

    /**
     * sample_no VARCHAR(60)<br>
     * 设置 样品编号
     */
    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo == null ? null : sampleNo.trim();
    }

    /**
     * sample_type INTEGER(10)<br>
     * 获得 样品类型
     */
    public Integer getSampleType() {
        return sampleType;
    }

    /**
     * sample_type INTEGER(10)<br>
     * 设置 样品类型
     */
    public void setSampleType(Integer sampleType) {
        this.sampleType = sampleType;
    }

    /**
     * sample_seq INTEGER(10)<br>
     * 获得 样品顺序
     */
    public Integer getSampleSeq() {
        return sampleSeq;
    }

    /**
     * sample_seq INTEGER(10)<br>
     * 设置 样品顺序
     */
    public void setSampleSeq(Integer sampleSeq) {
        this.sampleSeq = sampleSeq;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 获得 订单编号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(50)<br>
     * 设置 订单编号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * category VARCHAR(10)<br>
     * 获得 物料分类
     */
    public String getCategory() {
        return category;
    }

    /**
     * category VARCHAR(10)<br>
     * 设置 物料分类
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * description VARCHAR(2048)<br>
     * 获得 物料描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * description VARCHAR(2048)<br>
     * 设置 物料描述
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * composition VARCHAR(500)<br>
     * 获得 物料材质
     */
    public String getComposition() {
        return composition;
    }

    /**
     * composition VARCHAR(500)<br>
     * 设置 物料材质
     */
    public void setComposition(String composition) {
        this.composition = composition == null ? null : composition.trim();
    }

    /**
     * color VARCHAR(500)<br>
     * 获得 物料颜色
     */
    public String getColor() {
        return color;
    }

    /**
     * color VARCHAR(500)<br>
     * 设置 物料颜色
     */
    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    /**
     * sample_remark VARCHAR(4000)<br>
     * 获得 样品备注信息
     */
    public String getSampleRemark() {
        return sampleRemark;
    }

    /**
     * sample_remark VARCHAR(4000)<br>
     * 设置 样品备注信息
     */
    public void setSampleRemark(String sampleRemark) {
        this.sampleRemark = sampleRemark == null ? null : sampleRemark.trim();
    }

    /**
     * end_use VARCHAR(500)<br>
     * 获得 物料用途
     */
    public String getEndUse() {
        return endUse;
    }

    /**
     * end_use VARCHAR(500)<br>
     * 设置 物料用途
     */
    public void setEndUse(String endUse) {
        this.endUse = endUse == null ? null : endUse.trim();
    }

    /**
     * material VARCHAR(300)<br>
     * 获得 物料名称
     */
    public String getMaterial() {
        return material;
    }

    /**
     * material VARCHAR(300)<br>
     * 设置 物料名称
     */
    public void setMaterial(String material) {
        this.material = material == null ? null : material.trim();
    }

    /**
     * applicable_flag TINYINT(3) 必填<br>
     * 获得 NC 样品标识
     */
    public Integer getApplicableFlag() {
        return applicableFlag;
    }

    /**
     * applicable_flag TINYINT(3) 必填<br>
     * 设置 NC 样品标识
     */
    public void setApplicableFlag(Integer applicableFlag) {
        this.applicableFlag = applicableFlag;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 CreatedDate
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 CreatedDate
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 CreatedBy
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 CreatedBy
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 ModifiedDate
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 ModifiedDate
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 ModifiedBy
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 ModifiedBy
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 获得 
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 设置 
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    /**
     * other_sample_info LONGVARCHAR(65535)<br>
     * 获得 其它样品信息
     */
    public String getOtherSampleInfo() {
        return otherSampleInfo;
    }

    /**
     * other_sample_info LONGVARCHAR(65535)<br>
     * 设置 其它样品信息
     */
    public void setOtherSampleInfo(String otherSampleInfo) {
        this.otherSampleInfo = otherSampleInfo == null ? null : otherSampleInfo.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sampleInstanceId=").append(sampleInstanceId);
        sb.append(", sampleParentId=").append(sampleParentId);
        sb.append(", rdReportMatrixId=").append(rdReportMatrixId);
        sb.append(", sampleNo=").append(sampleNo);
        sb.append(", sampleType=").append(sampleType);
        sb.append(", sampleSeq=").append(sampleSeq);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", category=").append(category);
        sb.append(", description=").append(description);
        sb.append(", composition=").append(composition);
        sb.append(", color=").append(color);
        sb.append(", sampleRemark=").append(sampleRemark);
        sb.append(", endUse=").append(endUse);
        sb.append(", material=").append(material);
        sb.append(", applicableFlag=").append(applicableFlag);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append(", otherSampleInfo=").append(otherSampleInfo);
        sb.append("]");
        return sb.toString();
    }
}