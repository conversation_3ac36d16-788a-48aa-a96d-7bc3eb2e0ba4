/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdRelationshipSubReportInput implements Serializable {

    private Integer sourceType;
    private String objectNo;
    private String externalId;
    private String externalNo;
    private String subReportNo;
    private String externalObjectNo;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
