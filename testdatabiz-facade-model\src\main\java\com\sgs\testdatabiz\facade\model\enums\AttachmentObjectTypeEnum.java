package com.sgs.testdatabiz.facade.model.enums;

/**
 * @author: shawn.yang
 * @create: 2023-04-20 16:25
 */
public enum AttachmentObjectTypeEnum {
    TRF(1,"TRF"),
    ORDER(2,""),
    REPORT(3,"Report"),
    SUB_REPORT(4,"SubReport"),
    QUOTATION(5,"Quotation"),
    INVOICE(6,"Invoice"),
    TEST_SAMPLE(7,"TestSample"),
    TEST_MATRIX(8,"TestMatrix")



    ;

//    1:TRF;\n2:Order;\n3:Report;\n4:SubReport;\n5:Quotation;\n6:Invoice;


    private final Integer code;
    private final String desc;


    AttachmentObjectTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static AttachmentObjectTypeEnum from(Integer code){
        for (AttachmentObjectTypeEnum value : values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
