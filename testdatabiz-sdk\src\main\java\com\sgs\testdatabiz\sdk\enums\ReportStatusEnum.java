package com.sgs.testdatabiz.sdk.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/1/9 11:28
 */
public enum ReportStatusEnum {
    New(201, "New"),
    Cancelled(202, "Cancelled"),
    Approved(203, "Approved"),
    Draft(204, "Draft"),
    Reworked(205, "Reworked"),
    Combined(206, "Combined"),
    Replaced(207, "Replaced"),
    Completed(208, "Completed");

    private int code;
    private String message;
    public static final Map<Integer, ReportStatusEnum> maps = new HashMap<Integer, ReportStatusEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            ReportStatusEnum[] var1 = ReportStatusEnum.values();
            int var2 = var1.length;

            for (int var3 = 0; var3 < var2; ++var3) {
                ReportStatusEnum reportStatus = var1[var3];
                this.put(reportStatus.getCode(), reportStatus);
            }

        }
    };

    private ReportStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public static ReportStatusEnum getCode(Integer code) {
        return code != null && maps.containsKey(code) ? (ReportStatusEnum) maps.get(code) : null;
    }

    public boolean check(ReportStatusEnum... reportStatus) {
        if (reportStatus != null && reportStatus.length > 0) {
            ReportStatusEnum[] var2 = reportStatus;
            int var3 = reportStatus.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                ReportStatusEnum status = var2[var4];
                if (this.getCode() == status.getCode()) {
                    return true;
                }
            }

            return false;
        } else {
            return false;
        }
    }

    public static boolean checkStatus(ReportStatusEnum status, ReportStatusEnum... reportStatus) {
        return status != null && reportStatus != null && reportStatus.length > 0 ? check(status.getCode(), reportStatus) : false;
    }

    public static boolean check(Integer status, ReportStatusEnum... reportStatus) {
        if (status != null && maps.containsKey(status) && reportStatus != null && reportStatus.length > 0) {
            ReportStatusEnum[] var2 = reportStatus;
            int var3 = reportStatus.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                ReportStatusEnum reportStatu = var2[var4];
                if (status == reportStatu.getCode()) {
                    return true;
                }
            }

            return false;
        } else {
            return false;
        }
    }
}
