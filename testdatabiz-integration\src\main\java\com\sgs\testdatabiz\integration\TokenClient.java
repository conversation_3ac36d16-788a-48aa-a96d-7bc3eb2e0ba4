package com.sgs.testdatabiz.integration;

import com.sgs.core.domain.UserInfo;
import com.sgs.otsnotes.facade.model.common.BizException;
import com.sgs.otsnotes.facade.model.info.SysUserInfo;
import com.sgs.testdatabiz.core.common.RedisHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

/**
 *
 */
@Component
public class TokenClient  {
    private static final Logger logger = LoggerFactory.getLogger(TokenClient.class);
    @Autowired
    private RedisHelper redisHelper;

    /**
     * SGS Token Key
     */
    public final String SGS_TOKEN = "sgsToken";

    /**
     *
     * @return
     */
    public HttpServletRequest getRequestContext() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        ServletRequestAttributes sra = (ServletRequestAttributes)requestAttributes;
        // DIG-8555  Change this condition so that it does not always evaluate to "false"
        /*if (sra == null) {
            return null;
        }*/
        return sra.getRequest();
    }

    /**
     * Get SGS Token From HTTP Request
     * @param request
     * @return sgsToken or null
     */
    public String getToken(HttpServletRequest request) {
        String sgsToken = null;
        try {
            sgsToken = (String)request.getAttribute(SGS_TOKEN);
            if (StringUtils.isNoneBlank(sgsToken)){
                return sgsToken;
            }
            sgsToken = request.getParameter(SGS_TOKEN);
            if (StringUtils.isNoneBlank(sgsToken)){
                return sgsToken;
            }
            sgsToken = request.getHeader(SGS_TOKEN);
            if (StringUtils.isNoneBlank(sgsToken)){
                return sgsToken;
            }
            sgsToken = request.getHeader("X-Cookie");
            if (StringUtils.isNoneBlank(sgsToken)){
                return sgsToken;
            }
            Cookie[] cookies = request.getCookies();
            if (cookies == null || cookies.length <= 0){
                return sgsToken;
            }
            for (Cookie cookie : cookies) {
                if(StringUtils.equalsIgnoreCase(SGS_TOKEN, cookie.getName())){
                    return cookie.getValue();
                }
            }
        } catch (Exception e) {
            throw new BizException("Failed to get cache userinfo");
        }
        return sgsToken;
    }

    /**
     * Get SGS UserInfo
     * @param token - token generated by user management
     * @return UserInfo
     */
    public UserInfo getUser(String token) {
        if (StringUtils.isBlank(token)){
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes instanceof  ServletRequestAttributes){
                token = getToken(((ServletRequestAttributes)requestAttributes).getRequest());
            }
            if (StringUtils.isBlank(token)){
                return null;
            }
        }
        return redisHelper.get(token);
    }

    public String getUserLabCode(String token){
        UserInfo user = getUser(token);
        return user == null ? "" : user.getCurrentLabCode();
    }

    public String getUserProductLineCodeByLab(String labCode) {
        String[] splitLabs = labCode.split(" ");
        if (splitLabs != null && splitLabs.length > 1){
            return splitLabs[1].trim();
        }
        return StringUtils.EMPTY;
    }

    /**
     * Get SGS UserInfo
     * @param request
     * @return
     */
    public UserInfo getUser(HttpServletRequest request) {
        String sgsToken = getToken(request);
        if (StringUtils.isNoneBlank(sgsToken)) {
            return getUser(sgsToken);
        }
        return null;
    }

    /**
     *
     * @param sgsToken
     * @return
     */
    public UserInfo getUserInfo(String sgsToken) {
        return getUser(sgsToken);
    }

    /**
     *
     * @return
     */
    public String getToken() {
        return getToken(this.getRequestContext());
    }

    /**
     *
     * @return
     */
    public UserInfo getUser() {
        String sgsToken = getToken(this.getRequestContext());
        if (StringUtils.isNoneBlank(sgsToken)) {
            return getUser(sgsToken);
        }
        return null;
    }

    /**
     *
     * @param sgsToken
     * @return
     */


    /**
     *
     * @return
     */
    public SysUserInfo getSysUserInfo(){
        return this.getSysUserInfo(getToken());
    }

    /**
     *
     * @param sgsToken
     * @return
     */
    public SysUserInfo getSysUserInfo(String sgsToken){
        SysUserInfo sysUser = new SysUserInfo();
        try {
            UserInfo user = this.getUser(sgsToken);
            if(user == null){
                return null;
            }
            if (!StringUtils.isEmpty(user.getCurrentLabCode())){
                String[] splitLabs = user.getCurrentLabCode().split(" ");
                if (splitLabs != null && splitLabs.length > 1){
                    sysUser.setLab(splitLabs[0].trim());
                    sysUser.setBu(splitLabs[1].trim());
                }
            }
        }catch (Exception ex){
            logger.error("TokenClient.getSysUserInfo Error：", ex);
        }
        return sysUser;
    }
}
