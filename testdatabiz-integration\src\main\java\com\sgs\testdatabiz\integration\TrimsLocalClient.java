package com.sgs.testdatabiz.integration;

import com.google.common.collect.Lists;
import com.sgs.trimslocal.facade.ICitationFacade;
import com.sgs.trimslocal.facade.IPpArtifactRelFacade;
import com.sgs.trimslocal.facade.IPpFacade;
import com.sgs.trimslocal.facade.model.artifact.req.PpArtifactRelListReq;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRsp;
import com.sgs.trimslocal.facade.model.citation.req.CitationVersionReq;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationVersionRsp;
import com.sgs.trimslocal.facade.model.common.BaseResponse;
import com.sgs.trimslocal.facade.model.pp.req.GetPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Component
public class TrimsLocalClient {
    private static final Logger logger = LoggerFactory.getLogger(TrimsLocalClient.class);
    @Autowired
    private IPpFacade ppFacade;
    @Autowired
    private IPpArtifactRelFacade iPpArtifactRelFacade;
    @Autowired
    private ICitationFacade citationFacade;

    public List<GetPpBaseInfoRsp> getPpBaseInfo(Set<Long> ppBaseIds){
        GetPpInfoReq infoReq = new GetPpInfoReq();
        infoReq.setPpBaseIds(ppBaseIds);
        BaseResponse<List<GetPpBaseInfoRsp>> response = ppFacade.getPpBaseInfo(infoReq);
        if(response == null || response.getStatus() != 200){
            return Lists.newArrayList();
        }
        return response.getData();
    }

    /**
     *
     * @param aIds
     * @return
     */
    public List<PpArtifactRsp> getPpArtifactRelListByArtifactIds(Set<Long> aIds){
        if (aIds == null || aIds.isEmpty()){
            return null;
        }
        PpArtifactRelListReq ppArtifactRelListReq = new PpArtifactRelListReq();
        ppArtifactRelListReq.setArtifactIds(Lists.newArrayList(aIds));
        BaseResponse<List<PpArtifactRsp>> response = iPpArtifactRelFacade.getPpArtifactRelListByArtifactIds(ppArtifactRelListReq);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
            return Lists.newArrayList();
        }
        return response.getData();
    }

    /**
     *
     * @param citationVersionIds
     * @return
     */
    public List<CitationVersionRsp> getCitationInfoList(Set<Integer> citationVersionIds){
        if (citationVersionIds == null || citationVersionIds.isEmpty()){
            return null;
        }
        CitationVersionReq citationVersionReq = new CitationVersionReq();
        citationVersionReq.setCitationVersionIds(citationVersionIds);
        BaseResponse<List<CitationVersionRsp>> response = citationFacade.getCitationInfoByCitationVersionId(citationVersionReq);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
            return Lists.newArrayList();
        }
        return response.getData();
    }
}
