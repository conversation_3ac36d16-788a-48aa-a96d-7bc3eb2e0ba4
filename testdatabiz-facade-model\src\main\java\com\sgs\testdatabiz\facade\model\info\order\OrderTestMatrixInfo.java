package com.sgs.testdatabiz.facade.model.info.order;

/**
 *
 */
public final class OrderTestMatrixInfo {
    /**
     *
     */
    private Long ppBaseId;

    /**
     *
     */
    private Integer ppVersionId;

    /**
     *
     */
    private Integer testLineMappingId;

    /**
     *
     */
    private String subContractNo;

    /**
     *
     */
    private String slimCode;

    /**
     *
     */
    private String testLineInstanceId;

    /**
     *
     */
    private String testSampleId;

    /**
     *
     */
    private String testMatrixId;

    /**
     *
     */
    private String testSampleNo;

    /**
     *
     */
    private Integer sampleType;

    /**
     *
     */
    private String category;

    /**
     *
     */
    private int ppNo;

    /**
     *
     */
    private int mappingPpNo;

    /**
     *
     */
    private Integer testLineId;

    /**
     *
     */
    private Integer testLineVersionId;

    /**
     *
     */
    private Integer citationId;

    /**
     *
     */
    private Integer citationVersionId;

    /**
     *
     */
    private Integer testLineType;

    /**
     * 1：存在订单上、2：SLIM Mapping 上转换的
     */
    private int orderMatrix;

    public Long getPpBaseId() {
        return ppBaseId;
    }

    public void setPpBaseId(Long ppBaseId) {
        this.ppBaseId = ppBaseId;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    public Integer getTestLineMappingId() {
        return testLineMappingId;
    }

    public void setTestLineMappingId(Integer testLineMappingId) {
        this.testLineMappingId = testLineMappingId;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public String getSlimCode() {
        return slimCode;
    }

    public void setSlimCode(String slimCode) {
        this.slimCode = slimCode;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }

    public String getTestSampleId() {
        return testSampleId;
    }

    public void setTestSampleId(String testSampleId) {
        this.testSampleId = testSampleId;
    }

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public String getTestSampleNo() {
        return testSampleNo;
    }

    public void setTestSampleNo(String testSampleNo) {
        this.testSampleNo = testSampleNo;
    }

    public Integer getSampleType() {
        return sampleType;
    }

    public void setSampleType(Integer sampleType) {
        this.sampleType = sampleType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public int getPpNo() {
        return ppNo;
    }

    public void setPpNo(int ppNo) {
        this.ppNo = ppNo;
    }

    public int getMappingPpNo() {
        return mappingPpNo;
    }

    public void setMappingPpNo(int mappingPpNo) {
        this.mappingPpNo = mappingPpNo;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getTestLineVersionId() {
        return testLineVersionId;
    }

    public void setTestLineVersionId(Integer testLineVersionId) {
        this.testLineVersionId = testLineVersionId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public Integer getCitationVersionId() {
        return citationVersionId;
    }

    public void setCitationVersionId(Integer citationVersionId) {
        this.citationVersionId = citationVersionId;
    }

    public Integer getTestLineType() {
        return testLineType;
    }

    public void setTestLineType(Integer testLineType) {
        this.testLineType = testLineType;
    }

    public int getOrderMatrix() {
        return orderMatrix;
    }

    public void setOrderMatrix(int orderMatrix) {
        this.orderMatrix = orderMatrix;
    }
}
