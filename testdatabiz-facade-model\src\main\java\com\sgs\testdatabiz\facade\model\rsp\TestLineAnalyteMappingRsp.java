package com.sgs.testdatabiz.facade.model.rsp;

import com.sgs.framework.core.common.PrintFriendliness;

public final class TestLineAnalyteMappingRsp extends PrintFriendliness {
    /**
     *
     */
    private Integer id;

    /**
     *
     */
    private Integer testLineMappingId;

    /**
     *
     */
    private String analyteCode;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTestLineMappingId() {
        return testLineMappingId;
    }

    public void setTestLineMappingId(Integer testLineMappingId) {
        this.testLineMappingId = testLineMappingId;
    }

    public String getAnalyteCode() {
        return analyteCode;
    }

    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode;
    }
}
