package com.sgs.testdatabiz.facade.model.req.rd;

import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 11:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ImportQuotationReq extends BaseModel {

    private String reportNo;

//    private RdOrderDTO order;
    private List<RdQuotationDTO> quotationList;

    @Override
    public String getExtId() {
        return reportNo;
    }
}
