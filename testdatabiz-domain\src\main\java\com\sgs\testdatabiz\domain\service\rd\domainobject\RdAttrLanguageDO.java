/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.framework.tool.utils.Func;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdAttrLanguageDO {
    private Integer languageId;
    private String labelName;
    private String customerLabel;
    private String value;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;


    public String getValue() {
        return Func.isBlank(value) ? "" : value;
    }
}
