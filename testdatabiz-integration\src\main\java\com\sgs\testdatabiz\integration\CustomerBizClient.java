package com.sgs.testdatabiz.integration;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.testdatabiz.core.config.InterfaceConfig;
import com.sgs.testdatabiz.facade.model.dto.TrfReportDTO;
import com.sgs.testdatabiz.facade.model.dto.ValidationStatsDTO;
import com.sgs.testdatabiz.integration.config.req.ConfigGetReq;
import com.sgs.testdatabiz.integration.model.dict.DictValueDTO;
import com.sgs.testdatabiz.integration.model.validation.ValidationRequest;
import com.sgs.testdatabiz.integration.model.validation.ValidationResponse;

@Component
public class CustomerBizClient {
    private static final Logger logger = LoggerFactory.getLogger(CustomerBizClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;

    private static final String Fail = "Fail";

    private static final String Header_REQUEST_ID = "requestId";
    private static final String Header_SYSTEM_ID="systemId";
    private static final String Header_LAB_CODE = "labCode";


    public CustomResult<String> queryConfig(ConfigGetReq req) {
        CustomResult customResult = new CustomResult(false);

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getBaseUrl(), "customerbiz/api/config/queryConfig");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(localiLayerUrl, req, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (response.getStatus() == 200 ) {
                String data = response.getData().toString();
                customResult.setData(data);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.queryConfig param({})  请求失败:{}", JSONObject.toJSONString(req), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
        } catch (Exception e) {
            logger.error("queryConfig failure.{}", e);
        }
        return customResult;
    }


    /**
     * 调用dictValueService的getDictValueList方法，获取字典值列表
     * 
     * @param systemId 系统ID
     * @param dictType 字典类型
     * @return 字典值列表
     */
    public CustomResult<List<DictValueDTO>> getDictValueList(Integer systemId, String dictType) {
        CustomResult<List<DictValueDTO>> customResult = new CustomResult<>(false);
        String url = String.format("%s%s", interfaceConfig.getBaseUrl(), "/customerbiz/api/config/queryDictValue");
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("systemId", systemId);
            params.put("dictType", dictType);
            
            // 记录请求日志
            logger.info("getDictValueList request, systemId:{}, dictType:{}", systemId, dictType);
            
            String jsonResult = HttpClientUtil.post(url, params);
            // 解析返回结果
            BaseResponse response = JSON.parseObject(jsonResult, BaseResponse.class);
            if (response != null && response.getStatus() == 200) {
                // 请求成功,记录返回结果
                logger.info("getDictValueList success, response:{}", jsonResult);
                List<DictValueDTO> dictValues = JSON.parseArray(JSON.toJSONString(response.getData()), DictValueDTO.class);
                customResult.setSuccess(true);
                customResult.setData(dictValues);
            } else {
                // 请求失败,记录错误信息
                String errorMsg = response != null ? response.getMessage() : "response is null";
                logger.error("getDictValueList failed, error:{}", errorMsg);
                customResult.setMsg(errorMsg);
            }
        } catch (Exception e) {
            logger.error("getDictValueList failure.", e);
            customResult.setMsg(e.getMessage());
        }
        return customResult;
    }

    public void addStatsRecord(Integer systemId, String labCode, ValidationStatsDTO stats) {

        String url = String.format("%s%s", interfaceConfig.getBaseUrl(), "customerbiz/api/v2/validation/add_stats");
        try {
            Map<String,String> header=new HashMap<>();
            header.put(Header_SYSTEM_ID, String.valueOf(systemId));
            header.put(Header_LAB_CODE, labCode);
            header.put(Header_REQUEST_ID, UUID.randomUUID().toString());
            String jsonResult = HttpClientUtil.postJsonHeader(url,stats,header);
            logger.info("add_stats response {}, request {}", jsonResult,JSONObject.toJSONString(stats));
        } catch (Exception e) {
            logger.error("add_stats failure.", e);
        }
    }

    public CustomResult<ValidationResponse> validationReportData(ValidationRequest reqs ) {

        CustomResult<ValidationResponse> customResult = new CustomResult<ValidationResponse>(false);
        String localiLayerUrl = String.format("%s%s", interfaceConfig.getBaseUrl(), "customerbiz/api/v2/validation/customized");
        try {

            Map<String,String> header=new HashMap<>();
            header.put(Header_SYSTEM_ID,String.valueOf(reqs.getSystemId()));
            header.put(Header_LAB_CODE, reqs.getLabCode());
            header.put(Header_REQUEST_ID, UUID.randomUUID().toString());
            String jsonResult = HttpClientUtil.postJsonHeader(localiLayerUrl,reqs,header);
            BaseResponse response = JSON.parseObject(jsonResult, BaseResponse.class);
            if (response.getStatus() == 200 ) {
                customResult.setSuccess(true);
                setResponse(response,reqs, customResult);
                return customResult;
            }else if(response.getStatus()!=500){
                setResponse(response,reqs, customResult);
                return customResult;
            }
            logger.error("validation param({})  请求失败:{}", JSONObject.toJSONString(reqs), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
        } catch (Exception e) {
            logger.error("validation failure.{}", e);
            customResult.setMsg(e.getMessage());
        }
        return customResult;
    }

    private void setResponse(BaseResponse response,ValidationRequest reqs, CustomResult<ValidationResponse> customResult) {
        ValidationResponse validationResponse = JSON.parseObject(response.getData().toString(), ValidationResponse.class);
        customResult.setData(validationResponse);
    }

    public CustomResult<List<TrfReportDTO>> reportRelationship(List<String> reportNoList) {
        CustomResult<List<TrfReportDTO>> customResult = new CustomResult<>(false);
        String url = String.format("%s%s", interfaceConfig.getBaseUrl(), "customerbiz/api/v2/trf/report/relationship");
        try {
            Map<String,String> header = new HashMap<>();
            header.put(Header_REQUEST_ID, UUID.randomUUID().toString());
            
            Map<String, Object> body = new HashMap<>();
            body.put("reportNoList", reportNoList);
            String jsonResult = HttpClientUtil.postJsonHeader(url, body, header);
            BaseResponse response = JSON.parseObject(jsonResult, BaseResponse.class);
            if (response.getStatus() == 200) {
                customResult.setSuccess(true);
                List<TrfReportDTO> trfReportPOs = Objects.isNull(response.getData()) ? Collections.emptyList() : JSON.parseArray(response.getData().toString(), TrfReportDTO.class);
                customResult.setData(trfReportPOs);
                return customResult;
            }
            logger.error("reportRelationship param({}) request failed:{}", JSONObject.toJSONString(reportNoList), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
            
        } catch (Exception e) {
            logger.error("reportRelationship failure.", e);
            customResult.setMsg(e.getMessage());
        }
        return customResult;
    }


}
