package com.sgs.testdatabiz.domain.service.utils;

import com.sgs.testdatabiz.domain.service.rd.filter.constants.ConclusionFilterConstants;

public class ConclusionCodeToStringUtil {

    public static String convertConclusionCode(String code) {
        if (code == null) {
            return null;
        }

        try {
            int numericCode = Integer.parseInt(code);
            switch (numericCode) {
                case ConclusionFilterConstants.Code.CODE_PASS:
                    return ConclusionFilterConstants.Code.STR_PASS;
                case ConclusionFilterConstants.Code.CODE_FAIL:
                    return ConclusionFilterConstants.Code.STR_FAIL;
                case ConclusionFilterConstants.Code.CODE_NC:
                    return ConclusionFilterConstants.Code.STR_NC;
                case ConclusionFilterConstants.Code.CODE_CANCELLED:
                    return ConclusionFilterConstants.Code.STR_CANCELLED;
                default:
                    return code;
            }
        } catch (NumberFormatException e) {
            // 如果已经是字符串形式，统一转换为标准格式
            String upperCode = code.toUpperCase();
            switch (upperCode) {
                case "P":
                    return ConclusionFilterConstants.Code.STR_PASS;
                case "F":
                    return ConclusionFilterConstants.Code.STR_FAIL;
                default:
                    return upperCode;
            }
        }
    }
}
