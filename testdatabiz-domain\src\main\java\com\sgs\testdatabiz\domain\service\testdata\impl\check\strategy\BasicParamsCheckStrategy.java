package com.sgs.testdatabiz.domain.service.testdata.impl.check.strategy;

import com.sgs.testdatabiz.domain.service.testdata.impl.check.RTDContext;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 基础参数校验（为空等）
 * @author: shawn.yang
 * @create: 2023-03-27 17:09
 */
public class BasicParamsCheckStrategy extends AbstractDataCheckStrategy{
    private static final Logger logger = LoggerFactory.getLogger(BasicParamsCheckStrategy.class);


    @Override
    public boolean doCheck(final ReportTestDataInfo reportTestData,final RTDContext rtdContext) {
        if (reportTestData.getSourceType() <0){
            return false;
        }

        if (StringUtils.isEmpty(reportTestData.getProductLineCode())){
            return false;
        }

        //todo check....



        return true;
    }

    @Override
    public boolean isMute() {
        return false;
    }
}
