package com.sgs.testdatabiz.facade.model.enums;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @ClassName BioFieldEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/5/4
 */
public enum BioFieldEnums {

    OrderNo(Lists.newArrayList("NOTES.OrderNo", "GPO.OrderNo")),
    ResponsibleTeam(Lists.newArrayList("NOTES.ResponsibleTeam")),
    ResponsibleCR(Lists.newArrayList("NOTES.ResponsibleCR")),
    SubcontractCreatedBy(Lists.newArrayList("NOTES.SubcontractCreatedBy")),
    SubcontractCreatedDate(Lists.newArrayList("NOTES.SubcontractCreatedDate")),
    OrderExpectedDueDate(Lists.newArrayList("NOTES.OrderExpectedDueDate")),
    SubcontractNo(Lists.newArrayList("NOTES.SubcontractNo", "GPO.SubcontractNo"));

    private List<String> fieldName;

    BioFieldEnums(List<String> fieldName) {
        this.fieldName = fieldName;
    }

    public List<String> getFieldName() {
        return fieldName;
    }

    public static boolean check(String bioField, BioFieldEnums... enums) {
        if (StringUtils.isBlank(bioField) || enums == null || enums.length == 0) {
            return false;
        }
        for (BioFieldEnums anEnum : enums) {
            for (String fieldNameItem: anEnum.fieldName){
                if (StringUtils.equalsIgnoreCase(fieldNameItem, bioField)) {
                    return true;
                }
            }
        }
        return false;
    }
}
