package com.sgs.testdatabiz.domain.service.rd.filter;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.domain.service.rd.domainobject.ReportDataDO;
import com.sgs.testdatabiz.domain.service.rd.filter.chain.FilterChainBuilder;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

@Slf4j
@Component
public class ReportDataFilterStrategyFactory {
    
    @Autowired
    private FilterChainBuilder defaultChainBuilder;

    /**
     * 执行所有过滤器
     */
    public ReportDataDO applyFilters(ReportDataDO data, FilterContext context) {
        if (data == null) {
            return null;
        }
        
        // 获取所有过滤器
        List<ReportDataFilter> filters = defaultChainBuilder.buildFilters();
        if (Func.isEmpty(filters)) {
            return data;
        }
        log.info("ReportDataFilters:{}", filters);
        // 依次执行过滤器
        ReportDataDO result = data;
        for (ReportDataFilter filter : filters) {
            result = filter.doFilter(result, context);
        }
        
        return result;
    }
} 