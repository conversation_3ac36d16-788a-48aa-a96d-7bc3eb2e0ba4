package com.sgs.testdatabiz.facade.model.rsp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

/**
 * @ClassName SlimConfigRsp
 * @Description slim 配置表
 * <AUTHOR>
 * @Date 2022/5/31
 */
@ToString
@ApiModel(value = "SlimConfigRsp",description = "SlimConfigRsp")
public class SlimConfigRsp {
    @ApiModelProperty(notes = "slimLabCode")
    private String slimLabCode;
    @ApiModelProperty(notes = "productLineCode")
    private String productLineCode;
    @ApiModelProperty(notes = "labCode")
    private String labCode;
    @ApiModelProperty(notes = "locationCode")
    private String locationCode;
    @ApiModelProperty(notes = "fromSlimRegisterPath")
    private String fromSlimRegisterPath;
    @ApiModelProperty(notes = "fromSlimReportDataPath")
    private String fromSlimReportDataPath;
    @ApiModelProperty(notes = "webServicesUrl")
    private String webServicesUrl;
    @ApiModelProperty(notes = "ftpAddr")
    private String ftpAddr;
    @ApiModelProperty(notes = "ftpUserName")
    private String ftpUserName;
    @ApiModelProperty(notes = "ftpPassWord")
    private String ftpPassWord;
    @ApiModelProperty(notes = "toNotesRtfPath")
    private String toNotesRtfPath;

    @ApiModelProperty(notes = "ToSlimJobPath")
    private String toSlimJobPath;

    @ApiModelProperty(notes = "email")
    private String email;
    @ApiModelProperty(notes = "ValidateType")
    private Integer ValidateType;

//    @ApiModelProperty(notes = "文件格式  内部使用")
//    @JsonIgnore
//    private FtpFilterType filterType;
    @ApiModelProperty(notes = "端口号 内部使用")
    @JsonIgnore
    private Integer port;


    public String getToSlimJobPath() {
        return toSlimJobPath;
    }

    public void setToSlimJobPath(String toSlimJobPath) {
        this.toSlimJobPath = toSlimJobPath;
    }

    public String getSlimLabCode() {
        return slimLabCode;
    }

    public void setSlimLabCode(String slimLabCode) {
        this.slimLabCode = slimLabCode;
    }

    public String getProductLineCode() {
        return productLineCode;
    }

    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getFromSlimRegisterPath() {
        return fromSlimRegisterPath;
    }

    public void setFromSlimRegisterPath(String fromSlimRegisterPath) {
        this.fromSlimRegisterPath = fromSlimRegisterPath;
    }

    public String getFromSlimReportDataPath() {
        return fromSlimReportDataPath;
    }

    public void setFromSlimReportDataPath(String fromSlimReportDataPath) {
        this.fromSlimReportDataPath = fromSlimReportDataPath;
    }

    public String getWebServicesUrl() {
        return webServicesUrl;
    }

    public void setWebServicesUrl(String webServicesUrl) {
        this.webServicesUrl = webServicesUrl;
    }

    public String getFtpAddr() {
        return ftpAddr;
    }

    public void setFtpAddr(String ftpAddr) {
        this.ftpAddr = ftpAddr;
    }

    public String getFtpUserName() {
        return ftpUserName;
    }

    public void setFtpUserName(String ftpUserName) {
        this.ftpUserName = ftpUserName;
    }

    public String getFtpPassWord() {
        return ftpPassWord;
    }

    public void setFtpPassWord(String ftpPassWord) {
        this.ftpPassWord = ftpPassWord;
    }

    public String getToNotesRtfPath() {
        return toNotesRtfPath;
    }

    public void setToNotesRtfPath(String toNotesRtfPath) {
        this.toNotesRtfPath = toNotesRtfPath;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getValidateType() {
        return ValidateType;
    }

    public void setValidateType(Integer validateType) {
        ValidateType = validateType;
    }

//    public FtpFilterType getFilterType() {
//        return filterType;
//    }
//
//    public void setFilterType(FtpFilterType filterType) {
//        this.filterType = filterType;
//    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }
}
