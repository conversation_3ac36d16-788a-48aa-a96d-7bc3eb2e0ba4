<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.RdReportInfoExtMapper" >
<insert id="batchReportInsert" parameterType="list" >
    insert into tb_report
    (`id`,`lab_id`,`bu_id`,
    `system_id`,`report_id`,`report_no`,
    `original_report_no`,`report_header`,`report_address`,
    `report_status`,`report_due_date`,`report_certificate_name`,
    `report_created_by`,`report_created_date`,`report_approver_by`,
    `report_approver_date`,`softcopy_delivery_date`,`conclusion_code`,
    `customer_conclusion`,`review_conclusion`,`conclusion_remark`,
    `active_indicator`,`created_by`,`created_date`,
    `modified_by`,`modified_date`,`last_modified_timestamp`,
    `exclude_customer_interface`,`report_source_type`)
    values
    <foreach collection="list" item="item" index="index" separator="," >
        ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.buId, jdbcType=BIGINT},
        #{ item.systemId, jdbcType=BIGINT},#{ item.reportId, jdbcType=VARCHAR},#{ item.reportNo, jdbcType=VARCHAR},
        #{ item.originalReportNo, jdbcType=VARCHAR},#{ item.reportHeader, jdbcType=VARCHAR},#{ item.reportAddress, jdbcType=VARCHAR},
        #{ item.reportStatus, jdbcType=INTEGER},#{ item.reportDueDate, jdbcType=TIMESTAMP},#{ item.reportCertificateName, jdbcType=VARCHAR},
        #{ item.reportCreatedBy, jdbcType=VARCHAR},#{ item.reportCreatedDate, jdbcType=TIMESTAMP},#{ item.reportApproverBy, jdbcType=VARCHAR},
        #{ item.reportApproverDate, jdbcType=TIMESTAMP},#{ item.softcopyDeliveryDate, jdbcType=TIMESTAMP},#{ item.conclusionCode, jdbcType=VARCHAR},
        #{ item.customerConclusion, jdbcType=VARCHAR},#{ item.reviewConclusion, jdbcType=VARCHAR},#{ item.conclusionRemark, jdbcType=VARCHAR},
        #{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
        #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP},
        #{ item.excludeCustomerInterface, jdbcType=VARCHAR},#{ item.reportSourceType, jdbcType=INTEGER})
    </foreach>
    ON DUPLICATE KEY UPDATE
    original_report_no = VALUES(original_report_no),
    report_header = VALUES(report_header),
    report_address = VALUES(report_address),
    report_status = VALUES(report_status),
    report_due_date = VALUES(report_due_date),
    report_certificate_name = VALUES(report_certificate_name),
    report_created_by = VALUES(report_created_by),
    report_created_date = VALUES(report_created_date),
    report_approver_by = VALUES(report_approver_by),
    report_approver_date = VALUES(report_approver_date),
    softcopy_delivery_date = VALUES(softcopy_delivery_date),
    conclusion_code = VALUES(conclusion_code),
    customer_conclusion = VALUES(customer_conclusion),
    conclusion_remark = VALUES(conclusion_remark),
    created_by = VALUES(created_by),
    created_date = VALUES(created_date),
    modified_by = VALUES(modified_by),
    modified_date = VALUES(modified_date),
    review_conclusion = VALUES(review_conclusion),
    last_modified_timestamp = VALUES(last_modified_timestamp),
    exclude_customer_interface = VALUES(exclude_customer_interface),
    report_source_type = VALUES(report_source_type)
</insert>
</mapper>