/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * //TODO 这个时对外的映射mapping没有用
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestItemMappingDTO implements Serializable {
    private String testLineInstanceId;
    private List<RdAnalyteDTO> analyteList;
    private RdTestItemMappingExternalDTO external;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
