package com.sgs.testdatabiz.core.util;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 *
 */
public final class ListUtils {

    /**
     *
     * @param lists
     * @param predicate
     * @param <T>
     * @return
     */
    public static <T> T findFirst(List<T> lists, Predicate<T> predicate){
        if (lists == null || lists.isEmpty() || predicate == null){
            return null;
        }
        return lists.stream().filter(predicate).findFirst().orElse(null);
    }

    /**
     *
     * @param lists
     * @param predicate
     * @param <T>
     * @return
     */
    public static <T> List<T> filter(List<T> lists, Predicate<T> predicate){
        if (lists == null || lists.isEmpty() || predicate == null){
            return Lists.newArrayList();
        }
        return lists.stream().filter(predicate).collect(Collectors.toList());
    }
}
