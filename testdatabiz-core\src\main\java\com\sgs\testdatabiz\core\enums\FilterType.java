package com.sgs.testdatabiz.core.enums;

/**
 * 过滤器类型枚举
 */
public enum FilterType {
    
    CONCLUSION_CONVERTER("结论码转换过滤器"),
    TEST_LINE_STATUS("测试线状态过滤器"),
    ATTACHMENT_VISIBILITY("附件可见性过滤器"),
    TEST_LINE_REMARK("测试线备注过滤器"),
    PRETREATMENT_TEST_LINE("预处理测试线过滤器"),
    INVALID_STATUS("无效状态过滤器"),
    MATRIX_TEST_LINE("矩阵测试线过滤器"),
    REMOVE_FIELDS("字段移除过滤器");

    private String description;

    FilterType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
} 