package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/5/7 16:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCustomerProductDTO {

    private RdCustomerProductIdDTO id;

    @Data
    public static class RdCustomerProductIdDTO{
        @ApiModelProperty(value = "customerProductId",dataType = "string", required = true)
        private String customerProductId;

        @ApiModelProperty(value = "external",dataType = "RdCustomerProductIdExternalDTO")
        private RdCustomerProductIdExternalDTO external;

        @Data
        public static class RdCustomerProductIdExternalDTO{
            @ApiModelProperty(value = "customerProductAssocId",dataType = "string", required = true)
            private String customerProductAssocId;
        }
    }
}
