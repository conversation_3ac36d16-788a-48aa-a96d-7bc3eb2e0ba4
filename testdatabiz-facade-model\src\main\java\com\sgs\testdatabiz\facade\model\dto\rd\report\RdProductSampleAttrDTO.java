/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdProductSampleAttrDTO implements Serializable {

    @ApiModelProperty(value = "seq",dataType = "integer", required = true)
    private Integer seq;
    @ApiModelProperty(value = "labelName",dataType = "string", required = true)
    private String labelName;
    @ApiModelProperty(value = "customerLabel",dataType = "string", required = true)
    private String customerLabel;
    @ApiModelProperty(value = "dataType",dataType = "string", required = true)
    private String dataType;
    @ApiModelProperty(value = "labelCode",dataType = "string", required = true)
    private String labelCode;
    private String value;
    @ApiModelProperty(value = "displayInReport",dataType = "string", required = true)
    private String displayInReport;
    //DIG-9960
    @ApiModelProperty(value = "mandatoryFlag",dataType = "string", required = true)
    private String mandatoryFlag;
    private List<RdAttrLanguageDTO> languageList;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "activeIndicator",dataType = "integer", required = true)
    private Integer activeIndicator;
}