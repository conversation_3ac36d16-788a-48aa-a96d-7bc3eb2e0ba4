package com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.ReadXmlLogPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReadXmlLogExtMapper {

    int insert(ReadXmlLogPO readXmlLogPO);

//    List<ReadXmlLogInfo> queryReadXmlLog(@Param("orderNo") String orderNo, @Param("subcontractNo") String subcontractNo, @Param("slimJobNo") String slimJobNo);

    Integer batchInsert(@Param("xmlLogList") List<ReadXmlLogPO> xmlLogList);

}
