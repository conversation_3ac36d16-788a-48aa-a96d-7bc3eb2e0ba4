<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestSampleGroupMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sample_group_id" property="sampleGroupId" jdbcType="VARCHAR" />
    <result column="sample_id" property="sampleId" jdbcType="VARCHAR" />
    <result column="main_sample_flag" property="mainSampleFlag" jdbcType="INTEGER" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sample_group_id, sample_id, main_sample_flag, active_indicator, created_date, 
    created_by, modified_date, modified_by, last_modified_timestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_sample_group
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_test_sample_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupExample" >
    delete from tb_test_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupPO" >
    insert into tb_test_sample_group (id, sample_group_id, sample_id, 
      main_sample_flag, active_indicator, created_date, 
      created_by, modified_date, modified_by, 
      last_modified_timestamp)
    values (#{id,jdbcType=BIGINT}, #{sampleGroupId,jdbcType=VARCHAR}, #{sampleId,jdbcType=VARCHAR}, 
      #{mainSampleFlag,jdbcType=INTEGER}, #{activeIndicator,jdbcType=TINYINT}, now(), 
      #{createdBy,jdbcType=VARCHAR}, now(), #{modifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupPO" >
    insert into tb_test_sample_group
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sampleGroupId != null" >
        sample_group_id,
      </if>
      <if test="sampleId != null" >
        sample_id,
      </if>
      <if test="mainSampleFlag != null" >
        main_sample_flag,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sampleGroupId != null" >
        #{sampleGroupId,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null" >
        #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="mainSampleFlag != null" >
        #{mainSampleFlag,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_sample_group
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sampleGroupId != null" >
        sample_group_id = #{record.sampleGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleId != null" >
        sample_id = #{record.sampleId,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSampleFlag != null" >
        main_sample_flag = #{record.mainSampleFlag,jdbcType=INTEGER},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_sample_group
    set id = #{record.id,jdbcType=BIGINT},
      sample_group_id = #{record.sampleGroupId,jdbcType=VARCHAR},
      sample_id = #{record.sampleId,jdbcType=VARCHAR},
      main_sample_flag = #{record.mainSampleFlag,jdbcType=INTEGER},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
    
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = now(),
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupPO" >
    update tb_test_sample_group
    <set >
      <if test="sampleGroupId != null" >
        sample_group_id = #{sampleGroupId,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null" >
        sample_id = #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="mainSampleFlag != null" >
        main_sample_flag = #{mainSampleFlag,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestSampleGroupPO" >
    update tb_test_sample_group
    set sample_group_id = #{sampleGroupId,jdbcType=VARCHAR},
      sample_id = #{sampleId,jdbcType=VARCHAR},
      main_sample_flag = #{mainSampleFlag,jdbcType=INTEGER},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
    
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = now(),
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_sample_group
      (`id`,`sample_group_id`,`sample_id`,
      `main_sample_flag`,`active_indicator`,`created_date`,
      `created_by`,`modified_date`,`modified_by`,
      `last_modified_timestamp`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.sampleGroupId, jdbcType=VARCHAR},#{ item.sampleId, jdbcType=VARCHAR},
      #{ item.mainSampleFlag, jdbcType=INTEGER},#{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.createdBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},
      #{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_sample_group 
      <set>
        <if test="item.sampleGroupId != null"> 
          `sample_group_id` = #{item.sampleGroupId, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleId != null"> 
          `sample_id` = #{item.sampleId, jdbcType = VARCHAR},
        </if> 
        <if test="item.mainSampleFlag != null"> 
          `main_sample_flag` = #{item.mainSampleFlag, jdbcType = INTEGER},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>