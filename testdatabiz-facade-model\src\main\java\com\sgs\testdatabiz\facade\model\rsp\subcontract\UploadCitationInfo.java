package com.sgs.testdatabiz.facade.model.rsp.subcontract;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.List;

public class UploadCitationInfo extends PrintFriendliness {

    private String citationName;

    private Integer testLineId;

    private String evaluationAlias;

    private List<UploadTestInfo> citations;

    public String getCitationName() {
        return citationName;
    }

    public void setCitationName(String citationName) {
        this.citationName = citationName;
    }

    public List<UploadTestInfo> getCitations() {
        return citations;
    }

    public void setCitations(List<UploadTestInfo> citations) {
        this.citations = citations;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }
}
