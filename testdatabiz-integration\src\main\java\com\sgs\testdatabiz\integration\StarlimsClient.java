package com.sgs.testdatabiz.integration;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.otsnotes.facade.model.enums.EnvironmentType;
import com.sgs.testdatabiz.core.config.StarLimsConfig;
import com.sgs.testdatabiz.core.util.EnvUtil;
import com.sgs.testdatabiz.core.util.HttpClientUtil;
import com.sgs.testdatabiz.facade.model.req.starlims.FolderReportInfoReq;
import com.sgs.testdatabiz.facade.model.req.starlims.FolderReportInfoRsp;
import com.sgs.testdatabiz.facade.model.req.starlims.FolderStarlimsReportInfoRsp;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveStarLimsReportReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class StarlimsClient {
    private static final Logger logger = LoggerFactory.getLogger(StarlimsClient.class);
    @Autowired
    private StarLimsConfig starLimsConfig;
    @Autowired
    private EnvUtil envUtil;

    /**
     *
     * @param reqObject
     * @return
     */
    public FolderReportInfoRsp getFolderReportInfoList(FolderReportInfoReq reqObject){
        try {
            String reqUrl = String.format("%s/ilayer.REST.lims/folderReport", starLimsConfig.getStarLimsUrl());
            Map<String,String> headerMaps = Maps.newHashMap();
            headerMaps.put(starLimsConfig.getHeaderUserKey(),starLimsConfig.getHeaderUserVal());
            headerMaps.put(starLimsConfig.getHeaderPassKey(),starLimsConfig.getHeaderPassVal());

            FolderReportInfoRsp rspObject = HttpClientUtil.doPost(reqUrl, headerMaps, reqObject, FolderReportInfoRsp.class);
            logger.info("FolderReportInfoRsp rspObject：{}", rspObject);
            return rspObject;
        }catch (Exception ex){
            logger.error("StarlimsClient.getFolderReportInfoList({}) reqObject（{}） 信息异常：{}.", reqObject.getFolderNo(), reqObject, ex);
            return null;
        }
    }


    /**
     *
     * @param reqObject
     * @return
     */
    public FolderStarlimsReportInfoRsp getFolderReportInfoList(FolderReportInfoReq reqObject,String labCode){
        try {
            final String apiUrl = "ilayer.REST.lims/folderReport";
            String reqUrl = String.format("%s/ilayer.REST.lims/folderReport", starLimsConfig.getStarLimsUrl());
//            String reqUrl = "https://starlims-cn.sgs.net/starlims11.sgs.prod/ilayer.REST.lims/folderReport";
            EnvironmentType activeProfile = envUtil.getActiveProfile();
            switch (activeProfile){
                case Prod:
                    // https://starlims-cn2.sgs.net/sgs.prod/ilayer.REST.lims/folderReport  -- GZ、SZ CCL
                    // TODO 临时使用，清洗GZ SL的conclusionId为空的情况
                    if (Lists.newArrayList("GZ CCL", "SZ CCL","GZ SL").contains(labCode)){
                        reqUrl = String.format("https://starlims-cn2.sgs.net/sgs.prod/%s", apiUrl);
                    }
                    break;
            }
            Map<String,String> headerMaps = Maps.newHashMap();
            headerMaps.put(starLimsConfig.getHeaderUserKey(),starLimsConfig.getHeaderUserVal());
            headerMaps.put(starLimsConfig.getHeaderPassKey(),starLimsConfig.getHeaderPassVal());

            FolderStarlimsReportInfoRsp rspObject = HttpClientUtil.doPost(reqUrl, headerMaps, reqObject, FolderStarlimsReportInfoRsp.class);
            logger.info("FolderReportInfoRsp rspObject：{}", rspObject);
            return rspObject;
        }catch (Exception ex){
            logger.error("StarlimsClient.getFolderReportInfoList({}) reqObject（{}） 信息异常：{}.", reqObject.getFolderNo(), reqObject, ex);
            return null;
        }
    }
}
