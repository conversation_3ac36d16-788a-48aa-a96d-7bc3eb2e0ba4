/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPpTestLineRelInput implements Serializable {

    private String ppTlRelId;
    private Long ppArtifactRelId;
    private Long ppBaseId;
    private Long rootPPBaseId;
    private Integer ppNo;
    private Integer ppVersionId;
    private String ppName;
    private String ppNotes;
    private Integer sectionId;
    private Integer sectionLevel;
    private String sectionName;
    private Long aid;
    private RdCitationInput citation;
    //    private RdConclusionDTO conclusion;
    private List<RdPpTestLineRelLangInput> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    /**
     * @since SCI-1276 Target Inspectorio
     */
    private String testCategoryCode;
}
