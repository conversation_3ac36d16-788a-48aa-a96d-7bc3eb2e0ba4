/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdServiceRequirementInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdServiceRequirementSampleDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceRequirementDO {
    private RdServiceRequirementReportDO report;
    private RdServiceRequirementSampleDTO sample;
    private RdServiceRequirementInvoiceDTO invoice;
    private String otherRequestRemark;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
