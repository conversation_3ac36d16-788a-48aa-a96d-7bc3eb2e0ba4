package com.sgs.testdatabiz.dbstorages.mybatis.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/23 16:43
 */
@Component
public class DataSourceBuilder {
    /**
     *
     */
    @Resource
    private DataSourceDynamicProperties dynamicDataSourceProperties;

    /**
     *
     * @return
     */
    public DruidDataSource buildDefaultDruidDataSource() {
        DruidDataSource dataSource = new DruidDataSource();

        dataSource.setInitialSize(dynamicDataSourceProperties.getInitialSize());
        dataSource.setMinIdle(dynamicDataSourceProperties.getMinIdle());
        dataSource.setMaxActive(dynamicDataSourceProperties.getMaxActive());
        dataSource.setMaxWait(dynamicDataSourceProperties.getMaxWait());
        dataSource.setTimeBetweenEvictionRunsMillis(dynamicDataSourceProperties.getTimeBetweenEvictionRunsMillis());
        dataSource.setMinEvictableIdleTimeMillis(dynamicDataSourceProperties.getMinEvictableIdleTimeMillis());

        // 验证失败时，是否将连接从池中丢弃
        dataSource.setTestWhileIdle(dynamicDataSourceProperties.isTestWhileIdle());
        dataSource.setTestOnBorrow(dynamicDataSourceProperties.isTestOnBorrow());
        dataSource.setTestOnReturn(dynamicDataSourceProperties.isTestOnReturn());
        dataSource.setPoolPreparedStatements(dynamicDataSourceProperties.isPoolPreparedStatements());
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(dynamicDataSourceProperties.getMaxPoolPreparedStatementPerConnectionSize());
        dataSource.setQueryTimeout(dynamicDataSourceProperties.getQueryTimeout());
        dataSource.setTransactionQueryTimeout(dynamicDataSourceProperties.getTransactionQueryTimeout());


        // 超时时间限制是否回收
        dataSource.setRemoveAbandoned(true);
        // 超时时间：单位为秒，3 * 60 = 180秒
        dataSource.setRemoveAbandonedTimeout(180);
        // 关闭Abandoned连接时输出错误日志
        dataSource.setLogAbandoned(true);

        return dataSource;
    }

    /**
     * 根据配置初始化所有数据源
     * @return
     */
    public DynamicDataSource getDynamicDataSource() {
        Map<Object, Object> targetDataSources = Maps.newHashMap();
        DynamicDataSource dynamicDataSource = new DynamicDataSource();

        // {SODA/GPO}_{DbName}_{Master/Slave}
        dynamicDataSourceProperties.getDynamic().entrySet().forEach(instanceEntry ->{
            // {SODA/GPO}
            String primary = instanceEntry.getKey().toUpperCase();
            DataSourceInstance dataSourceInstance = instanceEntry.getValue();
            dataSourceInstance.getSchema().entrySet().forEach(schemaEntry -> {
                // {DbName}
                String dbName = schemaEntry.getKey();
                schemaEntry.getValue().entrySet().forEach(connectEntry -> {
                    // {Master/Slave}
                    String masterSlave = connectEntry.getKey();

                    DruidDataSource dataSource = this.buildDefaultDruidDataSource();
                    DataSourceConnection connect = connectEntry.getValue();

                    dataSource.setDriverClassName(connect.getDriverClassName());
                    dataSource.setUrl(connect.getUrl());
                    dataSource.setUsername(connect.getUsername());
                    dataSource.setPassword(connect.getPassword());
                    /*dataSource.setValidationQuery(properties.getValidationQuery());
                    dataSource.setFilters("stat,wall");*/

                    String dataSourceKey = String.format("%s-%s", dbName, masterSlave).toUpperCase();
                    dynamicDataSource.put(dataSourceKey);

                    targetDataSources.put(dataSourceKey, dataSource);
                });
            });
        });
        // {SL/HL}_{DbName}_{Master/Slave}
        /*String defDataSourceKey = dynamicDataSourceProperties.getPrimary().toUpperCase();
        dynamicDataSource.setDefaultTargetDataSource(targetDataSources.get(defDataSourceKey));*/

        dynamicDataSource.setTargetDataSources(targetDataSources);

        dynamicDataSource.afterPropertiesSet();

        return dynamicDataSource;
    }

}
