package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 新增 certificate 对象，包含（CertificateName\AcceditationRemark、Report Approved Statement、Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCertificateDTO implements Serializable {
    private String certificateName;
    private String acceditationRemark;
    private String reportApprovedStatement;
    private String description;

}

