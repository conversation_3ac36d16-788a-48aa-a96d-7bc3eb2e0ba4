/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdOrderRelDTO extends RdOrderHeaderDTO implements Serializable {

    @ApiModelProperty(value = "systemId",dataType = "integer", required = true)
    private Integer systemId;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "0:无效 1:有效",dataType = "integer", required = true)
    private Integer activeIndicator;

    @ApiModelProperty(value = "orderInstanceId",dataType = "string", required = true)
    private String orderInstanceId;


}
