package com.sgs.testdatabiz.domain.service.testdata.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.domain.service.ReportTestDataService;
import com.sgs.testdatabiz.domain.service.testdata.IReportTestDataService;
import com.sgs.testdatabiz.facade.model.info.order.OrderTestMatrixInfo;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataResultInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixInfo;
import com.sgs.testdatabiz.facade.model.testdata.slim.SlimTestMatrixInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * @param <TInput>
 * @param <TOutput>
 */
@Deprecated
public abstract class AbstractTestDataServiceImpl<TInput, TOutput> implements IReportTestDataService<TInput, TOutput> {
    private final Logger logger = LoggerFactory.getLogger(AbstractTestDataServiceImpl.class);
    @Autowired
    private ReportTestDataService reportTestDataService;

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public CustomResult saveTestData(TInput reqObject) {
        CustomResult<ReportTestDataInfo> rspResult = new CustomResult();
        if (reqObject == null){
            return rspResult.fail("请求的对象为空.");
        }
        // 验证接口参数
        rspResult = this.inputValidate(reqObject);
        if (!rspResult.isSuccess()){
            return rspResult;
        }
        // 构建标准结构对象
        rspResult = this.inputBuild(reqObject);
        if (!rspResult.isSuccess()){
            return rspResult;
        }
        // 校验业务数据
        rspResult = this.checkData(rspResult.getData());
        if (!rspResult.isSuccess()){
            return rspResult;
        }
        // 保存Test Data数据
        return this.save(rspResult.getData());
    }

    /**
     * 校验数据
     * @param reqObject
     * @return
     */
    private CustomResult checkData(ReportTestDataInfo reqObject){
        CustomResult rspResult = new CustomResult();

        // 校验Test Matrix数据
        boolean isSuccess = this.checkTestMatrix(reqObject.getTestMatrixs());

        rspResult.setSuccess(isSuccess);
        rspResult.setData(reqObject);
        return rspResult;
    }

    /**
     * 校验Test Matrix数据
     * @param testMatrixs
     * @return
     */
    private boolean checkTestMatrix(List<TestDataTestMatrixInfo> testMatrixs) {
        if (testMatrixs == null || testMatrixs.isEmpty()){
            return false;
        }
        testMatrixs.removeIf(testMatrix->{
            // TODO 走规则引擎
            /*if (StringUtils.isBlank(testMatrix.getTestSampleNo()) && slimCheck){
                errorMsgs.add(this.getErrorMsg(EmailTypeEnum.SampleNo, String.format("EXTERNALIDENT:%s", testMatrix.getExternalident())));
                return true;
            }
            String slimSampleKey = String.format(String.format("%s_%s", testMatrix.getExternalCode(), testMatrix.getTestSampleNo()).toUpperCase());

            // TODO 走规则引擎
            if (dataType.check(ParseDataType.ParseFTPXml) && slimCheck && !testMatrixMaps.containsKey(slimSampleKey) && !testMatrixKey.contains(slimSampleKey)){
                testMatrixKey.add(slimSampleKey);
                String errorMsg = this.getTestMatrixError(reqParam.getTestMatrixs(), slimSamples, reqParam.getAlwaysSplits(), testMatrix.getExternalCode(), testMatrix.getTestSampleNo());
                if (!StringUtils.isBlank(errorMsg)){
                    errorMsgs.add(errorMsg);
                    return true;
                }
            }*/
            return this.checkTestData(testMatrix);
        });
        return !testMatrixs.isEmpty();
    }

    /**
     * 校验Test Data数据
     * @param testMatrix
     * @return
     */
    private boolean checkTestData(TestDataTestMatrixInfo testMatrix){
        List<TestDataResultInfo> testResults = testMatrix.getTestResults();
        if (testResults == null || testResults.isEmpty()){
            // TODO
            return true;
        }
        testResults.removeIf(tr->{
            // TODO Check Test Analyte Mapping



            return false;
        });
        return testResults.isEmpty();
    }
    // endregion

    /**
     * 保存Test Data数据
     * @param reqObject
     * @return
     */
    public CustomResult save(ReportTestDataInfo reqObject){
        return reportTestDataService.saveTestData(reqObject);
    }

    /**
     *
     * @param reportTestDataInfo
     * @return
     */
    @Override
    public CustomResult<TOutput> convertTestData(ReportTestDataInfo reportTestDataInfo) {
        return CustomResult.newSuccessInstance();
    }

    // region

    /**
     *
     * @param orderTestMatrixs
     * @param slimTestMatrixs
     * @param alwaysSplits
     * @param slimCode
     * @param testSampleNo
     * @return
     */
    public String getTestMatrixError(List<OrderTestMatrixInfo> orderTestMatrixs, List<SlimTestMatrixInfo> slimTestMatrixs, Set<String> alwaysSplits, String slimCode, String testSampleNo){
        if (orderTestMatrixs == null){
            orderTestMatrixs = Lists.newArrayList();
        }
        Set<OrderTestMatrixInfo> orderSamples = orderTestMatrixs.stream().filter(tm -> StringUtils.equalsIgnoreCase(tm.getSlimCode(), slimCode)).collect(Collectors.toSet());
        if (orderSamples == null || orderSamples.isEmpty()){
            logger.info("在Order中没有找到SCH_CODE:{} 对应的 TestLine，请确认是否有添加该TestLine，或者缺少该TestLine和Scheme的Mapping关系", slimCode);
            return String.format("在Order中没有找到SCH_CODE:%s 对应的 TestLine，请确认是否有添加该TestLine，或者缺少该TestLine和Scheme的Mapping关系", slimCode);
        }
        List<String> testSampleNos = Lists.newArrayList(StringUtils.split(testSampleNo, "+"));
        if (testSampleNos.size() > 1){
            testSampleNos.removeIf(sampleNo->{
                long slimCount = this.getSlimCount(slimTestMatrixs, slimCode, sampleNo);
                long orderCount = orderSamples.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getTestSampleNo(), sampleNo)).count();
                // 如果Slim的Sample存在，但Order的Matrix不存在则remove
                if (slimCount > 0 && orderCount <= 0){
                    return true;
                }
                return orderCount > 0 && slimCount > 0;
            });
            if (testSampleNos.isEmpty()){
                return StringUtils.EMPTY;
            }
            // 允许复测样，Slim 中存在的Sample，且在Order中也存在的Sample
            if (alwaysSplits != null && orderSamples.stream().filter(orderSample-> alwaysSplits.contains(String.format("%s_%s", orderSample.getTestLineId(), orderSample.getCitationId()))).count() > 0){
                /**
                 * 允许回传：Order Sample（C1,C1+C2）、SLIM （C1,C1+C2）
                 */
                if (orderSamples.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getTestSampleNo(), testSampleNo)).count() > 0 &&
                    slimTestMatrixs.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getTestSampleNo(), testSampleNo)).count() > 0){
                    return StringUtils.EMPTY;
                }
            }
            //
            if (orderSamples.stream().filter(sc-> testSampleNos.contains(sc.getTestSampleNo())).count() <= 0){
                /**
                 * 不允许回传：Order Sample（C1+C2）、SLIM （C1，C2，C1+C2）
                 */
                if (orderSamples.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getTestSampleNo(), testSampleNo)).count() > 0 &&
                    slimTestMatrixs.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getSlimCode(), slimCode) && testSampleNos.contains(sc.getTestSampleNo())).count() > 0){
                    return String.format("在Order中没有找到SCH_CODE:%s和DESCRIPTION_1:%s的Assign关系", slimCode, StringUtils.join(testSampleNos,"、"));
                }
                return StringUtils.EMPTY;
            }
            if (slimTestMatrixs.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getTestSampleNo(), testSampleNo)).count() <= 0){
                testSampleNos.add(testSampleNo);
            }
            logger.info("SCH_CODE：{} 下缺少 {}", slimCode, StringUtils.join(testSampleNos,"、"));
            return String.format("SCH_CODE：%s 下缺少 %s", slimCode, StringUtils.join(testSampleNos,"、"));
        }
        long slimCount = this.getSlimCount(slimTestMatrixs, slimCode, testSampleNo);
        long orderCount = orderSamples.stream().filter(sc -> StringUtils.equalsIgnoreCase(sc.getTestSampleNo(), testSampleNo)).count();
        if (slimCount <= 0 && orderCount > 0){
            return String.format("SCH_CODE：%s 下缺少 %s", slimCode, testSampleNo);
        }
        if (slimCount > 0 && orderCount <= 0){
            Set<String> slimSampleNos = this.getSlimSampleNos(slimTestMatrixs, slimCode);
            long sampleCount = orderSamples.stream().filter(os -> slimSampleNos.contains(os.getTestSampleNo())).count();
            if (orderSamples.size() == sampleCount){
                if (alwaysSplits != null && orderSamples.stream().filter(orderSample-> alwaysSplits.contains(String.format("%s_%s", orderSample.getTestLineId(), orderSample.getCitationId()))).count() > 0){
                    return StringUtils.EMPTY;
                }
            }
            return String.format("在Order中没有找到SCH_CODE:%s和DESCRIPTION_1:%s的Assign关系", slimCode, testSampleNo);
        }
        return StringUtils.EMPTY;
    }

    /**
     *
     * @param slimSamples
     * @param slimCode
     * @return
     */
    private Set<String> getSlimSampleNos(List<SlimTestMatrixInfo> slimSamples, String slimCode){
        if (slimSamples == null || slimSamples.isEmpty()){
            return Sets.newHashSet();
        }
        return slimSamples.stream().filter(slimSample -> {
            return StringUtils.equalsIgnoreCase(slimSample.getSlimCode(), slimCode);
        }).map(ts-> ts.getTestSampleNo()).collect(Collectors.toSet());
    }

    /**
     *
     * @param slimSamples
     * @param slimCode
     * @param testSampleNo
     * @return
     */
    private long getSlimCount(List<SlimTestMatrixInfo> slimSamples, String slimCode, String testSampleNo){
        if (slimSamples == null || slimSamples.isEmpty()){
            return 0;
        }
        return slimSamples.stream().filter(slimSample -> {
            return StringUtils.equalsIgnoreCase(slimSample.getSlimCode(), slimCode) &&
            StringUtils.equalsIgnoreCase(slimSample.getTestSampleNo(), testSampleNo);
        }).count();
    }
    // endregion

}
