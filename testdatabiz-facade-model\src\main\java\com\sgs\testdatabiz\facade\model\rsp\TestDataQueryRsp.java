package com.sgs.testdatabiz.facade.model.rsp;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2022/11/28 11:20
 */
public class TestDataQueryRsp extends PrintFriendliness {
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("分包单号/JobNos")
    private String objectNo;
    @ApiModelProperty("slimJobNo")
    private String externalNo;
    @ApiModelProperty("外部号")
    private String externalCode;
    @ApiModelProperty("sampleNo")
    private String sampleNo;

    @ApiModelProperty("外部 SampleNo")
    private String externalSampleNo;
    @ApiModelProperty("ReportLimit")
    private String reportLimit;
    @ApiModelProperty("analyteType；0：General、1：Conclusion")
    private String analyteType;
    @ApiModelProperty("TestAnalyteName")
    private String testAnalyteName;
    @ApiModelProperty("TestAnalyteName 中文")
    private String testAnalyteNameCN;
    @ApiModelProperty("reportUnit")
    private String reportUnit;
    @ApiModelProperty("reportUnitCN 中文")
    private String reportUnitCN;
    @ApiModelProperty("TestValue")
    private String testValue;
    @ApiModelProperty("analyte排序字段 analyteSeq")
    private String analyteSeq;
    @ApiModelProperty("MaterialName")
    private String materialName;
    @ApiModelProperty("MaterialTexture")
    private String materialTexture;
    @ApiModelProperty("UsedPosition")
    private String usedPosition;
    @ApiModelProperty("MaterialColor")
    private String materialColor;
    @ApiModelProperty("testLineId")
    private Integer testLineId;
    @ApiModelProperty("standardId")
    private Integer standardId;
    @ApiModelProperty("SystemId; 1:slim   2:fast")
    private Integer systemId;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(String analyteType) {
        this.analyteType = analyteType;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getTestAnalyteNameCN() {
        return testAnalyteNameCN;
    }

    public void setTestAnalyteNameCN(String testAnalyteNameCN) {
        this.testAnalyteNameCN = testAnalyteNameCN;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportUnitCN() {
        return reportUnitCN;
    }

    public void setReportUnitCN(String reportUnitCN) {
        this.reportUnitCN = reportUnitCN;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public String getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(String analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }
}
