package com.sgs.testdatabiz.integration.config.req;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ConfigGetReq {
    /**
     * csutomer_group VARCHAR(32) 必填<br>
     * 配置方分组
     */
    private String customerGroup;

    /**
     * customer_no VARCHAR(32) 必填<br>
     * 配置方编号
     */
    private String customerNo;

    /**
     * identity_id VARCHAR(32) 必填<br>
     * 配置方身份标识
     */
    private String identityId;

    /**
     * product_line VARCHAR(20)<br>
     * BU
     */
    private String productLine;


    private String configKey;
}
