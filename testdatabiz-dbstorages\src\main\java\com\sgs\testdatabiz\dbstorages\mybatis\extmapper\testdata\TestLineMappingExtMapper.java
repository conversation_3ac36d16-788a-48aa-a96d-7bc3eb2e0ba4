package com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineMappingInfoPO;
import com.sgs.testdatabiz.facade.model.dto.TestLineMappingDTO;
import com.sgs.testdatabiz.facade.model.req.TestLineMappingInfoReq;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public interface TestLineMappingExtMapper {
    /**
     *
     * @param labCode
     * @param testLineIds
     * @return
     */
    List<TestLineMappingInfoPO> getTestLineMappingByTestLineId(@Param("labCode") String labCode, @Param("testLineIds") Set<Integer> testLineIds);

    /**
     *
     * @param testLineMapping
     * @return
     */
    List<TestLineMappingInfoPO> getTestLineMappingInfoList(TestLineMappingInfoReq testLineMapping);



    List<TestLineMappingDTO> queryTestLineMappingByLabCode(@Param("labCode") String labCode, @Param("systemIds") List<Integer> systemIds, @Param("slimCodes") Set<String> slimCodes);
}
