/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestSampleDTO extends RdOrderHeaderDTO implements Serializable {


    @ApiModelProperty(value = "systemId",dataType = "integer", required = true)
    private Integer systemId;
    @ApiModelProperty(value = "testSampleInstanceId",dataType = "string", required = true)
    private String testSampleInstanceId;
    private String parentTestSampleId;
    private List<RdTestSampleGroupDTO> testSampleGroupList;
    @ApiModelProperty(value = "testSampleId",dataType = "string", required = true)
    private String testSampleNo;
    //SCI-1378
    private String testSampleName;
    // TODO 230922本次暂不移除，后续需移除
    private String externalSampleNo;
    private String externalSampleName;
    @ApiModelProperty(value = "testSampleType",dataType = "integer", required = true)
    private Integer testSampleType;
    @ApiModelProperty(value = "category",dataType = "string", required = true)
    private String category;
    @ApiModelProperty(value = "testSampleSeq",dataType = "integer", required = true)
    private Integer testSampleSeq;
    private RdMaterialAttrDTO materialAttr;
    private RdConclusionDTO conclusion;

    private RdTestSampleExternalDTO external;
    private List<RdAttachmentDTO> testSamplePhoto;

    private List<RdTestSampleLimitGroupDTO> limitGroupList;

    private String reportNo;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
