package com.sgs.testdatabiz.facade.model.rsp.subcontract;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class AnalyteInfo extends PrintFriendliness {

    private String analyte;

    private String reportUnit;

    private String reportLimit;

    private String analyteCN;
    private String reportUnitCN;

    @ApiModelProperty(value = "sample列表横向")
    private List<SampleTestDataInfo> sampleList;

    public String getAnalyteCN() {
        return analyteCN;
    }

    public void setAnalyteCN(String analyteCN) {
        this.analyteCN = analyteCN;
    }

    public String getReportUnitCN() {
        return reportUnitCN;
    }

    public void setReportUnitCN(String reportUnitCN) {
        this.reportUnitCN = reportUnitCN;
    }

    private Integer analyteSeq;

    public String getAnalyte() {
        return analyte;
    }

    public void setAnalyte(String analyte) {
        this.analyte = analyte;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public List<SampleTestDataInfo> getSampleList() {
        return sampleList;
    }

    public void setSampleList(List<SampleTestDataInfo> sampleList) {
        this.sampleList = sampleList;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }
}
