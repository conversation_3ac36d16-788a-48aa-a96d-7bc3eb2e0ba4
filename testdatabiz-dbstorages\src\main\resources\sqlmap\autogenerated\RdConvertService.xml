<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdQuotationMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="system_id" property="systemId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="quotation_no" property="quotationNo" jdbcType="VARCHAR" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="payer_customer_name" property="payerCustomerName" jdbcType="VARCHAR" />
    <result column="payer_boss_number" property="payerBossNumber" jdbcType="BIGINT" />
    <result column="service_item_type" property="serviceItemType" jdbcType="VARCHAR" />
    <result column="service_item_name" property="serviceItemName" jdbcType="VARCHAR" />
    <result column="service_item_seq" property="serviceItemSeq" jdbcType="INTEGER" />
    <result column="special_offer_flag" property="specialOfferFlag" jdbcType="INTEGER" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="service_item_list_unit_price" property="serviceItemListUnitPrice" jdbcType="DECIMAL" />
    <result column="service_item_sales_unit_price" property="serviceItemSalesUnitPrice" jdbcType="DECIMAL" />
    <result column="service_item_discount" property="serviceItemDiscount" jdbcType="DECIMAL" />
    <result column="service_item_exchange_rate_price" property="serviceItemExchangeRatePrice" jdbcType="DECIMAL" />
    <result column="service_item_sur_charge_price" property="serviceItemSurChargePrice" jdbcType="DECIMAL" />
    <result column="service_item_net_amount" property="serviceItemNetAmount" jdbcType="DECIMAL" />
    <result column="service_item_vat_amount" property="serviceItemVatAmount" jdbcType="DECIMAL" />
    <result column="service_item_total_amount" property="serviceItemTotalAmount" jdbcType="DECIMAL" />
    <result column="sum_net_amount" property="sumNetAmount" jdbcType="DECIMAL" />
    <result column="sum_vat_amount" property="sumVatAmount" jdbcType="DECIMAL" />
    <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
    <result column="adjustment_amount" property="adjustmentAmount" jdbcType="DECIMAL" />
    <result column="adjustment_discount" property="adjustmentDiscount" jdbcType="DECIMAL" />
    <result column="final_amount" property="finalAmount" jdbcType="DECIMAL" />
    <result column="pp_no" property="ppNo" jdbcType="INTEGER" />
    <result column="test_line_id" property="testLineId" jdbcType="BIGINT" />
    <result column="citation_type" property="citationType" jdbcType="TINYINT" />
    <result column="citation_id" property="citationId" jdbcType="INTEGER" />
    <result column="citation_name" property="citationName" jdbcType="VARCHAR" />
    <result column="citation_full_name" property="citationFullName" jdbcType="VARCHAR" />
    <result column="quotation_version_id" property="quotationVersionId" jdbcType="VARCHAR" />
    <result column="quotation_status" property="quotationStatus" jdbcType="TINYINT" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, lab_id, system_id, order_no, report_no, quotation_no, currency_code, payer_customer_name, 
    payer_boss_number, service_item_type, service_item_name, service_item_seq, special_offer_flag, 
    quantity, service_item_list_unit_price, service_item_sales_unit_price, service_item_discount, 
    service_item_exchange_rate_price, service_item_sur_charge_price, service_item_net_amount, 
    service_item_vat_amount, service_item_total_amount, sum_net_amount, sum_vat_amount, 
    total_amount, adjustment_amount, adjustment_discount, final_amount, pp_no, test_line_id, 
    citation_type, citation_id, citation_name, citation_full_name, quotation_version_id, 
    quotation_status, active_indicator, created_by, created_date, modified_by, modified_date, 
    last_modified_timestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_quotation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_quotation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_quotation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationExample" >
    delete from tb_quotation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationPO" >
    insert into tb_quotation (id, lab_id, system_id, 
      order_no, report_no, quotation_no, 
      currency_code, payer_customer_name, payer_boss_number, 
      service_item_type, service_item_name, service_item_seq, 
      special_offer_flag, quantity, service_item_list_unit_price, 
      service_item_sales_unit_price, service_item_discount, 
      service_item_exchange_rate_price, service_item_sur_charge_price, 
      service_item_net_amount, service_item_vat_amount, 
      service_item_total_amount, sum_net_amount, sum_vat_amount, 
      total_amount, adjustment_amount, adjustment_discount, 
      final_amount, pp_no, test_line_id, 
      citation_type, citation_id, citation_name, 
      citation_full_name, quotation_version_id, quotation_status, 
      active_indicator, created_by, created_date, 
      modified_by, modified_date, last_modified_timestamp
      )
    values (#{id,jdbcType=BIGINT}, #{labId,jdbcType=BIGINT}, #{systemId,jdbcType=BIGINT}, 
      #{orderNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{quotationNo,jdbcType=VARCHAR}, 
      #{currencyCode,jdbcType=VARCHAR}, #{payerCustomerName,jdbcType=VARCHAR}, #{payerBossNumber,jdbcType=BIGINT}, 
      #{serviceItemType,jdbcType=VARCHAR}, #{serviceItemName,jdbcType=VARCHAR}, #{serviceItemSeq,jdbcType=INTEGER}, 
      #{specialOfferFlag,jdbcType=INTEGER}, #{quantity,jdbcType=INTEGER}, #{serviceItemListUnitPrice,jdbcType=DECIMAL}, 
      #{serviceItemSalesUnitPrice,jdbcType=DECIMAL}, #{serviceItemDiscount,jdbcType=DECIMAL}, 
      #{serviceItemExchangeRatePrice,jdbcType=DECIMAL}, #{serviceItemSurChargePrice,jdbcType=DECIMAL}, 
      #{serviceItemNetAmount,jdbcType=DECIMAL}, #{serviceItemVatAmount,jdbcType=DECIMAL}, 
      #{serviceItemTotalAmount,jdbcType=DECIMAL}, #{sumNetAmount,jdbcType=DECIMAL}, #{sumVatAmount,jdbcType=DECIMAL}, 
      #{totalAmount,jdbcType=DECIMAL}, #{adjustmentAmount,jdbcType=DECIMAL}, #{adjustmentDiscount,jdbcType=DECIMAL}, 
      #{finalAmount,jdbcType=DECIMAL}, #{ppNo,jdbcType=INTEGER}, #{testLineId,jdbcType=BIGINT}, 
      #{citationType,jdbcType=TINYINT}, #{citationId,jdbcType=INTEGER}, #{citationName,jdbcType=VARCHAR}, 
      #{citationFullName,jdbcType=VARCHAR}, #{quotationVersionId,jdbcType=VARCHAR}, #{quotationStatus,jdbcType=TINYINT}, 
      #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, now(), 
      #{modifiedBy,jdbcType=VARCHAR}, now(), #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationPO" >
    insert into tb_quotation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="systemId != null" >
        system_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="quotationNo != null" >
        quotation_no,
      </if>
      <if test="currencyCode != null" >
        currency_code,
      </if>
      <if test="payerCustomerName != null" >
        payer_customer_name,
      </if>
      <if test="payerBossNumber != null" >
        payer_boss_number,
      </if>
      <if test="serviceItemType != null" >
        service_item_type,
      </if>
      <if test="serviceItemName != null" >
        service_item_name,
      </if>
      <if test="serviceItemSeq != null" >
        service_item_seq,
      </if>
      <if test="specialOfferFlag != null" >
        special_offer_flag,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="serviceItemListUnitPrice != null" >
        service_item_list_unit_price,
      </if>
      <if test="serviceItemSalesUnitPrice != null" >
        service_item_sales_unit_price,
      </if>
      <if test="serviceItemDiscount != null" >
        service_item_discount,
      </if>
      <if test="serviceItemExchangeRatePrice != null" >
        service_item_exchange_rate_price,
      </if>
      <if test="serviceItemSurChargePrice != null" >
        service_item_sur_charge_price,
      </if>
      <if test="serviceItemNetAmount != null" >
        service_item_net_amount,
      </if>
      <if test="serviceItemVatAmount != null" >
        service_item_vat_amount,
      </if>
      <if test="serviceItemTotalAmount != null" >
        service_item_total_amount,
      </if>
      <if test="sumNetAmount != null" >
        sum_net_amount,
      </if>
      <if test="sumVatAmount != null" >
        sum_vat_amount,
      </if>
      <if test="totalAmount != null" >
        total_amount,
      </if>
      <if test="adjustmentAmount != null" >
        adjustment_amount,
      </if>
      <if test="adjustmentDiscount != null" >
        adjustment_discount,
      </if>
      <if test="finalAmount != null" >
        final_amount,
      </if>
      <if test="ppNo != null" >
        pp_no,
      </if>
      <if test="testLineId != null" >
        test_line_id,
      </if>
      <if test="citationType != null" >
        citation_type,
      </if>
      <if test="citationId != null" >
        citation_id,
      </if>
      <if test="citationName != null" >
        citation_name,
      </if>
      <if test="citationFullName != null" >
        citation_full_name,
      </if>
      <if test="quotationVersionId != null" >
        quotation_version_id,
      </if>
      <if test="quotationStatus != null" >
        quotation_status,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="quotationNo != null" >
        #{quotationNo,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null" >
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="payerCustomerName != null" >
        #{payerCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="payerBossNumber != null" >
        #{payerBossNumber,jdbcType=BIGINT},
      </if>
      <if test="serviceItemType != null" >
        #{serviceItemType,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemName != null" >
        #{serviceItemName,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemSeq != null" >
        #{serviceItemSeq,jdbcType=INTEGER},
      </if>
      <if test="specialOfferFlag != null" >
        #{specialOfferFlag,jdbcType=INTEGER},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="serviceItemListUnitPrice != null" >
        #{serviceItemListUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemSalesUnitPrice != null" >
        #{serviceItemSalesUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemDiscount != null" >
        #{serviceItemDiscount,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemExchangeRatePrice != null" >
        #{serviceItemExchangeRatePrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemSurChargePrice != null" >
        #{serviceItemSurChargePrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemNetAmount != null" >
        #{serviceItemNetAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemVatAmount != null" >
        #{serviceItemVatAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemTotalAmount != null" >
        #{serviceItemTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="sumNetAmount != null" >
        #{sumNetAmount,jdbcType=DECIMAL},
      </if>
      <if test="sumVatAmount != null" >
        #{sumVatAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="adjustmentAmount != null" >
        #{adjustmentAmount,jdbcType=DECIMAL},
      </if>
      <if test="adjustmentDiscount != null" >
        #{adjustmentDiscount,jdbcType=DECIMAL},
      </if>
      <if test="finalAmount != null" >
        #{finalAmount,jdbcType=DECIMAL},
      </if>
      <if test="ppNo != null" >
        #{ppNo,jdbcType=INTEGER},
      </if>
      <if test="testLineId != null" >
        #{testLineId,jdbcType=BIGINT},
      </if>
      <if test="citationType != null" >
        #{citationType,jdbcType=TINYINT},
      </if>
      <if test="citationId != null" >
        #{citationId,jdbcType=INTEGER},
      </if>
      <if test="citationName != null" >
        #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="citationFullName != null" >
        #{citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="quotationVersionId != null" >
        #{quotationVersionId,jdbcType=VARCHAR},
      </if>
      <if test="quotationStatus != null" >
        #{quotationStatus,jdbcType=TINYINT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationExample" resultType="java.lang.Integer" >
    select count(*) from tb_quotation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_quotation
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.systemId != null" >
        system_id = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.quotationNo != null" >
        quotation_no = #{record.quotationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.currencyCode != null" >
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.payerCustomerName != null" >
        payer_customer_name = #{record.payerCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="record.payerBossNumber != null" >
        payer_boss_number = #{record.payerBossNumber,jdbcType=BIGINT},
      </if>
      <if test="record.serviceItemType != null" >
        service_item_type = #{record.serviceItemType,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceItemName != null" >
        service_item_name = #{record.serviceItemName,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceItemSeq != null" >
        service_item_seq = #{record.serviceItemSeq,jdbcType=INTEGER},
      </if>
      <if test="record.specialOfferFlag != null" >
        special_offer_flag = #{record.specialOfferFlag,jdbcType=INTEGER},
      </if>
      <if test="record.quantity != null" >
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.serviceItemListUnitPrice != null" >
        service_item_list_unit_price = #{record.serviceItemListUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceItemSalesUnitPrice != null" >
        service_item_sales_unit_price = #{record.serviceItemSalesUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceItemDiscount != null" >
        service_item_discount = #{record.serviceItemDiscount,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceItemExchangeRatePrice != null" >
        service_item_exchange_rate_price = #{record.serviceItemExchangeRatePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceItemSurChargePrice != null" >
        service_item_sur_charge_price = #{record.serviceItemSurChargePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceItemNetAmount != null" >
        service_item_net_amount = #{record.serviceItemNetAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceItemVatAmount != null" >
        service_item_vat_amount = #{record.serviceItemVatAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceItemTotalAmount != null" >
        service_item_total_amount = #{record.serviceItemTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.sumNetAmount != null" >
        sum_net_amount = #{record.sumNetAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.sumVatAmount != null" >
        sum_vat_amount = #{record.sumVatAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.totalAmount != null" >
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.adjustmentAmount != null" >
        adjustment_amount = #{record.adjustmentAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.adjustmentDiscount != null" >
        adjustment_discount = #{record.adjustmentDiscount,jdbcType=DECIMAL},
      </if>
      <if test="record.finalAmount != null" >
        final_amount = #{record.finalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.ppNo != null" >
        pp_no = #{record.ppNo,jdbcType=INTEGER},
      </if>
      <if test="record.testLineId != null" >
        test_line_id = #{record.testLineId,jdbcType=BIGINT},
      </if>
      <if test="record.citationType != null" >
        citation_type = #{record.citationType,jdbcType=TINYINT},
      </if>
      <if test="record.citationId != null" >
        citation_id = #{record.citationId,jdbcType=INTEGER},
      </if>
      <if test="record.citationName != null" >
        citation_name = #{record.citationName,jdbcType=VARCHAR},
      </if>
      <if test="record.citationFullName != null" >
        citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.quotationVersionId != null" >
        quotation_version_id = #{record.quotationVersionId,jdbcType=VARCHAR},
      </if>
      <if test="record.quotationStatus != null" >
        quotation_status = #{record.quotationStatus,jdbcType=TINYINT},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_quotation
    set id = #{record.id,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      system_id = #{record.systemId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      quotation_no = #{record.quotationNo,jdbcType=VARCHAR},
      currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      payer_customer_name = #{record.payerCustomerName,jdbcType=VARCHAR},
      payer_boss_number = #{record.payerBossNumber,jdbcType=BIGINT},
      service_item_type = #{record.serviceItemType,jdbcType=VARCHAR},
      service_item_name = #{record.serviceItemName,jdbcType=VARCHAR},
      service_item_seq = #{record.serviceItemSeq,jdbcType=INTEGER},
      special_offer_flag = #{record.specialOfferFlag,jdbcType=INTEGER},
      quantity = #{record.quantity,jdbcType=INTEGER},
      service_item_list_unit_price = #{record.serviceItemListUnitPrice,jdbcType=DECIMAL},
      service_item_sales_unit_price = #{record.serviceItemSalesUnitPrice,jdbcType=DECIMAL},
      service_item_discount = #{record.serviceItemDiscount,jdbcType=DECIMAL},
      service_item_exchange_rate_price = #{record.serviceItemExchangeRatePrice,jdbcType=DECIMAL},
      service_item_sur_charge_price = #{record.serviceItemSurChargePrice,jdbcType=DECIMAL},
      service_item_net_amount = #{record.serviceItemNetAmount,jdbcType=DECIMAL},
      service_item_vat_amount = #{record.serviceItemVatAmount,jdbcType=DECIMAL},
      service_item_total_amount = #{record.serviceItemTotalAmount,jdbcType=DECIMAL},
      sum_net_amount = #{record.sumNetAmount,jdbcType=DECIMAL},
      sum_vat_amount = #{record.sumVatAmount,jdbcType=DECIMAL},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      adjustment_amount = #{record.adjustmentAmount,jdbcType=DECIMAL},
      adjustment_discount = #{record.adjustmentDiscount,jdbcType=DECIMAL},
      final_amount = #{record.finalAmount,jdbcType=DECIMAL},
      pp_no = #{record.ppNo,jdbcType=INTEGER},
      test_line_id = #{record.testLineId,jdbcType=BIGINT},
      citation_type = #{record.citationType,jdbcType=TINYINT},
      citation_id = #{record.citationId,jdbcType=INTEGER},
      citation_name = #{record.citationName,jdbcType=VARCHAR},
      citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      quotation_version_id = #{record.quotationVersionId,jdbcType=VARCHAR},
      quotation_status = #{record.quotationStatus,jdbcType=TINYINT},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationPO" >
    update tb_quotation
    <set >
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="systemId != null" >
        system_id = #{systemId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="quotationNo != null" >
        quotation_no = #{quotationNo,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null" >
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="payerCustomerName != null" >
        payer_customer_name = #{payerCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="payerBossNumber != null" >
        payer_boss_number = #{payerBossNumber,jdbcType=BIGINT},
      </if>
      <if test="serviceItemType != null" >
        service_item_type = #{serviceItemType,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemName != null" >
        service_item_name = #{serviceItemName,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemSeq != null" >
        service_item_seq = #{serviceItemSeq,jdbcType=INTEGER},
      </if>
      <if test="specialOfferFlag != null" >
        special_offer_flag = #{specialOfferFlag,jdbcType=INTEGER},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="serviceItemListUnitPrice != null" >
        service_item_list_unit_price = #{serviceItemListUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemSalesUnitPrice != null" >
        service_item_sales_unit_price = #{serviceItemSalesUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemDiscount != null" >
        service_item_discount = #{serviceItemDiscount,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemExchangeRatePrice != null" >
        service_item_exchange_rate_price = #{serviceItemExchangeRatePrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemSurChargePrice != null" >
        service_item_sur_charge_price = #{serviceItemSurChargePrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemNetAmount != null" >
        service_item_net_amount = #{serviceItemNetAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemVatAmount != null" >
        service_item_vat_amount = #{serviceItemVatAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceItemTotalAmount != null" >
        service_item_total_amount = #{serviceItemTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="sumNetAmount != null" >
        sum_net_amount = #{sumNetAmount,jdbcType=DECIMAL},
      </if>
      <if test="sumVatAmount != null" >
        sum_vat_amount = #{sumVatAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null" >
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="adjustmentAmount != null" >
        adjustment_amount = #{adjustmentAmount,jdbcType=DECIMAL},
      </if>
      <if test="adjustmentDiscount != null" >
        adjustment_discount = #{adjustmentDiscount,jdbcType=DECIMAL},
      </if>
      <if test="finalAmount != null" >
        final_amount = #{finalAmount,jdbcType=DECIMAL},
      </if>
      <if test="ppNo != null" >
        pp_no = #{ppNo,jdbcType=INTEGER},
      </if>
      <if test="testLineId != null" >
        test_line_id = #{testLineId,jdbcType=BIGINT},
      </if>
      <if test="citationType != null" >
        citation_type = #{citationType,jdbcType=TINYINT},
      </if>
      <if test="citationId != null" >
        citation_id = #{citationId,jdbcType=INTEGER},
      </if>
      <if test="citationName != null" >
        citation_name = #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="citationFullName != null" >
        citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="quotationVersionId != null" >
        quotation_version_id = #{quotationVersionId,jdbcType=VARCHAR},
      </if>
      <if test="quotationStatus != null" >
        quotation_status = #{quotationStatus,jdbcType=TINYINT},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdQuotationPO" >
    update tb_quotation
    set lab_id = #{labId,jdbcType=BIGINT},
      system_id = #{systemId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      quotation_no = #{quotationNo,jdbcType=VARCHAR},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      payer_customer_name = #{payerCustomerName,jdbcType=VARCHAR},
      payer_boss_number = #{payerBossNumber,jdbcType=BIGINT},
      service_item_type = #{serviceItemType,jdbcType=VARCHAR},
      service_item_name = #{serviceItemName,jdbcType=VARCHAR},
      service_item_seq = #{serviceItemSeq,jdbcType=INTEGER},
      special_offer_flag = #{specialOfferFlag,jdbcType=INTEGER},
      quantity = #{quantity,jdbcType=INTEGER},
      service_item_list_unit_price = #{serviceItemListUnitPrice,jdbcType=DECIMAL},
      service_item_sales_unit_price = #{serviceItemSalesUnitPrice,jdbcType=DECIMAL},
      service_item_discount = #{serviceItemDiscount,jdbcType=DECIMAL},
      service_item_exchange_rate_price = #{serviceItemExchangeRatePrice,jdbcType=DECIMAL},
      service_item_sur_charge_price = #{serviceItemSurChargePrice,jdbcType=DECIMAL},
      service_item_net_amount = #{serviceItemNetAmount,jdbcType=DECIMAL},
      service_item_vat_amount = #{serviceItemVatAmount,jdbcType=DECIMAL},
      service_item_total_amount = #{serviceItemTotalAmount,jdbcType=DECIMAL},
      sum_net_amount = #{sumNetAmount,jdbcType=DECIMAL},
      sum_vat_amount = #{sumVatAmount,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      adjustment_amount = #{adjustmentAmount,jdbcType=DECIMAL},
      adjustment_discount = #{adjustmentDiscount,jdbcType=DECIMAL},
      final_amount = #{finalAmount,jdbcType=DECIMAL},
      pp_no = #{ppNo,jdbcType=INTEGER},
      test_line_id = #{testLineId,jdbcType=BIGINT},
      citation_type = #{citationType,jdbcType=TINYINT},
      citation_id = #{citationId,jdbcType=INTEGER},
      citation_name = #{citationName,jdbcType=VARCHAR},
      citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      quotation_version_id = #{quotationVersionId,jdbcType=VARCHAR},
      quotation_status = #{quotationStatus,jdbcType=TINYINT},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_quotation
      (`id`,`lab_id`,`system_id`,
      `order_no`,`report_no`,`quotation_no`,
      `currency_code`,`payer_customer_name`,`payer_boss_number`,
      `service_item_type`,`service_item_name`,`service_item_seq`,
      `special_offer_flag`,`quantity`,`service_item_list_unit_price`,
      `service_item_sales_unit_price`,`service_item_discount`,`service_item_exchange_rate_price`,
      `service_item_sur_charge_price`,`service_item_net_amount`,`service_item_vat_amount`,
      `service_item_total_amount`,`sum_net_amount`,`sum_vat_amount`,
      `total_amount`,`adjustment_amount`,`adjustment_discount`,
      `final_amount`,`pp_no`,`test_line_id`,
      `citation_type`,`citation_id`,`citation_name`,
      `citation_full_name`,`quotation_version_id`,`quotation_status`,
      `active_indicator`,`created_by`,`created_date`,
      `modified_by`,`modified_date`,`last_modified_timestamp`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.systemId, jdbcType=BIGINT},
      #{ item.orderNo, jdbcType=VARCHAR},#{ item.reportNo, jdbcType=VARCHAR},#{ item.quotationNo, jdbcType=VARCHAR},
      #{ item.currencyCode, jdbcType=VARCHAR},#{ item.payerCustomerName, jdbcType=VARCHAR},#{ item.payerBossNumber, jdbcType=BIGINT},
      #{ item.serviceItemType, jdbcType=VARCHAR},#{ item.serviceItemName, jdbcType=VARCHAR},#{ item.serviceItemSeq, jdbcType=INTEGER},
      #{ item.specialOfferFlag, jdbcType=INTEGER},#{ item.quantity, jdbcType=INTEGER},#{ item.serviceItemListUnitPrice, jdbcType=DECIMAL},
      #{ item.serviceItemSalesUnitPrice, jdbcType=DECIMAL},#{ item.serviceItemDiscount, jdbcType=DECIMAL},#{ item.serviceItemExchangeRatePrice, jdbcType=DECIMAL},
      #{ item.serviceItemSurChargePrice, jdbcType=DECIMAL},#{ item.serviceItemNetAmount, jdbcType=DECIMAL},#{ item.serviceItemVatAmount, jdbcType=DECIMAL},
      #{ item.serviceItemTotalAmount, jdbcType=DECIMAL},#{ item.sumNetAmount, jdbcType=DECIMAL},#{ item.sumVatAmount, jdbcType=DECIMAL},
      #{ item.totalAmount, jdbcType=DECIMAL},#{ item.adjustmentAmount, jdbcType=DECIMAL},#{ item.adjustmentDiscount, jdbcType=DECIMAL},
      #{ item.finalAmount, jdbcType=DECIMAL},#{ item.ppNo, jdbcType=INTEGER},#{ item.testLineId, jdbcType=BIGINT},
      #{ item.citationType, jdbcType=TINYINT},#{ item.citationId, jdbcType=INTEGER},#{ item.citationName, jdbcType=VARCHAR},
      #{ item.citationFullName, jdbcType=VARCHAR},#{ item.quotationVersionId, jdbcType=VARCHAR},#{ item.quotationStatus, jdbcType=TINYINT},
      #{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_quotation 
      <set>
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.systemId != null"> 
          `system_id` = #{item.systemId, jdbcType = BIGINT},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.quotationNo != null"> 
          `quotation_no` = #{item.quotationNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.currencyCode != null"> 
          `currency_code` = #{item.currencyCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.payerCustomerName != null"> 
          `payer_customer_name` = #{item.payerCustomerName, jdbcType = VARCHAR},
        </if> 
        <if test="item.payerBossNumber != null"> 
          `payer_boss_number` = #{item.payerBossNumber, jdbcType = BIGINT},
        </if> 
        <if test="item.serviceItemType != null"> 
          `service_item_type` = #{item.serviceItemType, jdbcType = VARCHAR},
        </if> 
        <if test="item.serviceItemName != null"> 
          `service_item_name` = #{item.serviceItemName, jdbcType = VARCHAR},
        </if> 
        <if test="item.serviceItemSeq != null"> 
          `service_item_seq` = #{item.serviceItemSeq, jdbcType = INTEGER},
        </if> 
        <if test="item.specialOfferFlag != null"> 
          `special_offer_flag` = #{item.specialOfferFlag, jdbcType = INTEGER},
        </if> 
        <if test="item.quantity != null"> 
          `quantity` = #{item.quantity, jdbcType = INTEGER},
        </if> 
        <if test="item.serviceItemListUnitPrice != null"> 
          `service_item_list_unit_price` = #{item.serviceItemListUnitPrice, jdbcType = DECIMAL},
        </if> 
        <if test="item.serviceItemSalesUnitPrice != null"> 
          `service_item_sales_unit_price` = #{item.serviceItemSalesUnitPrice, jdbcType = DECIMAL},
        </if> 
        <if test="item.serviceItemDiscount != null"> 
          `service_item_discount` = #{item.serviceItemDiscount, jdbcType = DECIMAL},
        </if> 
        <if test="item.serviceItemExchangeRatePrice != null"> 
          `service_item_exchange_rate_price` = #{item.serviceItemExchangeRatePrice, jdbcType = DECIMAL},
        </if> 
        <if test="item.serviceItemSurChargePrice != null"> 
          `service_item_sur_charge_price` = #{item.serviceItemSurChargePrice, jdbcType = DECIMAL},
        </if> 
        <if test="item.serviceItemNetAmount != null"> 
          `service_item_net_amount` = #{item.serviceItemNetAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.serviceItemVatAmount != null"> 
          `service_item_vat_amount` = #{item.serviceItemVatAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.serviceItemTotalAmount != null"> 
          `service_item_total_amount` = #{item.serviceItemTotalAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.sumNetAmount != null"> 
          `sum_net_amount` = #{item.sumNetAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.sumVatAmount != null"> 
          `sum_vat_amount` = #{item.sumVatAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.totalAmount != null"> 
          `total_amount` = #{item.totalAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.adjustmentAmount != null"> 
          `adjustment_amount` = #{item.adjustmentAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.adjustmentDiscount != null"> 
          `adjustment_discount` = #{item.adjustmentDiscount, jdbcType = DECIMAL},
        </if> 
        <if test="item.finalAmount != null"> 
          `final_amount` = #{item.finalAmount, jdbcType = DECIMAL},
        </if> 
        <if test="item.ppNo != null"> 
          `pp_no` = #{item.ppNo, jdbcType = INTEGER},
        </if> 
        <if test="item.testLineId != null"> 
          `test_line_id` = #{item.testLineId, jdbcType = BIGINT},
        </if> 
        <if test="item.citationType != null"> 
          `citation_type` = #{item.citationType, jdbcType = TINYINT},
        </if> 
        <if test="item.citationId != null"> 
          `citation_id` = #{item.citationId, jdbcType = INTEGER},
        </if> 
        <if test="item.citationName != null"> 
          `citation_name` = #{item.citationName, jdbcType = VARCHAR},
        </if> 
        <if test="item.citationFullName != null"> 
          `citation_full_name` = #{item.citationFullName, jdbcType = VARCHAR},
        </if> 
        <if test="item.quotationVersionId != null"> 
          `quotation_version_id` = #{item.quotationVersionId, jdbcType = VARCHAR},
        </if> 
        <if test="item.quotationStatus != null"> 
          `quotation_status` = #{item.quotationStatus, jdbcType = TINYINT},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>