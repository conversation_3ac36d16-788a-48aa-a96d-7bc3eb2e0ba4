package com.sgs.testdatabiz.integration.model.dict;

import java.io.Serializable;

import lombok.Data;

/**
 * 字典值数据传输对象
 * 用于存储从远程服务获取的字典值信息
 */
@Data
public class DictValueDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 字典值ID
     */
    private Long id;
    
    /**
     * 系统ID
     */
    private Integer systemId;
    
    /**
     * 字典类型
     */
    private String dictType;
    
    /**
     * 字典标签
     */
    private String dictLabel;
    
    /**
     * 字典值
     */
    private String dictValue;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 备注
     */
    private String remark;
} 