package com.sgs.testdatabiz.facade.model.req.starlims;

import com.sgs.framework.core.base.BaseRequest;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2022/11/01 0018 15:33
 */

public class ReceiveTestLanguages extends BaseRequest {
    /**
     * analyteId
     */
    private Integer id;

    /**
     *
     */
    private String analyte;

    /**
     * analyteName
     */
    private String analyteAlias;

    /**
     *
     */
    private String languageId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAnalyte() {
        return analyte;
    }

    public void setAnalyte(String analyte) {
        this.analyte = analyte;
    }

    public String getAnalyteAlias() {
        return analyteAlias;
    }

    public void setAnalyteAlias(String analyteAlias) {
        this.analyteAlias = analyteAlias;
    }

    public String getLanguageId() {
        return languageId;
    }

    public void setLanguageId(String languageId) {
        this.languageId = languageId;
    }
}
