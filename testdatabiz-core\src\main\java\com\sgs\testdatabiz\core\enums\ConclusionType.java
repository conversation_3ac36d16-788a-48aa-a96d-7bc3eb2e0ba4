package com.sgs.testdatabiz.core.enums;

import com.sgs.framework.tool.utils.Func;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2022/10/26 0026 19:10
 */
public enum ConclusionType {

    Pass("40", "Pass"),
    Fail("10", "Fail"),
    Inconclusive("30", "Inconclusive"),
    Warning("30", "Warning"),
    Need("30", "Need"),
    Data_Only("20", "Data Only"),
    NA("60", "NA"),
    Exempt("50", "Exempt");

    static Map<String, ConclusionType> maps = new HashMap<>();
    private String conclusion;
    private String key;

    ConclusionType(String conclusion, String key) {
        this.conclusion = conclusion;
        this.key = key;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 根据Key得到枚举的Value
     * Lambda表达式，比较判断（JDK 1.8）
     *
     * @param key
     * @return
     */
    public static ConclusionType getEnumType(String key) {
        ConclusionType[] alarmGrades = ConclusionType.values();
        ConclusionType result = Arrays.asList(alarmGrades).stream()
                .filter(alarmGrade -> alarmGrade.getKey().equals(key))
                .findFirst().orElse(null);
        return result;
    }

    static {
        for (ConclusionType type : ConclusionType.values()) {
            maps.put(type.getConclusion(), type);
        }
    }

    public static boolean check(String conclusionType) {
        if (Func.isBlank(conclusionType) || !maps.containsKey(conclusionType)) {
            return false;
        }
        return true;
    }
}
