package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExtPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportExtMapper {
    int countByExample(RdReportExtExample example);

    int deleteByExample(RdReportExtExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportExtPO record);

    int insertSelective(RdReportExtPO record);

    List<RdReportExtPO> selectByExampleWithBLOBs(RdReportExtExample example);

    List<RdReportExtPO> selectByExample(RdReportExtExample example);

    RdReportExtPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportExtPO record, @Param("example") RdReportExtExample example);

    int updateByExampleWithBLOBs(@Param("record") RdReportExtPO record, @Param("example") RdReportExtExample example);

    int updateByExample(@Param("record") RdReportExtPO record, @Param("example") RdReportExtExample example);

    int updateByPrimaryKeySelective(RdReportExtPO record);

    int updateByPrimaryKeyWithBLOBs(RdReportExtPO record);

    int updateByPrimaryKey(RdReportExtPO record);

    int batchInsert(List<RdReportExtPO> list);

    int batchUpdate(List<RdReportExtPO> list);
}