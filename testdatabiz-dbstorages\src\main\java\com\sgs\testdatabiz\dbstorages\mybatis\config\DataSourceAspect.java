package com.sgs.testdatabiz.dbstorages.mybatis.config;

import com.sgs.testdatabiz.core.util.AopUtils;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.DatabaseTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.lang.reflect.Method;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 动态处理数据源，根据命名区分
 * Created by <PERSON> on 2019/4/28.
 */
@Aspect
@Component
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class DataSourceAspect {
    private static Logger logger = LoggerFactory.getLogger(DataSourceAspect.class);
    //
    private static final String PackagePrefix = "^(?<prefix>com.sgs.testdatabiz.dbstorages.mybatis.*mapper.)(?<dbName>[A-Za-z]{1,})(?<suffix>[\\.][\\w]{1,})";

    /**
     *
     */
    @Pointcut("execution(* com.sgs.testdatabiz.dbstorages.mybatis.mapper..*.*(..)) || execution(* com.sgs.testdatabiz.dbstorages.mybatis.extmapper..*.*(..))")
    public void aspect() {

    }

    /**
     *
     * @param point
     */
    @Before("aspect() || @annotation(RoutingDataSource)")
    public void before(JoinPoint point) {
        try {
            String declaringTypeName = point.getSignature().getDeclaringTypeName();
            Matcher matcher = Pattern.compile(PackagePrefix).matcher(declaringTypeName);
            if (!matcher.find()) {
                throw new RuntimeException(String.format("Get dbName(%s) Error!", declaringTypeName));
            }
            /*String prefix = matcher.group("prefix");
            String suffix = matcher.group("suffix");*/
            String dbName = matcher.group("dbName");

            /*
            如果注解打在DAO上，DAO里面调用的方法就用DAO上打的注解的，
            如果DAO中调用多个方法，一个走注解的数据源，第二个开始根据规则匹配数据源（因为第一个执行完后会将数据源重置after执行）*/
            ThreadDataSourceInfo databaseType = DatabaseContextHolder.getDataSource();
            if (databaseType != null) {
                if (StringUtils.isBlank(databaseType.getDbName())){
                    databaseType.setDbName(dbName);
                }
                return;
            }
            // 判断是否存在注解中指定数据源
            if (TransactionSynchronizationManager.isActualTransactionActive()) {
                DatabaseContextHolder.setDatabaseContext(dbName, DatabaseTypeEnum.Master);
                return;
            }
            Method targetMethod = AopUtils.getMethodFromTarget(point);

            // 如果注解打在Mapper中的方法上，使用下面方式
            //RoutingDataSource routingDataSource = point.getSignature().getDeclaringType().getMethod(methodName, method.getParameterTypes()).getAnnotation(RoutingDataSource.class);
            RoutingDataSource routingDataSource = AnnotationUtils.findAnnotation(targetMethod, RoutingDataSource.class);
            if (routingDataSource != null) {
                // 设置成注解中指定的数据源
                DatabaseContextHolder.setDatabaseContext(dbName, routingDataSource.value());
                return;
            }

            // TODO 禁用 Mapper Method 前缀判定数据源
            /*for (DatabaseTypeEnum type : DatabaseTypeEnum.values()) {
                List<String> values = DynamicDataSource.METHOD_TYPE_MAP.get(type);
                for (String key : values) {
                    if (methodName.startsWith(key)) {
                        DatabaseContextHolder.setDatabaseType(type);
                        break;
                    }
                }
            }*/
            DatabaseContextHolder.setDatabaseContext(dbName, DatabaseTypeEnum.Master);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    @After("aspect() || @annotation(RoutingDataSource)")
    public void after(JoinPoint point) {
        DatabaseContextHolder.clear();
    }
}
