package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.facade.model.req.SlimConfigReq;
import com.sgs.testdatabiz.facade.model.rsp.SlimConfigRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName TestDataExtMapper
 * @Description test data mapper
 * <AUTHOR>
 * @Date 2022/5/31
 */
public interface SlimConfigExtMapper {
    /**
     *
     * @param productLineCode
     * @param locationCode
     * @return
     */
    SlimConfigRsp getConfInfo(@Param("productLineCode") String productLineCode, @Param("locationCode") String locationCode);

    /**
     *
     * @param req
     * @return
     */
    List<SlimConfigRsp> getConfInfoList(SlimConfigReq req);

    /**
     *
     * @return
     */
    List<String> getTableInfoList();

    Integer checkTable(@Param("tableName") String tableName);

    Integer createTestDataConfigTable(@Param("tableName") String tableName);
}
