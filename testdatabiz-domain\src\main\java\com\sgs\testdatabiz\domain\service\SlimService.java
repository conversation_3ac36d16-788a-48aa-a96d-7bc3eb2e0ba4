package com.sgs.testdatabiz.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.otsnotes.facade.model.enums.PriorityLevel;
import com.sgs.otsnotes.facade.model.enums.StatusEnum;
import com.sgs.testdatabiz.core.annotation.SourceType;
import com.sgs.testdatabiz.core.util.*;
import com.sgs.testdatabiz.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataObjectRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.SlimConfigExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.enums.AnalyteTypeEnum;
import com.sgs.testdatabiz.facade.model.enums.ExternalCodeTransConditionIdEnum;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldInfo;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataLangInfo;
import com.sgs.testdatabiz.facade.model.req.CleanConclusionReq;
import com.sgs.testdatabiz.facade.model.req.slim.SlimSaveTestDataReq;
import com.sgs.testdatabiz.facade.model.req.slim.TestDataItemReq;
import com.sgs.testdatabiz.facade.model.testdata.TestDataConditionInfo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@SourceType(sourceType = SourceTypeEnum.SLIM)
public class SlimService extends AbstractTestDataService<SlimSaveTestDataReq> {
    private static final Logger logger = LoggerFactory.getLogger(SlimService.class);
    @Autowired
    private SlimConfigExtMapper slimConfigExtMapper;
    @Autowired
    private TestDataObjectRelExtMapper objectRelExtMapper;
    @Autowired
    private TestDataInfoExtMapper testDataInfoExtMapper;
    @Autowired
    private TestDataMatrixInfoExtMapper matrixInfoExtMapper;
    @Autowired
    private SnowflakeIdWorker idWorker;

    @Override
    protected CustomResult<TestDataDTO> doInvoke(SlimSaveTestDataReq reqObject) {
        CustomResult<TestDataDTO> rspResult = new CustomResult();

        String userName = SourceTypeEnum.SLIM.getDesc();
        UserInfo localUser = UserHelper.getLocalUser();
        if (localUser != null) {
            userName = localUser.getRegionAccount();
        }
        TestDataObjectRelPO relPO = new TestDataObjectRelPO();
        List<TestDataMatrixInfoPO> testDataMatrixs = Lists.newArrayList();
        List<TestDataInfoPO> testDataInfos = Lists.newArrayList();

        TestDataObjectRelPO rel = new TestDataObjectRelPO();
        rel.setReportNo(reqObject.getReportNo());
        rel.setObjectNo(reqObject.getObjectNo());
        rel.setExternalNo(reqObject.getExternalNo());
        rel.setSourceType(SourceTypeEnum.SLIM.getCode());

        String suffix = StringUtil.getTestDataSuffix(reqObject.getLabCode());

        relPO.setId(UUID.randomUUID().toString());
        // objectNo + externalNo
        TestDataObjectRelPO reportObjectRel = objectRelExtMapper.getReportObjectRelInfo(rel);
        if (reportObjectRel != null) {
            relPO.setId(reportObjectRel.getId());
        }
        relPO.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        relPO.setLabCode(reqObject.getLabCode());
        relPO.setObjectNo(reqObject.getObjectNo());
        relPO.setOrderNo(reqObject.getOrderNo());
        relPO.setCompleteDate(reqObject.getCompletedDate());
        relPO.setParentOrderNo(StringUtil.defaultString(reqObject.getParentOrderNo(), reqObject.getOrderNo()));
        relPO.setReportNo(reqObject.getReportNo());
        relPO.setExternalId(null);
        relPO.setExternalNo(reqObject.getExternalNo());
        relPO.setSourceType(SourceTypeEnum.SLIM.getCode());
        relPO.setLanguageId(LanguageType.English.getLanguageId());

        relPO.setBizVersionId(this.getTestDataReportObjectRelMd5(relPO));
        relPO.setActiveIndicator(StatusEnum.VALID.getCode());
        relPO.setCreatedBy(userName);
        relPO.setCreatedDate(DateUtils.getNow());
        relPO.setModifiedBy(userName);
        relPO.setModifiedDate(DateUtils.getNow());

        List<TestDataItemReq> testDatas = reqObject.getTestDatas();
        Map<String, List<TestDataItemReq>> matrixMaps = testDatas.stream().filter(item -> StringUtil.isNotEmpty(item.getExternalCode()) && StringUtil.isNotEmpty(item.getExternalSampleNo()))
                .collect(Collectors.groupingBy(item -> item.getExternalCode() + "_" + item.getTestLineId()  + "_" + item.getCitationId() + "_" + item.getExternalSampleNo()));

        // 获取 matrix 中的 <bizVersionId, id>关系
        List<TestDataMatrixInfoPO> testDataMatrixInfoPOS = matrixInfoExtMapper.queryMatrix(relPO.getId(), suffix);
        Map<String, Long> matrixBizMaps = Maps.newConcurrentMap();
        if (!CollectionUtils.isEmpty(testDataMatrixInfoPOS)) {
            matrixBizMaps = testDataMatrixInfoPOS.stream().collect(Collectors.toMap(TestDataMatrixInfoPO::getBizVersionId, TestDataMatrixInfoPO::getId, (k1, k2) -> k1));
        }

        for (String matrix : matrixMaps.keySet()) {
            List<TestDataItemReq> testDataItemReqs = matrixMaps.get(matrix);
            TestDataMatrixInfoPO testMatrix = new TestDataMatrixInfoPO();
            TestDataItemReq testDataItemReq = testDataItemReqs.get(0);

            testMatrix.setTestLineMappingId(testDataItemReq.getTestLineMappingId());
            testMatrix.setTestMatrixId(testDataItemReq.getTestMatrixId());
            testMatrix.setObjectRelId(relPO.getId());
            String externalCode = testDataItemReq.getExternalCode();
            testMatrix.setExternalCode(externalCode);
            Integer testConditionId = ExternalCodeTransConditionIdEnum.getConditionIdByExternalCode(externalCode);
            if(testConditionId != null){
                List<TestDataConditionInfo> testConditions = Lists.newArrayList();
                TestDataConditionInfo testCondition = new TestDataConditionInfo();
                testCondition.setTestConditionId(testConditionId);
                testConditions.add(testCondition);
                /*String conditionJSON = "[{\"languages\":[{\"languageId\":null,\"testConditionName\":\"\"}],\"testConditionId\":"+conditionIdByExternalCode+",\"testConditionName\":\"\",\"testConditionSeq\":1}]";
                JSONArray objects = JSON.parseArray(conditionJSON);*/
                testMatrix.setCondition(JSONObject.toJSONString(testConditions));
            }

            testMatrix.setPpVersionId(NumberUtil.toInt(testDataItemReq.getPpVersionId()));
            testMatrix.setTestLineId(testDataItemReq.getTestLineId());
            testMatrix.setCitationId(testDataItemReq.getCitationId());
            testMatrix.setCitationVersionId(testDataItemReq.getCitationVersionId());
            testMatrix.setSampleId(testDataItemReq.getTestSampleId());
            testMatrix.setSampleNo(testDataItemReq.getSampleNo());
            testMatrix.setExternalSampleNo(testDataItemReq.getExternalSampleNo());

            TestDataMatrixExtFieldInfo extField = new TestDataMatrixExtFieldInfo();
            extField.setMaterialName(testDataItemReq.getMaterialName());
            extField.setMaterialColor(testDataItemReq.getMaterialColor());
            extField.setMaterialTexture(testDataItemReq.getMaterialTexture());
            extField.setUsedPosition(testDataItemReq.getUsedPosition());
            testMatrix.setExtFields(JSONObject.toJSONString(extField));

            testMatrix.setBizVersionId(this.getTestDataReportMatrixMd5(testMatrix));
            // 设置 Id
            testMatrix.setId(NumberUtil.defaultIfBlank(matrixBizMaps.get(testMatrix.getBizVersionId()), idWorker.nextId()));

            testMatrix.setActiveIndicator(StatusEnum.VALID.getCode());
            testMatrix.setCreatedBy(userName);
            testMatrix.setCreatedDate(DateUtils.getNow());
            testMatrix.setModifiedBy(userName);
            testMatrix.setModifiedDate(DateUtils.getNow());
            testMatrix.setTestLineSeq(testDataItemReq.getTestLineSeq());
            testDataMatrixs.add(testMatrix);

            for (TestDataItemReq itemReq : testDataItemReqs) {
                // DIG-8533
                if (AnalyteTypeEnum.check(itemReq.getAnalyteType(), AnalyteTypeEnum.Conclusion)){
                    PriorityLevel priorityLevel = this.getPriorityLevel(itemReq.getTestValue());
                    if (priorityLevel != null){
                        testMatrix.setConclusionId(String.valueOf(priorityLevel.getLevel()));
                        testMatrix.setConclusionDisplay(priorityLevel.getMessage());
                    }
                }
                TestDataInfoPO testDataInfoPO = new TestDataInfoPO();

                testDataInfoPO.setId(idWorker.nextId());
                testDataInfoPO.setObjectRelId(relPO.getId());
                testDataInfoPO.setTestDataMatrixId(testMatrix.getId());
                testDataInfoPO.setAnalyteName(itemReq.getTestAnalyteName());
                testDataInfoPO.setAnalyteType(itemReq.getAnalyteType());
                testDataInfoPO.setAnalyteCode(itemReq.getAnalyteCode());
                testDataInfoPO.setAnalyteSeq(itemReq.getAnalyteSeq());
                testDataInfoPO.setReportUnit(itemReq.getReportUnit());
                testDataInfoPO.setTestValue(itemReq.getTestValue());
                testDataInfoPO.setReportLimit(itemReq.getReportLimit());
                testDataInfoPO.setCasNo(itemReq.getCasNo());
                testDataInfoPO.setLimitUnit(itemReq.getLimitUnit());

                if (StringUtils.isNotBlank(itemReq.getTestAnalyteNameCN()) || StringUtils.isNotBlank(itemReq.getReportUnitCN())){
                    List<TestDataLangInfo> langs = Lists.newArrayList();
                    TestDataLangInfo lang = new TestDataLangInfo();
                    lang.setLanguageId(LanguageType.Chinese.getLanguageId());
                    lang.setTestAnalyteName(itemReq.getTestAnalyteNameCN());
                    lang.setReportUnit(org.apache.commons.lang3.StringUtils.defaultString(itemReq.getReportUnitCN(), null));
                    langs.add(lang);
                    testDataInfoPO.setLanguages(JSONObject.toJSONString(langs));
                }

                testDataInfoPO.setBizVersionId(this.getTestDataReportMd5(testDataInfoPO));
                testDataInfoPO.setActiveIndicator(StatusEnum.VALID.getCode());
                testDataInfoPO.setCreatedBy(userName);
                testDataInfoPO.setCreatedDate(DateUtils.getNow());
                testDataInfoPO.setModifiedBy(userName);
                testDataInfoPO.setModifiedDate(DateUtils.getNow());
                testDataInfos.add(testDataInfoPO);
            }
        }

        TestDataDTO testDataDTO = new TestDataDTO();
        testDataDTO.setTestDataObjectRels(Lists.newArrayList(relPO));
        testDataDTO.setTestDataMatrixInfos(testDataMatrixs);
        testDataDTO.setTestDataInfos(testDataInfos);
        testDataDTO.setTestDataSuffix(StringUtil.getTestDataSuffix(reqObject.getLabCode()));
        TestDataObjectRelDTO objectRelDTO = new TestDataObjectRelDTO();
        objectRelDTO.setObjectNo(reqObject.getObjectNo());
        objectRelDTO.setExternalNo(reqObject.getExternalNo());
        objectRelDTO.setModifiedDate(DateUtils.getNow());
        objectRelDTO.setModifiedBy(userName);
        testDataDTO.setObjectRelDTO(objectRelDTO);

        CustomResult customResult = this.doDeal(testDataDTO);
        if (!customResult.isSuccess()) {
            return rspResult.fail(customResult.getMsg());
        }
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param conclusionCode
     * @return
     */
    private PriorityLevel getPriorityLevel(String conclusionCode){
        if (StringUtils.isBlank(conclusionCode)){
            return null;
        }
        for (PriorityLevel level: PriorityLevel.values()) {
            if (StringUtils.equalsIgnoreCase(level.getMessage(), conclusionCode)){
                return level;
            }
        }
        return null;
    }

    /**
     *
     * @return
     */
    public CustomResult cleanConclusion(CleanConclusionReq reqObject){
        CustomResult rspResult = new CustomResult();
        final String prefix = "tb_test_data_info_";
        List<String> tables = slimConfigExtMapper.getTableInfoList();
        if (tables == null || tables.isEmpty()){
            return rspResult;
        }

        for (String tableName: tables) {
            if (StringUtils.isNotBlank(reqObject.getTableName())){
                if (!StringUtils.equalsIgnoreCase(reqObject.getTableName(), tableName)){
                    continue;
                }
            }
            String suffix = StringUtils.remove(tableName, prefix);
            if (StringUtils.isBlank(suffix)){
                continue;
            }
            long testDataId = 0;
            do {
                List<TestDataInfoPO> testDatas = testDataInfoExtMapper.getTestDataInfoList(testDataId, suffix);
                if (testDatas == null || testDatas.isEmpty()){
                    break;
                }
                Map<Long, TestDataMatrixInfoPO> testMatrixMaps = Maps.newHashMap();
                for (TestDataInfoPO testData: testDatas) {
                    // 0无效，1有效
                    testData.setActiveIndicator(0);
                    testData.setModifiedDate(DateUtils.getNow());

                    testDataId = testData.getId();

                    PriorityLevel priorityLevel = this.getPriorityLevel(testData.getTestValue());
                    if (priorityLevel == null){
                        continue;
                    }
                    TestDataMatrixInfoPO testMatrix = new TestDataMatrixInfoPO();
                    testMatrix.setId(testData.getTestDataMatrixId());
                    testMatrix.setConclusionId(String.valueOf(priorityLevel.getLevel()));
                    testMatrix.setConclusionDisplay(priorityLevel.getMessage());
                    testMatrix.setModifiedDate(DateUtils.getNow());

                    testMatrixMaps.put(testData.getTestDataMatrixId(), testMatrix);
                }
                if (!testMatrixMaps.isEmpty()){
                    matrixInfoExtMapper.batchUpdate(testMatrixMaps.values(), suffix);
                }
                if (!testDatas.isEmpty()){
                    testDataInfoExtMapper.batchUpdate(testDatas, suffix);
                }
            }while (true);
        }

        rspResult.setSuccess(true);
        return rspResult;
    }
}
