package com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix;

import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.testdatabiz.facade.model.dto.starlims.MatrixFilterDTO;
import com.sgs.testdatabiz.facade.model.dto.starlims.SubContractMatrixDTO;

/**
 * <AUTHOR>
 * @date 2024/11/27 19:17
 */
public interface MatrixInfoFilter {

    public SubContractMatrixDTO filterStarlimsMatrix(MatrixFilterDTO reportData);

}
