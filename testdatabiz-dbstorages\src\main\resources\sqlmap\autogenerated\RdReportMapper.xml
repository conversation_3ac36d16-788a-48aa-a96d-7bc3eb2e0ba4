<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="bu_id" property="buId" jdbcType="BIGINT" />
    <result column="system_id" property="systemId" jdbcType="BIGINT" />
    <result column="report_id" property="reportId" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="original_report_no" property="originalReportNo" jdbcType="VARCHAR" />
    <result column="report_header" property="reportHeader" jdbcType="VARCHAR" />
    <result column="report_address" property="reportAddress" jdbcType="VARCHAR" />
    <result column="report_status" property="reportStatus" jdbcType="INTEGER" />
    <result column="report_due_date" property="reportDueDate" jdbcType="TIMESTAMP" />
    <result column="report_certificate_name" property="reportCertificateName" jdbcType="VARCHAR" />
    <result column="report_created_by" property="reportCreatedBy" jdbcType="VARCHAR" />
    <result column="report_created_date" property="reportCreatedDate" jdbcType="TIMESTAMP" />
    <result column="report_approver_by" property="reportApproverBy" jdbcType="VARCHAR" />
    <result column="report_approver_date" property="reportApproverDate" jdbcType="TIMESTAMP" />
    <result column="softcopy_delivery_date" property="softcopyDeliveryDate" jdbcType="TIMESTAMP" />
    <result column="conclusion_code" property="conclusionCode" jdbcType="VARCHAR" />
    <result column="customer_conclusion" property="customerConclusion" jdbcType="VARCHAR" />
    <result column="review_conclusion" property="reviewConclusion" jdbcType="VARCHAR" />
    <result column="conclusion_remark" property="conclusionRemark" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
    <result column="exclude_customer_interface" property="excludeCustomerInterface" jdbcType="VARCHAR" />
    <result column="report_source_type" property="reportSourceType" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, lab_id, bu_id, system_id, report_id, report_no, original_report_no, report_header, 
    report_address, report_status, report_due_date, report_certificate_name, report_created_by, 
    report_created_date, report_approver_by, report_approver_date, softcopy_delivery_date, 
    conclusion_code, customer_conclusion, review_conclusion, conclusion_remark, active_indicator, 
    created_by, created_date, modified_by, modified_date, last_modified_timestamp, exclude_customer_interface, 
    report_source_type
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExample" >
    delete from tb_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportPO" >
    insert into tb_report (id, lab_id, bu_id, 
      system_id, report_id, report_no, 
      original_report_no, report_header, report_address, 
      report_status, report_due_date, report_certificate_name, 
      report_created_by, report_created_date, report_approver_by, 
      report_approver_date, softcopy_delivery_date, 
      conclusion_code, customer_conclusion, review_conclusion, 
      conclusion_remark, active_indicator, created_by, 
      created_date, modified_by, modified_date, 
      last_modified_timestamp, exclude_customer_interface, 
      report_source_type)
    values (#{id,jdbcType=BIGINT}, #{labId,jdbcType=BIGINT}, #{buId,jdbcType=BIGINT}, 
      #{systemId,jdbcType=BIGINT}, #{reportId,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{originalReportNo,jdbcType=VARCHAR}, #{reportHeader,jdbcType=VARCHAR}, #{reportAddress,jdbcType=VARCHAR}, 
      #{reportStatus,jdbcType=INTEGER}, #{reportDueDate,jdbcType=TIMESTAMP}, #{reportCertificateName,jdbcType=VARCHAR}, 
      #{reportCreatedBy,jdbcType=VARCHAR}, #{reportCreatedDate,jdbcType=TIMESTAMP}, #{reportApproverBy,jdbcType=VARCHAR}, 
      #{reportApproverDate,jdbcType=TIMESTAMP}, #{softcopyDeliveryDate,jdbcType=TIMESTAMP}, 
      #{conclusionCode,jdbcType=VARCHAR}, #{customerConclusion,jdbcType=VARCHAR}, #{reviewConclusion,jdbcType=VARCHAR}, 
      #{conclusionRemark,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, 
      now(), #{modifiedBy,jdbcType=VARCHAR}, now(), 
      #{lastModifiedTimestamp,jdbcType=TIMESTAMP}, #{excludeCustomerInterface,jdbcType=VARCHAR}, 
      #{reportSourceType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportPO" >
    insert into tb_report
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="buId != null" >
        bu_id,
      </if>
      <if test="systemId != null" >
        system_id,
      </if>
      <if test="reportId != null" >
        report_id,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="originalReportNo != null" >
        original_report_no,
      </if>
      <if test="reportHeader != null" >
        report_header,
      </if>
      <if test="reportAddress != null" >
        report_address,
      </if>
      <if test="reportStatus != null" >
        report_status,
      </if>
      <if test="reportDueDate != null" >
        report_due_date,
      </if>
      <if test="reportCertificateName != null" >
        report_certificate_name,
      </if>
      <if test="reportCreatedBy != null" >
        report_created_by,
      </if>
      <if test="reportCreatedDate != null" >
        report_created_date,
      </if>
      <if test="reportApproverBy != null" >
        report_approver_by,
      </if>
      <if test="reportApproverDate != null" >
        report_approver_date,
      </if>
      <if test="softcopyDeliveryDate != null" >
        softcopy_delivery_date,
      </if>
      <if test="conclusionCode != null" >
        conclusion_code,
      </if>
      <if test="customerConclusion != null" >
        customer_conclusion,
      </if>
      <if test="reviewConclusion != null" >
        review_conclusion,
      </if>
      <if test="conclusionRemark != null" >
        conclusion_remark,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
      <if test="excludeCustomerInterface != null" >
        exclude_customer_interface,
      </if>
      <if test="reportSourceType != null" >
        report_source_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="buId != null" >
        #{buId,jdbcType=BIGINT},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="reportId != null" >
        #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="originalReportNo != null" >
        #{originalReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportHeader != null" >
        #{reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="reportAddress != null" >
        #{reportAddress,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null" >
        #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="reportDueDate != null" >
        #{reportDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportCertificateName != null" >
        #{reportCertificateName,jdbcType=VARCHAR},
      </if>
      <if test="reportCreatedBy != null" >
        #{reportCreatedBy,jdbcType=VARCHAR},
      </if>
      <if test="reportCreatedDate != null" >
        #{reportCreatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportApproverBy != null" >
        #{reportApproverBy,jdbcType=VARCHAR},
      </if>
      <if test="reportApproverDate != null" >
        #{reportApproverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="softcopyDeliveryDate != null" >
        #{softcopyDeliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="conclusionCode != null" >
        #{conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusion != null" >
        #{customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="reviewConclusion != null" >
        #{reviewConclusion,jdbcType=VARCHAR},
      </if>
      <if test="conclusionRemark != null" >
        #{conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="excludeCustomerInterface != null" >
        #{excludeCustomerInterface,jdbcType=VARCHAR},
      </if>
      <if test="reportSourceType != null" >
        #{reportSourceType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportExample" resultType="java.lang.Integer" >
    select count(*) from tb_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.buId != null" >
        bu_id = #{record.buId,jdbcType=BIGINT},
      </if>
      <if test="record.systemId != null" >
        system_id = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.reportId != null" >
        report_id = #{record.reportId,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.originalReportNo != null" >
        original_report_no = #{record.originalReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportHeader != null" >
        report_header = #{record.reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAddress != null" >
        report_address = #{record.reportAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.reportStatus != null" >
        report_status = #{record.reportStatus,jdbcType=INTEGER},
      </if>
      <if test="record.reportDueDate != null" >
        report_due_date = #{record.reportDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportCertificateName != null" >
        report_certificate_name = #{record.reportCertificateName,jdbcType=VARCHAR},
      </if>
      <if test="record.reportCreatedBy != null" >
        report_created_by = #{record.reportCreatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.reportCreatedDate != null" >
        report_created_date = #{record.reportCreatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportApproverBy != null" >
        report_approver_by = #{record.reportApproverBy,jdbcType=VARCHAR},
      </if>
      <if test="record.reportApproverDate != null" >
        report_approver_date = #{record.reportApproverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.softcopyDeliveryDate != null" >
        softcopy_delivery_date = #{record.softcopyDeliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.conclusionCode != null" >
        conclusion_code = #{record.conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerConclusion != null" >
        customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewConclusion != null" >
        review_conclusion = #{record.reviewConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionRemark != null" >
        conclusion_remark = #{record.conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.excludeCustomerInterface != null" >
        exclude_customer_interface = #{record.excludeCustomerInterface,jdbcType=VARCHAR},
      </if>
      <if test="record.reportSourceType != null" >
        report_source_type = #{record.reportSourceType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report
    set id = #{record.id,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      bu_id = #{record.buId,jdbcType=BIGINT},
      system_id = #{record.systemId,jdbcType=BIGINT},
      report_id = #{record.reportId,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      original_report_no = #{record.originalReportNo,jdbcType=VARCHAR},
      report_header = #{record.reportHeader,jdbcType=VARCHAR},
      report_address = #{record.reportAddress,jdbcType=VARCHAR},
      report_status = #{record.reportStatus,jdbcType=INTEGER},
      report_due_date = #{record.reportDueDate,jdbcType=TIMESTAMP},
      report_certificate_name = #{record.reportCertificateName,jdbcType=VARCHAR},
      report_created_by = #{record.reportCreatedBy,jdbcType=VARCHAR},
      report_created_date = #{record.reportCreatedDate,jdbcType=TIMESTAMP},
      report_approver_by = #{record.reportApproverBy,jdbcType=VARCHAR},
      report_approver_date = #{record.reportApproverDate,jdbcType=TIMESTAMP},
      softcopy_delivery_date = #{record.softcopyDeliveryDate,jdbcType=TIMESTAMP},
      conclusion_code = #{record.conclusionCode,jdbcType=VARCHAR},
      customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      review_conclusion = #{record.reviewConclusion,jdbcType=VARCHAR},
      conclusion_remark = #{record.conclusionRemark,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      exclude_customer_interface = #{record.excludeCustomerInterface,jdbcType=VARCHAR},
      report_source_type = #{record.reportSourceType,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportPO" >
    update tb_report
    <set >
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="buId != null" >
        bu_id = #{buId,jdbcType=BIGINT},
      </if>
      <if test="systemId != null" >
        system_id = #{systemId,jdbcType=BIGINT},
      </if>
      <if test="reportId != null" >
        report_id = #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="originalReportNo != null" >
        original_report_no = #{originalReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportHeader != null" >
        report_header = #{reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="reportAddress != null" >
        report_address = #{reportAddress,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null" >
        report_status = #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="reportDueDate != null" >
        report_due_date = #{reportDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportCertificateName != null" >
        report_certificate_name = #{reportCertificateName,jdbcType=VARCHAR},
      </if>
      <if test="reportCreatedBy != null" >
        report_created_by = #{reportCreatedBy,jdbcType=VARCHAR},
      </if>
      <if test="reportCreatedDate != null" >
        report_created_date = #{reportCreatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportApproverBy != null" >
        report_approver_by = #{reportApproverBy,jdbcType=VARCHAR},
      </if>
      <if test="reportApproverDate != null" >
        report_approver_date = #{reportApproverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="softcopyDeliveryDate != null" >
        softcopy_delivery_date = #{softcopyDeliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="conclusionCode != null" >
        conclusion_code = #{conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusion != null" >
        customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="reviewConclusion != null" >
        review_conclusion = #{reviewConclusion,jdbcType=VARCHAR},
      </if>
      <if test="conclusionRemark != null" >
        conclusion_remark = #{conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="excludeCustomerInterface != null" >
        exclude_customer_interface = #{excludeCustomerInterface,jdbcType=VARCHAR},
      </if>
      <if test="reportSourceType != null" >
        report_source_type = #{reportSourceType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportPO" >
    update tb_report
    set lab_id = #{labId,jdbcType=BIGINT},
      bu_id = #{buId,jdbcType=BIGINT},
      system_id = #{systemId,jdbcType=BIGINT},
      report_id = #{reportId,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      original_report_no = #{originalReportNo,jdbcType=VARCHAR},
      report_header = #{reportHeader,jdbcType=VARCHAR},
      report_address = #{reportAddress,jdbcType=VARCHAR},
      report_status = #{reportStatus,jdbcType=INTEGER},
      report_due_date = #{reportDueDate,jdbcType=TIMESTAMP},
      report_certificate_name = #{reportCertificateName,jdbcType=VARCHAR},
      report_created_by = #{reportCreatedBy,jdbcType=VARCHAR},
      report_created_date = #{reportCreatedDate,jdbcType=TIMESTAMP},
      report_approver_by = #{reportApproverBy,jdbcType=VARCHAR},
      report_approver_date = #{reportApproverDate,jdbcType=TIMESTAMP},
      softcopy_delivery_date = #{softcopyDeliveryDate,jdbcType=TIMESTAMP},
      conclusion_code = #{conclusionCode,jdbcType=VARCHAR},
      customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      review_conclusion = #{reviewConclusion,jdbcType=VARCHAR},
      conclusion_remark = #{conclusionRemark,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      exclude_customer_interface = #{excludeCustomerInterface,jdbcType=VARCHAR},
      report_source_type = #{reportSourceType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report
      (`id`,`lab_id`,`bu_id`,
      `system_id`,`report_id`,`report_no`,
      `original_report_no`,`report_header`,`report_address`,
      `report_status`,`report_due_date`,`report_certificate_name`,
      `report_created_by`,`report_created_date`,`report_approver_by`,
      `report_approver_date`,`softcopy_delivery_date`,`conclusion_code`,
      `customer_conclusion`,`review_conclusion`,`conclusion_remark`,
      `active_indicator`,`created_by`,`created_date`,
      `modified_by`,`modified_date`,`last_modified_timestamp`,
      `exclude_customer_interface`,`report_source_type`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.buId, jdbcType=BIGINT},
      #{ item.systemId, jdbcType=BIGINT},#{ item.reportId, jdbcType=VARCHAR},#{ item.reportNo, jdbcType=VARCHAR},
      #{ item.originalReportNo, jdbcType=VARCHAR},#{ item.reportHeader, jdbcType=VARCHAR},#{ item.reportAddress, jdbcType=VARCHAR},
      #{ item.reportStatus, jdbcType=INTEGER},#{ item.reportDueDate, jdbcType=TIMESTAMP},#{ item.reportCertificateName, jdbcType=VARCHAR},
      #{ item.reportCreatedBy, jdbcType=VARCHAR},#{ item.reportCreatedDate, jdbcType=TIMESTAMP},#{ item.reportApproverBy, jdbcType=VARCHAR},
      #{ item.reportApproverDate, jdbcType=TIMESTAMP},#{ item.softcopyDeliveryDate, jdbcType=TIMESTAMP},#{ item.conclusionCode, jdbcType=VARCHAR},
      #{ item.customerConclusion, jdbcType=VARCHAR},#{ item.reviewConclusion, jdbcType=VARCHAR},#{ item.conclusionRemark, jdbcType=VARCHAR},
      #{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP},
      #{ item.excludeCustomerInterface, jdbcType=VARCHAR},#{ item.reportSourceType, jdbcType=INTEGER}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_report 
      <set>
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.buId != null"> 
          `bu_id` = #{item.buId, jdbcType = BIGINT},
        </if> 
        <if test="item.systemId != null"> 
          `system_id` = #{item.systemId, jdbcType = BIGINT},
        </if> 
        <if test="item.reportId != null"> 
          `report_id` = #{item.reportId, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.originalReportNo != null"> 
          `original_report_no` = #{item.originalReportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportHeader != null"> 
          `report_header` = #{item.reportHeader, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportAddress != null"> 
          `report_address` = #{item.reportAddress, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportStatus != null"> 
          `report_status` = #{item.reportStatus, jdbcType = INTEGER},
        </if> 
        <if test="item.reportDueDate != null"> 
          `report_due_date` = #{item.reportDueDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.reportCertificateName != null"> 
          `report_certificate_name` = #{item.reportCertificateName, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportCreatedBy != null"> 
          `report_created_by` = #{item.reportCreatedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportCreatedDate != null"> 
          `report_created_date` = #{item.reportCreatedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.reportApproverBy != null"> 
          `report_approver_by` = #{item.reportApproverBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportApproverDate != null"> 
          `report_approver_date` = #{item.reportApproverDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.softcopyDeliveryDate != null"> 
          `softcopy_delivery_date` = #{item.softcopyDeliveryDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.conclusionCode != null"> 
          `conclusion_code` = #{item.conclusionCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerConclusion != null"> 
          `customer_conclusion` = #{item.customerConclusion, jdbcType = VARCHAR},
        </if> 
        <if test="item.reviewConclusion != null"> 
          `review_conclusion` = #{item.reviewConclusion, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionRemark != null"> 
          `conclusion_remark` = #{item.conclusionRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.excludeCustomerInterface != null"> 
          `exclude_customer_interface` = #{item.excludeCustomerInterface, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportSourceType != null"> 
          `report_source_type` = #{item.reportSourceType, jdbcType = INTEGER},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>