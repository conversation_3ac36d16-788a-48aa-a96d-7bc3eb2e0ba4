/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RdAnalyteInput implements Serializable{

    private String analyteInstanceId;
    private Long analyteBaseId;
    private Integer analyteId;
    private String analyteName;
    private Integer analyteSeq;
    private Integer analyteLimitVersionId;
    private Long unitBaseId;
    private Long unitId;
    private String reportUnit;
    private String casNo;
    private List<RdAnalyteLanguageInput> languageList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
