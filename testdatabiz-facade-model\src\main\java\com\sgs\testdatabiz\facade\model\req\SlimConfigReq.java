package com.sgs.testdatabiz.facade.model.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

/**
 * @ClassName SlimConfigReq
 * @Description slimConfig 的配置查询类
 * <AUTHOR>
 * @Date 2022/5/31
 */
@ToString
@ApiModel(value = "SlimConfigReq",description = "SlimConfigReq")
public class SlimConfigReq extends BaseRequest {
    @ApiModelProperty(required = false,notes = "labCode")
    private String labCode;

    @ApiModelProperty(required = false,notes = "locationCode")
    private String locationCode;

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }
}
