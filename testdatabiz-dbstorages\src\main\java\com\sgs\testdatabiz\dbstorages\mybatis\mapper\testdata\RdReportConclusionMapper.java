package com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionExample;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdReportConclusionMapper {
    int countByExample(RdReportConclusionExample example);

    int deleteByExample(RdReportConclusionExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdReportConclusionPO record);

    int insertSelective(RdReportConclusionPO record);

    List<RdReportConclusionPO> selectByExample(RdReportConclusionExample example);

    RdReportConclusionPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdReportConclusionPO record, @Param("example") RdReportConclusionExample example);

    int updateByExample(@Param("record") RdReportConclusionPO record, @Param("example") RdReportConclusionExample example);

    int updateByPrimaryKeySelective(RdReportConclusionPO record);

    int updateByPrimaryKey(RdReportConclusionPO record);

    int batchInsert(List<RdReportConclusionPO> list);

    int batchUpdate(List<RdReportConclusionPO> list);
}