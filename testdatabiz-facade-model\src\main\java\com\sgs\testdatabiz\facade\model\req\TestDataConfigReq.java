package com.sgs.testdatabiz.facade.model.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @ClassName TestDataConfigReq
 * @Description 获取配置数据请求类
 * <AUTHOR>
 * @Date 2023/5/6
 */
@ApiModel(value = "TestDataConfigReq", description = "TestDataConfigReq，如果直接传递null，返回general数据")
public class TestDataConfigReq  extends BaseRequest {
    @ApiModelProperty("customerGroupCode")
    private String customerGroupCode;
    @ApiModelProperty("testLineIds")
    private List<Integer> testLineIds;
    @ApiModelProperty("citationIds")
    private List<Integer> citationIds;
    @ApiModelProperty("conditionIds")
    private List<Integer> conditionIds;
    @ApiModelProperty("languageId")
    private Integer languageId;

    public String getCustomerGroupCode() {
        return customerGroupCode;
    }

    public void setCustomerGroupCode(String customerGroupCode) {
        this.customerGroupCode = customerGroupCode;
    }

    public List<Integer> getTestLineIds() {
        return testLineIds;
    }

    public void setTestLineIds(List<Integer> testLineIds) {
        this.testLineIds = testLineIds;
    }

    public List<Integer> getCitationIds() {
        return citationIds;
    }

    public void setCitationIds(List<Integer> citationIds) {
        this.citationIds = citationIds;
    }

    public List<Integer> getConditionIds() {
        return conditionIds;
    }

    public void setConditionIds(List<Integer> conditionIds) {
        this.conditionIds = conditionIds;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }
}
