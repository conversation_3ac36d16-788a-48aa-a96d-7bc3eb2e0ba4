<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.RdQuotationExtMapper" >

  <select id="selectQuotationNos" resultType="java.lang.String">
    select distinct quotation_no
        from tb_quotation
        where quotation_no in
      <foreach item="param" index="index" collection="quotationNos" open="(" separator="," close=")">
          #{param}
      </foreach>
          and system_id = #{systemId}
  </select>
</mapper>