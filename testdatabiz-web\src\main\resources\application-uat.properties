jdbc.datasource.read=get,select,count,list,query,find,search,sum
jdbc.datasource.write=add,create,update,delete,remove,insert

##\u6570\u636E\u5E93\u914D\u7F6E
validationQuery=SELECT 'x'


# Test Data Master DB
datasource.dynamic.common.product-lines=SL,HL,MR,AUTO,IND-PL,AFL,EEC,EE,CPCH,EMC,ACAD
datasource.dynamic.common.schema.testdata.master.driver-class-name=com.mysql.jdbc.Driver
datasource.dynamic.common.schema.testdata.master.url=***************************************************************************************************************************************************************************************************************************
datasource.dynamic.common.schema.testdata.master.username=datadb_user_uat
datasource.dynamic.common.schema.testdata.master.password=DaDB_sgs_1324UAT

# Test Data slave DB
datasource.dynamic.common.schema.testdata.slave.driver-class-name=com.mysql.jdbc.Driver
datasource.dynamic.common.schema.testdata.slave.url=**************************************************************************************************************************************************************************************************************************
datasource.dynamic.common.schema.testdata.slave.username=datadb_user_uat
datasource.dynamic.common.schema.testdata.slave.password=DaDB_sgs_1324UAT


# redis.cluster.nodes
spring.redis.nodes=10.168.128.235:7000,10.168.128.235:7001,10.168.128.235:7002,10.168.128.234:7003,10.168.128.234:7004,10.168.128.234:7005
# Redis???????????? == redis.cluster.password
spring.redis.password=
# ?????????????
spring.redis.timeout=10000
# ?????????????????????????16??????? 0 ?? 15
spring.redis.database=0
# ??????�????????????????
spring.redis.max-redirects=6

# ????????????????????????�??????????????????????
spring.redis.pool.max-idle=20
# ??????????????????????????????
spring.redis.pool.min-idle=5
# ??????????????????�?????�???????==redis.pool.maxTotal
spring.redis.pool.max-active=20
# ????????????????????�?????�???????
spring.redis.pool.max-wait=1000
# redis.pool.testOnBorrow
spring.redis.pool.testOnBorrow=true


# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
tomcat.protocol=org.apache.coyote.http11.Http11Nio2Protocol
# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0738\uFFFD\uFFFD\u00E3\uFFFD\uFFFD\uFFFD\u04AA\uFFFD\uFFFD\u05F0
#tomcat.protocol=org.apache.coyote.http11.Http11AprProtocol
tomcat.connectionTimeout=20000
tomcat.maxConnections=2000
tomcat.maxThreads=700
tomcat.uriEncoding=UTF-8
tomcat.acceptCount=2000
#webEnvironment=false
tomcat.port=8092
# \uFFFD\uFFFDdubbo\u042D\uFFFD\uFFFD\uFFFD\uFFFD20880\uFFFD\u02FF\u06B1\uFFFD\u00B6\uFFFD\uFFFD\uFFFD\uFFFD
dubbo.port=29539
zookeeper.address=*************:2181,*************:2181,*************:2181
# kafka
kafka.bootstrap-servers=*************:9092,*************:9092,*************:9092

user.management.url=https://cnapp-uat.sgs.net/UserManagementApi
localiLayer.url=https://cnlocalilayer-uat.sgs.net
frameWorkApi.url=https://cnapp-uat.sgs.net/FrameWorkApi
notification.url=https://cnapp-uat.sgs.net/NotificationApi
base.url=https://cnapp-uat.sgs.net/



#----------- Start Starlims -----------
starLims.url=https://uat-cn-starlims.sgs.net/STARLIMS11.sgs.uat
starLims.authId=a1b172dde7ce4a3fb1028bf981496a9a
starLims.header.user.key=STARLIMSUser
starLims.header.user.val=TRIMS
starLims.header.pass.key=STARLIMSPass
starLims.header.pass.val=Tr1m5
#----------- End Starlims -----------
swagger.is.enable=true

# API Base URLs
api.base.otsnotes=https://cnapp-uat.sgs.net/otsnotes2api
api.base.preorder=https://cnapp-uat.sgs.net/preorder
