<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportMatrixLangMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="rd_report_matrix_id" property="rdReportMatrixId" jdbcType="BIGINT" />
    <result column="language_id" property="languageId" jdbcType="INTEGER" />
    <result column="evaluation_alias" property="evaluationAlias" jdbcType="VARCHAR" />
    <result column="evaluation_name" property="evaluationName" jdbcType="VARCHAR" />
    <result column="citation_name" property="citationName" jdbcType="VARCHAR" />
    <result column="citation_full_name" property="citationFullName" jdbcType="VARCHAR" />
    <result column="method_desc" property="methodDesc" jdbcType="VARCHAR" />
    <result column="customer_conclusion" property="customerConclusion" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, rd_report_matrix_id, language_id, evaluation_alias, evaluation_name, citation_name, 
    citation_full_name, method_desc, customer_conclusion, created_by, created_date, modified_by, 
    modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_matrix_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_matrix_lang
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_matrix_lang
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangExample" >
    delete from tb_report_matrix_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangPO" >
    insert into tb_report_matrix_lang (id, rd_report_matrix_id, language_id, 
      evaluation_alias, evaluation_name, citation_name, 
      citation_full_name, method_desc, customer_conclusion, 
      created_by, created_date, modified_by, 
      modified_date)
    values (#{id,jdbcType=BIGINT}, #{rdReportMatrixId,jdbcType=BIGINT}, #{languageId,jdbcType=INTEGER}, 
      #{evaluationAlias,jdbcType=VARCHAR}, #{evaluationName,jdbcType=VARCHAR}, #{citationName,jdbcType=VARCHAR}, 
      #{citationFullName,jdbcType=VARCHAR}, #{methodDesc,jdbcType=VARCHAR}, #{customerConclusion,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, now(), #{modifiedBy,jdbcType=VARCHAR}, 
      now())
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangPO" >
    insert into tb_report_matrix_lang
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="rdReportMatrixId != null" >
        rd_report_matrix_id,
      </if>
      <if test="languageId != null" >
        language_id,
      </if>
      <if test="evaluationAlias != null" >
        evaluation_alias,
      </if>
      <if test="evaluationName != null" >
        evaluation_name,
      </if>
      <if test="citationName != null" >
        citation_name,
      </if>
      <if test="citationFullName != null" >
        citation_full_name,
      </if>
      <if test="methodDesc != null" >
        method_desc,
      </if>
      <if test="customerConclusion != null" >
        customer_conclusion,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="rdReportMatrixId != null" >
        #{rdReportMatrixId,jdbcType=BIGINT},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="evaluationAlias != null" >
        #{evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="evaluationName != null" >
        #{evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="citationFullName != null" >
        #{citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="methodDesc != null" >
        #{methodDesc,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusion != null" >
        #{customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_matrix_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_matrix_lang
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.rdReportMatrixId != null" >
        rd_report_matrix_id = #{record.rdReportMatrixId,jdbcType=BIGINT},
      </if>
      <if test="record.languageId != null" >
        language_id = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.evaluationAlias != null" >
        evaluation_alias = #{record.evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluationName != null" >
        evaluation_name = #{record.evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="record.citationName != null" >
        citation_name = #{record.citationName,jdbcType=VARCHAR},
      </if>
      <if test="record.citationFullName != null" >
        citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.methodDesc != null" >
        method_desc = #{record.methodDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.customerConclusion != null" >
        customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_matrix_lang
    set id = #{record.id,jdbcType=BIGINT},
      rd_report_matrix_id = #{record.rdReportMatrixId,jdbcType=BIGINT},
      language_id = #{record.languageId,jdbcType=INTEGER},
      evaluation_alias = #{record.evaluationAlias,jdbcType=VARCHAR},
      evaluation_name = #{record.evaluationName,jdbcType=VARCHAR},
      citation_name = #{record.citationName,jdbcType=VARCHAR},
      citation_full_name = #{record.citationFullName,jdbcType=VARCHAR},
      method_desc = #{record.methodDesc,jdbcType=VARCHAR},
      customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now()
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangPO" >
    update tb_report_matrix_lang
    <set >
      <if test="rdReportMatrixId != null" >
        rd_report_matrix_id = #{rdReportMatrixId,jdbcType=BIGINT},
      </if>
      <if test="languageId != null" >
        language_id = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="evaluationAlias != null" >
        evaluation_alias = #{evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="evaluationName != null" >
        evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        citation_name = #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="citationFullName != null" >
        citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      </if>
      <if test="methodDesc != null" >
        method_desc = #{methodDesc,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusion != null" >
        customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportMatrixLangPO" >
    update tb_report_matrix_lang
    set rd_report_matrix_id = #{rdReportMatrixId,jdbcType=BIGINT},
      language_id = #{languageId,jdbcType=INTEGER},
      evaluation_alias = #{evaluationAlias,jdbcType=VARCHAR},
      evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      citation_name = #{citationName,jdbcType=VARCHAR},
      citation_full_name = #{citationFullName,jdbcType=VARCHAR},
      method_desc = #{methodDesc,jdbcType=VARCHAR},
      customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report_matrix_lang
      (`id`,`rd_report_matrix_id`,`language_id`,
      `evaluation_alias`,`evaluation_name`,`citation_name`,
      `citation_full_name`,`method_desc`,`customer_conclusion`,
      `created_by`,`created_date`,`modified_by`,
      `modified_date`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.rdReportMatrixId, jdbcType=BIGINT},#{ item.languageId, jdbcType=INTEGER},
      #{ item.evaluationAlias, jdbcType=VARCHAR},#{ item.evaluationName, jdbcType=VARCHAR},#{ item.citationName, jdbcType=VARCHAR},
      #{ item.citationFullName, jdbcType=VARCHAR},#{ item.methodDesc, jdbcType=VARCHAR},#{ item.customerConclusion, jdbcType=VARCHAR},
      #{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},
      #{ item.modifiedDate, jdbcType=TIMESTAMP}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_report_matrix_lang 
      <set>
        <if test="item.rdReportMatrixId != null"> 
          `rd_report_matrix_id` = #{item.rdReportMatrixId, jdbcType = BIGINT},
        </if> 
        <if test="item.languageId != null"> 
          `language_id` = #{item.languageId, jdbcType = INTEGER},
        </if> 
        <if test="item.evaluationAlias != null"> 
          `evaluation_alias` = #{item.evaluationAlias, jdbcType = VARCHAR},
        </if> 
        <if test="item.evaluationName != null"> 
          `evaluation_name` = #{item.evaluationName, jdbcType = VARCHAR},
        </if> 
        <if test="item.citationName != null"> 
          `citation_name` = #{item.citationName, jdbcType = VARCHAR},
        </if> 
        <if test="item.citationFullName != null"> 
          `citation_full_name` = #{item.citationFullName, jdbcType = VARCHAR},
        </if> 
        <if test="item.methodDesc != null"> 
          `method_desc` = #{item.methodDesc, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerConclusion != null"> 
          `customer_conclusion` = #{item.customerConclusion, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>