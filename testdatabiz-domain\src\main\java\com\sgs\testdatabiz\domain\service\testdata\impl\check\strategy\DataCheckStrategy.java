package com.sgs.testdatabiz.domain.service.testdata.impl.check.strategy;

import com.sgs.testdatabiz.domain.service.testdata.impl.check.RTDContext;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

/**
 * TestData 校验策略
 * @author: shawn.yang
 * @create: 2023-03-27 17:07
 */
public interface DataCheckStrategy {

    /**
     * check testData的具体实现
     * @param reportTestData 标准ReportTestData结构
     * @param rtdContext 上下文，暂时用来往上下文添加check中的message
     * @return
     */
    boolean check(final ReportTestDataInfo reportTestData,final RTDContext rtdContext);

    /**
     * 是否mute check方法
     * 如果muteCheck == true，check()方法的结果会被忽略，继续执行下一个DataCheckStrategy
     * 如果muteCheck == false，check()方法的结果不会被忽略，一旦check()==false,不会继续执行下一个DataCheckStrategy
     * @return
     */
    boolean isMute();

    /**
     * 获取下一个策略
     * @return 下一个策略
     */
    DataCheckStrategy getNext();

    /**
     * 设置下一个策略
     * @param next 下一个策略
     * @return 下一个策略
     */
    DataCheckStrategy setNext(final DataCheckStrategy next);
}
