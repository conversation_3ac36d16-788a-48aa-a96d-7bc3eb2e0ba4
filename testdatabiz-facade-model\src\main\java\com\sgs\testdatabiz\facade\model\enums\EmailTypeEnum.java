package com.sgs.testdatabiz.facade.model.enums;

public enum EmailTypeEnum {
    SampleNo(1,"%s%s", "EXTERNALIDENT在Slim中缺少DESCRIPTION_1："),
    AnalyteName(2,"%s（%s）", "SCH_CODE(ANALYTECODE)缺少中文Analyte Name(LSA_DESC)："),
    TestValue(3,"%s（%s）", "SCH_CODE(ANALYTECODE)缺少ROUNDEDVALUE："),
    Scheme(4,"%s%s", "在Order中没有找到SCH_CODE对应的 TestLine，请确认是否有添加该TestLine，或者缺少该TestLine和Scheme的Mapping关系："),
    SchemeLackSample(5,"%s：%s", "SCH_CODE下缺少Sample："),
    SchemeAssignSample(6,"%s和%s", "在Order中没有找到SCH_CODE和DESCRIPTION_1的Assign关系："),
    AnalyteCode(7,"%s：%s", "SCH_CODE缺少Analyte (Sample-AnalyteCode)：")
    ;

    private final int type;
    private final String format;
    private final String key;

    EmailTypeEnum(int type, String format, String key) {
        this.type = type;
        this.format = format;
        this.key = key;
    }

    public int getType() {
        return type;
    }

    public String getKey() {
        return key;
    }

    public String getFormat() {
        return format;
    }

    public static EmailTypeEnum findType(Integer type) {
        if (type == null) {
            return null;
        }
        for (EmailTypeEnum enu : EmailTypeEnum.values()) {
            if (enu.getType() == type.intValue()) {
                return enu;
            }
        }
        return null;
    }

    public boolean check(EmailTypeEnum emailType) {
        if (emailType == null) {
            return false;
        }
        return this.getType() == emailType.getType();
    }
}
