package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdEfilingDO {

    private List<String> exclusionCitationCode;
    private List<String> rbsrCitationCode;
    private String disclaimCode;
    private String cpscLabId;
    private String labType;
    private String labName;
    private String labEmail;
    private String labTelephoneNumber;
    private EfilingAddressInfo addressInfo;


    @Data
    public static class EfilingAddressInfo {
        private String address;
        private String city;
        private String country;
        private String province;
        private String postal;
    }
}
