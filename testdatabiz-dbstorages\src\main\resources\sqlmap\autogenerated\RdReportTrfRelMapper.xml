<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportTrfRelMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="bu_id" property="buId" jdbcType="BIGINT" />
    <result column="trf_ref_system_id" property="trfRefSystemId" jdbcType="INTEGER" />
    <result column="trf_no" property="trfNo" jdbcType="VARCHAR" />
    <result column="order_system_id" property="orderSystemId" jdbcType="INTEGER" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, lab_id, bu_id, trf_ref_system_id, trf_no, order_system_id, order_no, report_no, 
    active_indicator, created_by, created_date, modified_by, modified_date, last_modified_timestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_trf_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_trf_rel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_trf_rel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelExample" >
    delete from tb_report_trf_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelPO" >
    insert into tb_report_trf_rel (id, lab_id, bu_id, 
      trf_ref_system_id, trf_no, order_system_id, 
      order_no, report_no, active_indicator, 
      created_by, created_date, modified_by, 
      modified_date, last_modified_timestamp
      )
    values (#{id,jdbcType=BIGINT}, #{labId,jdbcType=BIGINT}, #{buId,jdbcType=BIGINT}, 
      #{trfRefSystemId,jdbcType=INTEGER}, #{trfNo,jdbcType=VARCHAR}, #{orderSystemId,jdbcType=INTEGER}, 
      #{orderNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=VARCHAR}, now(), #{modifiedBy,jdbcType=VARCHAR}, 
      now(), #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelPO" >
    insert into tb_report_trf_rel
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="buId != null" >
        bu_id,
      </if>
      <if test="trfRefSystemId != null" >
        trf_ref_system_id,
      </if>
      <if test="trfNo != null" >
        trf_no,
      </if>
      <if test="orderSystemId != null" >
        order_system_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="buId != null" >
        #{buId,jdbcType=BIGINT},
      </if>
      <if test="trfRefSystemId != null" >
        #{trfRefSystemId,jdbcType=INTEGER},
      </if>
      <if test="trfNo != null" >
        #{trfNo,jdbcType=VARCHAR},
      </if>
      <if test="orderSystemId != null" >
        #{orderSystemId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_trf_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_trf_rel
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.buId != null" >
        bu_id = #{record.buId,jdbcType=BIGINT},
      </if>
      <if test="record.trfRefSystemId != null" >
        trf_ref_system_id = #{record.trfRefSystemId,jdbcType=INTEGER},
      </if>
      <if test="record.trfNo != null" >
        trf_no = #{record.trfNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSystemId != null" >
        order_system_id = #{record.orderSystemId,jdbcType=INTEGER},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_trf_rel
    set id = #{record.id,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      bu_id = #{record.buId,jdbcType=BIGINT},
      trf_ref_system_id = #{record.trfRefSystemId,jdbcType=INTEGER},
      trf_no = #{record.trfNo,jdbcType=VARCHAR},
      order_system_id = #{record.orderSystemId,jdbcType=INTEGER},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelPO" >
    update tb_report_trf_rel
    <set >
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="buId != null" >
        bu_id = #{buId,jdbcType=BIGINT},
      </if>
      <if test="trfRefSystemId != null" >
        trf_ref_system_id = #{trfRefSystemId,jdbcType=INTEGER},
      </if>
      <if test="trfNo != null" >
        trf_no = #{trfNo,jdbcType=VARCHAR},
      </if>
      <if test="orderSystemId != null" >
        order_system_id = #{orderSystemId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportTrfRelPO" >
    update tb_report_trf_rel
    set lab_id = #{labId,jdbcType=BIGINT},
      bu_id = #{buId,jdbcType=BIGINT},
      trf_ref_system_id = #{trfRefSystemId,jdbcType=INTEGER},
      trf_no = #{trfNo,jdbcType=VARCHAR},
      order_system_id = #{orderSystemId,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report_trf_rel
      (`id`,`lab_id`,`bu_id`,
      `trf_ref_system_id`,`trf_no`,`order_system_id`,
      `order_no`,`report_no`,`active_indicator`,
      `created_by`,`created_date`,`modified_by`,
      `modified_date`,`last_modified_timestamp`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.buId, jdbcType=BIGINT},
      #{ item.trfRefSystemId, jdbcType=INTEGER},#{ item.trfNo, jdbcType=VARCHAR},#{ item.orderSystemId, jdbcType=INTEGER},
      #{ item.orderNo, jdbcType=VARCHAR},#{ item.reportNo, jdbcType=VARCHAR},#{ item.activeIndicator, jdbcType=TINYINT},
      #{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},
      #{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_report_trf_rel 
      <set>
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.buId != null"> 
          `bu_id` = #{item.buId, jdbcType = BIGINT},
        </if> 
        <if test="item.trfRefSystemId != null"> 
          `trf_ref_system_id` = #{item.trfRefSystemId, jdbcType = INTEGER},
        </if> 
        <if test="item.trfNo != null"> 
          `trf_no` = #{item.trfNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.orderSystemId != null"> 
          `order_system_id` = #{item.orderSystemId, jdbcType = INTEGER},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>