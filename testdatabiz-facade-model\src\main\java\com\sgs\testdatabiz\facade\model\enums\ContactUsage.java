package com.sgs.testdatabiz.facade.model.enums;


import java.util.HashMap;
import java.util.Map;

public enum ContactUsage {
    NONE(0, "none"),
    SALES(1, "sales"),
    TS(2, "ts"),
    CAS(3, "cas"),
    SUB_REPORT_REVIEWER(4, "subReportReviewer"),
    CS(5,"cs"),
    ;

    ContactUsage(Integer type, String code) {
        this.type = type;
        this.code = code;
    }

    private Integer type;
    private String code;

    public String getCode() {
        return code;
    }

    public Integer getType() {
        return type;
    }

    public static final Map<String, ContactUsage> maps = new HashMap<String, ContactUsage>() {

        {
            for (ContactUsage contactUsage : ContactUsage.values()) {
                put(contactUsage.getCode(), contactUsage);
            }
        }
    };

    public static ContactUsage getCode(String code) {
        if (code == null || code.length() == 0 || !maps.containsKey(code)) {
            return ContactUsage.NONE;
        }
        return maps.get(code);
    }
}
