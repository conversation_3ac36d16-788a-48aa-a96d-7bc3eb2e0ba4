package com.sgs.testdatabiz.dbstorages.mybatis.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * Created by <PERSON> on 2019/4/28.
 */
@Configuration
@MapperScan(
value = {
    "com.sgs.testdatabiz.dbstorages.mybatis"
},
sqlSessionFactoryRef = "sqlSessionFactoryDynamic")
@EnableTransactionManagement
public class DynamicDataSourceConfig {
    @Resource
    private DataSourceBuilder dataSourceBuilder;

    /**
     * 构造多数据源连接池
     */
    @Bean("dynamicDataSource")
    @Primary
    public DynamicDataSource dynamicDataSource(){
        return dataSourceBuilder.getDynamicDataSource();
    }

    /**
     *
     * @return
     * @throws Exception
     */
    @Bean("sqlSessionFactoryDynamic")
    public SqlSessionFactory sqlSessionFactoryDynamic() throws Exception {
        SqlSessionFactoryBean fb = new SqlSessionFactoryBean();

        fb.setDataSource(this.dynamicDataSource());
        fb.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:spring/mybatis-settings.xml"));
        // resultType
        //fb.setTypeAliasesPackage("com.sgs.testdatabiz.dbstorages.mybatis.model,com.sgs.testdatabiz.dbstorages.mybatis.extmodel");
        fb.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:sqlmap/**/*.xml"));

        return fb.getObject();
    }

    /**
     *
     * @param dynamicDataSource
     * @return
     */
    @Bean("transactionManager")
    @Primary
    public DataSourceTransactionManager transactionManager(DynamicDataSource dynamicDataSource) {
        return new DataSourceTransactionManager(dynamicDataSource);
    }

    /**
     *
     * 编程式事务模板
     * 示例：transactionTemplate.execute((status) -> {方法体});
     */
    @Bean("transactionTemplate")
    public TransactionTemplate transactionTemplate(@Qualifier("transactionManager") DataSourceTransactionManager transactionManager) {
        TransactionTemplate transactionTemplate = new TransactionTemplate();
        transactionTemplate.setTransactionManager(transactionManager);
        // 设置事务传播属性
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        /*// 设置事务的隔离级别,设置为读已提交（默认是ISOLATION_DEFAULT:使用的是底层数据库的默认的隔离级别）
        transactionTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        // 设置是否只读，默认是false
        transactionTemplate.setReadOnly(true);
        // 默认使用的是数据库底层的默认的事务的超时时间
        transactionTemplate.setTimeout(30000);*/
        return transactionTemplate;
    }
}
