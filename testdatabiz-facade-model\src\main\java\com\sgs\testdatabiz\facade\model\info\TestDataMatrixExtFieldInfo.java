package com.sgs.testdatabiz.facade.model.info;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/28 19:29
 */
public class TestDataMatrixExtFieldInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer appFactorId;

    /**
     *
     */
    private String appFactorName;

    /**
     *
     */
    private String materialName;

    /**
     *
     */
    private String materialTexture;

    /**
     *
     */
    private String usedPosition;

    /**
     *
     */
    private String materialColor;

    private String referFromSampleNo;
    //SCI-1378
    private List<String> referFromReportNo;
    /**
     *
     */
    private List<TestDataMatrixExtFieldLangInfo> languages;

    public String getReferFromSampleNo() {
        return referFromSampleNo;
    }

    public void setReferFromSampleNo(String referFromSampleNo) {
        this.referFromSampleNo = referFromSampleNo;
    }

    public List<String> getReferFromReportNo() {
        return referFromReportNo;
    }

    public void setReferFromReportNo(List<String> referFromReportNo) {
        this.referFromReportNo = referFromReportNo;
    }

    public Integer getAppFactorId() {
        return appFactorId;
    }

    public void setAppFactorId(Integer appFactorId) {
        this.appFactorId = appFactorId;
    }

    public String getAppFactorName() {
        return appFactorName;
    }

    public void setAppFactorName(String appFactorName) {
        this.appFactorName = appFactorName;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public List<TestDataMatrixExtFieldLangInfo> getLanguages() {
        return languages;
    }

    public void setLanguages(List<TestDataMatrixExtFieldLangInfo> languages) {
        this.languages = languages;
    }
}
