package com.sgs.testdatabiz.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum XmlLogCodeEnum {
    None(0,false,""),

    //slim
    SLIM_BIND_SUCCESS(11,"%s和%s关系绑定成功"),
    SLIM_BIND_SUCCESS_TODO(12,"%s读取%s关系绑定成功，变更为todo文件"),
    SLIM_HAS_BIND_TODO(13,"%s已有关联关系不进行绑定处理。变更为todo文件"),
    SLIM_READ_SUCCESS(21,"%s读取%s数据成功"),
    SLIM_READ_SUCCESS_TODO(22,"%s读取%s数据成功"),
    SLIM_SUBCONTRACT_REGISTER(10011,"XML中的Subcontract为空"),
    SLIM_AND_SUBCONTRACT(20013,"%s"),
    SLIM_AND_SUBCONTRACT_NODB(20013,false,"%s"),
    SLIM_NOT_TOSLIM(10013,"SubcontractNo %s 的分包类型属于%s类型，无法进行关联"),
    SLIM_HAS_BIND(10014,"%s已有关联关系不进行绑定处理"),
    SLIM_SUBCONTRACT_NOTEXISTS_REGISTER(10021,"SubcontractNo %s在系统中不存在"),
    SLIM_SUBCONTRACT_NOTEXISTS_REPORT(20014,"SubcontractNo %s在系统中不存在"),
    SLIM_SUBCONTRACT_COMPLETED_REGISTER(10031,"SubcontractNo %s已经%s，不能再进行数据修改"),
    SLIM_SUBCONTRACT_COMPLETED_REPORT(20031,"SubcontractNo %s已经%s，不能再进行数据修改"),
    SLIM_NO_REPORT(10034,"orderNo:%s,查询report数据为空"),
    SLIM_REPORT_STATUS(10035,"report_%s当前状态已变更为(status：%s)，不在接收slim"),
    SLIM_TOSLIMTESTRESULT(20012,"%s"),
    SLIM_OBJ_NULL(10081,"转换文件到对象为null，不做处理"),
    SLIM_LABCODE_NOT_SAME(10082,"解析XML(%s)文件里的LabCode(%s)与FTP的LabCode(%s)不一致."),
    SLIM_JOBNO_NULL(10083,"没有获取到slimJobNo（PRO_NO属性值为null），不作处理"),
    SLIM_JOBNO_NULL_REPORT(20083,"没有获取到slimJobNo（PRO_NO属性值为null），不作处理"),
    SLIM_COUNT_NOT_SAME(10084,"获取NOTES.SubcontractNo对应的属性数量不唯一(%s)，无法处理"),
    SLIM_FILE_NAME_ERROR(10085,false,"文件名称不符合协定标准，无法处理"),
    SLIM_JOB_ERROR(10086,false,"文件(slimJobNo_%s)处理失败"),
    SLIM_NOT_FOUND_SUBCONTRACT(10087,false,"没有找到slimJobNo_%s 所属的有效的subContractNo,不做处理"),

    // starlims




    ;

    private final int code;
    private boolean dbLog;
    private final String desc;

    public int getCode() {
        return code;
    }

    public boolean isDbLog() {
        return dbLog;
    }

    public String getDesc() {
        return desc;
    }

    XmlLogCodeEnum(int code, String desc){
        this.code = code;
        this.dbLog = true;
        this.desc = desc;
    }

    XmlLogCodeEnum(int code, boolean dbLog, String desc){
        this(code, desc);
        this.dbLog = dbLog;
    }

    public static final Map<Integer, XmlLogCodeEnum> maps = new HashMap<Integer, XmlLogCodeEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (XmlLogCodeEnum logCode : XmlLogCodeEnum.values()) {
                put(logCode.getCode(), logCode);
            }
        }
    };

    public static boolean checkLogCode(Integer code, XmlLogCodeEnum... xmlLogCodeEnums){
        if (code == null || !maps.containsKey(code) || xmlLogCodeEnums == null || xmlLogCodeEnums.length <= 0) {
            return false;
        }
        for (XmlLogCodeEnum xmlLogCodeEnum : xmlLogCodeEnums){
            if (code == xmlLogCodeEnum.getCode()){
                return true;
            }
        }
        return false;
    }

}
