/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCustomerProductDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdPurchaseOrderDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTrfDO {
    private Integer refSystemId;
    private String trfNo;
    private Integer serviceLevel;
    private String trfTemplateOwner;
    private String trfTemplateId;
    private String trfTemplateName;
    private Date trfSubmissionDate;
    private Date serviceStartDate;
    private Date serviceEndDate;

    private Integer interfaceExclude;

    private String source;

    // add 20230529
    private List<RdOrderRelDO> orderList;

    private RdTrfOtherDO other;

    private List<RdCustomerProductDO> customerProductList;
    private List<RdPurchaseOrderDO> purchaseOrderList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
