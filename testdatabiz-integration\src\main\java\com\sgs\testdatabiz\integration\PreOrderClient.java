package com.sgs.testdatabiz.integration;

import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.common.BaseResponse;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.order.OrderSimplifyInfoReq;
import com.sgs.preorder.facade.model.rsp.order.OrderSimplifyInfoRsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
public class PreOrderClient {
    private static final Logger logger = LoggerFactory.getLogger(PreOrderClient.class);
    @Autowired
    private OrderFacade orderFacade;

    /**
     *
     * @param orderNo
     * @param productLineCode
     * @return
     */
    public OrderInfoDto getOrderInfoByOrderNo(String orderNo, String productLineCode){
        OrderIdReq reqObject = new OrderIdReq();
        reqObject.setOrderNo(orderNo);
        reqObject.setProductLineCode(productLineCode);
        try {
            BaseResponse<OrderInfoDto> response = orderFacade.getOrderInfoByOrderNo(reqObject);
            return response.getData();
        }catch (Exception e){
            logger.error("PreOrderClient.getOrderInfoByOrderNo 异常,errMsg:{}",e.getMessage(),e);
        }
        return null;
    }

    /**
     *
     * @param orderNo
     * @param productLineCode
     * @return
     */
    public OrderSimplifyInfoRsp getOrderSimplifyInfo(String orderNo, String productLineCode){
        OrderSimplifyInfoReq reqObject = new OrderSimplifyInfoReq();
        reqObject.setOrderNo(orderNo);
        reqObject.setProductLineCode(productLineCode);
        try {
            BaseResponse<OrderSimplifyInfoRsp> response =  orderFacade.getOrderSimplifyInfo(reqObject);
            return response.getData();
        }catch (Exception e){
            logger.error("PreOrderClient.getOrderInfoByOrderNo 异常,errMsg:{}",e.getMessage(),e);
        }
        return null;
    }

}
