package com.sgs.testdatabiz.domain.service.rd.filter.impl.common;

import com.sgs.testdatabiz.core.enums.FilterType;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.domain.service.rd.filter.AbstractReportDataFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.FilterContext;
import com.sgs.testdatabiz.domain.service.rd.filter.annotation.DefaultFilter;
import com.sgs.testdatabiz.domain.service.rd.filter.impl.common.visitor.RemoveFieldsVisitor;
import com.sgs.testdatabiz.domain.service.rd.filter.impl.common.visitor.ReportDataVisitor;
import com.sgs.testdatabiz.domain.service.rd.filter.impl.common.visitor.ReportDataVisitorAdapter;
import org.springframework.stereotype.Component;

/**
 * 移除字段过滤器
 * 使用访问者模式处理报告数据中的各个字段
 */
@DefaultFilter(order = 6)
@Component
public class RemoveFieldsFilter extends AbstractReportDataFilter {

    @Override
    protected String getFilterType() {
        return FilterType.REMOVE_FIELDS.name();
    }

    @Override
    protected ReportDataDO doFilterInternal(ReportDataDO reportData, FilterContext context) {
        if (reportData == null) {
            return null;
        }

        // 创建访问者和适配器
        ReportDataVisitor visitor = new RemoveFieldsVisitor();
        ReportDataVisitorAdapter adapter = new ReportDataVisitorAdapter(visitor, reportData);
        
        // 开始访问处理
        adapter.visit();
        
        return reportData;
    }
} 