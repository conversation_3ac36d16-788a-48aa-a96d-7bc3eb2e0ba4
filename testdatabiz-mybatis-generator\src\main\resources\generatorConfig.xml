<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="SqlServerTables" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 自动识别数据库关键字，默认false，如果设置为true，根据SqlReservedWords中定义的关键字列表；
        一般保留默认值，遇到数据库关键字（Java关键字），使用columnOverride覆盖-->
        <property name="autoDelimitKeywords" value="true"/>
        <!--beginningDelimiter、endingDelimiter默认为（"），但Mysql中不能这么写，所以要将这两个默认值改为“单反引号（'）”-->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="mapUnderscoreToCamelCase" value="true"/>

        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <plugin type="com.sgs.testdatabiz.mybatis.generator.RenameModelPlugin">
            <property name="prefixes2Remove" value="tb,sgs"/>
            <property name="suffix2Append" value="PO"/>
        </plugin>

        <!--自定义批量插入-->
        <plugin type="com.sgs.testdatabiz.mybatis.generator.plugin.BatchInsertPlugin"></plugin>
        <!--自定义批量更新-->
        <plugin type="com.sgs.testdatabiz.mybatis.generator.plugin.BatchUpdatePlugin"></plugin>

        <plugin type="com.sgs.testdatabiz.mybatis.generator.ForceCreateUpdateTimePlugin">
            <property name="insertTimeColumns" value="created_date"/>
            <property name="lastUpdateTimeColumns" value="modified_date"/>
            <property name="dbCurrentTimeExpr" value="now()"/>
        </plugin>

        <commentGenerator type="com.sgs.testdatabiz.mybatis.generator.DBCommentGenerator">
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
            connectionURL="*********************************************************************************************************************************************"
            userId="datadb_user_test"
            password="DaDB_sgs_1127Test">
            <property name="remarksReporting" value="true"/>
        </jdbcConnection>


        <javaTypeResolver type="com.sgs.testdatabiz.mybatis.generator.plugin.MyJavaTypeResolver">
            <property name="forceBigDecimals" value="false" />
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.sgs.testdatabiz.dbstorages.mybatis.model"
          targetProject="testdatabiz-dbstorages\src\main\java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="sqlmap.autogenerated" targetProject="testdatabiz-dbstorages\src\main\resources">
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata"
          targetProject="testdatabiz-dbstorages\src\main\java">
        </javaClientGenerator>

<!--        <table tableName="tb_test_data_object_rel" domainObjectName="TestDataObjectRel">-->
<!--            <property name="useActualColumnNames" value="true"/>-->
<!--        </table>-->

<!--        <table tableName="tb_test_data_matrix_info" domainObjectName="TestDataMatrixInfo">-->
<!--            <property name="useActualColumnNames" value="true"/>-->
<!--        </table>-->

<!--        <table tableName="tb_test_line_analyte_mapping" domainObjectName="TestLineAnalyteMappingInfo">-->
<!--            <property name="useActualColumnNames" value="true"/>-->
<!--        </table>-->


        <table tableName="tb_test_data_matrix_fix_temp" domainObjectName="testDatamatrixFixTemp">
            <property name="mapUnderscoreToCamelCase" value="true"/>
        </table>
<!--        <table tableName="tb_report_ext" domainObjectName="RdReportExt">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--        </table>-->
<!--        <table tableName="tb_report_trf_rel" domainObjectName="RdReportTrfRel">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--        </table>-->

<!--        <table tableName="tb_report_conclusion" domainObjectName="RdReportConclusion">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--        </table>-->

<!--        <table tableName="tb_report_lang" domainObjectName="RdReportLang">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--        </table>-->

<!--        <table tableName="tb_report_product_dff" domainObjectName="RdReportProductDff">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--        </table>-->

<!--        <table tableName="tb_attachment" domainObjectName="RdAttachment">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--        </table>-->


    </context>

</generatorConfiguration>
