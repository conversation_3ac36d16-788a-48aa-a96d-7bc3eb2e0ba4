package com.sgs.testdatabiz.web.config;

import com.google.common.collect.Sets;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Collections;
import java.util.List;

/**
 *
 */
@Slf4j
@EnableOpenApi
@Configuration // 声明该类为配置类
public class SwaggerConfig {

    @Value("${swagger.is.enable}")
    private Boolean swaggerEnable;

    /**
     *
     * @return
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("SGS TetData Biz Api Documentation") // 任意，请稍微规范点
                .description("TetData Biz Api Documentation") // 任意，请稍微规范点
                //.termsOfServiceUrl("http://localhost:8086/swagger/swagger-ui.html")
                .contact(new Contact("Michael", "", "<EMAIL>"))
                .version("1.1.0")
                .build();
    }

    /**
     * https://blog.csdn.net/weixin_44203158/article/details/109137799
     *
     * @return
     */
    @Bean
    public Docket createRestApi() {
        log.info(">>>>>>>>>>>>>>>>>> swagger is {} >>>>>>>>>>>>>>>>>>",swaggerEnable?"enable":"disabled");
        return new Docket(DocumentationType.OAS_30).pathMapping("/")
                // 定义是否开启swagger，false为关闭，可以通过变量控制
                .enable(swaggerEnable)
                // 将api的元信息设置为包含在json ResourceListing响应中。
                .apiInfo(apiInfo())
                // 接口调试地址
                /*.host(swaggerProperties.getTryHost())*/
                /*.host("http://localhost:8996/yichat/api")*/
                // 选择哪些接口作为swagger的doc发布
                .select()
                .apis(RequestHandlerSelectors.any())
                .apis(RequestHandlerSelectors.basePackage("com.sgs.testdatabiz.web.controllers"))
                .paths(PathSelectors.any())
                .build()
                // 支持的通讯协议集合
                .protocols(Sets.newHashSet("https", "http"))
                // 授权信息设置，必要的header token等认证信息
                .securitySchemes(securitySchemes())
                // 授权信息全局应用
                .securityContexts(securityContexts());
    }

    /**
     * 设置授权信息
     * @return
     */
    private List<SecurityScheme> securitySchemes() {
        ApiKey apiKey = new ApiKey("accessToken", "token", In.HEADER.toValue());
        return Collections.singletonList(apiKey);
    }

    /**
     * 授权信息全局应用
     * @return
     */
    private List<SecurityContext> securityContexts() {
        return Collections.singletonList(
                SecurityContext.builder()
                        .securityReferences(Collections.singletonList(new SecurityReference("accessToken", new AuthorizationScope[]{new AuthorizationScope("global", "")})))
                        .build()
        );
    }
}
