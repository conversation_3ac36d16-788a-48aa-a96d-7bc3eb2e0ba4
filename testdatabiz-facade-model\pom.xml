<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sgs-testdatabiz</artifactId>
        <groupId>com.sgs.testdatabiz</groupId>
        <version>1.3.44</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${testdatabiz-facade.version}</version>
    <artifactId>testdatabiz-facade-model</artifactId>

    <dependencies>
        <!-- alibaba.fastjson.JSON 序列化 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!--com.fasterxml.jackson.annotation.JsonProperty 相关包-->
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.spec.javax.ws.rs</groupId>
            <artifactId>jboss-jaxrs-api_2.0_spec</artifactId>
            <version>1.0.0.Final</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>30.1.1-jre</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.otsnotes</groupId>
            <artifactId>otsnotes-facade-model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sgs.testdatabiz</groupId>
                    <artifactId>testdatabiz-facade</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
