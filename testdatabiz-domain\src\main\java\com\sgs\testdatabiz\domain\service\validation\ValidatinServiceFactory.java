package com.sgs.testdatabiz.domain.service.validation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 校验服务工厂类
 * 用于管理和获取各种校验服务
 * 主要功能：
 * 1. 自动注册所有实现了ValidationService接口的服务
 * 2. 根据校验类型获取对应的校验服务列表
 * 3. 获取所有可用的校验服务
 * 4. 支持一个校验类型对应多个校验服务的情况
 */
@Component
@Slf4j
public class ValidatinServiceFactory {

    /**
     * 所有校验服务的Map集合
     * key: 校验类型
     * value: 对应类型的校验服务列表，按照order排序
     */
    private final Map<String, List<ValidationService>> validationServiceMap;

    @Autowired
    public ValidatinServiceFactory(List<ValidationService> validationServices) {
        validationServiceMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(validationServices)) {
            // 按照校验类型分组
            for (ValidationService service : validationServices) {
                String type = service.getType();
                validationServiceMap.computeIfAbsent(type, k -> new ArrayList<>()).add(service);
                log.info("[ValidatinServiceFactory] 注册校验服务: type={}, class={}, order={}", 
                    type, service.getClass().getSimpleName(), service.getOrder());
            }
            
            // 对每个类型的服务列表按order排序
            validationServiceMap.forEach((type, services) -> {
                services.sort(Comparator.comparingInt(ValidationService::getOrder));
                log.info("[ValidatinServiceFactory] 类型{}的校验服务排序完成，共{}个服务", type, services.size());
            });
        }
        log.info("[ValidatinServiceFactory] 总共注册了{}个校验类型", validationServiceMap.size());
    }

    /**
     * 获取指定类型的校验服务列表
     * 返回的列表已按照order排序
     *
     * @param validationTypes 校验类型列表
     * @return 校验服务列表
     */
    public List<ValidationService> getValidationServiceList(List<String> validationTypes) {
        if (CollectionUtils.isEmpty(validationTypes)) {
            return Collections.emptyList();
        }

        return validationTypes.stream()
                .filter(validationServiceMap::containsKey)
                .flatMap(type -> validationServiceMap.get(type).stream())
                .collect(Collectors.toList());
    }

    /**
     * 获取指定类型的校验服务列表
     *
     * @param type 校验类型
     * @return 校验服务列表，如果类型不存在返回空列表
     */
    public List<ValidationService> getValidationServices(String type) {
        return validationServiceMap.getOrDefault(type, Collections.emptyList());
    }

    /**
     * 获取所有已注册的校验服务
     * 
     * @return 所有可用的校验服务列表
     */
    public List<ValidationService> getAllValidationServices() {
        return validationServiceMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }
}
