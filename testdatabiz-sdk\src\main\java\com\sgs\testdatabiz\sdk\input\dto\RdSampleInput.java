/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdSampleInput implements Serializable {
    private String testSampleInstanceId;
    private String templateId;
    // add 20230720
    private String sampleNo;
    // add 20230720
    private String externalSampleNo;
    private String productItemNo;
    private List<RdProductSampleAttrInput> sampleAttrList;
    private RdConclusionInput conclusion;
    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
