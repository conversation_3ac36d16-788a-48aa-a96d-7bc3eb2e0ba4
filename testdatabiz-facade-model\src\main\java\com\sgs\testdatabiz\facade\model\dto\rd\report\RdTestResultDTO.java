/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestResultDTO extends RdOrderHeaderDTO implements Serializable {


    private String reportNo;
    // add 20230529
    private Integer systemId;
    private String testMatrixId;
    private String subReportNo;
    private RdTestResultResultDTO testResult;
    private Integer testResultSeq;
    private RdReportLimitDTO reportLimit;
    // add 230922
    private RdMethodLimitDTO methodLimit;
    //SCI-1378
    private RdShareDataReferDTO shareDataRefer;
    private List<RdTestResultLanguageDTO> languageList;
    private String testResultInstanceId;
    private Date lastModifiedTimestamp;
    //JSON串
    private String metaData;
    private String casNo;
    private Integer activeIndicator;
}
