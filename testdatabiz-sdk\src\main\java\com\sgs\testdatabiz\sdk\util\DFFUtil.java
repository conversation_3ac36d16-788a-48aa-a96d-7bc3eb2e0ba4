package com.sgs.testdatabiz.sdk.util;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class DFFUtil {

    private static final String UAT = "UAT";
    private static final String PROD = "PROD";
    private static final String TEST = "TEST";
    private static Map<String,String> dffUrlMap = new HashMap<>();
    static{
        dffUrlMap.put(UAT, "https://cnapp-uat.sgs.net/");
        dffUrlMap.put(PROD, "https://cnapp.sgs.net/");
    }
    public static List<String> formGroupIdList(List<JSONArray> groupRespList) {
        return groupRespList.stream()
                .filter(groupResp -> CollectionUtils.isNotEmpty(groupResp)
                        && groupResp.getJSONObject(0).containsKey("formGroupID")
                        && StringUtils.isNotBlank(groupResp.getJSONObject(0).getString("formGroupID")))
                .map(groupResp -> groupResp.getJSONObject(0).getString("formGroupID"))
                .collect(Collectors.toList());
    }

    public static String queryDffFormGeneral(String templateId,String env) {
        JSONArray result = getDffFromInfo(templateId, env);
        if(Func.isNotEmpty(result)) {
            List<JSONArray> list = ImmutableList.of(result);
            List<String> fomrGroupIds = formGroupIdList(list);
            if(Func.isNotEmpty(fomrGroupIds)&& !fomrGroupIds.isEmpty()){
                return fomrGroupIds.get(0);
            }
        }
        return null;
    }

    private static  JSONArray getDffFromInfo(String templateId, String env) {
        long startMillis = System.currentTimeMillis();
        String baseUrl = dffUrlMap.get(env);
        String url = String.format("%s/DFFV2Api/dff/queryDffFormGeneral", baseUrl);
        try {
            String response = HttpClientUtil.post(url, ImmutableMap.of("id", templateId));
            return JSONArray.parseArray(response);
        } catch (Exception e) {
            log.info("DffClient.queryDffFormGeneral Error : {}", e.getMessage());
        } finally {
            log.info("DffClient.queryDffFormGeneral 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return null;
    }
}
