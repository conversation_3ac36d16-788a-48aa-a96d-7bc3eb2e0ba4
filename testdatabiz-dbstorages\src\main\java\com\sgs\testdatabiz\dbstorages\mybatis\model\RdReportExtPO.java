package com.sgs.testdatabiz.dbstorages.mybatis.model;

import lombok.Data;

import java.util.Date;

@Data
public class RdReportExtPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 唯一标识
     */
    private Long id;

    /**
     * rd_report_id BIGINT(20) 必填<br>
     * RD Report 唯一标识
     */
    private Long rdReportId;

    /**
     * version VARCHAR(20) 必填<br>
     * 对应数据的版本号
     */
    private String version;

    /**
     * created_date TIMESTAMP(19)<br>
     * 
     */
    private Date createdDate;

    /**
     * update_version INTEGER(10) 默认值[0]<br>
     * 
     */
    private Integer updateVersion;

    /**
     * request_json LONGVARCHAR(16777215)<br>
     * 数据体
     */
    private String requestJson;
    /**
     * traceability_id VARCHAR(50)<br>
     */
    private String traceabilityId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", traceabilityId=").append(traceabilityId);
        sb.append(", rdReportId=").append(rdReportId);
        sb.append(", version=").append(version);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", updateVersion=").append(updateVersion);
        sb.append(", requestJson=").append(requestJson);
        sb.append("]");
        return sb.toString();
    }

}