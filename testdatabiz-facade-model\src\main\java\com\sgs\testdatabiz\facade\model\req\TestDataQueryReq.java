package com.sgs.testdatabiz.facade.model.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @ClassName TestDataQueryReq
 * @Description 查询testData
 * <AUTHOR>
 * @Date 2022/5/31
 */
@ApiModel(value = "TestDataQueryReq", description = "TestDataQueryReq")
public class TestDataQueryReq extends BaseRequest {
    /**
     * 查询指定SystemId的数据
     */
    @ApiModelProperty(required = true, notes = "sourceType； 1:slim   2:fast")
    private List<Integer> sourceTypes;

    /**
     * HL 调用接口时传递参数
     */
    @ApiModelProperty(required = true, notes = "systemId； 1:slim   2:fast")
    private List<Integer> systemIds;

    /**
     *
     */
    @ApiModelProperty(required = true, notes = "订单号")
    private String orderNo;
    /**
     *
     */
    @ApiModelProperty("ObjectNo 分包单号/JobNo ")
    private List<String> objectNos;
    /**
     *
     */
    @ApiModelProperty(required = true, notes = "订单Lab")
    private String labCode;
    /**
     *
     */
    @ApiModelProperty("订单中的TestLineId")
    private List<TestDataQueryTestLineItemReq> testLine;
    /**
     *
     */
    @ApiModelProperty("查询指定的sampleNo")
    private List<String> sampleNos;
    /**
     *
     */
    @ApiModelProperty("0：General、1：Conclusion")
    private Integer analyteType;
    /**
     * 这个属性的意思是，导出数据时，需要全量数据，
     * DB中的sampleNo会多于业务DB中的sample，
     * 如果用业务DB的sample查询，就限制了范围，
     * 会导致导出需要的数据少一部分
     * 比如 业务中的testline 对应的smaple 只有1,2,1+2
     * 但是testData中有1,2，1+2,3+4,3,4 这种场景
     * 根据实际需求，赋值
     *
     *  */
    @ApiModelProperty("导出数据时，需要全量数据")
    private Boolean needSampleQuery;

    @ApiModelProperty("返回数据是否需要翻译处理scheme code （CTX_GC_ISO14362_A1_Z ->直接还原;CTX_GC_ISO14362_B1_Z -> 着色剂萃取）")
    private Boolean transSchemeCode = false;

    private List<Integer> testLineIds;

    public List<Integer> getSystemIds() {
        return systemIds;
    }

    public void setSystemIds(List<Integer> systemIds) {
        this.systemIds = systemIds;
    }

    public Boolean getNeedSampleQuery() {
        return needSampleQuery;
    }

    public void setNeedSampleQuery(Boolean needSampleQuery) {
        this.needSampleQuery = needSampleQuery;
    }

    public Integer getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<String> getObjectNos() {
        return objectNos;
    }

    public void setObjectNos(List<String> objectNos) {
        this.objectNos = objectNos;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public List<TestDataQueryTestLineItemReq> getTestLine() {
        return testLine;
    }

    public void setTestLine(List<TestDataQueryTestLineItemReq> testLine) {
        this.testLine = testLine;
    }

    public List<String> getSampleNos() {
        return sampleNos;
    }

    public void setSampleNos(List<String> sampleNos) {
        this.sampleNos = sampleNos;
    }

    public List<Integer> getSourceTypes() {
        return sourceTypes;
    }

    public void setSourceTypes(List<Integer> sourceTypes) {
        this.sourceTypes = sourceTypes;
    }

    public Boolean getTransSchemeCode() {
        return transSchemeCode;
    }

    public void setTransSchemeCode(Boolean transSchemeCode) {
        this.transSchemeCode = transSchemeCode;
    }

    public List<Integer> getTestLineIds() {
        return testLineIds;
    }

    public void setTestLineIds(List<Integer> testLineIds) {
        this.testLineIds = testLineIds;
    }
}
