package com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix;

import com.alibaba.fastjson.JSONObject;
import com.sgs.testdatabiz.facade.model.dto.starlims.MatrixFilterDTO;
import com.sgs.testdatabiz.facade.model.dto.starlims.SubContractMatrixDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Starlims 回传数据时，按照匹配逻辑设置数据
 * 1. 使用 MatrixId
 * 2. 使用 TestLine + Sample 匹配
 * 3. 使用 TestLine 匹配
 * 4. 设置默认值
 */
@Component
public class StarlimsMatrixFilterHandler implements ApplicationContextAware  {
    private final Logger logger = LoggerFactory.getLogger(StarlimsMatrixFilterHandler.class);

    private LinkedHashMap<String, Object> matrixInfoMap = new LinkedHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 获取所有Bean
        String[] beanNames = applicationContext.getBeanNamesForType(Object.class);
        for (String beanName : beanNames) {
            // 检查Bean是否有@MyCustomAnnotation注解
            if (applicationContext.findAnnotationOnBean(beanName, MatrixInfo.class) != null) {
                // 将Bean添加到Map中
                matrixInfoMap.put(beanName, applicationContext.getBean(beanName));
            }
        }
        // 根据注解的order属性对Bean进行排序
        matrixInfoMap = matrixInfoMap.entrySet().stream()
                .sorted(Comparator.comparingInt(e -> {
                    MatrixInfo annotation = applicationContext.findAnnotationOnBean(e.getKey(), MatrixInfo.class);
                    return annotation.sort();
                }))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new // 使用LinkedHashMap来保持排序的顺序
                ));
        logger.info("初始化StarlimsMatrixFilterHandler, 排序结果：{}", JSONObject.toJSONString(matrixInfoMap));
    }

    public SubContractMatrixDTO findTestMatrixId(MatrixFilterDTO matrixFilterDTO) {
        for (Object bean : matrixInfoMap.values()) {
            if (bean instanceof MatrixInfoFilter) {
                SubContractMatrixDTO subContractMatrixDTO = ((MatrixInfoFilter) bean).filterStarlimsMatrix(matrixFilterDTO);
                if (subContractMatrixDTO != null) {
                    return subContractMatrixDTO;
                }
            }
        }
        return null;
    }


}
