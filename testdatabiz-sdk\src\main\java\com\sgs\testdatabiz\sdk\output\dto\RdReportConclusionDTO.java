package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdReportConclusionDTO extends BaseModel{

    private Long rdReportId;
    private Long rdOrderId;
    private Long labId;
    private String objectId;
    private String objectName;
    private String orderNo;
    private String reportNo;
    private String testLineInstanceId;
    private String sampleInstanceId;
    private String customerConclusionId;
    private String customerConclusion;
    private String conclusionCode;
    private String conclusionId;
    private Integer conclusionLevel;
    private String conclusionRemark;
    private String ppSampleRelId;
    private String ppArtifactRelId;
    private Integer sectionId;
    private String customerConclusionIdLabel;
    private String conclusionCodeLabel;
    private String conclusionIdLabel;
    private String conclusionLevelLabel;
    private String sectionIdLabel;
    private String comments;
    private Integer activeIndicator;
    private Date lastModifiedTimestamp;
    private String conclusionInstanceId;
   private Integer testRequestFlag; //SCI-1378
    private String testMatrixId;
}
