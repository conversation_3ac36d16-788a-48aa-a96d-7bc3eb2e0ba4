package com.sgs.testdatabiz.facade.model.rsp;

import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.testdatabiz.facade.model.enums.ErrorEnum;

public class ErrorRsp extends PrintFriendliness {
    /**
     * 500：订单不存在
     */
    private int code;

    /**
     *
     */
    private String msg;

    public ErrorRsp(ErrorEnum bizError){
        this.code = bizError.getStatus();
        this.msg = bizError.getText();
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
