/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.domain.service.rd.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestLineLanguageDO {
    private Integer languageId;
    private String evaluationAlias;
    private String evaluationName;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
    //SCI-1378 增加labSectionName
    private String labSectionName;
}