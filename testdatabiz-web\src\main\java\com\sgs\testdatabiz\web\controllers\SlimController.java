package com.sgs.testdatabiz.web.controllers;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.ISlimTestDataFacade;
import com.sgs.testdatabiz.facade.model.req.CleanConclusionReq;
import com.sgs.testdatabiz.facade.model.req.slim.SlimSaveTestDataReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/slim")
@Api(value = "/slim", tags = "slim")
public class SlimController {

    private static final Logger logger = LoggerFactory.getLogger(SlimController.class);
    @Autowired
    private ISlimTestDataFacade slimTestDataFacade;

    @PostMapping("/saveSlimTestData")
    @ApiOperation(value = "slim数据保存")
    public BaseResponse saveSlimTestData(@RequestBody SlimSaveTestDataReq reqObject) {
        return slimTestDataFacade.saveSlimTestData(reqObject);
    }

    /**
     * 清洗Conclusion数据
     * @param reqObject
     * @return
     */
    @PostMapping("/cleanConclusion")
    @ApiOperation(value = "清洗Conclusion数据")
    public BaseResponse cleanConclusion(@RequestBody CleanConclusionReq reqObject) {
        return slimTestDataFacade.cleanConclusion(reqObject);
    }

    /**
     * 清洗Conclusion数据
     * @param reqObject
     * @return
     */
    @GetMapping("/getMailTo")
    @ApiOperation(value = "根据bu+location获取email配置")
    public BaseResponse getMailTo(String productLineCode, String locationCode) {
        return slimTestDataFacade.getMailTo(productLineCode, locationCode);
    }


}
