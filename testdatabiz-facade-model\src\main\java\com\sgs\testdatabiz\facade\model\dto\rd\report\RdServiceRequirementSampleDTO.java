/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceRequirementSampleDTO implements Serializable {
    private Integer liquid;
    private RdDeliveryDTO returnResidueSample;
    private RdDeliveryDTO returnTestSample;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
