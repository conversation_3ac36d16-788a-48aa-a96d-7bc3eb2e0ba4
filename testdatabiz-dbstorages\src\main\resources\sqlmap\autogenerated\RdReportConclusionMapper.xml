<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportConclusionMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="conclusion_instance_id" property="conclusionInstanceId" jdbcType="VARCHAR" />
    <result column="object_id" property="objectId" jdbcType="VARCHAR" />
    <result column="pp_artifact_rel_id" property="ppArtifactRelId" jdbcType="BIGINT" />
    <result column="pp_sample_rel_id" property="ppSampleRelId" jdbcType="VARCHAR" />
    <result column="section_id" property="sectionId" jdbcType="INTEGER" />
    <result column="test_line_instance_id" property="testLineInstanceId" jdbcType="VARCHAR" />
    <result column="sample_instance_id" property="sampleInstanceId" jdbcType="VARCHAR" />
    <result column="conclusion_level_id" property="conclusionLevelId" jdbcType="INTEGER" />
    <result column="conclusion_code" property="conclusionCode" jdbcType="VARCHAR" />
    <result column="customer_conclusion" property="customerConclusion" jdbcType="VARCHAR" />
    <result column="conclusion_remark" property="conclusionRemark" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="last_modified_timestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, lab_id, order_no, report_no, conclusion_instance_id, object_id, pp_artifact_rel_id, 
    pp_sample_rel_id, section_id, test_line_instance_id, sample_instance_id, conclusion_level_id, 
    conclusion_code, customer_conclusion, conclusion_remark, active_indicator, created_by, 
    created_date, modified_by, modified_date, last_modified_timestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_conclusion
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_conclusion
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_conclusion
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionExample" >
    delete from tb_report_conclusion
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionPO" >
    insert into tb_report_conclusion (id, lab_id, order_no, 
      report_no, conclusion_instance_id, object_id, 
      pp_artifact_rel_id, pp_sample_rel_id, section_id, 
      test_line_instance_id, sample_instance_id, conclusion_level_id, 
      conclusion_code, customer_conclusion, conclusion_remark, 
      active_indicator, created_by, created_date, 
      modified_by, modified_date, last_modified_timestamp
      )
    values (#{id,jdbcType=BIGINT}, #{labId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{conclusionInstanceId,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, 
      #{ppArtifactRelId,jdbcType=BIGINT}, #{ppSampleRelId,jdbcType=VARCHAR}, #{sectionId,jdbcType=INTEGER}, 
      #{testLineInstanceId,jdbcType=VARCHAR}, #{sampleInstanceId,jdbcType=VARCHAR}, #{conclusionLevelId,jdbcType=INTEGER}, 
      #{conclusionCode,jdbcType=VARCHAR}, #{customerConclusion,jdbcType=VARCHAR}, #{conclusionRemark,jdbcType=VARCHAR}, 
      #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, now(), 
      #{modifiedBy,jdbcType=VARCHAR}, now(), #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionPO" >
    insert into tb_report_conclusion
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="conclusionInstanceId != null" >
        conclusion_instance_id,
      </if>
      <if test="objectId != null" >
        object_id,
      </if>
      <if test="ppArtifactRelId != null" >
        pp_artifact_rel_id,
      </if>
      <if test="ppSampleRelId != null" >
        pp_sample_rel_id,
      </if>
      <if test="sectionId != null" >
        section_id,
      </if>
      <if test="testLineInstanceId != null" >
        test_line_instance_id,
      </if>
      <if test="sampleInstanceId != null" >
        sample_instance_id,
      </if>
      <if test="conclusionLevelId != null" >
        conclusion_level_id,
      </if>
      <if test="conclusionCode != null" >
        conclusion_code,
      </if>
      <if test="customerConclusion != null" >
        customer_conclusion,
      </if>
      <if test="conclusionRemark != null" >
        conclusion_remark,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="true" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="true" >
        modified_date,
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="conclusionInstanceId != null" >
        #{conclusionInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="ppArtifactRelId != null" >
        #{ppArtifactRelId,jdbcType=BIGINT},
      </if>
      <if test="ppSampleRelId != null" >
        #{ppSampleRelId,jdbcType=VARCHAR},
      </if>
      <if test="sectionId != null" >
        #{sectionId,jdbcType=INTEGER},
      </if>
      <if test="testLineInstanceId != null" >
        #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="sampleInstanceId != null" >
        #{sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="conclusionLevelId != null" >
        #{conclusionLevelId,jdbcType=INTEGER},
      </if>
      <if test="conclusionCode != null" >
        #{conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusion != null" >
        #{customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="conclusionRemark != null" >
        #{conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_conclusion
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_conclusion
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionInstanceId != null" >
        conclusion_instance_id = #{record.conclusionInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.objectId != null" >
        object_id = #{record.objectId,jdbcType=VARCHAR},
      </if>
      <if test="record.ppArtifactRelId != null" >
        pp_artifact_rel_id = #{record.ppArtifactRelId,jdbcType=BIGINT},
      </if>
      <if test="record.ppSampleRelId != null" >
        pp_sample_rel_id = #{record.ppSampleRelId,jdbcType=VARCHAR},
      </if>
      <if test="record.sectionId != null" >
        section_id = #{record.sectionId,jdbcType=INTEGER},
      </if>
      <if test="record.testLineInstanceId != null" >
        test_line_instance_id = #{record.testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleInstanceId != null" >
        sample_instance_id = #{record.sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionLevelId != null" >
        conclusion_level_id = #{record.conclusionLevelId,jdbcType=INTEGER},
      </if>
      <if test="record.conclusionCode != null" >
        conclusion_code = #{record.conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerConclusion != null" >
        customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionRemark != null" >
        conclusion_remark = #{record.conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_conclusion
    set id = #{record.id,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      conclusion_instance_id = #{record.conclusionInstanceId,jdbcType=VARCHAR},
      object_id = #{record.objectId,jdbcType=VARCHAR},
      pp_artifact_rel_id = #{record.ppArtifactRelId,jdbcType=BIGINT},
      pp_sample_rel_id = #{record.ppSampleRelId,jdbcType=VARCHAR},
      section_id = #{record.sectionId,jdbcType=INTEGER},
      test_line_instance_id = #{record.testLineInstanceId,jdbcType=VARCHAR},
      sample_instance_id = #{record.sampleInstanceId,jdbcType=VARCHAR},
      conclusion_level_id = #{record.conclusionLevelId,jdbcType=INTEGER},
      conclusion_code = #{record.conclusionCode,jdbcType=VARCHAR},
      customer_conclusion = #{record.customerConclusion,jdbcType=VARCHAR},
      conclusion_remark = #{record.conclusionRemark,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
    
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionPO" >
    update tb_report_conclusion
    <set >
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="conclusionInstanceId != null" >
        conclusion_instance_id = #{conclusionInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        object_id = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="ppArtifactRelId != null" >
        pp_artifact_rel_id = #{ppArtifactRelId,jdbcType=BIGINT},
      </if>
      <if test="ppSampleRelId != null" >
        pp_sample_rel_id = #{ppSampleRelId,jdbcType=VARCHAR},
      </if>
      <if test="sectionId != null" >
        section_id = #{sectionId,jdbcType=INTEGER},
      </if>
      <if test="testLineInstanceId != null" >
        test_line_instance_id = #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="sampleInstanceId != null" >
        sample_instance_id = #{sampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="conclusionLevelId != null" >
        conclusion_level_id = #{conclusionLevelId,jdbcType=INTEGER},
      </if>
      <if test="conclusionCode != null" >
        conclusion_code = #{conclusionCode,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusion != null" >
        customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      </if>
      <if test="conclusionRemark != null" >
        conclusion_remark = #{conclusionRemark,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="false" >
        created_date = now(),
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="true" >
        modified_date = now(),
      </if>
      <if test="lastModifiedTimestamp != null" >
        last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportConclusionPO" >
    update tb_report_conclusion
    set lab_id = #{labId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      conclusion_instance_id = #{conclusionInstanceId,jdbcType=VARCHAR},
      object_id = #{objectId,jdbcType=VARCHAR},
      pp_artifact_rel_id = #{ppArtifactRelId,jdbcType=BIGINT},
      pp_sample_rel_id = #{ppSampleRelId,jdbcType=VARCHAR},
      section_id = #{sectionId,jdbcType=INTEGER},
      test_line_instance_id = #{testLineInstanceId,jdbcType=VARCHAR},
      sample_instance_id = #{sampleInstanceId,jdbcType=VARCHAR},
      conclusion_level_id = #{conclusionLevelId,jdbcType=INTEGER},
      conclusion_code = #{conclusionCode,jdbcType=VARCHAR},
      customer_conclusion = #{customerConclusion,jdbcType=VARCHAR},
      conclusion_remark = #{conclusionRemark,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
    
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = now(),
      last_modified_timestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report_conclusion
      (`id`,`lab_id`,`order_no`,
      `report_no`,`conclusion_instance_id`,`object_id`,
      `pp_artifact_rel_id`,`pp_sample_rel_id`,`section_id`,
      `test_line_instance_id`,`sample_instance_id`,`conclusion_level_id`,
      `conclusion_code`,`customer_conclusion`,`conclusion_remark`,
      `active_indicator`,`created_by`,`created_date`,
      `modified_by`,`modified_date`,`last_modified_timestamp`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=BIGINT},#{ item.orderNo, jdbcType=VARCHAR},
      #{ item.reportNo, jdbcType=VARCHAR},#{ item.conclusionInstanceId, jdbcType=VARCHAR},#{ item.objectId, jdbcType=VARCHAR},
      #{ item.ppArtifactRelId, jdbcType=BIGINT},#{ item.ppSampleRelId, jdbcType=VARCHAR},#{ item.sectionId, jdbcType=INTEGER},
      #{ item.testLineInstanceId, jdbcType=VARCHAR},#{ item.sampleInstanceId, jdbcType=VARCHAR},#{ item.conclusionLevelId, jdbcType=INTEGER},
      #{ item.conclusionCode, jdbcType=VARCHAR},#{ item.customerConclusion, jdbcType=VARCHAR},#{ item.conclusionRemark, jdbcType=VARCHAR},
      #{ item.activeIndicator, jdbcType=TINYINT},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_report_conclusion 
      <set>
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = BIGINT},
        </if> 
        <if test="item.orderNo != null"> 
          `order_no` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportNo != null"> 
          `report_no` = #{item.reportNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionInstanceId != null"> 
          `conclusion_instance_id` = #{item.conclusionInstanceId, jdbcType = VARCHAR},
        </if> 
        <if test="item.objectId != null"> 
          `object_id` = #{item.objectId, jdbcType = VARCHAR},
        </if> 
        <if test="item.ppArtifactRelId != null"> 
          `pp_artifact_rel_id` = #{item.ppArtifactRelId, jdbcType = BIGINT},
        </if> 
        <if test="item.ppSampleRelId != null"> 
          `pp_sample_rel_id` = #{item.ppSampleRelId, jdbcType = VARCHAR},
        </if> 
        <if test="item.sectionId != null"> 
          `section_id` = #{item.sectionId, jdbcType = INTEGER},
        </if> 
        <if test="item.testLineInstanceId != null"> 
          `test_line_instance_id` = #{item.testLineInstanceId, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleInstanceId != null"> 
          `sample_instance_id` = #{item.sampleInstanceId, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionLevelId != null"> 
          `conclusion_level_id` = #{item.conclusionLevelId, jdbcType = INTEGER},
        </if> 
        <if test="item.conclusionCode != null"> 
          `conclusion_code` = #{item.conclusionCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerConclusion != null"> 
          `customer_conclusion` = #{item.customerConclusion, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionRemark != null"> 
          `conclusion_remark` = #{item.conclusionRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `last_modified_timestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>