package com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.filtermatrix;

import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.BaseMatrixInfoFilter;
import com.sgs.testdatabiz.domain.service.testdata.impl.handler.matrix.MatrixInfo;
import com.sgs.testdatabiz.facade.model.dto.starlims.MatrixFilterDTO;
import com.sgs.testdatabiz.facade.model.dto.starlims.SubContractMatrixDTO;
import com.sgs.testdatabiz.facade.model.enums.MatrixSource;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveReportData;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *  匹配 TestLine
 */
@Component
@MatrixInfo(value = "StarLimsTestLine", sort = 3)
public class StarlimsTestLineFilter extends BaseMatrixInfoFilter {


    @Override
    public SubContractMatrixDTO filterStarlimsMatrix(MatrixFilterDTO matrixFilterDTO) {
        ReceiveReportData reportData = matrixFilterDTO.getReportData();
        List<SubContractTestMatrixInfo> subContractTestMatrixList = matrixFilterDTO.getSubContractTestMatrixList();
        ChemPpArtifactTestLineInfoRsp chemTestLine = matrixFilterDTO.getChemTestLine();

        SubContractMatrixDTO testMatrixInfo = new SubContractMatrixDTO();
        // 匹配 TestLine
        SubContractTestMatrixInfo subContractTestMatrixInfo = subContractTestMatrixList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(findPpTestLineCitationKey(item), findPpTestLineCitationKey(chemTestLine)))
                .findFirst().orElse(null);
        if (subContractTestMatrixInfo != null) {
            String testLineInstanceId = subContractTestMatrixInfo.getTestLineInstanceId();

            String matrixId = StringUtils.defaultIfBlank(reportData.getExternalMatrixId(), reportData.getId());
            String testSampleId = StringUtils.defaultIfBlank(reportData.getExternalId(), reportData.getSampleNo());
            Integer citationType = NumberUtil.toInt(0);
            String matrixSource = MatrixSource.StarLims.getSource();
            setTestMatrixInfo(testMatrixInfo, matrixId, testLineInstanceId, testSampleId, citationType, matrixSource);
            return testMatrixInfo;
        }

        return null;
    }

}
