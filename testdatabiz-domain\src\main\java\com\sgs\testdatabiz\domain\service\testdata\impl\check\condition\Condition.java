package com.sgs.testdatabiz.domain.service.testdata.impl.check.condition;

import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

import java.util.LinkedList;
import java.util.function.Predicate;

/**
 * check Data的匹配条件
 * @author: shawn.yang
 * @create: 2023-03-27 18:12
 */
public final class Condition {
    private final LinkedList<Predicate<ReportTestDataInfo>> cndList;

    public Condition(LinkedList<Predicate<ReportTestDataInfo>> cndList) {
        this.cndList = cndList;
    }

    public static ConditionBuilder builder(){
        return new ConditionBuilder();
    }



    public boolean matched(ReportTestDataInfo reportTestDataInfo){
        return cndList.stream().allMatch(p -> p.test(reportTestDataInfo));
    }

    public static class ConditionBuilder {
        private final LinkedList<Predicate<ReportTestDataInfo>> list = new LinkedList<>();


        ConditionBuilder() {
        }

        public Condition.ConditionBuilder and(Predicate<ReportTestDataInfo> cnd){
            list.add(cnd);
            return this;
        }


        public Condition build() {
            return new Condition(list);
        }

    }
    

}
