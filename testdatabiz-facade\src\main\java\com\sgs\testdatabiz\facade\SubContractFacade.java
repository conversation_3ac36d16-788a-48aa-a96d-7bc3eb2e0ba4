package com.sgs.testdatabiz.facade;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubTestDataReq;
import com.sgs.testdatabiz.facade.model.req.backsubcontract.SubCompleteTestDataReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.InputStreamReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubcontractNoReq;
import com.sgs.testdatabiz.facade.model.rsp.subcontract.ReportDataRsp;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public interface SubContractFacade {

//    BaseResponse<XSSFWorkbook> downLoadTemplate(SubcontractNoReq reqObject);
//
//    BaseResponse uploadSubTemplate(InputStreamReq reqObject);

    BaseResponse<ReportDataRsp> querySubTestData(SubcontractNoReq reqObject);

    BaseResponse saveSubCompleteTestData(SubCompleteTestDataReq reqObject);

    BaseResponse cancelSubTestData(SubcontractNoReq reqObject);

    BaseResponse saveEnterSubContractTestData(SubTestDataReq reqObject);

}
