/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceItemDTO implements Serializable {
    private String serviceItemName;
    private String costCenter;
    private Integer ppNo;
    private String ppName;
    private Integer testLineId;
    private Integer citationId;
    private Integer citationType;
    private String citationName;
    private String evaluationAlias;
    private String serviceItemInstanceId;
    private BigDecimal serviceItemListUnitPrice;
    private BigDecimal serviceItemSalesUnitPrice;
    private BigDecimal serviceItemDiscount;
    private Integer quantity;
    private BigDecimal serviceItemNetAmount;
    private BigDecimal serviceItemVATAmount;
    private BigDecimal serviceItemTotalAmount;
    private List<RdServiceItemLanguageDTO> languageList;
    private RdServiceItemExternalDTO externalInfo;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
    //SCI-1378
    private String serviceItemType;
    private Integer serviceItemSeq;
    //SCI-1469 5位小数，汇率字段
    private BigDecimal exchangeRate;
    //SCI-1469 2 位小数，额外追加多少费用
    private BigDecimal surCharge;
}
