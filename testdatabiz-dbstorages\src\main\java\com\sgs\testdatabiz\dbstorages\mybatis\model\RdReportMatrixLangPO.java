package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class RdReportMatrixLangPO {
    /**
     * id BIGINT(19) 必填<br>
     * RD 数据唯一标识
     */
    private Long id;

    /**
     * rd_report_matrix_id BIGINT(19)<br>
     * RD Report matrix标识
     */
    private Long rdReportMatrixId;

    /**
     * language_id INTEGER(10)<br>
     * 语言标识
     */
    private Integer languageId;

    /**
     * evaluation_alias VARCHAR(500)<br>
     * 测试项别名
     */
    private String evaluationAlias;

    /**
     * evaluation_name VARCHAR(255)<br>
     * 测试项名称
     */
    private String evaluationName;

    /**
     * citation_name VARCHAR(1000)<br>
     * 测试标准名称
     */
    private String citationName;

    /**
     * citation_full_name VARCHAR(255)<br>
     * 测试标准全称
     */
    private String citationFullName;

    /**
     * method_desc VARCHAR(512)<br>
     * 
     */
    private String methodDesc;

    /**
     * customer_conclusion VARCHAR(50)<br>
     * 客户测试结论
     */
    private String customerConclusion;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 RD 数据唯一标识
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 RD 数据唯一标识
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * rd_report_matrix_id BIGINT(19)<br>
     * 获得 RD Report matrix标识
     */
    public Long getRdReportMatrixId() {
        return rdReportMatrixId;
    }

    /**
     * rd_report_matrix_id BIGINT(19)<br>
     * 设置 RD Report matrix标识
     */
    public void setRdReportMatrixId(Long rdReportMatrixId) {
        this.rdReportMatrixId = rdReportMatrixId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 获得 语言标识
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 设置 语言标识
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * evaluation_alias VARCHAR(500)<br>
     * 获得 测试项别名
     */
    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    /**
     * evaluation_alias VARCHAR(500)<br>
     * 设置 测试项别名
     */
    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias == null ? null : evaluationAlias.trim();
    }

    /**
     * evaluation_name VARCHAR(255)<br>
     * 获得 测试项名称
     */
    public String getEvaluationName() {
        return evaluationName;
    }

    /**
     * evaluation_name VARCHAR(255)<br>
     * 设置 测试项名称
     */
    public void setEvaluationName(String evaluationName) {
        this.evaluationName = evaluationName == null ? null : evaluationName.trim();
    }

    /**
     * citation_name VARCHAR(1000)<br>
     * 获得 测试标准名称
     */
    public String getCitationName() {
        return citationName;
    }

    /**
     * citation_name VARCHAR(1000)<br>
     * 设置 测试标准名称
     */
    public void setCitationName(String citationName) {
        this.citationName = citationName == null ? null : citationName.trim();
    }

    /**
     * citation_full_name VARCHAR(255)<br>
     * 获得 测试标准全称
     */
    public String getCitationFullName() {
        return citationFullName;
    }

    /**
     * citation_full_name VARCHAR(255)<br>
     * 设置 测试标准全称
     */
    public void setCitationFullName(String citationFullName) {
        this.citationFullName = citationFullName == null ? null : citationFullName.trim();
    }

    /**
     * method_desc VARCHAR(512)<br>
     * 获得 
     */
    public String getMethodDesc() {
        return methodDesc;
    }

    /**
     * method_desc VARCHAR(512)<br>
     * 设置 
     */
    public void setMethodDesc(String methodDesc) {
        this.methodDesc = methodDesc == null ? null : methodDesc.trim();
    }

    /**
     * customer_conclusion VARCHAR(50)<br>
     * 获得 客户测试结论
     */
    public String getCustomerConclusion() {
        return customerConclusion;
    }

    /**
     * customer_conclusion VARCHAR(50)<br>
     * 设置 客户测试结论
     */
    public void setCustomerConclusion(String customerConclusion) {
        this.customerConclusion = customerConclusion == null ? null : customerConclusion.trim();
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", rdReportMatrixId=").append(rdReportMatrixId);
        sb.append(", languageId=").append(languageId);
        sb.append(", evaluationAlias=").append(evaluationAlias);
        sb.append(", evaluationName=").append(evaluationName);
        sb.append(", citationName=").append(citationName);
        sb.append(", citationFullName=").append(citationFullName);
        sb.append(", methodDesc=").append(methodDesc);
        sb.append(", customerConclusion=").append(customerConclusion);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append("]");
        return sb.toString();
    }
}