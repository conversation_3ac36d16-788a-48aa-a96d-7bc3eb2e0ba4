/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.quotation;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdAttachmentDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCustomerDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderHeaderDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdServiceItemDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdQuotationDTO extends RdOrderHeaderDTO implements Serializable{

    // add 20230529
    private Integer systemId;
    private String quotationNo;
    private RdQuotationRelationshipDTO relationship;
    private RdCustomerDTO payer;
    private String currency;
    private BigDecimal netAmount;
    private BigDecimal vatAmount;
    private BigDecimal totalAmount;
    private BigDecimal discount;
    private BigDecimal adjustmentAmount;
    private BigDecimal finalAmount;
    // add 20230529 version -> quotationVersionId
    private String quotationVersionId;
    private Integer quotationStatus;
    private List<RdServiceItemDTO> serviceItemList;
    private List<RdAttachmentDTO> quotationFileList;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

    private String quotationInstanceId;
}
