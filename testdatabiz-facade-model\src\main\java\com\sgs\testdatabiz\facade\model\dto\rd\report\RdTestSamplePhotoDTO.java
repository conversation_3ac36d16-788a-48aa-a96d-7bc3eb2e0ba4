/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestSamplePhotoDTO implements Serializable {

    private String fileType;
    private Integer toCustomerFlag;
    private String fileName;
    private String filePath;
    private String cloudId;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
