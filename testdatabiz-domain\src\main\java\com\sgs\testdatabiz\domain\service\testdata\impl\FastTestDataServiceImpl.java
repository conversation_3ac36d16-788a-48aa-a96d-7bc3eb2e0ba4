package com.sgs.testdatabiz.domain.service.testdata.impl;

/**
 *
 */
@Deprecated
public class FastTestDataServiceImpl {
//    /**
//     * 接口参数验证
//     * @param reqObject
//     * @return
//     */
//    @Override
//    protected CustomResult validate(SlimJobInfo reqObject) {
//        CustomResult rspResult = new CustomResult();
//
//
//        return rspResult;
//    }
//
//    /**
//     * 构建标准数据对象
//     * @param reqObject
//     * @param reqParam
//     * @return
//     */
//    @Override
//    protected CustomResult<ReportTestDataInfo> build(String reqObject, TestDataParamInfo reqParam) {
//        CustomResult rspResult = new CustomResult();
//
//
//        rspResult.setSuccess(true);
//        return rspResult;
//    }


}
