package com.sgs.testdatabiz.domain.service.rd.manager;

import cn.hutool.core.util.StrUtil;
import com.sgs.extsystem.facade.model.customer.req.CheckTestLineMappingReq;
import com.sgs.extsystem.facade.model.customer.req.MappingTestLineReq;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportMatrixLangMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestSampleGroupMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.TestSampleMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.*;
import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import com.sgs.testdatabiz.domain.service.utils.convertor.TestMatrixBuilder;
import com.sgs.testdatabiz.facade.model.rsp.config.CheckTestLineMappingExistsDTO;
import com.sgs.testdatabiz.integration.LocaliLayerClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: shawn.yang
 * @create: 2023-04-18 15:13
 */
@Slf4j
@Component
public class ReportMatrixManager {
    @Autowired
    private TestDataMatrixInfoExtMapper testDataMatrixInfoExtMapper;

    @Autowired
    private RdReportMatrixLangMapper reportMatrixLangMapper;

    @Autowired
    private ReportResultManager reportResultManager;

    @Autowired
    private TestSampleMapper testSampleMapper;

    @Autowired
    private TestSampleGroupMapper sampleGroupMapper;

    @Autowired
    private AttachmentManager attachmentManager;

    @Autowired
    private LocaliLayerClient localiLayerClient;

    @Autowired
    private TestMatrixBuilder testMatrixBuilder;

    private static final List<Integer> checkTestLineRefSystemIdList = new ArrayList<>();

    static {
        // 伊利
        checkTestLineRefSystemIdList.add(10016);
    }

    /**
     * ILayer定义 testResultType 配置为ReportConclusion 时，返回 2 ;
     */
    public static final String TEST_RESULT_TYPE_REPORT_CONCLUSION = "2";

    public Optional<CustomResult<List<CheckTestLineMappingExistsDTO>>> checkTestLinesMapping(List<RdTestLineDO> line, String buCode, Integer refSystemId) {
        CheckTestLineMappingReq req = new CheckTestLineMappingReq();
        req.setRefSystemId(refSystemId);
        req.setProductLineCode(buCode);
        List<Integer> collect = line.stream().map(RdTestLineDO::getTestLineId).collect(Collectors.toList());
        List<MappingTestLineReq> reqList = new ArrayList<>();
        collect.forEach(
                id -> {
                    MappingTestLineReq mappingTestLineReq = new MappingTestLineReq();
                    mappingTestLineReq.setTestLineId(id);
                    reqList.add(mappingTestLineReq);
                }
        );
        req.setTestLines(reqList);
        try {
            CustomResult<List<CheckTestLineMappingExistsDTO>> listCustomResult = localiLayerClient.queryTestLineMappingExists(req);
            if (listCustomResult.isSuccess()) {
                return Optional.ofNullable(listCustomResult);
            }
            throw new BizException("Error querying test line mapping exists");
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    public void validateLabelReviewCount(int count, int totalLines) {
        if (count > 0 && count != totalLines) {
            throw new BizException("Label review test can only appear alone");
        }
    }


    private Map<String, List<RdTestLineDO>> groupByTestLineInstanceId(List<RdTestLineDO> lines) {
        return lines.stream().collect(Collectors.groupingBy(RdTestLineDO::getTestLineInstanceId));
    }

    private Map<String, List<RdTestSampleDO>> groupByTestSampleInstanceId(List<RdTestSampleDO> samples) {
        return samples.stream().collect(Collectors.groupingBy(RdTestSampleDO::getTestSampleInstanceId));
    }


    /**
     * 保存Matrix和TestData数据
     *
     * @param reportMatrixList
     * @param testResultList
     * @param testLineList
     * @param testSampleList
     * @param conditionGroupList
     * @param testDataObjectRelList
     * @param labId
     * @param orderNo
     * @param reportNo
     * @param reportId
     * @param suffix
     */
    public void importReportMatrixAndTestData(
            List<RdReportMatrixDO> reportMatrixList,
            List<RdTestResultDO> testResultList,
            List<RdTestLineDO> testLineList,
            List<RdTestSampleDO> testSampleList,
            List<RdConditionGroupDO> conditionGroupList,
            List<TestDataObjectRelPO> testDataObjectRelList,
            Long labId,
            String orderNo,
            String reportNo,
            Long reportId,
            String suffix,
            String buCode,
            Long systemId,
            Integer refSystemId
    ) {

        if (Func.isEmpty(reportMatrixList)) {
            return;
        }
        // 通过流处理报告矩阵列表，提取唯一的测试线实例ID集合
        // 优化：提前计算和过滤，减少循环次数
        Set<String> distinctTestLineInstanceIds = reportMatrixList.stream()
                .map(RdReportMatrixDO::getTestLineInstanceId)
                .collect(Collectors.toSet());

        // 根据测试线实例ID过滤测试线列表，只保留存在于报告矩阵中的测试线
        List<RdTestLineDO> filteredTestLines = testLineList.stream()
                .filter(line -> distinctTestLineInstanceIds.contains(line.getTestLineInstanceId()))
                .collect(Collectors.toList());

        if (checkTestLineRefSystemIdList.contains(refSystemId)) {
            // 检查测试线与测试数据对象的关系，过滤出符合条件的测试线，并统计特定测试结果类型的数量
            List<CheckTestLineMappingExistsDTO> list = checkTestLinesMapping(filteredTestLines, buCode, refSystemId)
                    .map(CustomResult::getData)
                    .orElseGet(ArrayList::new);
            long count = list.stream().filter(l -> TEST_RESULT_TYPE_REPORT_CONCLUSION.equals(l.getTestResultType())).count();
            // 校验标签审核计数是否符合预期
            validateLabelReviewCount(Math.toIntExact(count), filteredTestLines.size());
            // SCI-436 End
        }

        // 构建测试数据对象与测试线的映射关系，用于后续的映射查找优化
        Map<String, String> objectRelMap = new HashMap<>();
        if (Func.isNotEmpty(testDataObjectRelList)) {
            for (TestDataObjectRelPO testDataObjectRelPO : testDataObjectRelList) {
                objectRelMap.put(testDataObjectRelPO.getExternalObjectNo(), testDataObjectRelPO.getId());
            }
        }
        // 根据测试线实例ID分组测试线，以便按测试线进行操作
        Map<String, List<RdTestLineDO>> testLineMap = groupByTestLineInstanceId(filteredTestLines);
        // 根据测试样本实例ID分组测试样本，以便按测试样本进行操作
        Map<String, List<RdTestSampleDO>> testSampleMap = groupByTestSampleInstanceId(testSampleList);


        // 构建testMatrix
        Map testMatrixMap = testMatrixBuilder.buildTestMatrix(
                reportMatrixList,
                testLineMap,
                testSampleMap,
                conditionGroupList,
                objectRelMap,
                labId,
                orderNo,
                reportNo,
                reportId
        );
        if (Func.isNotEmpty(testMatrixMap)) {
            List<String> objectRelIds = testDataObjectRelList.stream().map(TestDataObjectRelPO::getId).distinct().collect(Collectors.toList());
            Object testDataMatrixObject = testMatrixMap.get("testDataMatrix");
            if (Func.isNotEmpty(testDataMatrixObject)) {

                Map<Long, Long> matrixMap = new HashMap<>();

                List<TestDataMatrixInfoPO> testDataMatrixList = (List<TestDataMatrixInfoPO>) testDataMatrixObject;
                Map<String, TestDataMatrixInfoPO> testMatrixMaps = testDataMatrixList.stream().collect(Collectors.toMap(testMatrixBuilder::getTestDataReportMatrixMd5, Function.identity()));
                // 根据ObjectRel查询testDataMatrix数据
                if (Func.isNotEmpty(objectRelIds)) {

                    List<TestDataMatrixInfoPO> testDataMatrixInfoPOS = testDataMatrixInfoExtMapper.queryMatrixList(objectRelIds, suffix);
                    if (Func.isNotEmpty(testDataMatrixInfoPOS)) {

                        Object testDataMatrixLang = testMatrixMap.get("testDataMatrixLang");
                        Map<Long, List<RdReportMatrixLangPO>> matrixLangMap = new HashMap<>();
                        List<RdReportMatrixLangPO> matrixLangList = new ArrayList<>();
                        if (Func.isNotEmpty(testDataMatrixLang)) {
                            matrixLangList = (List<RdReportMatrixLangPO>) testDataMatrixLang;
                            matrixLangMap = matrixLangList.stream().collect(Collectors.groupingBy(RdReportMatrixLangPO::getRdReportMatrixId));
                        }
                        Map<Long, List<RdReportMatrixLangPO>> finalMatrixLangMap = matrixLangMap;
                        testDataMatrixInfoPOS.forEach(dbMatrix -> {
                            TestDataMatrixInfoPO testDataMatrix = testMatrixMaps.get(testMatrixBuilder.getTestDataReportMatrixMd5(dbMatrix));
                            if (testDataMatrix != null) {
                                Long id = testDataMatrix.getId();
                                List<RdReportMatrixLangPO> langPOList = finalMatrixLangMap.get(id);
                                if (Func.isNotEmpty(langPOList)) {
                                    // 更新MatrixLang下reportMatrixId
                                    langPOList.forEach(l -> l.setRdReportMatrixId(dbMatrix.getId()));
                                }
                                testDataMatrix.setId(dbMatrix.getId());
                                matrixMap.put(id, dbMatrix.getId());
                                return;
                            }
                            dbMatrix.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
                            testDataMatrixList.add(dbMatrix);
                        });
                        // 处理testDataMatrixLang数据
                        if (Func.isNotEmpty(matrixLangList)) {
                            List<Long> matrixIds = matrixLangList.stream().map(RdReportMatrixLangPO::getRdReportMatrixId).collect(Collectors.toList());
                            this.deleteTestMatrixLang(matrixIds);
                            reportMatrixLangMapper.batchInsert(matrixLangList);
                        }
                    }
                }

                testDataMatrixInfoExtMapper.batchInsert(testDataMatrixList, suffix);

                // 保存testData数据
                reportResultManager.saveTestDataList(
                        testResultList,
                        testLineList,
                        labId,
                        orderNo,
                        reportNo,
                        reportId,
                        suffix,
                        objectRelMap,
                        objectRelIds,
                        testDataMatrixList
                );

                // 保存 testSample数据
                Object testSample = testMatrixMap.get("testSample");
                if (Func.isNotEmpty(testSample)) {
                    List<Long> matrixIds = testDataMatrixList.stream().map(TestDataMatrixInfoPO::getId).collect(Collectors.toList());
                    // fixme 待修复，testSample和group update
                    List<TestSamplePO> testSamplePOList = (List<TestSamplePO>) testSample;
//                    List<String> sampleIds = testSamplePOList.stream().map(TestSamplePO::getId).collect(Collectors.toList());

                    this.deleteSampleByMatrixIds(matrixIds, matrixMap);
                    testSamplePOList.forEach(v -> {
                        Long rdReportMatrixId = v.getRdReportMatrixId();
                        Long aLong = matrixMap.get(rdReportMatrixId);
                        if (Func.isNotEmpty(aLong)) {
                            v.setRdReportMatrixId(aLong);
                        }
                    });
                    if (Func.isNotEmpty(testSamplePOList)) {
                        testSampleMapper.batchInsert(testSamplePOList);
                    }
                    Object sampleGroup = testMatrixMap.get("sampleGroup");
                    if (Func.isNotEmpty(sampleGroup)) {
                        List<TestSampleGroupPO> sampleGroupPOList = (List<TestSampleGroupPO>) sampleGroup;
//                        List<String> sampleGroupIdList = sampleGroupPOList.stream().map(TestSampleGroupPO::getSampleGroupId).collect(Collectors.toList());
//                        List<TestSampleGroupPO> sampleGroupList = new ArrayList<>();
                        sampleGroupMapper.batchInsert(sampleGroupPOList);
                    }
                }
            }

            Object testMatrixFile = testMatrixMap.get("testMatrixFile");
            if (Func.isNotEmpty(testMatrixFile)) {
                List<RdAttachmentDO> attachmentDOS = (List<RdAttachmentDO>) testMatrixFile;
                attachmentManager.batchInsertAttachmentDOList(attachmentDOS, systemId, reportNo, labId);
            }
        }
    }

    public void deleteSampleGroup(List<String> sampleGroupIds) {

    }

    public void deleteSampleByMatrixIds(List<Long> matrixIds, Map<Long, Long> matrixMap) {

//        for (int i = 0; i < matrixIds.size(); i++) {
//            Long aLong = matrixMap.get(matrixIds.get(i));
//            if (Func.isNotEmpty(aLong)) {
//                matrixIds.set(i, aLong);
//            }
//        }
//        List<Long> list = new ArrayList<>();
//        matrixIds.forEach(
//                l -> {
//                    Long aLong = matrixMap.get(l);
//                    if (Func.isNotEmpty(aLong)) {
//                        list.add(aLong);
//                    } else {
//                        list.add(l);
//                    }
//                }
//        );
//        matrixIds = list;

        TestSampleExample sampleExample = new TestSampleExample();
        sampleExample.createCriteria().andRdReportMatrixIdIn(matrixIds);

        List<TestSamplePO> testSamplePOList = testSampleMapper.selectByExample(sampleExample);
        if (Func.isNotEmpty(testSamplePOList)) {

            testSampleMapper.deleteByExample(sampleExample);

            List<String> sampleIds = testSamplePOList.stream().map(TestSamplePO::getSampleInstanceId).collect(Collectors.toList());
            TestSampleGroupExample sampleGroupExample = new TestSampleGroupExample();
            sampleGroupExample.createCriteria().andSampleIdIn(sampleIds);
            sampleGroupMapper.deleteByExample(sampleGroupExample);

//            testSamplePOList.forEach(l -> l.setActiveIndicator(ActiveType.Disable.getStatus()));
//            testSampleMapper.batchUpdate(testSamplePOList);

//            List<String> collect = testSamplePOList.stream().map(TestSamplePO::getId).collect(Collectors.toList());
//            List<String> list = this.compareList(collect, sampleIds);
//
//            TestSampleGroupExample sampleGroupExample = new TestSampleGroupExample();
//            sampleGroupExample.createCriteria().andSampleIdIn(list);
//            List<TestSampleGroupPO> testSampleGroupPOS = sampleGroupMapper.selectByExample(sampleGroupExample);
//
//            if (Func.isNotEmpty(testSampleGroupPOS)) {
//                testSampleGroupPOS.forEach(l -> l.setActiveIndicator(ActiveType.Disable.getStatus()));
//                sampleGroupMapper.batchUpdate(testSampleGroupPOS);
//            }
        }
    }

    public List<TestSamplePO> getTestSampleList(List<Long> matrixIds) {
        TestSampleExample sampleExample = new TestSampleExample();
        sampleExample.createCriteria().andRdReportMatrixIdIn(matrixIds).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return testSampleMapper.selectByExample(sampleExample);
    }

    public List<TestSampleGroupPO> getTestSampleGroupList(List<String> sampleIds) {
        TestSampleGroupExample sampleGroupExample = new TestSampleGroupExample();
        sampleGroupExample.createCriteria().andSampleIdIn(sampleIds).andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        return sampleGroupMapper.selectByExample(sampleGroupExample);
    }

    private List<String> compareList(List<String> a, List<String> b) {
        List<String> list = new ArrayList<>();
        a.forEach(
                l -> {
                    if (!b.contains(l)) {
                        list.add(l);
                    }
                }
        );
        return list;
    }

    public List<TestDataMatrixInfoPO> getTestMatrixList(String suffix, Long reportId) {
        return testDataMatrixInfoExtMapper.queryTestMatrixInfoByReportId(reportId, suffix);
    }

//    public List<TestDataMatrixInfoPO> queryTestMatrixInfoByOrderNo(String suffix, String orderNo) {
//        return testDataMatrixInfoExtMapper.queryTestMatrixInfoByOrderNo(orderNo, suffix);
//    }

    public List<RdReportMatrixLangPO> getReportMatrixLangList(List<Long> matrixIds) {
        RdReportMatrixLangExample matrixLangExample = new RdReportMatrixLangExample();
        matrixLangExample.createCriteria().andRdReportMatrixIdIn(matrixIds);
        return reportMatrixLangMapper.selectByExample(matrixLangExample);
    }

    public void deleteTestMatrixLang(List<Long> testMatrixIds) {
        RdReportMatrixLangExample matrixLangExample = new RdReportMatrixLangExample();
        matrixLangExample.createCriteria().andRdReportMatrixIdIn(testMatrixIds);
        reportMatrixLangMapper.deleteByExample(matrixLangExample);
    }
}
