package com.sgs.testdatabiz.sdk.client;

import com.sgs.testdatabiz.sdk.model.DictEnumInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class FrameWorkResp {
    ConcurrentHashMap<String, List<DictEnumInfo>> dataDictionaryMap = new ConcurrentHashMap<>();
}
