/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdPpTestLineRelDTO implements Serializable {
    @ApiModelProperty(value = "ppTlRelId",dataType = "string", required = true)
    private String ppTlRelId;
    private Long ppArtifactRelId;
    private Long ppBaseId;
    private Long rootPPBaseId;
    @ApiModelProperty(value = "ppNo",dataType = "integer", required = true)
    private Integer ppNo;
    private Integer ppVersionId;
    @ApiModelProperty(value = "ppName",dataType = "string", required = true)
    private String ppName;
    private String ppNotes;
    private Integer sectionId;
    private Integer sectionLevel;
    private String sectionName;
    private Long aid;
    private RdCitationDTO citation;
    //    private RdConclusionDTO conclusion;
    @ApiModelProperty(value = "languageList",dataType = "list", required = true)
    private List<RdPpTestLineRelLangDTO> languageList;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "activeIndicator",dataType = "integer", required = true)
    private Integer activeIndicator;

    /**
     * @since SCI-1276 Target Inspectorio
     */
    private String testCategoryCode;
}
