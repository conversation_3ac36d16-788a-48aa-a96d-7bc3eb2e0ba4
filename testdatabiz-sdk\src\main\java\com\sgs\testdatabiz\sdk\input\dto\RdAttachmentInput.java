/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdAttachmentInput implements Serializable{

    private String fileName;
    private String fileType;
    private Integer toCustomerFlag;
    private Integer objectType;
    private String objectNo;
    private String objectId;
    private String cloudId;
    private String filePath;
    private String bizType;
    private Integer languageId;

    private String attachmentInstanceId;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
