package com.sgs.testdatabiz.domain.service.validation.type.tablename;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.core.errorcode.ErrorCode;
import com.sgs.testdatabiz.core.errorcode.ErrorCodeFactory;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.domain.service.ConfigService;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.model.SubContactTableNameValidationDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@Component("subContactTableNameValidationService")
public class SubContactTableNameValidationService implements ValidationService {

    private static final String SUB_CONTACT_TABLE_NAME_MATRIX_INFO = "tb_test_data_matrix_info_";
    private static final String SUB_CONTACT_TABLE_NAME_TEST_DATA_INFO = "tb_test_data_info_";
    @Autowired
    private ConfigService configService;
    @Override
    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO) {

        if(!(validationRequestDTO instanceof SubContactTableNameValidationDTO)){
            return ValidationResultDTO.success("Validation request is not instance of SubContactTableNameValidationDTO");
        }
        AtomicReference<ValidationResultDTO> validationResultDTO = new AtomicReference<>();
        SubContactTableNameValidationDTO subContactTableNameValidationDTO = (SubContactTableNameValidationDTO) validationRequestDTO;
        String labCode = subContactTableNameValidationDTO.getLabCode();
        String suffix = StringUtil.getTestDataSuffix(labCode);
        String message = String.format("tb_test_data_matrix_info or test_data_info not exist with surffix," + suffix);
        if(Func.isNotEmpty(suffix)){
            if(checkMatrixTableNameExistFlag(suffix)&&checkTestDataTableNameExistFlag(suffix)){
                validationResultDTO.set(ValidationResultDTO.success(message));
                return validationResultDTO.get();
            }
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.REPORT_DATA_ERROR, ErrorBizModelEnum.SUB_CONTACT_TEST_DATA_BIZ, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.TABLE_NAME_NOT_EXIST);
            validationResultDTO.set(ValidationResultDTO.fail(errorCode, message));
        }
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.REPORT_DATA_ERROR, ErrorBizModelEnum.SUB_CONTACT_TEST_DATA_BIZ, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.TABLE_NAME_NOT_EXIST);
        validationResultDTO.set(ValidationResultDTO.fail(errorCode, message));
        return validationResultDTO.get();
    }

    private boolean checkTestDataTableNameExistFlag(String suffix) {
        String testDataTableName = SUB_CONTACT_TABLE_NAME_TEST_DATA_INFO + suffix;
        boolean testDataTableNameExistFlag = false;
        List<String> tableNameList = configService.getTableNameList(testDataTableName);
        if(Func.isNotEmpty(tableNameList)){
           if(tableNameList.contains(testDataTableName)){
               testDataTableNameExistFlag = true;
           }
        }
        return testDataTableNameExistFlag;
    }
    private boolean checkMatrixTableNameExistFlag(String suffix) {
        boolean matrixTableNameExistFlag = false;
        String matrixTableName = SUB_CONTACT_TABLE_NAME_MATRIX_INFO + suffix;
        List<String> tableNameList = configService.getTableNameList(matrixTableName);
        if(Func.isNotEmpty(tableNameList)){
            if(tableNameList.contains(matrixTableName)){
                matrixTableNameExistFlag = true;
            }
        }
        return matrixTableNameExistFlag;
    }

//    @Override
//    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO) {
//        return null;
//    }

    @Override
    public String getType() {
        return ValidationTypeEnum.SUBCONTRACT_TABLE.getName();
    }

    @Override
    public Integer getOrder() {
        return 1;
    }
}
