<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.RdReportLangMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="rd_report_id" property="rdReportId" jdbcType="BIGINT" />
    <result column="language_id" property="languageId" jdbcType="TINYINT" />
    <result column="report_header" property="reportHeader" jdbcType="VARCHAR" />
    <result column="report_address" property="reportAddress" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, rd_report_id, language_id, report_header, report_address
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_lang
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_lang
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangExample" >
    delete from tb_report_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO" >
    insert into tb_report_lang (id, rd_report_id, language_id, 
      report_header, report_address)
    values (#{id,jdbcType=BIGINT}, #{rdReportId,jdbcType=BIGINT}, #{languageId,jdbcType=TINYINT}, 
      #{reportHeader,jdbcType=VARCHAR}, #{reportAddress,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO" >
    insert into tb_report_lang
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="rdReportId != null" >
        rd_report_id,
      </if>
      <if test="languageId != null" >
        language_id,
      </if>
      <if test="reportHeader != null" >
        report_header,
      </if>
      <if test="reportAddress != null" >
        report_address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="rdReportId != null" >
        #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=TINYINT},
      </if>
      <if test="reportHeader != null" >
        #{reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="reportAddress != null" >
        #{reportAddress,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_lang
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_lang
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.rdReportId != null" >
        rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      </if>
      <if test="record.languageId != null" >
        language_id = #{record.languageId,jdbcType=TINYINT},
      </if>
      <if test="record.reportHeader != null" >
        report_header = #{record.reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAddress != null" >
        report_address = #{record.reportAddress,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_lang
    set id = #{record.id,jdbcType=BIGINT},
      rd_report_id = #{record.rdReportId,jdbcType=BIGINT},
      language_id = #{record.languageId,jdbcType=TINYINT},
      report_header = #{record.reportHeader,jdbcType=VARCHAR},
      report_address = #{record.reportAddress,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO" >
    update tb_report_lang
    <set >
      <if test="rdReportId != null" >
        rd_report_id = #{rdReportId,jdbcType=BIGINT},
      </if>
      <if test="languageId != null" >
        language_id = #{languageId,jdbcType=TINYINT},
      </if>
      <if test="reportHeader != null" >
        report_header = #{reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="reportAddress != null" >
        report_address = #{reportAddress,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO" >
    update tb_report_lang
    set rd_report_id = #{rdReportId,jdbcType=BIGINT},
      language_id = #{languageId,jdbcType=TINYINT},
      report_header = #{reportHeader,jdbcType=VARCHAR},
      report_address = #{reportAddress,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_report_lang
      (`id`,`rd_report_id`,`language_id`,
      `report_header`,`report_address`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.rdReportId, jdbcType=BIGINT},#{ item.languageId, jdbcType=TINYINT},
      #{ item.reportHeader, jdbcType=VARCHAR},#{ item.reportAddress, jdbcType=VARCHAR}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_report_lang 
      <set>
        <if test="item.rdReportId != null"> 
          `rd_report_id` = #{item.rdReportId, jdbcType = BIGINT},
        </if> 
        <if test="item.languageId != null"> 
          `language_id` = #{item.languageId, jdbcType = TINYINT},
        </if> 
        <if test="item.reportHeader != null"> 
          `report_header` = #{item.reportHeader, jdbcType = VARCHAR},
        </if> 
        <if test="item.reportAddress != null"> 
          `report_address` = #{item.reportAddress, jdbcType = VARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>