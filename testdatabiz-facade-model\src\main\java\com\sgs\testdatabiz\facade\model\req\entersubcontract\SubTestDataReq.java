package com.sgs.testdatabiz.facade.model.req.entersubcontract;

import com.sgs.framework.core.base.BaseRequest;

import java.util.Date;
import java.util.List;

public class SubTestDataReq extends BaseRequest {

    private String orderNo;

    private String reportNo;

    private String objectNo;

    private Integer sourceType;

    private String parentOrderNo;
    /**
     *
     */
    private Date completedDate;

    private List<SubTestDataMatrixInfo> matrixInfos;

    private List<SubTestDataReport> testDataReports;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public List<SubTestDataMatrixInfo> getMatrixInfos() {
        return matrixInfos;
    }

    public void setMatrixInfos(List<SubTestDataMatrixInfo> matrixInfos) {
        this.matrixInfos = matrixInfos;
    }

    public List<SubTestDataReport> getTestDataReports() {
        return testDataReports;
    }

    public void setTestDataReports(List<SubTestDataReport> testDataReports) {
        this.testDataReports = testDataReports;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }
}
