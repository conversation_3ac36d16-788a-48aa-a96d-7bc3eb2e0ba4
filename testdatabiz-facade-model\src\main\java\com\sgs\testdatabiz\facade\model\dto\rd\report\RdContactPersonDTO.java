/**
  * Copyright 2023 json.cn 
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdContactPersonDTO implements Serializable{
    private String contactUsage;
    @ApiModelProperty(value = "contactName",dataType = "string", required = true)
    private String contactName;
    private String contactEmail;
    private String contactPhone;
    private String contactTelephone;
    private String contactMobile;
    private String contactFAX;
    private String regionAccount;
    private String responsibleTeamCode;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}