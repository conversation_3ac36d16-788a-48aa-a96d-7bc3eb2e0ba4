/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestResultResultDTO implements Serializable {

    private String testResultFullName;

    private RdTestResultNameDTO testResultFullNameRel;

    private String resultValue;
    private String resultValueRemark;
    private String resultUnit;
    //SCI-1378
    private String failRemark;
    private String reportRemark;
    private Integer failFlag;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}