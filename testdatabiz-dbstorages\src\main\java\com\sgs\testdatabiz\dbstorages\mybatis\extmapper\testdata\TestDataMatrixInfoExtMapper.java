package com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataAnalyteConclusionInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMatrixExtPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataConclusionInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;

public interface TestDataMatrixInfoExtMapper {
    /**
     *
     * @param testMatrixs
     * @param suffix
     * @return
     */
    Integer batchInsert(@Param("testMatrixs") List<TestDataMatrixInfoPO> testMatrixs, @Param("suffix") String suffix);

    Integer batchInsertSlim(@Param("testMatrixs") List<TestDataMatrixInfoPO> testMatrixs);

    /**
     *
     * @param objectRelIds
     * @param suffix
     * @param modifiedBy
     * @return
     */
    Integer updateInvalidOrValid(@Param("objectRelIds") Set<String> objectRelIds,
                                 @Param("suffix") String suffix,
                                 @Param("modifiedBy") String modifiedBy);

    /**
     *
     * @param testMatrixs
     * @param suffix
     * @return
     */
    Integer batchUpdate(@Param("testMatrixs") Collection<TestDataMatrixInfoPO> testMatrixs, @Param("suffix") String suffix);

    /**
     *
     * @param orderNo
     * @param suffix
     * @return
     */
    List<TestDataMatrixExtPO> getTestDataMatrixInfoList(@Param("orderNo") String orderNo, @Param("suffix") String suffix);

    /**
     *
     * @param objectRelId
     * @param suffix
     * @return
     */
    List<TestDataMatrixInfoPO> queryMatrix(@Param("objectRelId") String objectRelId,@Param("suffix") String suffix);
    List<TestDataMatrixInfoPO> queryMatrixList(@Param("objectRelIds") List<String> objectRelIds,@Param("suffix") String suffix);
//    List<TestDataMatrixInfoPO> queryTestMatrixInfoByOrderNo(@Param("orderNo") String orderNo,@Param("suffix") String suffix);

    List<TestDataMatrixInfoPO> queryTestMatrixInfoByReportId(@Param("reportId")Long reportId, @Param("suffix")String suffix);

    /**
     *
     * @param orderNo
     * @param sourceTypes
     * @param suffix
     * @return
     */
    List<TestDataConclusionInfoPO> getConclusionInfoList(@Param("orderNo") String orderNo, @Param("sourceTypes") Set<Integer> sourceTypes, @Param("suffix") String suffix);


    List<TestDataConclusionInfoPO> getAllConclusionInfoList(@Param("orderNo") String orderNo, @Param("sourceTypes") Set<Integer> sourceTypes, @Param("suffix") String suffix);

    List<TestDataAnalyteConclusionInfoPO> getTestDataAnalyteConclusionInfoList(@Param("testDataMatrixId") String testDataMatrixId, @Param("suffix") String suffix);
    /**
     *
     * @param objectRelId
     * @param ModifiedDate
     * @return
     */
    Integer delTestMatrixInvalid(@Param("objectRelId")String objectRelId, @Param("modifiedDate") Date ModifiedDate);

    void updateConclusionIdById(@Param("conclusionId") String conclusionId, @Param("testMatrixId") Long testMatrixId, @Param("suffix") String suffix);

    void emptyTestMatrixByReport(@Param("rdReportId") Long rdReportId, @Param("suffix") String suffix);
}
