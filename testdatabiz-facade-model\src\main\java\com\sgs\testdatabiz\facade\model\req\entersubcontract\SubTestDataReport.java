package com.sgs.testdatabiz.facade.model.req.entersubcontract;

import com.sgs.framework.core.common.PrintFriendliness;

public class SubTestDataReport extends PrintFriendliness {

    private String testMatrixId;

    private String analyteName;

    private String reportUnit;

    private String testValue;

    private String reportLimit;

    private Integer analyteSeq;

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public String getAnalyteName() {
        return analyteName;
    }

    public void setAnalyteName(String analyteName) {
        this.analyteName = analyteName;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }
}
