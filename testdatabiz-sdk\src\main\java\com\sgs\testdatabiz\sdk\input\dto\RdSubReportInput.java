/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.sdk.input.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdSubReportInput implements Serializable {

    private Integer sourceType;
    private String objectNo;
    private String subReportId;
    private String subReportNo;
    private String externalObjectNo;


    private List<RdAttachmentInput> subReportFileList;


    private Date lastModifiedTimestamp;

    private Integer activeIndicator;

}
