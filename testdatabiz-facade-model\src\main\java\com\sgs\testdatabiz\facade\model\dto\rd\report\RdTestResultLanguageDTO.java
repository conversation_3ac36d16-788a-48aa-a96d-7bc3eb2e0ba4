/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdTestResultLanguageDTO implements Serializable {
    private Integer languageId;
    private String testResultFullName;
    // TODO 未来应该移除
    private String limitValueFullName;
    private String testAnalyteName;
    private String reportValueLimitFullName;
    private String methodValueLimitFullName;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
