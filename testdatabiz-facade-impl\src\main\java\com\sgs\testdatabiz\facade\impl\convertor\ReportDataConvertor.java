package com.sgs.testdatabiz.facade.impl.convertor;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdAttachmentPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportLangPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.RdReportProductDffPO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdAttachmentDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdProductDO;
import com.sgs.testdatabiz.domain.service.rd.domainobject.RdReportLanguageDO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public final class ReportDataConvertor {

    public static List<RdReportLanguageDO> convertorReportLang(List<RdReportLangPO> reportLangPOS) {
        if (Func.isNotEmpty(reportLangPOS)) {
            List<RdReportLanguageDO> list = new ArrayList<>();
            reportLangPOS.forEach(
                    l -> {
                        RdReportLanguageDO reportLanguageDO = new RdReportLanguageDO();
                        BeanUtils.copyProperties(l, reportLanguageDO);
                        list.add(reportLanguageDO);
                    }
            );
            return list;
        }
        return null;
    }

    public static List<RdProductDO> convertorProductDff(List<RdReportProductDffPO> productDffList) {
        if (Func.isNotEmpty(productDffList)) {
            List<RdProductDO> list = new ArrayList<>();
            productDffList.forEach(
                    l -> {
                        RdProductDO productDO = new RdProductDO();
                        BeanUtils.copyProperties(l, productDO);
                        list.add(productDO);
                    }
            );
            return list;
        }
        return null;
    }

    public static List<RdAttachmentDO> convertorAttachment(List<RdAttachmentPO> attachmentList) {
        if (Func.isNotEmpty(attachmentList)) {
            List<RdAttachmentDO> list = new ArrayList<>();
            attachmentList.forEach(
                    l -> {
                        RdAttachmentDO fileDO = new RdAttachmentDO();
                        BeanUtils.copyProperties(l, fileDO);
                        // TODO 待确认
                        fileDO.setFileType(l.getBizType());
                        list.add(fileDO);
                    }
            );
            return list;
        }
        return null;
    }
}
