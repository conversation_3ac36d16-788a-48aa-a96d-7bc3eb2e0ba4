package com.sgs.testdatabiz.domain.service.rd.domainobject;

import java.util.List;

import com.sgs.testdatabiz.facade.model.base.BaseModel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/5/31 20:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportDataDO extends BaseModel {
    private RdReportDO header;
    private List<RdTestSampleDO> testSampleList;
    private List<RdTestLineDO> testLineList;
//    private List<RdReportMatrixDO> reportMatrixList;
    private List<RdTestResultDO> testResultList;//对应和testDataList一致
    private List<RdReportConclusionDO> reportConclusionList;
    private List<RdAttachmentDO> reportFileList;
//    private List<RdSubReportDO> subReportList;
    private List<RdConditionGroupDO> conditionGroupList;
    private List<RdTrfDO> trfList;
    private RdOrderDO order;
    private List<RdQuotationDO> quotationList;
    private List<RdInvoiceDO> invoiceList;
}
