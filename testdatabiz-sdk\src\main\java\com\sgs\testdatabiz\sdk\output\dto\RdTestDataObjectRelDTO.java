package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/8 14:01
 */
@Data
public class RdTestDataObjectRelDTO extends BaseModel {

    private String productLineCode;

    private Long labId;

    private String labCode;

    private String orderNo;

    private String parentOrderNo;

    private String reportNo;

    private String objectNo;

    private String externalId;

    private String externalNo;

    private String externalObjectNo;

    private Integer sourceType;

    private Integer languageId;

    private Date completeDate;

    private String bizVersionId;

    private String sourceTypeLabel;

    private Integer activeIndicator;

    private Date lastModifiedTimestamp;

}
