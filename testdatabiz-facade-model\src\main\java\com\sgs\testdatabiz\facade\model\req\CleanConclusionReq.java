package com.sgs.testdatabiz.facade.model.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "CleanConclusionReq",description = "清洗Conclusion数据")
public final class CleanConclusionReq extends BaseRequest {
    /**
     *
     */
    @ApiModelProperty("表名")
    private String tableName;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
