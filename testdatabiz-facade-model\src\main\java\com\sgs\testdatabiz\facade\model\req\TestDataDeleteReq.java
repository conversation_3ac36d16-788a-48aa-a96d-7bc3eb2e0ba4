package com.sgs.testdatabiz.facade.model.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName TestDataDeleteReq
 * @Description 删除testData对应数据，目前notes revise使用
 * <AUTHOR>
 * @Date 2022/5/31
 */
@ApiModel(value = "TestDataDeleteReq", description = "TestDataDeleteReq")
public class TestDataDeleteReq extends BaseRequest {
    /**
     *
     */
    @ApiModelProperty(required = true, notes = "externalNo: 对应slimjobNo等")
    private String externalNo;
    @ApiModelProperty(required = true, notes = "externalObjectNo")
    private String externalObjectNo;
    /**
     *
     */
    @ApiModelProperty(required = true, notes = "subcontractNo：分包单号")
    private String objectNo;
    @ApiModelProperty(required = true, notes = "LabCode")
    private String labCode;
    @ApiModelProperty(required = true, notes = "sourceType")
    private Integer sourceType;

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getExternalObjectNo() {
        return externalObjectNo;
    }

    public void setExternalObjectNo(String externalObjectNo) {
        this.externalObjectNo = externalObjectNo;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }
}
