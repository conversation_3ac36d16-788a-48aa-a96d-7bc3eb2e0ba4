package com.sgs.testdatabiz.sdk.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.sdk.input.dto.ReportDataInput;
import com.sgs.testdatabiz.sdk.util.mapper.ReportDataMapper;

import java.io.File;
import java.io.FileInputStream;

public class ReportDataDOConvertUtil {
    public static String convertToReportDataDO(String requestJson) {
        // TODO: Implement the conversion logic
        ReportDataDTO reportDataDO = JSON.parseObject(requestJson, ReportDataDTO.class);
        ReportDataInput reportDataInput = ReportDataMapper.INSTANCE.convert2ReportDataInput(reportDataDO);
        return JSON.toJSONString(reportDataInput);
    }


    public static void main(String[] args) {

        //从文件读取内容
        try{
            File file = new File("/Users/<USER>/Documents/test/test2");
        String json = readFile(file, "UTF-8");
        System.out.println(convertToReportDataDO(json));
        } catch (JSONException e) {
            // 处理 JSON 解析异常
            System.err.println("Failed to parse JSON: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            // 处理无效的 JSON 字符串
            System.err.println("Invalid JSON string: " + e.getMessage());
        }
    }

    public static String readFile(File file, String encoding) {
        try {
            byte[] buffer = new byte[(int) file.length()];
            FileInputStream fis = new FileInputStream(file);
            fis.read(buffer);
            fis.close();
            return new String(buffer, encoding);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
