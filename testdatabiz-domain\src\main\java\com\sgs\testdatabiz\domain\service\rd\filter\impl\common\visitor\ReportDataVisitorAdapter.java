package com.sgs.testdatabiz.domain.service.rd.filter.impl.common.visitor;

import com.sgs.testdatabiz.domain.service.rd.domainobject.*;
import java.util.Optional;
import java.util.List;

/**
 * 报告数据访问者适配器
 * 提供默认的访问者实现，子类可以根据需要重写特定的访问方法
 */
public class ReportDataVisitorAdapter {
    protected final ReportDataVisitor visitor;
    protected final ReportDataDO reportData;

    public ReportDataVisitorAdapter(ReportDataVisitor visitor, ReportDataDO reportData) {
        this.visitor = visitor;
        this.reportData = reportData;
    }

    /**
     * 访问报告数据的所有部分
     */
    public void visit() {
        // 访问报告头部
        visitHeader();

        // 访问测试样本列表
        visitTestSamples();

        // 访问测试线列表
        visitTestLines();

        // 访问测试结果列表
        visitTestResults();

        // 访问报告结论列表
        visitReportConclusions();

        // 访问报告文件列表
        visitReportFiles();

        // 访问条件组列表
        visitConditionGroups();

        // 访问 TRF 列表
        visitTrfs();

        // 访问订单信息
        visitOrder();

        // 访问报价列表
        visitQuotations();

        // 访问发票列表
        visitInvoices();
    }

    /**
     * 访问报告头部及其相关对象
     */
    private void visitHeader() {
        Optional.ofNullable(reportData.getHeader())
                .ifPresent(header -> {
                    visitor.visitReportHeader(header);

                    // 访问报告矩阵列表
                    Optional.ofNullable(header.getReportMatrixList())
                            .ifPresent(matrices -> matrices.forEach(this::visitReportMatrix));

                    // 访问证书列表
                    Optional.ofNullable(header.getReportCertificateList())
                            .ifPresent(certificates -> certificates.forEach(visitor::visitReportCertificate));

                    // 访问证书信息
                    Optional.ofNullable(header.getCertificate())
                            .ifPresent(visitor::visitCertificate);

                    // 访问结论信息
                    Optional.ofNullable(header.getConclusion())
                            .ifPresent(visitor::visitConclusion);
                });
    }

    /**
     * 访问报告矩阵及其相关对象
     */
    private void visitReportMatrix(RdReportMatrixDO matrix) {
        visitor.visitReportMatrix(matrix);

        // 访问条件列表
        Optional.ofNullable(matrix.getConditionList())
                .ifPresent(conditions -> conditions.forEach(visitor::visitCondition));

        // 访问样本列表
        visitMatrixSpecimens(matrix);

        // 访问结论
        Optional.ofNullable(matrix.getConclusion())
                .ifPresent(visitor::visitConclusion);

        // 访问文件列表
        Optional.ofNullable(matrix.getTestMatrixFileList())
                .ifPresent(files -> files.forEach(visitor::visitAttachment));
    }

    /**
     * 处理矩阵的样本列表
     * 遍历每个样本，处理其相关的属性和子对象
     */
    private void visitMatrixSpecimens(RdReportMatrixDO matrix) {
        // 处理样本列表
        Optional.ofNullable(matrix.getSpecimenList())
                .ifPresent(specimens -> specimens.forEach(visitor::visitSpecimen));

        // 处理测试样本
        String testSampleInstanceId = matrix.getTestSampleInstanceId();
        if (testSampleInstanceId != null) {
            Optional.ofNullable(reportData.getTestSampleList())
                    .ifPresent(testSamples -> testSamples.stream()
                            .filter(sample -> testSampleInstanceId.equals(sample.getTestSampleInstanceId()))
                            .findFirst()
                            .ifPresent(visitor::visitTestSample));
        }
    }

    /**
     * 访问测试样本列表
     */
    private void visitTestSamples() {
        Optional.ofNullable(reportData.getTestSampleList())
                .ifPresent(samples -> samples.forEach(sample -> {
                    visitor.visitTestSample(sample);

                    // 访问样本组列表
                    Optional.ofNullable(sample.getTestSampleGroupList())
                            .ifPresent(groups -> groups.forEach(visitor::visitTestSampleGroup));

                    // 访问材料属性
                    Optional.ofNullable(sample.getMaterialAttr())
                            .ifPresent(visitor::visitMaterialAttr);

                    // 访问结论
                    Optional.ofNullable(sample.getConclusion())
                            .ifPresent(visitor::visitConclusion);

                    // 访问照片列表
                    Optional.ofNullable(sample.getTestSamplePhoto())
                            .ifPresent(photos -> photos.forEach(visitor::visitAttachment));
                }));
    }

    /**
     * 访问测试线列表
     */
    private void visitTestLines() {
        Optional.ofNullable(reportData.getTestLineList())
                .ifPresent(lines -> lines.forEach(line -> {
                    visitor.visitTestLine(line);

                    // 访问外部信息
                    Optional.ofNullable(line.getExternal())
                            .ifPresent(visitor::visitTestLineExternal);

                    // 访问引用信息
                    Optional.ofNullable(line.getCitation())
                            .ifPresent(visitor::visitCitation);

                    // 访问 WI 信息
                    Optional.ofNullable(line.getWi())
                            .ifPresent(visitor::visitWi);

                    // 访问分析物列表
                    Optional.ofNullable(line.getAnalyteList())
                            .ifPresent(analytes -> analytes.forEach(visitor::visitAnalyte));

                    // 访问 PP 测试线关系列表
                    Optional.ofNullable(line.getPpTestLineRelList())
                            .ifPresent(rels -> rels.forEach(visitor::visitPpTestLineRel));

                    // 访问结论
                    Optional.ofNullable(line.getConclusion())
                            .ifPresent(visitor::visitConclusion);
                }));
    }

    /**
     * 访问测试结果列表
     */
    private void visitTestResults() {
        Optional.ofNullable(reportData.getTestResultList())
                .ifPresent(results -> results.forEach(result -> {
                    visitor.visitTestResult(result);

                    // 访问子结果
                    Optional.ofNullable(result.getTestResult())
                            .ifPresent(visitor::visitTestResultResult);
                }));
    }

    /**
     * 访问报告结论列表
     */
    private void visitReportConclusions() {
        Optional.ofNullable(reportData.getReportConclusionList())
                .ifPresent(conclusions -> conclusions.forEach(visitor::visitReportConclusion));
    }

    /**
     * 访问报告文件列表
     */
    private void visitReportFiles() {
        Optional.ofNullable(reportData.getReportFileList())
                .ifPresent(files -> files.forEach(visitor::visitAttachment));
    }

    /**
     * 访问条件组列表
     */
    private void visitConditionGroups() {
        Optional.ofNullable(reportData.getConditionGroupList())
                .ifPresent(groups -> groups.forEach(visitor::visitConditionGroup));
    }

    /**
     * 访问 TRF 列表
     */
    private void visitTrfs() {
        Optional.ofNullable(reportData.getTrfList())
                .ifPresent(trfs -> trfs.forEach(visitor::visitTrf));
    }

    /**
     * 访问订单信息
     */
    private void visitOrder() {
        Optional.ofNullable(reportData.getOrder())
                .ifPresent(visitor::visitOrder);
    }

    /**
     * 访问报价列表
     */
    private void visitQuotations() {
        Optional.ofNullable(reportData.getQuotationList())
                .ifPresent(quotations -> quotations.forEach(visitor::visitQuotation));
    }

    /**
     * 访问发票列表
     */
    private void visitInvoices() {
        Optional.ofNullable(reportData.getInvoiceList())
                .ifPresent(invoices -> invoices.forEach(visitor::visitInvoice));
    }
} 