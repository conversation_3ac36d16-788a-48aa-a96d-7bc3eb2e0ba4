package com.sgs.testdatabiz.domain.service.rd.filter.constants;

public class ConclusionFilterConstants {
    
    public static class Code {
        public static final String STR_PASS = "PASS";
        public static final String STR_FAIL = "FAIL";
        public static final String STR_NC = "NC";
        public static final String STR_CANCELLED = "CANCELLED";

        public static final int CODE_PASS = 10;
        public static final int CODE_FAIL = 20;
        public static final int CODE_NC = 40;
        public static final int CODE_CANCELLED = 60;
    }
    
    public static class FieldPath {
        public static final String HEADER_CONCLUSION = "header.conclusion";
        public static final String TESTLINE_CONCLUSION = "testLine.conclusion";
    }
    
    public static class FilterName {
        public static final String CODE_TO_STRING = "ConclusionCodeToStringFilter";
        public static final String STRING_TO_CODE = "ConclusionStringToCodeFilter";
    }
    
    public static class Message {
        public static final String CONVERT = "Convert conclusion code from %s to %s";
    }
} 